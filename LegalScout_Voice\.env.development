# Vapi Configuration
# ==================
# CRITICAL FIX: API Key vs Assistant ID Confusion Resolved
# Based on MAKE_VAPI_WORK.md documentation
#
# ✅ CORRECT API Key for client-side operations
VITE_VAPI_PUBLIC_KEY=6734febc-fc65-4669-93b0-929b31ff6564

# Vapi API Base URL - Do not change this
VITE_VAPI_BASE_URL=https://api.vapi.ai

# Vapi Secret Key for server-side operations
VITE_VAPI_SECRET_KEY=6734febc-fc65-4669-93b0-929b31ff6564
VAPI_TOKEN=6734febc-fc65-4669-93b0-929b31ff6564

# ✅ CORRECT Default Assistant ID (NOT an API key) - Updated to match working assistant
VITE_VAPI_DEFAULT_ASSISTANT_ID=f9b97d13-f9c4-40af-a660-62ba5925ff2a

# Development Mode Settings
NODE_ENV=development
VITE_DEV_MODE=false

# Other configurations from .env
VITE_SUPABASE_URL=https://utopqxsvudgrtiwenlzl.supabase.co
VITE_SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU
VITE_FALLBACK_MODE=false
VITE_APIFY_API_TOKEN=**********************************************

# Gmail OAuth Configuration
VITE_GMAIL_CLIENT_ID=your_gmail_client_id_here
VITE_GMAIL_REDIRECT_URI=http://localhost:5174/auth/callback