# 🎯 Vapi Implementation Guide: Dashboard vs Subdomains

This guide explains the correct implementation patterns for different parts of the LegalScout Voice application.

## 📋 **Implementation Patterns**

### **1. Dashboard Implementation (React SDK)**

**Use Case:** Dashboard preview, testing, admin interfaces, React components
**Pattern:** `@vapi-ai/web` npm package with React integration
**API Exposure:** `window.Vapi` class constructor

```javascript
// Dashboard/React app pattern
import Vapi from '@vapi-ai/web';

const vapi = new Vapi('YOUR_PUBLIC_API_KEY');
vapi.start('YOUR_ASSISTANT_ID');

// Event handling
vapi.on('call-start', () => console.log('Call started'));
vapi.on('call-end', () => console.log('Call ended'));
vapi.on('message', (message) => console.log(message));
```

**Files Using This Pattern:**
- `src/components/VapiCall.jsx`
- `src/hooks/useVapiCall.js`
- `src/pages/Dashboard.jsx`
- `src/tests/VapiCallDiagnostics.jsx`

### **2. Subdomain Implementation (HTML Script Tag)**

**Use Case:** Public-facing attorney subdomains, client-facing pages, embedded widgets
**Pattern:** HTML script tag with widget configuration
**API Exposure:** `window.vapiSDK.run()` method

```javascript
// Subdomain/public page pattern
(function (d, t) {
  var g = document.createElement(t),
    s = d.getElementsByTagName(t)[0];
  g.src = "https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js";
  g.defer = true;
  g.async = true;
  s.parentNode.insertBefore(g, s);

  g.onload = function () {
    vapiInstance = window.vapiSDK.run({
      apiKey: "YOUR_PUBLIC_API_KEY",
      assistant: "YOUR_ASSISTANT_ID",
      config: buttonConfig,
    });
  };
})(document, "script");
```

**Files Using This Pattern:**
- `public/subdomain-vapi-widget.js`
- `public/attorney-subdomain-template.html`
- `public/test-official-vapi.html`

## 🔧 **Implementation Details**

### **Dashboard Implementation**

#### **Advantages:**
- ✅ Full programmatic control
- ✅ React integration
- ✅ Custom UI components
- ✅ Advanced event handling
- ✅ Testing and debugging tools

#### **Usage:**
```jsx
// In React components
import { useVapiCall } from '../hooks/useVapiCall';

const MyComponent = () => {
  const { startCall, stopCall, status } = useVapiCall({
    subdomain: 'attorney-subdomain',
    assistantId: 'assistant-id'
  });

  return (
    <button onClick={startCall}>
      {status === 'connected' ? 'End Call' : 'Start Call'}
    </button>
  );
};
```

### **Subdomain Implementation**

#### **Advantages:**
- ✅ Simple integration
- ✅ Automatic widget UI
- ✅ No build process required
- ✅ Works on any website
- ✅ Optimized for public pages

#### **Usage:**
```html
<!-- In HTML pages -->
<script src="/subdomain-vapi-widget.js"></script>
<script>
  initializeVapiWidget({
    subdomain: 'attorney-subdomain',
    assistantId: 'assistant-id-override', // optional
    customConfig: { ... } // optional
  });
</script>
```

## 🚨 **Common Issues & Solutions**

### **Issue 1: Mixed Implementation**
**Problem:** Using React SDK pattern in subdomain pages or vice versa
**Solution:** Use the correct pattern for each use case

### **Issue 2: CDN Loading Failures**
**Problem:** `window.Vapi` vs `window.vapiSDK` confusion
**Solution:** Our `vapiLoader.js` now bridges both patterns

### **Issue 3: Assistant ID Confusion**
**Problem:** Using different assistant IDs in different places
**Solution:** Centralized configuration in `vapiConstants.js`

## 📁 **File Structure**

```
LegalScout_Voice/
├── src/                          # Dashboard/React Implementation
│   ├── components/VapiCall.jsx   # React SDK pattern
│   ├── hooks/useVapiCall.js      # React SDK pattern
│   └── utils/vapiLoader.js       # Bridges both patterns
├── public/                       # Subdomain Implementation
│   ├── subdomain-vapi-widget.js  # HTML script tag pattern
│   └── attorney-subdomain-template.html
└── docs/
    └── VAPI_IMPLEMENTATION_GUIDE.md
```

## 🧪 **Testing Both Patterns**

### **Test Dashboard Implementation:**
```bash
npm run dev
# Navigate to: http://localhost:5174/vapi-test
```

### **Test Subdomain Implementation:**
```bash
# Navigate to: http://localhost:5174/test-official-vapi.html
# Or: http://localhost:5174/attorney-subdomain-template.html
```

## 🎯 **Best Practices**

### **For Dashboard:**
1. Always use `@vapi-ai/web` npm package
2. Use `useVapiCall` hook for state management
3. Handle events properly with React patterns
4. Use TypeScript for better type safety

### **For Subdomains:**
1. Always use HTML script tag pattern
2. Load attorney-specific configuration
3. Use widget for simple integration
4. Customize button appearance per attorney

### **For Both:**
1. Use the same API key (`VITE_VAPI_PUBLIC_KEY`)
2. Use consistent assistant IDs
3. Handle errors gracefully
4. Log events for debugging

## 🔄 **Migration Path**

If you need to migrate from one pattern to another:

### **Dashboard → Subdomain:**
1. Replace React components with HTML script tag
2. Use `window.vapiSDK.run()` instead of `new Vapi()`
3. Update event handling to widget pattern

### **Subdomain → Dashboard:**
1. Install `@vapi-ai/web` package
2. Replace HTML script tag with React import
3. Use `useVapiCall` hook for state management

## 📞 **Support**

If you encounter issues:
1. Check the browser console for errors
2. Verify you're using the correct pattern
3. Test with the diagnostic tools
4. Review this implementation guide
