# 🧪 Vapi Integration Testing Guide

This guide provides comprehensive testing tools to diagnose and resolve Vapi call issues in LegalScout Voice.

## 🚀 Quick Start

### 1. Command Line Test (Recommended First Step)

Run the automated test script to check basic configuration:

```bash
node scripts/test-vapi-integration.js
```

This will test:
- ✅ Environment variables
- ✅ File structure
- ✅ API connectivity
- ✅ Assistant configuration

### 2. Browser-Based Diagnostics

If the command line tests pass, run the interactive browser tests:

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Navigate to the test page:
   ```
   http://localhost:5174/vapi-test
   ```

3. Click "Run All Tests" to perform comprehensive diagnostics

## 🔍 What Each Test Checks

### Command Line Tests

| Test | Purpose | Common Issues |
|------|---------|---------------|
| **Environment Variables** | Validates all required Vapi env vars are set | Missing or incorrect API keys |
| **File Structure** | Ensures all Vapi integration files exist | Missing components after updates |
| **API Connectivity** | Tests connection to Vapi API | Network issues, invalid API key |
| **Assistant Configuration** | Verifies default assistant exists | Wrong assistant ID, deleted assistant |

### Browser Tests

| Test | Purpose | Common Issues |
|------|---------|---------------|
| **SDK Loading** | Tests if Vapi SDK loads in browser | CDN issues, network blocking |
| **Instance Creation** | Creates Vapi instance with API key | Invalid API key, browser compatibility |
| **Event Listeners** | Sets up call event handlers | SDK version issues |
| **Call Start** | Attempts to start actual call | Assistant issues, permissions |

## 🚨 Common Issues & Solutions

### 1. 401 Unauthorized Error
**Problem**: API key is invalid or incorrect
**Solution**: 
- Check `.env.development` has correct `VITE_VAPI_PUBLIC_KEY`
- Ensure you're using the API key, not assistant ID
- Verify API key has correct permissions in Vapi dashboard

### 2. Assistant Not Found (404)
**Problem**: Assistant ID doesn't exist
**Solution**:
- Check `VITE_VAPI_DEFAULT_ASSISTANT_ID` in `.env.development`
- Verify assistant exists in your Vapi account
- Create a new assistant if needed

### 3. SDK Loading Fails
**Problem**: Vapi SDK won't load in browser
**Solution**:
- Check network connectivity
- Verify CDN access to Vapi resources
- Try different browser or disable ad blockers

### 4. Call Connects But No Audio
**Problem**: Call starts but assistant doesn't speak
**Solution**:
- Check voice configuration in assistant
- Verify browser microphone permissions
- Test with different voice provider/model

### 5. Network/CORS Issues
**Problem**: Requests blocked by browser
**Solution**:
- Ensure running on localhost (not file://)
- Check browser console for CORS errors
- Verify Vapi API endpoints are accessible

## 📋 Test Results Interpretation

### ✅ All Tests Pass
Your Vapi integration is correctly configured. If calls still don't work:
1. Check browser console for runtime errors
2. Verify microphone permissions
3. Test with different browsers
4. Check Vapi service status

### ❌ Environment Variable Tests Fail
Fix your `.env.development` file:
1. Ensure all required variables are set
2. Check API key format (should be UUID)
3. Verify assistant ID format (should be UUID)
4. Remove any extra quotes or spaces

### ❌ API Connectivity Tests Fail
Network or authentication issue:
1. Check internet connection
2. Verify API key is correct and active
3. Test API key in Vapi dashboard
4. Check for firewall/proxy blocking

### ❌ Browser Tests Fail
Client-side integration issue:
1. Check browser console for errors
2. Verify SDK loading in Network tab
3. Test in different browser
4. Check for JavaScript errors

## 🔧 Advanced Debugging

### Enable Detailed Logging

Add this to your browser console for verbose Vapi logs:
```javascript
localStorage.setItem('vapi-debug', 'true');
```

### Manual API Testing

Test your API key directly:
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.vapi.ai/assistant
```

### Check Assistant Configuration

Verify your assistant settings:
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.vapi.ai/assistant/YOUR_ASSISTANT_ID
```

## 📞 Getting Help

If tests continue to fail:

1. **Check the logs**: Both command line and browser tests provide detailed error messages
2. **Review MAKE_VAPI_WORK.md**: Contains solutions to known issues
3. **Test with minimal setup**: Try the simplest possible Vapi integration first
4. **Check Vapi status**: Verify Vapi services are operational

## 🎯 Success Criteria

Your Vapi integration is working correctly when:

- ✅ All command line tests pass
- ✅ All browser tests pass  
- ✅ Call starts without errors
- ✅ Assistant speaks first message
- ✅ Voice input/output works
- ✅ Call can be ended cleanly

Once all tests pass, your Vapi integration should work reliably in the main application.
