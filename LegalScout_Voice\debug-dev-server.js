/**
 * Debug Development Server
 * Step-by-step debugging to find the router issue
 */

console.log('🔍 Starting debug dev server...');

try {
  console.log('1. Importing express...');
  const express = await import('express');
  console.log('✅ Express imported');

  console.log('2. Importing cors...');
  const cors = await import('cors');
  console.log('✅ CORS imported');

  console.log('3. Importing dotenv...');
  const dotenv = await import('dotenv');
  console.log('✅ Dotenv imported');

  console.log('4. Loading environment variables...');
  dotenv.default.config();
  console.log('✅ Environment variables loaded');

  console.log('5. Creating Express app...');
  const app = express.default();
  console.log('✅ Express app created');

  console.log('6. Setting up CORS...');
  app.use(cors.default({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'apikey', 'X-Client-Info', 'Prefer', 'Accept'],
    credentials: true
  }));
  console.log('✅ CORS configured');

  console.log('7. Setting up JSON parsing...');
  app.use(express.default.json());
  console.log('✅ JSON parsing configured');

  console.log('8. Setting up basic route...');
  app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });
  console.log('✅ Basic route configured');

  console.log('9. Testing server startup...');
  const PORT = 3002;
  const server = app.listen(PORT, () => {
    console.log(`✅ Debug server running on http://localhost:${PORT}`);
    console.log('🎉 Basic server setup successful!');
    
    // Now try to import the API handler
    console.log('10. Attempting to import API handler...');
    import('./api/index.js')
      .then(() => {
        console.log('✅ API handler imported successfully after server start');
      })
      .catch((error) => {
        console.error('❌ API handler import failed after server start:', error);
        console.error('Stack trace:', error.stack);
      });
  });

} catch (error) {
  console.error('❌ Debug server setup failed:', error);
  console.error('Stack trace:', error.stack);
}
