#!/usr/bin/env node

/**
 * Fix Import Meta Errors Script
 * 
 * This script fixes "Cannot use import statement outside a module" errors
 * by replacing direct import.meta usage with safe eval-based access.
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing import.meta errors in public directory...');

const publicDir = path.join(__dirname, 'public');

// Files to fix (based on the search results)
const filesToFix = [
  'critical-production-fix.js',
  'emergency-api-key-fix.js',
  'error-loop-fix.js',
  'fix-key-confusion.js',
  'fix-vapi-auth.js',
  'production-cors-fix.js',
  'production-signin-fix.js'
];

// Patterns to replace
const replacements = [
  {
    // Direct import.meta.env access
    pattern: /import\.meta\.env/g,
    replacement: 'getSafeImportMetaEnv()'
  },
  {
    // window.import.meta.env access
    pattern: /window\.import\.meta\.env/g,
    replacement: 'getSafeImportMetaEnv()'
  },
  {
    // typeof import !== 'undefined' checks
    pattern: /typeof import !== 'undefined'/g,
    replacement: 'false'
  }
];

// Safe import.meta.env accessor function
const safeAccessorFunction = `
// Safe import.meta.env accessor
function getSafeImportMetaEnv() {
  try {
    return eval('import.meta.env');
  } catch (e) {
    // Fallback to window globals or empty object
    return window.__VITE_ENV__ || {};
  }
}
`;

function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Add the safe accessor function if not already present
    if (!content.includes('getSafeImportMetaEnv')) {
      content = safeAccessorFunction + '\n' + content;
      modified = true;
    }

    // Apply replacements
    replacements.forEach(({ pattern, replacement }) => {
      const originalContent = content;
      content = content.replace(pattern, replacement);
      if (content !== originalContent) {
        modified = true;
      }
    });

    // Write back if modified
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${path.basename(filePath)}`);
      return true;
    } else {
      console.log(`📝 No changes needed: ${path.basename(filePath)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
let fixedCount = 0;
let totalCount = 0;

filesToFix.forEach(fileName => {
  const filePath = path.join(publicDir, fileName);
  totalCount++;
  if (fixFile(filePath)) {
    fixedCount++;
  }
});

console.log('\n📊 Summary:');
console.log(`Total files processed: ${totalCount}`);
console.log(`Files fixed: ${fixedCount}`);
console.log(`Files unchanged: ${totalCount - fixedCount}`);

if (fixedCount > 0) {
  console.log('\n✅ Import.meta errors have been fixed!');
  console.log('💡 The fixed files now use safe eval-based access to import.meta.env');
} else {
  console.log('\n📝 No files needed fixing.');
}
