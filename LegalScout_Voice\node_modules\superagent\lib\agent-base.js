"use strict";

const defaults = ['use', 'on', 'once', 'set', 'query', 'type', 'accept', 'auth', 'withCredentials', 'sortQuery', 'retry', 'ok', 'redirects', 'timeout', 'buffer', 'serialize', 'parse', 'ca', 'key', 'pfx', 'cert', 'disableTLSCerts'];
class Agent {
  constructor() {
    this._defaults = [];
  }
  _setDefaults(request) {
    for (const def of this._defaults) {
      request[def.fn](...def.args);
    }
  }
}
for (const fn of defaults) {
  // Default setting for all requests from this agent
  Agent.prototype[fn] = function () {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    this._defaults.push({
      fn,
      args
    });
    return this;
  };
}
module.exports = Agent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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