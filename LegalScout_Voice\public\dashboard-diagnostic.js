/**
 * Dashboard Diagnostic Tool
 * 
 * Comprehensive diagnostic tool to test all dashboard components and identify issues
 */

(function() {
  'use strict';

  // Prevent multiple loads
  if (window.__DASHBOARD_DIAGNOSTIC_LOADED) {
    console.log('[DashboardDiagnostic] Already loaded');
    return;
  }

  window.__DASHBOARD_DIAGNOSTIC_LOADED = true;

  // Configuration
  const config = {
    testTimeout: 5000,
    apiTimeout: 10000
  };

  // Logging utility
  function log(message, level = 'info') {
    const prefix = '[DashboardDiagnostic]';
    const timestamp = new Date().toISOString();
    console[level](`${timestamp} ${prefix} ${message}`);
  }

  // Test results storage
  const testResults = {
    environment: {},
    vapi: {},
    supabase: {},
    api: {},
    iframe: {},
    auth: {},
    overall: { passed: 0, failed: 0, warnings: 0 }
  };

  // Test environment variables
  function testEnvironment() {
    log('Testing environment variables...');
    
    const envVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_KEY', 
      'VITE_VAPI_PUBLIC_KEY',
      'VITE_VAPI_SECRET_KEY'
    ];

    envVars.forEach(varName => {
      let value;
      try {
        // Safely access import.meta.env
        const importMetaEnv = eval('import.meta.env');
        value = importMetaEnv[varName];
      } catch (e) {
        // Fallback to window globals
        value = window[varName];
      }

      testResults.environment[varName] = {
        present: !!value,
        value: value ? `${value.substring(0, 10)}...` : 'Not set'
      };

      if (value) {
        log(`✅ ${varName}: Present`);
        testResults.overall.passed++;
      } else {
        log(`❌ ${varName}: Missing`, 'error');
        testResults.overall.failed++;
      }
    });
  }

  // Test Vapi SDK
  function testVapiSDK() {
    log('Testing Vapi SDK...');
    
    // Check if Vapi is loaded
    if (window.Vapi) {
      log('✅ Vapi SDK loaded');
      testResults.vapi.loaded = true;
      testResults.overall.passed++;
      
      // Test Vapi instantiation
      try {
        const testKey = 'test-key-12345';
        const vapi = new window.Vapi(testKey);
        
        if (vapi && typeof vapi.start === 'function') {
          log('✅ Vapi instantiation successful');
          testResults.vapi.instantiation = true;
          testResults.overall.passed++;
        } else {
          log('❌ Vapi instantiation failed - missing methods', 'error');
          testResults.vapi.instantiation = false;
          testResults.overall.failed++;
        }
      } catch (error) {
        log(`❌ Vapi instantiation failed: ${error.message}`, 'error');
        testResults.vapi.instantiation = false;
        testResults.overall.failed++;
      }
    } else {
      log('❌ Vapi SDK not loaded', 'error');
      testResults.vapi.loaded = false;
      testResults.overall.failed++;
    }

    // Check helper functions
    if (window.createVapiInstance) {
      log('✅ Vapi helper function available');
      testResults.vapi.helper = true;
      testResults.overall.passed++;
    } else {
      log('⚠️ Vapi helper function not available', 'warn');
      testResults.vapi.helper = false;
      testResults.overall.warnings++;
    }
  }

  // Test Supabase
  function testSupabase() {
    log('Testing Supabase...');
    
    if (window.supabase) {
      log('✅ Supabase client loaded');
      testResults.supabase.loaded = true;
      testResults.overall.passed++;
      
      // Test basic query
      window.supabase
        .from('attorneys')
        .select('id')
        .limit(1)
        .then(({ data, error }) => {
          if (error) {
            log(`❌ Supabase query failed: ${error.message}`, 'error');
            testResults.supabase.query = false;
            testResults.overall.failed++;
          } else {
            log('✅ Supabase query successful');
            testResults.supabase.query = true;
            testResults.overall.passed++;
          }
        })
        .catch(error => {
          log(`❌ Supabase query error: ${error.message}`, 'error');
          testResults.supabase.query = false;
          testResults.overall.failed++;
        });
    } else {
      log('❌ Supabase client not loaded', 'error');
      testResults.supabase.loaded = false;
      testResults.overall.failed++;
    }
  }

  // Test API endpoints
  async function testAPI() {
    log('Testing API endpoints...');
    
    const endpoints = [
      '/api/health',
      '/api/env',
      '/api/sync-tools/manage-auth-state'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, {
          method: endpoint.includes('manage-auth-state') ? 'POST' : 'GET',
          headers: {
            'Content-Type': 'application/json'
          },
          body: endpoint.includes('manage-auth-state') ? JSON.stringify({
            authData: { user: { email: '<EMAIL>' } },
            action: 'test'
          }) : undefined
        });

        if (response.ok) {
          log(`✅ ${endpoint}: ${response.status}`);
          testResults.api[endpoint] = { status: response.status, success: true };
          testResults.overall.passed++;
        } else {
          log(`❌ ${endpoint}: ${response.status}`, 'error');
          testResults.api[endpoint] = { status: response.status, success: false };
          testResults.overall.failed++;
        }
      } catch (error) {
        log(`❌ ${endpoint}: ${error.message}`, 'error');
        testResults.api[endpoint] = { error: error.message, success: false };
        testResults.overall.failed++;
      }
    }
  }

  // Test iframe functionality
  function testIframes() {
    log('Testing iframe functionality...');
    
    // Check for iframe manager
    if (window.DashboardIframeManager) {
      log('✅ Dashboard Iframe Manager loaded');
      testResults.iframe.manager = true;
      testResults.overall.passed++;
      
      // Test iframe detection
      const iframeCount = window.DashboardIframeManager.getIframeCount();
      log(`Found ${iframeCount} preview iframes`);
      testResults.iframe.count = iframeCount;
      
      if (iframeCount > 0) {
        log('✅ Preview iframes detected');
        testResults.overall.passed++;
      } else {
        log('⚠️ No preview iframes found', 'warn');
        testResults.overall.warnings++;
      }
    } else {
      log('❌ Dashboard Iframe Manager not loaded', 'error');
      testResults.iframe.manager = false;
      testResults.overall.failed++;
    }

    // Check for regular iframes
    const allIframes = document.querySelectorAll('iframe');
    log(`Total iframes on page: ${allIframes.length}`);
    testResults.iframe.total = allIframes.length;
  }

  // Test authentication state
  function testAuth() {
    log('Testing authentication state...');
    
    // Check for auth context
    if (window.supabase && window.supabase.auth) {
      log('✅ Supabase auth available');
      testResults.auth.available = true;
      testResults.overall.passed++;
      
      // Get current session
      window.supabase.auth.getSession()
        .then(({ data: { session }, error }) => {
          if (error) {
            log(`❌ Auth session error: ${error.message}`, 'error');
            testResults.auth.session = false;
            testResults.overall.failed++;
          } else if (session) {
            log('✅ User authenticated');
            testResults.auth.session = true;
            testResults.auth.userId = session.user.id;
            testResults.overall.passed++;
          } else {
            log('⚠️ No active session', 'warn');
            testResults.auth.session = false;
            testResults.overall.warnings++;
          }
        })
        .catch(error => {
          log(`❌ Auth check failed: ${error.message}`, 'error');
          testResults.auth.session = false;
          testResults.overall.failed++;
        });
    } else {
      log('❌ Supabase auth not available', 'error');
      testResults.auth.available = false;
      testResults.overall.failed++;
    }
  }

  // Generate diagnostic report
  function generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      results: testResults,
      summary: {
        total: testResults.overall.passed + testResults.overall.failed + testResults.overall.warnings,
        passed: testResults.overall.passed,
        failed: testResults.overall.failed,
        warnings: testResults.overall.warnings,
        score: Math.round((testResults.overall.passed / (testResults.overall.passed + testResults.overall.failed)) * 100) || 0
      }
    };

    log('='.repeat(50));
    log('DASHBOARD DIAGNOSTIC REPORT');
    log('='.repeat(50));
    log(`Total Tests: ${report.summary.total}`);
    log(`Passed: ${report.summary.passed}`);
    log(`Failed: ${report.summary.failed}`);
    log(`Warnings: ${report.summary.warnings}`);
    log(`Score: ${report.summary.score}%`);
    log('='.repeat(50));

    // Store report globally
    window.__DASHBOARD_DIAGNOSTIC_REPORT = report;
    
    return report;
  }

  // Run all tests
  async function runDiagnostics() {
    log('Starting dashboard diagnostics...');
    
    testEnvironment();
    testVapiSDK();
    testSupabase();
    testIframes();
    testAuth();
    
    // API tests are async
    await testAPI();
    
    // Generate final report
    const report = generateReport();
    
    // Dispatch completion event
    const event = new CustomEvent('dashboardDiagnosticsComplete', {
      detail: report
    });
    window.dispatchEvent(event);
    
    return report;
  }

  // Expose public API
  window.DashboardDiagnostic = {
    run: runDiagnostics,
    getReport: () => window.__DASHBOARD_DIAGNOSTIC_REPORT,
    testEnvironment,
    testVapiSDK,
    testSupabase,
    testAPI,
    testIframes,
    testAuth
  };

  log('Dashboard Diagnostic Tool loaded. Run DashboardDiagnostic.run() to start tests.');

})();
