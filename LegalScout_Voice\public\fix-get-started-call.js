/**
 * Fix Get Started Call Issues
 * 
 * This script addresses the "Meeting ended due to ejection" error
 * by ensuring proper assistant configuration and call setup.
 */

(function() {
  'use strict';
  
  console.log('[FixGetStartedCall] 🔧 Initializing call fix...');
  
  // Configuration constants
  const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
  const PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';
  
  // Assistant configuration that prevents "Meeting ended due to ejection"
  const FIXED_ASSISTANT_CONFIG = {
    firstMessage: "Hello! I'm your legal assistant. I'm here to help you with your legal questions and connect you with the right attorney. How can I assist you today?",
    firstMessageMode: "assistant-speaks-first",
    systemMessage: `You are a helpful legal assistant for LegalScout. Your role is to:
1. Greet clients warmly and professionally
2. Ask about their legal needs and situation
3. Gather basic information about their case
4. Provide general legal guidance (not specific legal advice)
5. Help connect them with appropriate attorneys
6. Be empathetic and understanding

Always maintain a professional, helpful tone and remember that you cannot provide specific legal advice, but you can offer general information and guidance.`,
    voice: {
      provider: "playht",
      voiceId: "sarah"
    },
    transcriber: {
      provider: "deepgram",
      model: "nova-2",
      language: "en"
    },
    model: {
      provider: "openai",
      model: "gpt-4o",
      temperature: 0.7,
      maxTokens: 500
    },
    recordingEnabled: false,
    endCallMessage: "Thank you for using LegalScout. Have a great day!",
    endCallPhrases: ["goodbye", "end call", "hang up", "that's all"],
    maxDurationSeconds: 1800, // 30 minutes
    silenceTimeoutSeconds: 30,
    responseDelaySeconds: 0.4,
    llmRequestDelaySeconds: 0.1,
    numWordsToInterruptAssistant: 2,
    maxWordsPerMinute: 150,
    backgroundSound: "office"
  };
  
  /**
   * Fix Vapi instance creation and configuration
   */
  function fixVapiInstance() {
    console.log('[FixGetStartedCall] 🔧 Fixing Vapi instance...');
    
    // Store original Vapi constructor
    const OriginalVapi = window.Vapi;
    if (!OriginalVapi) {
      console.warn('[FixGetStartedCall] ⚠️ Vapi not available, cannot apply fix');
      return;
    }
    
    // Create enhanced Vapi constructor
    window.Vapi = function(apiKey, options = {}) {
      console.log('[FixGetStartedCall] 🚀 Creating enhanced Vapi instance');
      
      // Merge default options to prevent audio issues
      const enhancedOptions = {
        transcriber: {
          provider: "deepgram",
          model: "nova-2"
        },
        // Disable problematic audio features
        audioDeviceManagement: {
          enabled: false
        },
        ...options
      };
      
      // Create the original instance
      const instance = new OriginalVapi(apiKey, enhancedOptions);
      
      // Store original start method
      const originalStart = instance.start;
      
      // Enhanced start method with assistant config fix
      instance.start = function(assistantIdOrConfig, overrides = {}) {
        console.log('[FixGetStartedCall] 🎯 Enhanced start called');
        
        let finalConfig;
        
        if (typeof assistantIdOrConfig === 'string') {
          // If it's our problematic assistant, use fixed config
          if (assistantIdOrConfig === ASSISTANT_ID) {
            console.log('[FixGetStartedCall] 🔧 Applying fixed assistant configuration');
            finalConfig = {
              ...FIXED_ASSISTANT_CONFIG,
              ...overrides
            };
          } else {
            // For other assistants, use as-is
            finalConfig = assistantIdOrConfig;
          }
        } else {
          // If it's already a config object, enhance it
          finalConfig = {
            ...FIXED_ASSISTANT_CONFIG,
            ...assistantIdOrConfig,
            ...overrides
          };
        }
        
        console.log('[FixGetStartedCall] 📋 Final config:', {
          hasFirstMessage: !!finalConfig.firstMessage,
          hasSystemMessage: !!finalConfig.systemMessage,
          firstMessageMode: finalConfig.firstMessageMode,
          voice: finalConfig.voice
        });
        
        // Call original start with enhanced config
        return originalStart.call(this, finalConfig);
      };
      
      // Enhanced error handling
      const originalOn = instance.on;
      instance.on = function(event, callback) {
        if (event === 'error') {
          const enhancedCallback = function(error) {
            console.error('[FixGetStartedCall] 🚨 Vapi error intercepted:', error);
            
            // Handle specific errors
            if (error.message && error.message.includes('Meeting has ended')) {
              console.log('[FixGetStartedCall] 🔄 Attempting recovery from meeting ejection...');
              
              // Try to restart with fixed config after a delay
              setTimeout(() => {
                console.log('[FixGetStartedCall] 🔄 Restarting call with fixed configuration...');
                instance.start(FIXED_ASSISTANT_CONFIG);
              }, 2000);
            }
            
            // Call original callback
            if (callback) callback(error);
          };
          
          return originalOn.call(this, event, enhancedCallback);
        }
        
        return originalOn.call(this, event, callback);
      };
      
      console.log('[FixGetStartedCall] ✅ Enhanced Vapi instance created');
      return instance;
    };
    
    // Copy static properties
    Object.setPrototypeOf(window.Vapi, OriginalVapi);
    Object.assign(window.Vapi, OriginalVapi);
    
    console.log('[FixGetStartedCall] ✅ Vapi constructor enhanced');
  }
  
  /**
   * Fix environment variables
   */
  function fixEnvironmentVariables() {
    console.log('[FixGetStartedCall] 🔧 Fixing environment variables...');
    
    // Ensure window globals are set
    window.VITE_VAPI_PUBLIC_KEY = PUBLIC_KEY;
    window.VITE_VAPI_DEFAULT_ASSISTANT_ID = ASSISTANT_ID;
    
    // Fix import.meta.env if available
    try {
      const importMetaEnv = eval('import.meta.env');
      if (importMetaEnv) {
        importMetaEnv.VITE_VAPI_PUBLIC_KEY = PUBLIC_KEY;
        importMetaEnv.VITE_VAPI_DEFAULT_ASSISTANT_ID = ASSISTANT_ID;
      }
    } catch (e) {
      // import.meta not available, use window globals
    }
    
    console.log('[FixGetStartedCall] ✅ Environment variables fixed');
  }
  
  /**
   * Add call monitoring and recovery
   */
  function addCallMonitoring() {
    console.log('[FixGetStartedCall] 📊 Adding call monitoring...');
    
    // Monitor for call failures
    window.addEventListener('vapiError', function(event) {
      const error = event.detail;
      console.log('[FixGetStartedCall] 🚨 Vapi error detected:', error);
      
      if (error.errorMsg && error.errorMsg.includes('Meeting has ended')) {
        console.log('[FixGetStartedCall] 🔄 Implementing recovery strategy...');
        
        // Dispatch recovery event
        window.dispatchEvent(new CustomEvent('callRecoveryNeeded', {
          detail: { error, timestamp: Date.now() }
        }));
      }
    });
    
    console.log('[FixGetStartedCall] ✅ Call monitoring added');
  }
  
  /**
   * Initialize all fixes
   */
  function initializeFixes() {
    console.log('[FixGetStartedCall] 🚀 Initializing all fixes...');
    
    try {
      fixEnvironmentVariables();
      fixVapiInstance();
      addCallMonitoring();
      
      console.log('[FixGetStartedCall] ✅ All fixes applied successfully');
      
      // Expose configuration for debugging
      window.FIXED_ASSISTANT_CONFIG = FIXED_ASSISTANT_CONFIG;
      window.GET_STARTED_CALL_FIX_ACTIVE = true;
      
    } catch (error) {
      console.error('[FixGetStartedCall] ❌ Error applying fixes:', error);
    }
  }
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeFixes);
  } else {
    initializeFixes();
  }
  
  console.log('[FixGetStartedCall] 📋 Get Started call fix script loaded');
  
})();
