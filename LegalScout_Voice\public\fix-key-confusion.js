
// Safe getSafeImportMetaEnv() accessor
function getSafeImportMetaEnv() {
  try {
    return eval('getSafeImportMetaEnv()');
  } catch (e) {
    // Fallback to window globals or empty object
    return window.__VITE_ENV__ || {};
  }
}

/**
 * Fix Key Confusion
 * 
 * The error shows we're using private key where public key should be used.
 * This script fixes the key confusion issue.
 */

// Check if disabled by clean auth solution
if (window.__FIX_KEY_CONFUSION_DISABLED) {
  console.log('[FixKeyConfusion] 🚫 Disabled by clean auth solution');
  return;
}

console.log('[FixKeyConfusion] Fixing Vapi key confusion...');

// 1. Remove the problematic fetch interceptor that's adding private key to client calls
function removeProblematicFetchInterceptor() {
  console.log('[FixKeyConfusion] Removing problematic fetch interceptor...');
  
  // Store the original fetch (before any interceptors)
  if (!window.originalFetchBeforeInterceptors) {
    window.originalFetchBeforeInterceptors = window.fetch;
  }
  
  // Reset fetch to original
  window.fetch = window.originalFetchBeforeInterceptors;
  
  console.log('[FixKeyConfusion] ✅ Fetch reset to original');
}

// 2. Ensure Vapi constructor uses PUBLIC key only
function fixVapiConstructor() {
  console.log('[FixKeyConfusion] Fixing Vapi constructor...');
  
  const originalVapi = window.Vapi;
  if (originalVapi) {
    window.Vapi = function(apiKey) {
      // ALWAYS use the public key for client-side Vapi instances
      const publicKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';
      
      console.log('[FixKeyConfusion] 🔑 Vapi constructor called with:', apiKey?.substring(0, 8) + '...');
      console.log('[FixKeyConfusion] 🔑 Forcing use of PUBLIC key:', publicKey.substring(0, 8) + '...');
      
      return new originalVapi(publicKey);
    };
    
    // Copy static properties
    Object.setPrototypeOf(window.Vapi, originalVapi);
    Object.assign(window.Vapi, originalVapi);
  }
}

// 3. Fix environment variables to ensure public key is used
function fixEnvironmentVariables() {
  console.log('[FixKeyConfusion] Fixing environment variables...');
  
  const publicKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';
  const privateKey = '6734febc-fc65-4669-93b0-929b31ff6564';
  
  // Set window variables
  window.VITE_VAPI_PUBLIC_KEY = publicKey;
  window.VITE_VAPI_SECRET_KEY = privateKey;
  
  // Fix getSafeImportMetaEnv()
  if (window.import && window.import.meta && window.getSafeImportMetaEnv()) {
    window.getSafeImportMetaEnv().VITE_VAPI_PUBLIC_KEY = publicKey;
    window.getSafeImportMetaEnv().VITE_VAPI_SECRET_KEY = privateKey;
  }
  
  console.log('[FixKeyConfusion] ✅ Environment variables fixed');
}

// 4. Add minimal fetch interceptor that ONLY fixes Supabase, not Vapi
function addMinimalFetchInterceptor() {
  console.log('[FixKeyConfusion] Adding minimal fetch interceptor...');
  
  const originalFetch = window.fetch;
  
  window.fetch = function(url, options = {}) {
    // ONLY intercept Supabase requests, NOT Vapi requests
    if (typeof url === 'string' && url.includes('supabase.co')) {
      console.log('[FixKeyConfusion] 📊 Fixing Supabase request headers');
      
      options.headers = options.headers || {};
      
      if (options.headers instanceof Headers) {
        const headersObj = {};
        for (const [key, value] of options.headers.entries()) {
          headersObj[key] = value;
        }
        options.headers = headersObj;
      }
      
      const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
      
      options.headers.apikey = supabaseKey;
      options.headers.Authorization = options.headers.Authorization || `Bearer ${supabaseKey}`;
      options.headers['Content-Type'] = options.headers['Content-Type'] || 'application/json';
    }
    
    // For Vapi requests, DO NOT modify headers - let them use what they need
    if (typeof url === 'string' && url.includes('api.vapi.ai')) {
      console.log('[FixKeyConfusion] 📞 Vapi request - NOT modifying headers');
    }
    
    return originalFetch.call(this, url, options);
  };
  
  console.log('[FixKeyConfusion] ✅ Minimal fetch interceptor added');
}

// 5. Monitor for key usage
function monitorKeyUsage() {
  console.log('[FixKeyConfusion] Setting up key usage monitoring...');
  
  const originalFetch = window.fetch;
  
  window.fetch = function(url, options = {}) {
    if (typeof url === 'string' && url.includes('api.vapi.ai')) {
      console.log('[FixKeyConfusion] 📞 Vapi API call detected:', url);
      
      // Check what key is being used
      if (options.headers) {
        const authHeader = options.headers.Authorization || options.headers.authorization;
        if (authHeader) {
          const key = authHeader.replace('Bearer ', '');
          const keyType = key.startsWith('310f0d43') ? 'PUBLIC' : 
                         key.startsWith('6734febc') ? 'PRIVATE' : 'UNKNOWN';
          
          console.log('[FixKeyConfusion] 🔑 Authorization header key type:', keyType);
          
          if (keyType === 'PRIVATE') {
            console.error('[FixKeyConfusion] ❌ PRIVATE key detected in client call - this will fail!');
          } else if (keyType === 'PUBLIC') {
            console.log('[FixKeyConfusion] ✅ PUBLIC key detected - this should work');
          }
        } else {
          console.log('[FixKeyConfusion] 📋 No Authorization header found');
        }
      }
    }
    
    return originalFetch.call(this, url, options);
  };
}

// 6. Main initialization
function initializeKeyConfusionFix() {
  console.log('[FixKeyConfusion] Initializing key confusion fixes...');
  
  // Apply fixes in order
  removeProblematicFetchInterceptor();
  fixEnvironmentVariables();
  fixVapiConstructor();
  addMinimalFetchInterceptor();
  monitorKeyUsage();
  
  console.log('[FixKeyConfusion] ✅ All key confusion fixes applied');
  
  // Add test function
  window.testKeyConfiguration = function() {
    console.log('[FixKeyConfusion] 🧪 Testing key configuration...');
    
    const publicKey = window.VITE_VAPI_PUBLIC_KEY || window.import?.meta?.env?.VITE_VAPI_PUBLIC_KEY;
    const privateKey = window.VITE_VAPI_SECRET_KEY || window.import?.meta?.env?.VITE_VAPI_SECRET_KEY;
    
    console.log('Public Key:', publicKey?.substring(0, 8) + '...');
    console.log('Private Key:', privateKey?.substring(0, 8) + '...');
    console.log('Public Key Correct:', publicKey === '310f0d43-27c2-47a5-a76d-e55171d024f7');
    console.log('Private Key Correct:', privateKey === '6734febc-fc65-4669-93b0-929b31ff6564');
    
    // Test Vapi constructor
    if (window.Vapi) {
      console.log('Testing Vapi constructor...');
      try {
        const testInstance = new window.Vapi('test-key');
        console.log('Vapi constructor works');
      } catch (e) {
        console.error('Vapi constructor error:', e.message);
      }
    }
    
    return {
      publicKeySet: !!publicKey,
      privateKeySet: !!privateKey,
      publicKeyCorrect: publicKey === '310f0d43-27c2-47a5-a76d-e55171d024f7',
      privateKeyCorrect: privateKey === '6734febc-fc65-4669-93b0-929b31ff6564',
      vapiAvailable: !!window.Vapi
    };
  };
}

// Initialize immediately
initializeKeyConfusionFix();

console.log('[FixKeyConfusion] Key confusion fix script loaded');
console.log('[FixKeyConfusion] 💡 Run window.testKeyConfiguration() to verify setup');
