/**
 * Production Vapi Debugging Script
 * 
 * This script adds comprehensive debugging to identify the exact cause
 * of the 401 error in production. It should be loaded before the main app.
 */

(function() {
  'use strict';
  
  console.log('[ProductionVapiDebug] 🔍 Initializing Vapi debugging...');
  
  // Store original fetch to intercept Vapi API calls
  const originalFetch = window.fetch;
  
  // Track all Vapi API calls
  window.fetch = function(url, options) {
    // Check if this is a Vapi API call
    if (typeof url === 'string' && url.includes('api.vapi.ai')) {
      console.log('[ProductionVapiDebug] 🌐 Intercepted Vapi API call:');
      console.log('  URL:', url);
      console.log('  Method:', options?.method || 'GET');
      console.log('  Headers:', options?.headers);
      
      // Extract and analyze the Authorization header
      if (options?.headers?.Authorization) {
        const authHeader = options.headers.Authorization;
        const token = authHeader.replace('Bearer ', '');
        const tokenPreview = token ? token.substring(0, 8) + '...' : 'NONE';
        
        console.log('  🔑 API Key Analysis:');
        console.log('    Token Preview:', tokenPreview);
        console.log('    Is Public Key:', token === '310f0d43-27c2-47a5-a76d-e55171d024f7');
        console.log('    Is Secret Key:', token === '6734febc-fc65-4669-93b0-929b31ff6564');
        
        // Determine if this is the wrong key for the operation
        if (url.includes('/phone-number') && token === '310f0d43-27c2-47a5-a76d-e55171d024f7') {
          console.error('[ProductionVapiDebug] ❌ ISSUE DETECTED: Using PUBLIC key for phone-number operation!');
          console.error('  This will cause a 401 error. Phone number operations require the SECRET key.');
        }
        
        if (url.includes('/assistant') && token === '310f0d43-27c2-47a5-a76d-e55171d024f7') {
          console.warn('[ProductionVapiDebug] ⚠️ Using PUBLIC key for assistant operation (might work for read-only)');
        }
      } else {
        console.error('[ProductionVapiDebug] ❌ No Authorization header found!');
      }
    }
    
    // Call original fetch and log the response
    return originalFetch.apply(this, arguments).then(response => {
      if (typeof url === 'string' && url.includes('api.vapi.ai')) {
        console.log('[ProductionVapiDebug] 📡 Vapi API Response:');
        console.log('  Status:', response.status, response.statusText);
        console.log('  OK:', response.ok);
        
        if (!response.ok) {
          // Clone the response to read the error without consuming it
          response.clone().text().then(errorText => {
            console.error('[ProductionVapiDebug] ❌ Error Response:', errorText);
            
            if (response.status === 401) {
              console.error('[ProductionVapiDebug] 🎯 401 UNAUTHORIZED - This is the issue!');
              console.error('  Likely causes:');
              console.error('  1. Wrong API key type (public vs secret)');
              console.error('  2. Invalid API key');
              console.error('  3. API key not properly loaded');
            }
          });
        }
      }
      
      return response;
    });
  };
  
  // Debug environment variable access
  console.log('[ProductionVapiDebug] 🌍 Environment Analysis:');
  
  // Check import.meta.env (safely)
  try {
    // Use eval to safely check for import.meta without causing syntax errors
    const hasImportMeta = (function() {
      try {
        return typeof eval('import.meta') !== 'undefined';
      } catch (e) {
        return false;
      }
    })();

    if (hasImportMeta) {
      console.log('  import.meta.env available:', true);
      const importMetaEnv = eval('import.meta.env');
      console.log('  VITE_VAPI_PUBLIC_KEY:', importMetaEnv.VITE_VAPI_PUBLIC_KEY ? 'SET' : 'NOT SET');
      console.log('  VITE_VAPI_SECRET_KEY:', importMetaEnv.VITE_VAPI_SECRET_KEY ? 'SET' : 'NOT SET');

      if (importMetaEnv.VITE_VAPI_PUBLIC_KEY) {
        console.log('  Public key preview:', importMetaEnv.VITE_VAPI_PUBLIC_KEY.substring(0, 8) + '...');
      }
      if (importMetaEnv.VITE_VAPI_SECRET_KEY) {
        console.log('  Secret key preview:', importMetaEnv.VITE_VAPI_SECRET_KEY.substring(0, 8) + '...');
      }
    } else {
      console.log('  import.meta.env available:', false);
    }
  } catch (e) {
    console.log('  import.meta.env error:', e.message);
  }
  
  // Check window globals
  console.log('  Window globals:');
  console.log('    VITE_VAPI_PUBLIC_KEY:', window.VITE_VAPI_PUBLIC_KEY ? 'SET' : 'NOT SET');
  console.log('    VITE_VAPI_SECRET_KEY:', window.VITE_VAPI_SECRET_KEY ? 'SET' : 'NOT SET');
  
  if (window.VITE_VAPI_PUBLIC_KEY) {
    console.log('    Public key preview:', window.VITE_VAPI_PUBLIC_KEY.substring(0, 8) + '...');
  }
  if (window.VITE_VAPI_SECRET_KEY) {
    console.log('    Secret key preview:', window.VITE_VAPI_SECRET_KEY.substring(0, 8) + '...');
  }
  
  // Test the vapiConfig module loading
  console.log('[ProductionVapiDebug] 🔧 Testing vapiConfig module...');
  
  // Add a global function to test vapiConfig loading (without problematic import)
  window.testVapiConfig = function() {
    console.log('[ProductionVapiDebug] 🧪 Testing vapiConfig access...');

    try {
      // Instead of importing, test the hardcoded values we know should work
      const expectedPublicKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Corrected API key
      const expectedAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // Working assistant ID

      console.log('  🔑 Expected configuration:');
      console.log('    API key:', expectedPublicKey.substring(0, 8) + '...');
      console.log('    Assistant ID:', expectedAssistantId);

      // Test environment variable access
      let envPublicKey = window.VITE_VAPI_PUBLIC_KEY;
      let envAssistantId = window.VITE_VAPI_DEFAULT_ASSISTANT_ID;

      // Safely check import.meta.env
      try {
        const importMetaEnv = eval('import.meta.env');
        envPublicKey = envPublicKey || importMetaEnv?.VITE_VAPI_PUBLIC_KEY;
        envAssistantId = envAssistantId || importMetaEnv?.VITE_VAPI_DEFAULT_ASSISTANT_ID;
      } catch (e) {
        // import.meta not available, use window globals only
      }

      console.log('  🌍 Environment variables:');
      console.log('    API key from env:', envPublicKey ? envPublicKey.substring(0, 8) + '...' : 'NOT SET');
      console.log('    Assistant ID from env:', envAssistantId || 'NOT SET');

      const result = {
        expectedPublicKey,
        expectedAssistantId,
        envPublicKey,
        envAssistantId,
        configCorrect: envPublicKey === expectedPublicKey && envAssistantId === expectedAssistantId
      };

      if (result.configCorrect) {
        console.log('  ✅ Configuration is correct');
      } else {
        console.log('  ⚠️ Configuration mismatch detected');
        if (envPublicKey !== expectedPublicKey) {
          console.log('    API key mismatch');
        }
        if (envAssistantId !== expectedAssistantId) {
          console.log('    Assistant ID mismatch');
        }
      }

      return result;
    } catch (error) {
      console.error('  ❌ Error testing vapiConfig:', error);
      return null;
    }
  };
  
  // Test vapiMcpService key usage
  window.testVapiMcpService = async function() {
    console.log('[ProductionVapiDebug] 🧪 Testing vapiMcpService...');
    
    try {
      // Check if vapiMcpService is available globally
      if (window.vapiMcpService) {
        console.log('  ✅ vapiMcpService available globally');
        
        const service = window.vapiMcpService;
        console.log('  Service state:');
        console.log('    Connected:', service.connected);
        console.log('    Use Direct:', service.useDirect);
        console.log('    Direct API Key:', service.directApiKey ? service.directApiKey.substring(0, 8) + '...' : 'NONE');
        
        // Test the key resolution in the service
        if (service.directApiKey) {
          console.log('  🔑 Service key analysis:');
          console.log('    Is Public Key:', service.directApiKey === '310f0d43-27c2-47a5-a76d-e55171d024f7');
          console.log('    Is Secret Key:', service.directApiKey === '6734febc-fc65-4669-93b0-929b31ff6564');
          
          if (service.directApiKey === '310f0d43-27c2-47a5-a76d-e55171d024f7') {
            console.error('  ❌ ISSUE: Service is using PUBLIC key for direct API calls!');
            console.error('  This will cause 401 errors for phone number operations.');
          }
        }
        
        return service;
      } else {
        console.error('  ❌ vapiMcpService not available globally');
        return null;
      }
    } catch (error) {
      console.error('  ❌ Error testing vapiMcpService:', error);
      return null;
    }
  };
  
  // Add a comprehensive diagnostic function
  window.runVapiDiagnostics = async function() {
    console.log('[ProductionVapiDebug] 🔍 Running comprehensive Vapi diagnostics...');
    
    const results = {
      timestamp: new Date().toISOString(),
      environment: {
        hostname: window.location.hostname,
        userAgent: navigator.userAgent
      },
      configTest: null,
      serviceTest: null,
      apiTest: null
    };
    
    // Test 1: Config module
    results.configTest = window.testVapiConfig();
    
    // Test 2: Service state
    results.serviceTest = await window.testVapiMcpService();
    
    // Test 3: Direct API call
    try {
      console.log('[ProductionVapiDebug] 🧪 Testing direct API call...');
      
      const testKey = results.configTest?.secretKey || '6734febc-fc65-4669-93b0-929b31ff6564';
      
      const response = await fetch('https://api.vapi.ai/phone-number', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${testKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      results.apiTest = {
        success: response.ok,
        status: response.status,
        statusText: response.statusText
      };
      
      if (response.ok) {
        const data = await response.json();
        results.apiTest.phoneNumberCount = Array.isArray(data) ? data.length : 0;
        console.log('  ✅ Direct API test successful');
      } else {
        const errorText = await response.text();
        results.apiTest.error = errorText;
        console.error('  ❌ Direct API test failed:', response.status, errorText);
      }
    } catch (error) {
      results.apiTest = {
        success: false,
        error: error.message
      };
      console.error('  ❌ Direct API test error:', error);
    }
    
    console.log('[ProductionVapiDebug] 📊 Diagnostic Results:', results);
    return results;
  };
  
  // Auto-run basic diagnostics after a short delay
  setTimeout(() => {
    console.log('[ProductionVapiDebug] 🚀 Auto-running basic diagnostics...');
    window.testVapiConfig();
  }, 2000);
  
  console.log('[ProductionVapiDebug] ✅ Debugging initialized');
  console.log('[ProductionVapiDebug] 💡 Available functions:');
  console.log('  - window.testVapiConfig()');
  console.log('  - window.testVapiMcpService()');
  console.log('  - window.runVapiDiagnostics()');
})();
