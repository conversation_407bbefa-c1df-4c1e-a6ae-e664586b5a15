<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDN Access Test</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1a1a1a;
            color: #00ff00;
        }
        .test-result {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .pass { background: #004400; }
        .fail { background: #440000; }
        .info { background: #004444; }
        button {
            padding: 10px 20px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover { background: #0088ff; }
    </style>
</head>
<body>
    <h1>🌐 CDN Access Test</h1>
    <p>This page tests if Vapi CDN URLs are accessible from your network.</p>
    
    <button onclick="testCDNAccess()">Test CDN Access</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script>
        let results = [];

        function addResult(message, type = 'info') {
            results.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateDisplay();
        }

        function updateDisplay() {
            const container = document.getElementById('results');
            container.innerHTML = results.map(r => 
                `<div class="test-result ${r.type}">[${r.timestamp}] ${r.message}</div>`
            ).join('');
        }

        window.clearResults = function() {
            results = [];
            updateDisplay();
        }

        window.testCDNAccess = async function() {
            results = [];
            addResult('🧪 Testing CDN accessibility...');

            const cdnUrls = [
                'https://cdn.jsdelivr.net/npm/@vapi-ai/web@2.3.1/dist/vapi.js',
                'https://unpkg.com/@vapi-ai/web@2.3.1/dist/vapi.js',
                'https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js',
                'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/vapi.js'
            ];

            for (const url of cdnUrls) {
                try {
                    addResult(`🔍 Testing: ${url}`, 'info');
                    
                    // Test with fetch first
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 5000);
                    
                    const response = await fetch(url, {
                        method: 'HEAD',
                        signal: controller.signal,
                        mode: 'no-cors' // Allow cross-origin requests
                    });
                    
                    clearTimeout(timeoutId);
                    
                    if (response.type === 'opaque' || response.ok) {
                        addResult(`✅ ${url} - Accessible`, 'pass');
                        
                        // Try to actually load the script
                        try {
                            await loadScript(url);
                            if (window.Vapi) {
                                addResult(`✅ ${url} - Vapi SDK loaded successfully!`, 'pass');
                                addResult(`📋 Vapi type: ${typeof window.Vapi}`, 'info');
                                break; // Stop testing once we find a working one
                            } else {
                                addResult(`⚠️ ${url} - Script loaded but Vapi not available`, 'info');
                            }
                        } catch (scriptError) {
                            addResult(`❌ ${url} - Script loading failed: ${scriptError.message}`, 'fail');
                        }
                    } else {
                        addResult(`❌ ${url} - HTTP ${response.status}`, 'fail');
                    }
                } catch (error) {
                    if (error.name === 'AbortError') {
                        addResult(`❌ ${url} - Timeout (5s)`, 'fail');
                    } else {
                        addResult(`❌ ${url} - Error: ${error.message}`, 'fail');
                    }
                }
            }

            addResult('🏁 CDN access test completed!');
        }

        function loadScript(url) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = url;
                script.crossOrigin = 'anonymous';
                
                const timeout = setTimeout(() => {
                    script.remove();
                    reject(new Error('Script loading timeout'));
                }, 5000);

                script.onload = () => {
                    clearTimeout(timeout);
                    resolve();
                };

                script.onerror = (event) => {
                    clearTimeout(timeout);
                    script.remove();
                    reject(new Error('Script loading error'));
                };

                document.head.appendChild(script);
            });
        }
    </script>
</body>
</html>
