<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Fix Test</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1a1a1a;
            color: #00ff00;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass { background: #004400; }
        .fail { background: #440000; }
        .info { background: #004444; }
        button {
            padding: 10px 20px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover { background: #0088ff; }
    </style>
</head>
<body>
    <h1>🔧 Import Statement Fix Test</h1>
    <p>This page tests if the "Cannot use import statement outside a module" error has been fixed.</p>
    
    <button onclick="testImportFix()">Test Import Fix</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <!-- Load the fixed production-debug-vapi.js script -->
    <script src="/production-debug-vapi.js"></script>

    <script>
        let results = [];

        function addResult(message, type = 'info') {
            results.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateDisplay();
        }

        function updateDisplay() {
            const container = document.getElementById('results');
            container.innerHTML = results.map(r => 
                `<div class="test-result ${r.type}">[${r.timestamp}] ${r.message}</div>`
            ).join('');
        }

        window.clearResults = function() {
            results = [];
            updateDisplay();
        }

        window.testImportFix = function() {
            results = [];
            addResult('🧪 Testing import statement fix...');

            // Test 1: Check if production-debug-vapi.js loaded without errors
            addResult('📦 Testing production-debug-vapi.js loading...');
            
            if (window.testVapiConfig && typeof window.testVapiConfig === 'function') {
                addResult('✅ production-debug-vapi.js loaded successfully', 'pass');
                addResult('✅ window.testVapiConfig function is available', 'pass');
            } else {
                addResult('❌ production-debug-vapi.js failed to load or testVapiConfig not available', 'fail');
                return;
            }

            // Test 2: Try calling the testVapiConfig function
            addResult('🔧 Testing testVapiConfig function...');
            
            try {
                const configResult = window.testVapiConfig();
                if (configResult) {
                    addResult('✅ testVapiConfig executed successfully', 'pass');
                    addResult(`📋 Config result: ${JSON.stringify(configResult, null, 2)}`, 'info');
                } else {
                    addResult('⚠️ testVapiConfig returned null', 'info');
                }
            } catch (error) {
                addResult(`❌ testVapiConfig failed: ${error.message}`, 'fail');
                addResult(`Stack: ${error.stack}`, 'fail');
                return;
            }

            // Test 3: Check for any console errors
            addResult('🔍 Checking for console errors...');
            
            // Monitor console errors for a few seconds
            const originalError = console.error;
            let errorCount = 0;
            
            console.error = function(...args) {
                const errorMessage = args.join(' ');
                if (errorMessage.includes('Cannot use import statement outside a module')) {
                    errorCount++;
                    addResult(`❌ Import error detected: ${errorMessage}`, 'fail');
                }
                originalError.apply(console, args);
            };

            setTimeout(() => {
                console.error = originalError;
                if (errorCount === 0) {
                    addResult('✅ No import statement errors detected', 'pass');
                } else {
                    addResult(`❌ ${errorCount} import statement errors detected`, 'fail');
                }
            }, 2000);

            // Test 4: Test other diagnostic functions
            addResult('🧪 Testing other diagnostic functions...');
            
            if (window.testVapiMcpService && typeof window.testVapiMcpService === 'function') {
                addResult('✅ window.testVapiMcpService is available', 'pass');
            } else {
                addResult('⚠️ window.testVapiMcpService not available', 'info');
            }

            if (window.runVapiDiagnostics && typeof window.runVapiDiagnostics === 'function') {
                addResult('✅ window.runVapiDiagnostics is available', 'pass');
            } else {
                addResult('⚠️ window.runVapiDiagnostics not available', 'info');
            }

            // Test 5: Check environment variables
            addResult('🌍 Testing environment variable access...');
            
            try {
                // Test import.meta.env access (this was causing issues before)
                if (typeof import !== 'undefined' && import.meta && import.meta.env) {
                    addResult('✅ import.meta.env is accessible', 'pass');
                    const vapiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
                    if (vapiKey) {
                        addResult(`✅ VITE_VAPI_PUBLIC_KEY found: ${vapiKey.substring(0, 8)}...`, 'pass');
                    } else {
                        addResult('⚠️ VITE_VAPI_PUBLIC_KEY not found in import.meta.env', 'info');
                    }
                } else {
                    addResult('ℹ️ import.meta.env not available (expected in non-module context)', 'info');
                }
            } catch (error) {
                if (error.message.includes('Cannot use import statement outside a module')) {
                    addResult(`❌ Import statement error still occurring: ${error.message}`, 'fail');
                } else {
                    addResult(`ℹ️ Other error (not import-related): ${error.message}`, 'info');
                }
            }

            addResult('🏁 Import fix test completed!');
        }

        // Auto-run test after page loads
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 Auto-running import fix test...');
                testImportFix();
            }, 1000);
        });
    </script>
</body>
</html>
