<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Official Vapi Test</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1a1a1a;
            color: #00ff00;
        }
        .test-result {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .pass { background: #004400; }
        .fail { background: #440000; }
        .info { background: #004444; }
        button {
            padding: 10px 20px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover { background: #0088ff; }
    </style>
</head>
<body>
    <h1>🎯 Official Vapi SDK Test</h1>
    <p>This page uses the exact same method as shown in Vapi's official documentation.</p>
    
    <button onclick="testOfficialVapi()">Test Official Vapi SDK</button>
    <button onclick="testVapiCall()">Test Vapi Call</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script>
        let results = [];
        var vapiInstance = null;

        function addResult(message, type = 'info') {
            results.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateDisplay();
        }

        function updateDisplay() {
            const container = document.getElementById('results');
            container.innerHTML = results.map(r => 
                `<div class="test-result ${r.type}">[${r.timestamp}] ${r.message}</div>`
            ).join('');
        }

        window.clearResults = function() {
            results = [];
            updateDisplay();
        }

        window.testOfficialVapi = function() {
            results = [];
            addResult('🧪 Testing official Vapi SDK loading...');

            // Use the exact same configuration as our working setup
            const assistant = "f9b97d13-f9c4-40af-a660-62ba5925ff2a"; // Working assistant ID
            const apiKey = "6734febc-fc65-4669-93b0-929b31ff6564"; // Working API key
            const buttonConfig = {
                position: "bottom-right",
                offset: "40px",
                width: "50px",
                height: "50px",
                idle: {
                    color: `linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%)`,
                    type: "pill",
                    title: "Have a quick question?",
                    subtitle: "Talk with our AI assistant",
                    icon: `https://unpkg.com/lucide-static@0.321.0/icons/phone.svg`,
                },
                loading: {
                    color: `linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%)`,
                    type: "pill",
                    title: "Connecting...",
                    subtitle: "Please wait",
                    icon: `https://unpkg.com/lucide-static@0.321.0/icons/loader-2.svg`,
                },
                active: {
                    color: `linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%)`,
                    type: "pill",
                    title: "Call is in progress...",
                    subtitle: "End the call.",
                    icon: `https://unpkg.com/lucide-static@0.321.0/icons/phone-off.svg`,
                },
            };

            addResult(`📋 Using assistant: ${assistant}`, 'info');
            addResult(`🔑 Using API key: ${apiKey.substring(0, 8)}...`, 'info');

            // Use the exact same loading method as Vapi documentation
            (function (d, t) {
                var g = document.createElement(t),
                    s = d.getElementsByTagName(t)[0];
                g.src = "https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js";
                g.defer = true;
                g.async = true;
                s.parentNode.insertBefore(g, s);

                g.onload = function () {
                    addResult('✅ Official Vapi SDK loaded successfully', 'pass');
                    
                    try {
                        if (window.vapiSDK) {
                            addResult('✅ window.vapiSDK is available', 'pass');
                            addResult(`📋 vapiSDK type: ${typeof window.vapiSDK}`, 'info');
                            addResult(`📋 vapiSDK methods: ${Object.keys(window.vapiSDK).join(', ')}`, 'info');
                            
                            // Initialize Vapi instance
                            vapiInstance = window.vapiSDK.run({
                                apiKey: apiKey,
                                assistant: assistant,
                                config: buttonConfig,
                            });
                            
                            if (vapiInstance) {
                                addResult('✅ Vapi instance created successfully', 'pass');
                                addResult(`📋 Instance type: ${typeof vapiInstance}`, 'info');
                                
                                // Check if instance has expected methods
                                if (vapiInstance.start && typeof vapiInstance.start === 'function') {
                                    addResult('✅ start() method available', 'pass');
                                } else {
                                    addResult('❌ start() method missing', 'fail');
                                }
                                
                                if (vapiInstance.stop && typeof vapiInstance.stop === 'function') {
                                    addResult('✅ stop() method available', 'pass');
                                } else {
                                    addResult('❌ stop() method missing', 'fail');
                                }
                                
                                // Set up event listeners
                                if (vapiInstance.on && typeof vapiInstance.on === 'function') {
                                    addResult('✅ Event system available', 'pass');
                                    
                                    vapiInstance.on('call-start', () => {
                                        addResult('🎉 Call started successfully!', 'pass');
                                    });
                                    
                                    vapiInstance.on('call-end', () => {
                                        addResult('📞 Call ended', 'info');
                                    });
                                    
                                    vapiInstance.on('error', (error) => {
                                        addResult(`❌ Call error: ${error.message || error}`, 'fail');
                                    });
                                    
                                    addResult('✅ Event listeners set up', 'pass');
                                } else {
                                    addResult('❌ Event system not available', 'fail');
                                }
                                
                            } else {
                                addResult('❌ Failed to create Vapi instance', 'fail');
                            }
                        } else {
                            addResult('❌ window.vapiSDK not available after loading', 'fail');
                        }
                    } catch (error) {
                        addResult(`❌ Error initializing Vapi: ${error.message}`, 'fail');
                    }
                };

                g.onerror = function() {
                    addResult('❌ Failed to load official Vapi SDK', 'fail');
                };
            })(document, "script");
        }

        window.testVapiCall = function() {
            if (!vapiInstance) {
                addResult('❌ No Vapi instance available. Run "Test Official Vapi SDK" first.', 'fail');
                return;
            }

            addResult('📞 Attempting to start call...', 'info');
            
            try {
                // The official SDK should handle the call automatically with the widget
                // But we can also try to start it programmatically
                if (vapiInstance.start && typeof vapiInstance.start === 'function') {
                    vapiInstance.start();
                    addResult('✅ Call start initiated', 'pass');
                } else {
                    addResult('ℹ️ Using widget-based call (check for widget on page)', 'info');
                }
            } catch (error) {
                addResult(`❌ Call start failed: ${error.message}`, 'fail');
            }
        }
    </script>
</body>
</html>
