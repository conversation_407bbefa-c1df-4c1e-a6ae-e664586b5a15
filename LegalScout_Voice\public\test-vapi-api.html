<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background-color: #d1fae5; color: #065f46; }
        .status.error { background-color: #fee2e2; color: #991b1b; }
        .status.loading { background-color: #fef3c7; color: #92400e; }
        
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background-color: #2563eb; }
        button:disabled { background-color: #9ca3af; cursor: not-allowed; }
        
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.info { color: #333; }
        .log-entry.success { color: #28a745; }
        .log-entry.error { color: #dc3545; }
        .log-entry.warning { color: #ffc107; }
        
        .timestamp {
            color: #6c757d;
            margin-right: 10px;
        }
        
        .test-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Vapi API Direct Test</h1>
    <p>This page tests the Vapi API directly without the SDK to identify authentication and configuration issues.</p>

    <div class="container">
        <h2>Configuration</h2>
        <div class="test-section">
            <h3>Current Settings</h3>
            <p><strong>API Key:</strong> <span id="apiKeyDisplay">Loading...</span></p>
            <p><strong>Assistant ID:</strong> <span id="assistantIdDisplay">Loading...</span></p>
            <p><strong>API Base URL:</strong> <span id="apiUrlDisplay">https://api.vapi.ai</span></p>
        </div>
    </div>

    <div class="container">
        <h2>API Tests</h2>
        <div id="status" class="status loading">Ready to test</div>
        
        <div>
            <button onclick="testApiKey()">Test API Key</button>
            <button onclick="listAllAssistants()">List All Assistants</button>
            <button onclick="testAssistantExists()">Test Assistant Exists</button>
            <button onclick="createTestAssistant()">Create Test Assistant</button>
            <button onclick="testCreateCall()">Test Create Call</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>

    <div class="container">
        <h2>Test Results</h2>
        <div id="logs" class="log-container">
            <div class="log-entry info">
                <span class="timestamp">[Starting]</span>
                Ready to run API tests...
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const CONFIG = {
            apiKey: '6734febc-fc65-4669-93b0-929b31ff6564',
            assistantId: '310f0d43-27c2-47a5-a76d-e55171d024f7',
            apiUrl: 'https://api.vapi.ai'
        };

        // Update display
        document.getElementById('apiKeyDisplay').textContent = CONFIG.apiKey.substring(0, 8) + '...';
        document.getElementById('assistantIdDisplay').textContent = CONFIG.assistantId;

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logsContainer = document.getElementById('logs');
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span>${message}`;
            
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            
            console.log(`[VapiAPITest] ${message}`);
        }

        function updateStatus(status, message) {
            const statusElement = document.getElementById('status');
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }

        async function makeApiRequest(endpoint, method = 'GET', body = null) {
            const url = `${CONFIG.apiUrl}${endpoint}`;
            const headers = {
                'Authorization': `Bearer ${CONFIG.apiKey}`,
                'Content-Type': 'application/json'
            };

            addLog(`🌐 Making ${method} request to: ${url}`, 'info');
            addLog(`🔑 Using API key: ${CONFIG.apiKey.substring(0, 8)}...`, 'info');

            try {
                const response = await fetch(url, {
                    method,
                    headers,
                    body: body ? JSON.stringify(body) : null
                });

                addLog(`📡 Response status: ${response.status} ${response.statusText}`, 'info');

                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }

                if (response.ok) {
                    addLog(`✅ Success: ${JSON.stringify(responseData, null, 2)}`, 'success');
                    return { success: true, data: responseData, status: response.status };
                } else {
                    addLog(`❌ Error: ${JSON.stringify(responseData, null, 2)}`, 'error');
                    return { success: false, error: responseData, status: response.status };
                }
            } catch (error) {
                addLog(`❌ Network error: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }

        async function testApiKey() {
            addLog('🔍 Testing API key validity...', 'info');
            updateStatus('loading', 'Testing API key...');

            // Test with a simple endpoint that should work with any valid API key
            const result = await makeApiRequest('/assistant');

            if (result.success) {
                addLog('✅ API key is valid!', 'success');
                updateStatus('success', 'API key is valid');
            } else {
                addLog(`❌ API key test failed: ${result.error}`, 'error');
                updateStatus('error', `API key invalid: ${result.status}`);
            }
        }

        async function listAllAssistants() {
            addLog('🔍 Listing all assistants in your account...', 'info');
            updateStatus('loading', 'Fetching assistants...');

            const result = await makeApiRequest('/assistant');

            if (result.success) {
                addLog('✅ Successfully retrieved assistants!', 'success');

                if (Array.isArray(result.data)) {
                    addLog(`📋 Found ${result.data.length} assistants:`, 'info');

                    result.data.forEach((assistant, index) => {
                        const assistantInfo = {
                            id: assistant.id,
                            name: assistant.name || 'Unnamed',
                            model: assistant.model?.model || 'Unknown model',
                            voice: assistant.voice ? `${assistant.voice.provider}:${assistant.voice.voiceId}` : 'No voice',
                            createdAt: assistant.createdAt ? new Date(assistant.createdAt).toLocaleDateString() : 'Unknown'
                        };

                        addLog(`${index + 1}. ${assistantInfo.name} (ID: ${assistantInfo.id})`, 'info');
                        addLog(`   Model: ${assistantInfo.model}, Voice: ${assistantInfo.voice}`, 'info');
                        addLog(`   Created: ${assistantInfo.createdAt}`, 'info');

                        // Check if this matches our current assistant ID
                        if (assistant.id === CONFIG.assistantId) {
                            addLog(`   ✅ This matches our current assistant ID!`, 'success');
                        }
                    });

                    if (result.data.length === 0) {
                        addLog('⚠️ No assistants found. You may need to create one first.', 'warning');
                    }
                } else {
                    addLog(`📋 Assistant data: ${JSON.stringify(result.data, null, 2)}`, 'info');
                }

                updateStatus('success', `Found ${Array.isArray(result.data) ? result.data.length : 'some'} assistants`);
            } else {
                addLog(`❌ Failed to list assistants: ${result.error}`, 'error');
                updateStatus('error', `Failed to list assistants: ${result.status}`);
            }
        }

        async function testAssistantExists() {
            addLog('🔍 Testing if assistant exists...', 'info');
            updateStatus('loading', 'Testing assistant...');

            const result = await makeApiRequest(`/assistant/${CONFIG.assistantId}`);
            
            if (result.success) {
                addLog('✅ Assistant exists and is accessible!', 'success');
                addLog(`📋 Assistant details: ${JSON.stringify(result.data, null, 2)}`, 'info');
                updateStatus('success', 'Assistant exists');
            } else {
                addLog(`❌ Assistant test failed: ${result.error}`, 'error');
                if (result.status === 404) {
                    addLog('💡 Assistant not found - this might be the issue!', 'warning');
                } else if (result.status === 401) {
                    addLog('💡 Unauthorized - API key might be invalid', 'warning');
                }
                updateStatus('error', `Assistant test failed: ${result.status}`);
            }
        }

        async function createTestAssistant() {
            addLog('🔍 Creating a test assistant...', 'info');
            updateStatus('loading', 'Creating assistant...');

            const assistantData = {
                name: "LegalScout Test Assistant",
                model: {
                    provider: "openai",
                    model: "gpt-4o",
                    messages: [
                        {
                            role: "system",
                            content: "You are Scout, a helpful AI legal assistant for LegalScout. You help potential clients understand their legal options and connect them with qualified attorneys. Always be professional, empathetic, and clear that you cannot provide legal advice, only general information."
                        }
                    ]
                },
                voice: {
                    provider: "11labs",
                    voiceId: "sarah"
                },
                firstMessage: "Hello! I'm Scout, your AI legal assistant from LegalScout. How can I help you today?",
                firstMessageMode: "assistant-speaks-first"
            };

            const result = await makeApiRequest('/assistant', 'POST', assistantData);

            if (result.success) {
                addLog('✅ Assistant created successfully!', 'success');
                addLog(`🤖 Assistant ID: ${result.data.id}`, 'success');
                addLog(`📋 Assistant details: ${JSON.stringify(result.data, null, 2)}`, 'info');

                // Update the config to use the new assistant ID
                CONFIG.assistantId = result.data.id;
                document.getElementById('assistantIdDisplay').textContent = result.data.id;

                addLog(`💡 Updated config to use new assistant ID: ${result.data.id}`, 'info');
                updateStatus('success', 'Assistant created and configured');
            } else {
                addLog(`❌ Assistant creation failed: ${result.error}`, 'error');
                if (result.status === 400) {
                    addLog('💡 Bad request - check the assistant data format', 'warning');
                } else if (result.status === 401) {
                    addLog('💡 Unauthorized - check your API key permissions', 'warning');
                }
                updateStatus('error', `Assistant creation failed: ${result.status}`);
            }
        }

        async function testCreateCall() {
            addLog('🔍 Testing call creation...', 'info');
            updateStatus('loading', 'Testing call creation...');

            const callData = {
                assistantId: CONFIG.assistantId,
                type: 'webCall'
            };

            const result = await makeApiRequest('/call', 'POST', callData);

            if (result.success) {
                addLog('✅ Call creation successful!', 'success');
                addLog(`📞 Call details: ${JSON.stringify(result.data, null, 2)}`, 'info');
                updateStatus('success', 'Call creation works');
            } else {
                addLog(`❌ Call creation failed: ${result.error}`, 'error');
                if (result.status === 400) {
                    addLog('💡 Bad request - check the call data format', 'warning');
                } else if (result.status === 404) {
                    addLog('💡 Assistant not found for call creation', 'warning');
                }
                updateStatus('error', `Call creation failed: ${result.status}`);
            }
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
            addLog('🧹 Logs cleared', 'info');
        }

        // Auto-run basic tests on load
        window.addEventListener('load', () => {
            addLog('🚀 Page loaded, ready for testing', 'info');
            updateStatus('success', 'Ready to test');
        });
    </script>
</body>
</html>
