<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Config Test</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1a1a1a;
            color: #00ff00;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass { background: #004400; }
        .fail { background: #440000; }
        .info { background: #004444; }
        button {
            padding: 10px 20px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover { background: #0088ff; }
    </style>
</head>
<body>
    <h1>🔧 Vapi Configuration Test</h1>
    <p>This page tests the Vapi configuration without the full React app.</p>
    
    <button onclick="runTests()">Run Tests</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script type="module">
        let results = [];

        function addResult(message, type = 'info') {
            results.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateDisplay();
        }

        function updateDisplay() {
            const container = document.getElementById('results');
            container.innerHTML = results.map(r => 
                `<div class="test-result ${r.type}">[${r.timestamp}] ${r.message}</div>`
            ).join('');
        }

        window.clearResults = function() {
            results = [];
            updateDisplay();
        }

        window.runTests = async function() {
            results = [];
            addResult('🧪 Starting Vapi configuration tests...');

            // Test 1: Environment Variables
            addResult('📋 Testing environment variables...');
            
            // Simulate Vite environment variables
            const envVars = {
                VITE_VAPI_PUBLIC_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
                VITE_VAPI_BASE_URL: 'https://api.vapi.ai',
                VITE_VAPI_DEFAULT_ASSISTANT_ID: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'
            };

            // Set them on window for testing
            Object.entries(envVars).forEach(([key, value]) => {
                window[key] = value;
            });

            for (const [key, value] of Object.entries(envVars)) {
                if (value) {
                    addResult(`✅ ${key}: ${value.substring(0, 8)}...`, 'pass');
                } else {
                    addResult(`❌ ${key}: Missing`, 'fail');
                }
            }

            // Test 2: API Key Format
            addResult('🔍 Testing API key format...');
            const apiKey = envVars.VITE_VAPI_PUBLIC_KEY;
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;
            
            if (uuidRegex.test(apiKey)) {
                addResult('✅ API key format is valid', 'pass');
            } else {
                addResult('❌ API key format is invalid', 'fail');
            }

            // Test 3: Assistant ID Format
            addResult('🤖 Testing assistant ID format...');
            const assistantId = envVars.VITE_VAPI_DEFAULT_ASSISTANT_ID;
            
            if (uuidRegex.test(assistantId)) {
                addResult('✅ Assistant ID format is valid', 'pass');
            } else {
                addResult('❌ Assistant ID format is invalid', 'fail');
            }

            // Test 4: API Connectivity
            addResult('🌐 Testing API connectivity...');
            try {
                const response = await fetch(`${envVars.VITE_VAPI_BASE_URL}/assistant`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ API connectivity successful (${Array.isArray(data) ? data.length : 'data'} assistants)`, 'pass');
                } else {
                    addResult(`❌ API connectivity failed: ${response.status} ${response.statusText}`, 'fail');
                }
            } catch (error) {
                addResult(`❌ API connectivity error: ${error.message}`, 'fail');
            }

            // Test 5: Specific Assistant
            addResult('🎯 Testing specific assistant...');
            try {
                const response = await fetch(`${envVars.VITE_VAPI_BASE_URL}/assistant/${assistantId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const assistant = await response.json();
                    addResult(`✅ Assistant found: ${assistant.name || 'Unknown'}`, 'pass');
                    addResult(`📋 Model: ${assistant.model?.model || 'Unknown'}`, 'info');
                    addResult(`🎤 Voice: ${assistant.voice?.voiceId || 'Unknown'}`, 'info');
                } else if (response.status === 404) {
                    addResult('❌ Assistant not found (404)', 'fail');
                } else {
                    addResult(`❌ Assistant test failed: ${response.status} ${response.statusText}`, 'fail');
                }
            } catch (error) {
                addResult(`❌ Assistant test error: ${error.message}`, 'fail');
            }

            // Test 6: Vapi SDK Loading
            addResult('📦 Testing Vapi SDK loading...');

            // Try multiple CDN URLs for Vapi SDK (official first)
            const cdnUrls = [
                'https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js', // Official Vapi CDN
                'https://cdn.jsdelivr.net/npm/@vapi-ai/web@2.3.1/dist/vapi.js',
                'https://unpkg.com/@vapi-ai/web@2.3.1/dist/vapi.js',
                'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/vapi.js'
            ];

            let sdkLoaded = false;

            for (const url of cdnUrls) {
                if (sdkLoaded) break;

                try {
                    addResult(`🔄 Trying CDN: ${url}`, 'info');

                    const script = document.createElement('script');
                    script.src = url;
                    script.crossOrigin = 'anonymous';

                    const loadPromise = new Promise((resolve, reject) => {
                        script.onload = () => {
                            addResult(`✅ Script loaded from: ${url}`, 'info');
                            resolve();
                        };
                        script.onerror = (event) => {
                            addResult(`❌ Script failed from: ${url}`, 'info');
                            reject(new Error(`Failed to load from ${url}`));
                        };
                        setTimeout(() => {
                            reject(new Error(`Timeout loading from ${url}`));
                        }, 5000);
                    });

                    document.head.appendChild(script);
                    await loadPromise;

                    // Check if Vapi is now available
                    if (window.Vapi && typeof window.Vapi === 'function') {
                        addResult('✅ Vapi SDK loaded successfully', 'pass');
                        sdkLoaded = true;

                        // Test instance creation
                        try {
                            const vapi = new window.Vapi(apiKey);
                            if (vapi && typeof vapi.start === 'function') {
                                addResult('✅ Vapi instance creation successful', 'pass');
                                addResult(`📋 Instance methods: ${Object.getOwnPropertyNames(vapi).join(', ')}`, 'info');
                            } else {
                                addResult('❌ Vapi instance invalid', 'fail');
                            }
                        } catch (instanceError) {
                            addResult(`❌ Vapi instance creation failed: ${instanceError.message || 'Unknown error'}`, 'fail');
                        }
                        break;
                    } else {
                        addResult(`❌ Vapi not available after loading from ${url}`, 'info');
                    }
                } catch (error) {
                    addResult(`❌ Error with ${url}: ${error.message || 'Unknown error'}`, 'info');
                }
            }

            if (!sdkLoaded) {
                addResult('❌ Failed to load Vapi SDK from all CDN sources', 'fail');
                addResult('💡 This might be due to network issues or CDN availability', 'info');
                addResult('💡 Try checking if you can access https://cdn.vapi.ai directly', 'info');
            }

            addResult('🏁 Tests completed!');
        }
    </script>
</body>
</html>
