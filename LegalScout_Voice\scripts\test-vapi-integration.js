#!/usr/bin/env node

/**
 * Vapi Integration Test Script
 * 
 * This script tests the Vapi integration without requiring a browser.
 * It validates environment variables, API connectivity, and assistant configuration.
 * 
 * Usage: node scripts/test-vapi-integration.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Test results
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0
};

// Logging functions
const log = (message, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`);
};

const logSuccess = (message) => log(`✅ ${message}`, colors.green);
const logError = (message) => log(`❌ ${message}`, colors.red);
const logWarning = (message) => log(`⚠️  ${message}`, colors.yellow);
const logInfo = (message) => log(`ℹ️  ${message}`, colors.blue);
const logHeader = (message) => log(`\n${colors.bright}${message}${colors.reset}`, colors.cyan);

// Load environment variables
function loadEnvironmentVariables() {
  logHeader('🔧 Loading Environment Variables');
  
  const envPath = path.join(__dirname, '../.env.development');
  
  if (!fs.existsSync(envPath)) {
    logError('Environment file not found: .env.development');
    return null;
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        envVars[key] = valueParts.join('=');
      }
    }
  });
  
  logSuccess('Environment variables loaded');
  return envVars;
}

// Test environment variables
function testEnvironmentVariables(envVars) {
  logHeader('🔍 Testing Environment Variables');
  
  const requiredVars = [
    'VITE_VAPI_PUBLIC_KEY',
    'VITE_VAPI_BASE_URL',
    'VITE_VAPI_DEFAULT_ASSISTANT_ID'
  ];
  
  let allPassed = true;
  
  requiredVars.forEach(varName => {
    if (!envVars[varName]) {
      logError(`Missing required environment variable: ${varName}`);
      allPassed = false;
      testResults.failed++;
    } else {
      logSuccess(`${varName}: ${envVars[varName].substring(0, 8)}...`);
      testResults.passed++;
    }
  });
  
  // Validate API key format
  const apiKey = envVars['VITE_VAPI_PUBLIC_KEY'];
  if (apiKey) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;
    if (!uuidRegex.test(apiKey)) {
      logError('API key format is invalid (should be UUID format)');
      allPassed = false;
      testResults.failed++;
    } else {
      logSuccess('API key format is valid');
      testResults.passed++;
    }
  }
  
  // Validate assistant ID format
  const assistantId = envVars['VITE_VAPI_DEFAULT_ASSISTANT_ID'];
  if (assistantId) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;
    if (!uuidRegex.test(assistantId)) {
      logError('Assistant ID format is invalid (should be UUID format)');
      allPassed = false;
      testResults.failed++;
    } else {
      logSuccess('Assistant ID format is valid');
      testResults.passed++;
    }
  }
  
  return allPassed;
}

// Test API connectivity
async function testApiConnectivity(envVars) {
  logHeader('🌐 Testing API Connectivity');
  
  const apiKey = envVars['VITE_VAPI_PUBLIC_KEY'];
  const baseUrl = envVars['VITE_VAPI_BASE_URL'];
  
  if (!apiKey || !baseUrl) {
    logError('Missing API key or base URL for connectivity test');
    testResults.failed++;
    return false;
  }
  
  try {
    // Test basic API connectivity
    logInfo('Testing basic API connectivity...');
    
    const response = await fetch(`${baseUrl}/assistant`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      logSuccess('API connectivity test passed');
      testResults.passed++;
      
      // Try to get the response data
      try {
        const data = await response.json();
        logInfo(`API returned ${Array.isArray(data) ? data.length : 'data'} assistants`);
      } catch (parseError) {
        logWarning('Could not parse API response, but connection succeeded');
        testResults.warnings++;
      }
      
      return true;
    } else {
      logError(`API connectivity test failed: ${response.status} ${response.statusText}`);
      testResults.failed++;
      return false;
    }
  } catch (error) {
    logError(`API connectivity test failed: ${error.message}`);
    testResults.failed++;
    return false;
  }
}

// Test specific assistant
async function testAssistant(envVars) {
  logHeader('🤖 Testing Default Assistant');
  
  const apiKey = envVars['VITE_VAPI_PUBLIC_KEY'];
  const baseUrl = envVars['VITE_VAPI_BASE_URL'];
  const assistantId = envVars['VITE_VAPI_DEFAULT_ASSISTANT_ID'];
  
  if (!apiKey || !baseUrl || !assistantId) {
    logError('Missing required variables for assistant test');
    testResults.failed++;
    return false;
  }
  
  try {
    logInfo(`Testing assistant: ${assistantId}`);
    
    const response = await fetch(`${baseUrl}/assistant/${assistantId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const assistant = await response.json();
      logSuccess('Assistant found and accessible');
      logInfo(`Assistant name: ${assistant.name || 'Unknown'}`);
      logInfo(`Assistant model: ${assistant.model?.model || 'Unknown'}`);
      logInfo(`Assistant voice: ${assistant.voice?.voiceId || 'Unknown'}`);
      testResults.passed++;
      return true;
    } else if (response.status === 404) {
      logError('Assistant not found - check VITE_VAPI_DEFAULT_ASSISTANT_ID');
      testResults.failed++;
      return false;
    } else {
      logError(`Assistant test failed: ${response.status} ${response.statusText}`);
      testResults.failed++;
      return false;
    }
  } catch (error) {
    logError(`Assistant test failed: ${error.message}`);
    testResults.failed++;
    return false;
  }
}

// Test file structure
function testFileStructure() {
  logHeader('📁 Testing File Structure');
  
  const requiredFiles = [
    'src/hooks/useVapiCall.js',
    'src/components/VapiCall.jsx',
    'src/services/VapiService.js',
    'src/utils/vapiLoader.js',
    'src/config/vapiConfig.js',
    'src/constants/vapiConstants.js'
  ];
  
  let allFound = true;
  
  requiredFiles.forEach(filePath => {
    const fullPath = path.join(__dirname, '..', filePath);
    if (fs.existsSync(fullPath)) {
      logSuccess(`Found: ${filePath}`);
      testResults.passed++;
    } else {
      logError(`Missing: ${filePath}`);
      allFound = false;
      testResults.failed++;
    }
  });
  
  return allFound;
}

// Main test function
async function runTests() {
  log(`${colors.bright}${colors.cyan}🧪 Vapi Integration Test Suite${colors.reset}\n`);
  log('This script tests the Vapi integration configuration and connectivity.\n');
  
  // Load environment variables
  const envVars = loadEnvironmentVariables();
  if (!envVars) {
    logError('Cannot proceed without environment variables');
    process.exit(1);
  }
  
  // Run all tests
  const tests = [
    () => testEnvironmentVariables(envVars),
    () => testFileStructure(),
    () => testApiConnectivity(envVars),
    () => testAssistant(envVars)
  ];
  
  for (const test of tests) {
    try {
      await test();
    } catch (error) {
      logError(`Test failed with error: ${error.message}`);
      testResults.failed++;
    }
  }
  
  // Print summary
  logHeader('📊 Test Summary');
  log(`${colors.green}✅ Passed: ${testResults.passed}${colors.reset}`);
  log(`${colors.red}❌ Failed: ${testResults.failed}${colors.reset}`);
  log(`${colors.yellow}⚠️  Warnings: ${testResults.warnings}${colors.reset}`);
  
  if (testResults.failed === 0) {
    logSuccess('\n🎉 All tests passed! Vapi integration appears to be configured correctly.');
    
    logInfo('\n📋 Next Steps:');
    logInfo('1. Start the development server: npm run dev');
    logInfo('2. Navigate to: http://localhost:5174/vapi-test');
    logInfo('3. Run the browser-based diagnostic tests');
    
  } else {
    logError('\n💥 Some tests failed. Please fix the issues above before proceeding.');
    
    logInfo('\n🔧 Common Solutions:');
    logInfo('1. Check your .env.development file has correct Vapi credentials');
    logInfo('2. Verify your API key has the correct permissions');
    logInfo('3. Ensure the assistant ID exists in your Vapi account');
    logInfo('4. Check network connectivity to api.vapi.ai');
    
    process.exit(1);
  }
}

// Run the tests
runTests().catch(error => {
  logError(`Test suite failed: ${error.message}`);
  process.exit(1);
});
