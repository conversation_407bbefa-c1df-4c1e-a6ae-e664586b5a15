/**
 * Simple Development Server for Local API Testing
 * Bypasses complex routing to avoid path-to-regexp issues
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();
try {
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('No .env.local file found, using only .env');
}

const app = express();
const PORT = process.env.DEV_API_PORT || 3001;

// Enable CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'apikey', 'X-Client-Info', 'Prefer', 'Accept'],
  credentials: true
}));

// Parse JSON bodies
app.use(express.json());

// Handle preflight requests
app.options('*', (req, res) => {
  res.status(200).end();
});

// Simple API endpoints without complex routing
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    message: 'Simple dev server is running'
  });
});

app.get('/api/env', (req, res) => {
  res.json({
    NODE_ENV: process.env.NODE_ENV,
    VITE_VAPI_PUBLIC_KEY: process.env.VITE_VAPI_PUBLIC_KEY ? 'Set' : 'Not set',
    VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL ? 'Set' : 'Not set',
    VITE_SUPABASE_KEY: process.env.VITE_SUPABASE_KEY ? 'Set' : 'Not set'
  });
});

// Vapi proxy endpoint for testing
app.all('/api/vapi-proxy/*', async (req, res) => {
  try {
    const vapiPath = req.path.replace('/api/vapi-proxy', '');
    const vapiUrl = `https://api.vapi.ai${vapiPath}`;
    
    const response = await fetch(vapiUrl, {
      method: req.method,
      headers: {
        'Authorization': req.headers.authorization || `Bearer ${process.env.VITE_VAPI_SECRET_KEY}`,
        'Content-Type': 'application/json',
        ...req.headers
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined
    });

    const data = await response.json();
    res.status(response.status).json(data);
  } catch (error) {
    console.error('Vapi proxy error:', error);
    res.status(500).json({ error: 'Vapi proxy error', message: error.message });
  }
});

// Bug report endpoint
app.post('/api/bug-report', (req, res) => {
  console.log('Bug report received:', req.body);
  res.json({ 
    success: true, 
    message: 'Bug report received',
    timestamp: new Date().toISOString()
  });
});

// Website import endpoint
app.post('/api/website-import', (req, res) => {
  console.log('Website import request:', req.body);
  res.json({ 
    success: true, 
    message: 'Website import request received',
    data: req.body
  });
});

// Debug website import endpoint
app.all('/api/debug-website-import', (req, res) => {
  res.json({ 
    status: 'available',
    message: 'Debug website import endpoint is working',
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Catch-all for other API endpoints
app.all('/api/*', (req, res) => {
  res.status(404).json({ 
    error: 'API endpoint not found',
    path: req.path,
    method: req.method,
    message: 'This is a simplified dev server. Some endpoints may not be available.'
  });
});

// Health check endpoint (non-API)
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    server: 'simple-dev-server'
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({ 
    error: 'Internal server error',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Simple Development API server running on http://localhost:${PORT}`);
  console.log(`📡 API endpoints available at http://localhost:${PORT}/api/*`);
  console.log(`🔍 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 This is a simplified server to bypass routing issues`);
});
