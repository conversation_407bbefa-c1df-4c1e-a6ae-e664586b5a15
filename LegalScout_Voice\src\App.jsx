import React, { useState, useEffect, Suspense, useRef, lazy } from 'react'
import { storeImage, processImageUrl } from './utils/imageStorage'
import { Routes, Route, Navigate, useLocation, Link } from 'react-router-dom'
import { ToastContainer, toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import './App.css'
import VapiCall from './components/VapiCall.jsx'
import Button from './components/Button.jsx'
import CallTransition from './components/CallTransition.jsx'
import { withDevTools, createDebugger, trackUserJourney } from './utils/debugConfig'
import { getAttorneyConfigAsync } from './config/attorneys'
import { DEFAULT_ASSISTANT_ID } from './constants/vapiConstants'
import MapView from './components/MapView.jsx'
import { useAuth } from './contexts/AuthContext'

import AttorneyDossier from './components/AttorneyDossier.jsx'
import CallSummary from './components/CallSummary.jsx'
import { getCurrentSubdomain } from './utils/subdomainTester'
import { isAttorneySubdomain as checkIsAttorneySubdomain } from './utils/subdomainExtraction'
import Navbar from './components/Navbar.jsx'
import Dashboard from './pages/DashboardNew.jsx'
import GlobeDossierView from './components/GlobeDossierView.jsx'
import TestSubdomains from './components/TestSubdomains.jsx'
import AnimatedBackground from './components/AnimatedBackground.jsx'
import ThemeToggle from './components/ThemeToggle.jsx'
import SignInButton from './components/SignInButton.jsx'
import AboutPage from './pages/AboutPage'
import SimplifiedPreview from "./components/SimplifiedPreview";
import EnhancedPreviewNew from "./components/preview/EnhancedPreviewNew";
import PreviewFrameLoader from "./components/preview/PreviewFrameLoader";
import SimplePreviewPage from "./pages/SimplePreviewPage";
import MobileActivateAssistant from "./components/mobile/MobileActivateAssistant";
import AuthOverlay from './components/AuthOverlay';
import AuthCallback from './pages/AuthCallback';
import SimpleCompleteProfile from './pages/SimpleCompleteProfile';
import CrmDemo from './pages/CrmDemo';
import LoginPage from './pages/LoginPage';
import { testSupabaseConnection, logSupabaseConfig } from './testSupabase';
import { initializeSupabaseConfig, verifySupabaseConfig } from './utils/supabaseConfigVerifier';
import TestComponent from './components/TestComponent';
import SubdomainTestPage from './pages/SubdomainTestPage';
import AttorneyProfileTest from './components/AttorneyProfileTest';
import VapiTestPage from './pages/VapiTestPage';
import VapiComparisonTest from './pages/VapiComparisonTest';
// Development components removed for production
// import BugReportButton from './components/BugReportButton';
// import VapiTestComponent from './components/VapiTestComponent';
// import VapiIntegrationTest from './pages/VapiIntegrationTest';
// import SubdomainEditorDemo from './pages/SubdomainEditorDemo';

// Create debugger for App component
const debug = createDebugger('App');

// Import fallback component directly
import SimpleDemoPageFallback from './pages/SimpleDemoPageFallback';

// Lazy load components
const PreviewPage = lazy(() => import('./pages/PreviewPage'));
// Use try-catch for SimpleDemoPage to handle import errors
const SimpleDemoPage = lazy(() => {
  try {
    return import('./pages/SimpleDemoPage');
  } catch (error) {
    console.error('Error loading SimpleDemoPage:', error);
    return Promise.resolve({ default: SimpleDemoPageFallback });
  }
});
const CallControl = lazy(() => import('./pages/CallControl'));

// Home component to contain the main app content
const Home = ({ isLoading, callActive, showAttorneyInfo, showCallSummary, attorneyProfile, startCall, endCall, callData, subdomain, setShowAttorneyInfo, setShowCallSummary, buttonText, isAttorneySubdomain, hideCreateAgentButton = false, isDarkTheme, vapiCallKey }) => {
  const [showTransition, setShowTransition] = useState(false);
  const [buttonPosition, setButtonPosition] = useState(null);
  const buttonRef = useRef(null);

  // Always declare hooks at the top level - never conditionally
  const [configMapping, setConfigMapping] = useState(null);

  // Always run useEffect - control behavior with conditions inside
  useEffect(() => {
    // Only load configMapping if we need it for attorney subdomain
    if (isAttorneySubdomain && attorneyProfile && !isLoading && attorneyProfile.firmName && subdomain && subdomain !== 'default') {
      import('./utils/configMapping').then(module => {
        setConfigMapping(module);
      });
    }
  }, [isAttorneySubdomain, attorneyProfile, isLoading, subdomain]);

  // Function to handle the button click and start the transition
  const handleStartCall = () => {
    // Get the button position for the transition animation
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setButtonPosition({
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      });
    }

    // Show the transition
    setShowTransition(true);

    // Track the call start
    debug.log('Call transition started', { timestamp: new Date().toISOString() });
    trackUserJourney('call_transition_started');

    // Debug logging removed for production
  };

  // Function to handle transition completion
  const handleTransitionComplete = () => {
    // Actually start the call after the transition completes
    // Hide the transition overlay first
    setShowTransition(false);

    // Then start the call after a brief delay to ensure the UI is updated
    setTimeout(() => {
      startCall();
    }, 100);
  };

  // Debug the condition values
  console.log('🔍 [App.jsx] Condition check:', {
    isAttorneySubdomain,
    hasAttorneyProfile: !!attorneyProfile,
    isLoading,
    firmName: attorneyProfile?.firmName,
    firm_name: attorneyProfile?.firm_name,
    subdomain,
    subdomainNotDefault: subdomain !== 'default'
  });

  // If this is an attorney subdomain and we have a valid attorney profile, render the preview component directly
  if (isAttorneySubdomain && attorneyProfile && !isLoading && (attorneyProfile.firmName || attorneyProfile.firm_name) && subdomain && subdomain !== 'default') {
    console.log('🎯 [App.jsx] Condition met! Rendering iframe for subdomain:', subdomain);
    // Import the preview component and render it directly with the attorney data
    const SimplifiedPreview = React.lazy(() => import('./components/preview/SimplifiedPreview'));

    if (!configMapping) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          backgroundColor: '#f5f7fa'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #f3f3f3',
            borderTop: '4px solid #4B9CD3',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}></div>
          <p style={{ marginTop: '20px', color: '#666' }}>Loading attorney profile...</p>
          <style>{`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}</style>
        </div>
      );
    }

    // Use the EXACT same URL pattern and mechanism as the working dashboard preview
    const previewUrl = `/simple-preview?subdomain=${encodeURIComponent(subdomain)}&theme=${isDarkTheme ? 'dark' : 'light'}&useEnhancedPreview=true`;

    return (
      <div className="attorney-subdomain-page" style={{ width: '100%', height: '100vh', margin: 0, padding: 0 }}>
        <iframe
          ref={(iframe) => {
            if (iframe) {
              // Use the EXACT same mechanism as the working dashboard preview
              // Wait for PREVIEW_READY message before sending config
              const handleMessage = async (event) => {
                if (event.data && event.data.type === 'PREVIEW_READY') {
                  console.log('🎯 [App.jsx] Received PREVIEW_READY from subdomain iframe, sending config...');

                  try {
                    if (!iframe.contentWindow) return;

                    // Use the EXACT same utility function as the working dashboard preview
                    const { previewConfig } = await import('./utils/previewConfigHandler').then(module =>
                      module.createAttorneyPreviewConfig(attorneyProfile)
                    );

                    // Add the theme and enhanced preview flags
                    previewConfig.theme = isDarkTheme ? 'dark' : 'light';
                    previewConfig.useEnhancedPreview = true;

                    // Ensure the Vapi assistant ID is set (critical for functionality)
                    if (attorneyProfile.vapi_assistant_id) {
                      previewConfig.vapi_assistant_id = attorneyProfile.vapi_assistant_id;
                      previewConfig.vapiAssistantId = attorneyProfile.vapi_assistant_id; // Include both formats for compatibility
                    }

                    // Send the complete config via postMessage (same as dashboard preview)
                    iframe.contentWindow.postMessage({
                      type: 'UPDATE_PREVIEW_CONFIG',
                      config: previewConfig
                    }, '*');

                    console.log('🎯 [App.jsx] Sent complete config to subdomain iframe via postMessage:', previewConfig);
                  } catch (error) {
                    console.error('🚨 [App.jsx] Error sending config via postMessage:', error);
                  }

                  // Remove the event listener after sending config
                  window.removeEventListener('message', handleMessage);
                }
              };

              // Add message listener for PREVIEW_READY
              window.addEventListener('message', handleMessage);

              // Also set up iframe onload as fallback
              iframe.onload = () => {
                console.log('🎯 [App.jsx] Subdomain iframe loaded, waiting for PREVIEW_READY message...');
              };
            }
          }}
          src={previewUrl}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            margin: 0,
            padding: 0
          }}
          title={`${attorneyProfile.firm_name || 'Attorney'} Preview`}
        />
      </div>
    );
  }

  // Otherwise, render the regular home page
  return (
    <>
      {isLoading ? (
        <div className="loading-indicator">Loading...</div>
      ) : (
        <>
          {!callActive && !showAttorneyInfo && !showCallSummary && (
            <div className="start-button-container" ref={buttonRef}>
              <Button
                onClick={handleStartCall}
                label="Get Started"
                mascot="/PRIMARY CLEAR.png"
                isLoading={false}
              />
            </div>
          )}

          {/* Call Transition Animation */}
          <CallTransition
            isActive={showTransition}
            onTransitionComplete={handleTransitionComplete}
            buttonPosition={buttonPosition}
            mascotUrl="/PRIMARY CLEAR.png"
          />

          {callActive && (
            <div className={`call-card-container ${callActive ? 'active' : ''}`}>
              <div className="call-card">
                <VapiCall
                  key={vapiCallKey} // Use stable key to prevent unnecessary unmounting
                  onEndCall={endCall}
                  subdomain={subdomain}
                  assistantId={DEFAULT_ASSISTANT_ID} // Always use the default assistant ID for the home page
                  forceDefaultAssistant={true} // Force using the default assistant
                  initializationDelay={1200}
                  showDebugPanel={false} // Disable the debug panel
                />
              </div>
            </div>
          )}

          <HomeContent
            showAttorneyInfo={showAttorneyInfo}
            callData={callData}
            showCallSummary={showCallSummary}
            setShowAttorneyInfo={setShowAttorneyInfo}
            setShowCallSummary={setShowCallSummary}
          />
        </>
      )}
    </>
  );
};

// Home component continued
const HomeContent = ({ showAttorneyInfo, callData, showCallSummary, setShowAttorneyInfo, setShowCallSummary }) => (
  <>
    {showAttorneyInfo && callData && (
      <div className="attorney-info-container">
        <div className="map-dossier-container">
          <MapView attorney={callData.attorney} />
          <AttorneyDossier attorney={callData.attorney} />
        </div>
        <button className="back-button" onClick={() => setShowAttorneyInfo(false)}>
          Back to Start
        </button>
      </div>
    )}

    {showCallSummary && callData && (
      <div className="call-summary-container">
        <CallSummary data={callData.summary} />
        <button className="back-button" onClick={() => setShowCallSummary(false)}>
          Back to Start
        </button>
      </div>
    )}
  </>
);

function App() {
  const location = useLocation();
  const { user } = useAuth(); // Add useAuth hook to get user
  const [callActive, setCallActive] = useState(false)
  const [showAttorneyInfo, setShowAttorneyInfo] = useState(false)
  const [showCallSummary, setShowCallSummary] = useState(false)
  const [callData, setCallData] = useState(null)
  const [subdomain, setSubdomain] = useState(null)
  const [isAttorneySubdomain, setIsAttorneySubdomain] = useState(false)
  const [attorneyProfile, setAttorneyProfile] = useState(null)
  const [isDevelopment, setIsDevelopment] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [availableSubdomains, setAvailableSubdomains] = useState(['default'])
  const [isDarkTheme, setIsDarkTheme] = useState(true)
  const [showSubdomains, setShowSubdomains] = useState(false)
  const [selectedPracticeArea, setSelectedPracticeArea] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [activeConfigTab, setActiveConfigTab] = useState('firm');
  const [showAuthOverlay, setShowAuthOverlay] = useState(false);
  const [configMode, setConfigMode] = useState('url');

  // Add missing state variables
  const [firmName, setFirmName] = useState('Smith & Associates, LLP');
  const [logoUrl, setLogoUrl] = useState('');
  const [state, setState] = useState('');
  const [primaryColor, setPrimaryColor] = useState('#2c3e50');
  const [secondaryColor, setSecondaryColor] = useState('#3498db');
  const [buttonColor, setButtonColor] = useState('#3498db');

  // Debug logging removed for production
  const [backgroundColor, setBackgroundColor] = useState('#f0f4f8');
  const [backgroundOpacity, setBackgroundOpacity] = useState(0.3);
  const [buttonText, setButtonText] = useState('Start Consultation');
  const [buttonOpacity, setButtonOpacity] = useState(1);
  const [previewHeight, setPreviewHeight] = useState(600);
  const [practiceDescription, setPracticeDescription] = useState('**Welcome to our legal practice**\n\nOur team of experienced attorneys is dedicated to providing you with exceptional legal representation. We combine expertise with a client-focused approach to help you navigate complex legal challenges.\n\n### How we can help:\n- Personalized legal solutions\n- Clear communication throughout your case\n- Decades of combined experience');
  const [welcomeMessage, setWelcomeMessage] = useState('Hello, I\'m an AI assistant from Smith & Associates. How can I help you today?');
  const [informationGathering, setInformationGathering] = useState('To better assist you, I\'ll need a few details about your situation.');
  const [attorneyName, setAttorneyName] = useState('John Smith');
  const [practiceAreaBackgroundOpacity, setPracticeAreaBackgroundOpacity] = useState(0.2);
  const [textBackgroundColor, setTextBackgroundColor] = useState('#634C38');

  const [firmUrl, setFirmUrl] = useState('');
  const [isUrlLoading, setIsUrlLoading] = useState(false);

  const iframeRef = useRef(null);

  // Practice areas configuration
  const practiceAreas = {
    'Personal Injury': {
      questions: "I want to know the circumstances of their injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?",
      practiceDescription: "**Our firm specializes in personal injury law**, and we have a proven track record of success in obtaining favorable settlements and verdicts for our clients.\n\n### Our Services:\n- Car accident claims\n- Slip and fall cases\n- Medical malpractice\n- Workplace injuries\n\nWe work on a contingency basis - *you don't pay unless we win*.",
      welcomeMessage: "Welcome to our personal injury consultation. I'm your virtual legal assistant and I'm here to help gather information about your case.",
      informationGathering: "I want to know the circumstances of your injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?"
    },
    'Family Law': {
      questions: "I need to understand the nature of their family law issue. Are they seeking a divorce, child custody, child support, alimony, or a modification of an existing order?",
      practiceDescription: "**Our firm is dedicated to helping families** navigate the complexities of family law. We handle all aspects of divorce, including property division, child custody and visitation.\n\n### Practice Areas:\n- Divorce proceedings\n- Child custody & support\n- Spousal support/alimony\n- Prenuptial agreements\n\n> We approach each case with compassion and understanding during difficult times.",
      welcomeMessage: "Welcome to our family law consultation. I'm your virtual legal assistant and I'm here to help with your family law matters.",
      informationGathering: "I need to understand the nature of your family law issue. Are you seeking a divorce, child custody, child support, alimony, or a modification of an existing order?"
    },
    'Criminal Defense': {
      questions: "I need to know the charges against the client, where they are in the criminal process, and if there are any deadlines or court dates already scheduled.",
      practiceDescription: "## Criminal Defense Experts\n\nOur **experienced criminal defense attorneys** provide aggressive representation for all criminal matters, from misdemeanors to serious felony charges.\n\n### Our Approach:\n1. Thorough case evaluation\n2. Strategic defense planning\n3. Aggressive courtroom advocacy\n4. Pursuit of best possible outcomes\n\n[Contact us](#) immediately if you've been charged with a crime.",
      welcomeMessage: "Thank you for considering our firm for your criminal defense needs. I'm here to gather some initial information about your case.",
      informationGathering: "Please tell me about the charges you're facing and where you are in the legal process. All information is confidential and protected by attorney-client privilege."
    }
  };

  // Handle practice area selection
  const handlePracticeAreaChange = (e) => {
    const area = e.target.value;
    setSelectedPracticeArea(area);

    if (area && practiceAreas[area]) {
      setWelcomeMessage(practiceAreas[area].welcomeMessage);
      setInformationGathering(practiceAreas[area].informationGathering);
      setPracticeDescription(practiceAreas[area].practiceDescription);
    }
  };

  // Handle file upload for logo
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        // Store the image in localStorage and get the ID
        const imageId = storeImage(reader.result);
        console.log('Stored image with ID:', imageId);
        // Set the logo URL to the image ID
        setLogoUrl(imageId);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle logo removal
  const handleRemoveLogo = () => {
    setLogoUrl('');
  };

  const goToPreview = () => {
    setShowPreview(true);
  };

  // Handle URL submission for auto-configuration
  const handleUrlSubmit = async (e) => {
    if (e && e.preventDefault) {
      e.preventDefault();
    }

    // Get the URL from the event, localStorage, or state
    let urlToProcess = firmUrl;

    // Check if there's a URL in the event
    if (e && e.detail && e.detail.url) {
      console.log('Using URL from event:', e.detail.url);
      urlToProcess = e.detail.url;
      setFirmUrl(urlToProcess); // Update the state
    }

    // Check localStorage as a fallback
    if (!urlToProcess) {
      const storedUrl = localStorage.getItem('pendingUrlAutoConfig');
      if (storedUrl) {
        console.log('Using URL from localStorage:', storedUrl);
        urlToProcess = storedUrl;
        setFirmUrl(urlToProcess); // Update the state
      }
    }

    if (!urlToProcess) {
      alert('Please enter a URL');
      return;
    }

    console.log('Processing URL:', urlToProcess);

    // Show loading state
    setIsUrlLoading(true);

    // Show a toast notification
    toast.info('Auto-configuring from website...', {
      position: 'top-center',
      autoClose: false,
      hideProgressBar: false,
      closeOnClick: false,
      pauseOnHover: true,
      draggable: false,
      progress: undefined,
      toastId: 'auto-configure-toast'
    });

    try {
      // Import the website scraper utility
      const { scrapeWebsite } = await import('./utils/websiteScraper');

      // Call the scraper to extract website data
      const extractedData = await scrapeWebsite(firmUrl);
      console.log('Extracted website data:', extractedData);

      // Update state with extracted information
      if (extractedData) {
        // Basic information
        setFirmName(extractedData.firmName || 'Your Law Firm');
        setAttorneyName(extractedData.attorneyName || '');

        // Visual elements
        if (extractedData.logo && extractedData.logo.url) {
          setLogoUrl(extractedData.logo.url);
        }

        // Colors
        if (extractedData.colors) {
          setPrimaryColor(extractedData.colors.primary || '#4B74AA');
          setSecondaryColor(extractedData.colors.secondary || '#2C3E50');
          setButtonColor(extractedData.colors.accent || extractedData.colors.secondary || '#3498db');
          setBackgroundColor(extractedData.colors.background || '#1a1a1a');
        }

        // Content
        setPracticeDescription(extractedData.contentAnalysis?.services?.join(', ') || 'Specializing in various legal matters with a client-focused approach.');

        // Set welcome message and information gathering prompts
        setWelcomeMessage(extractedData.welcomeMessage || ('Hello, I\'m an AI assistant from ' + extractedData.firmName + '. How can I help you today?'));
        setInformationGathering(extractedData.informationGathering || 'To better assist you, I\'ll need a few details about your situation.');

        // Set button text
        setButtonText(extractedData.buttonText || 'Start Consultation');

        // Set state if available
        if (extractedData.address && extractedData.address.state) {
          setState(extractedData.address.state);
        }

        // Determine practice area based on extracted data
        if (extractedData.practiceAreas && extractedData.practiceAreas.length > 0) {
          // Try to match extracted practice areas with our predefined ones
          const practiceAreaMap = {
            'personal injury': 'Personal Injury',
            'accident': 'Personal Injury',
            'family': 'Family Law',
            'divorce': 'Family Law',
            'criminal': 'Criminal Defense',
            'defense': 'Criminal Defense'
          };

          // Look for matches in the extracted practice areas
          for (const area of extractedData.practiceAreas) {
            const lowerArea = area.toLowerCase();
            for (const [keyword, mappedArea] of Object.entries(practiceAreaMap)) {
              if (lowerArea.includes(keyword) && practiceAreas[mappedArea]) {
                setSelectedPracticeArea(mappedArea);
                // Don't override the extracted welcome message and information gathering
                // with the predefined ones unless they're empty
                if (!extractedData.welcomeMessage) {
                  setWelcomeMessage(practiceAreas[mappedArea].welcomeMessage);
                }
                if (!extractedData.informationGathering) {
                  setInformationGathering(practiceAreas[mappedArea].informationGathering);
                }
                break;
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error scraping website:', error);

      // Fallback to basic extraction if the scraper fails
      const domain = firmUrl.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
      const generatedFirmName = domain
        .split(/[.-]/)
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ') + " Law";

      setFirmName(generatedFirmName);
      setWelcomeMessage('Hello, I\'m an AI assistant from ' + generatedFirmName + '. How can I help you today?');

      // Close the loading toast and show an error toast
      toast.dismiss('auto-configure-toast');
      toast.warning('Could not fully analyze the website. Basic information has been extracted.', {
        position: 'top-center',
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true
      });

      // Switch to manual config mode to show the extracted information
      setConfigMode('manual');
    } finally {
      setIsUrlLoading(false);

      // Close the loading toast if it's still open
      setTimeout(() => {
        toast.dismiss('auto-configure-toast');

        // Show success toast if we're still in URL mode (no error occurred)
        if (configMode === 'url') {
          toast.success('Website auto-configuration complete!', {
            position: 'top-center',
            autoClose: 3000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true
          });

          // Switch to manual config mode to show the extracted information
          setConfigMode('manual');
        }
      }, 1000);
    }
  };

  // Helper functions for color handling
  const hexToRgb = (hex) => {
    // Remove # if present
    hex = hex.replace('#', '');

    // Parse hex values
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return `${r}, ${g}, ${b}`;
  };

  const getContrastColor = (hexColor) => {
    // Convert hex to RGB
    let hex = hexColor.replace('#', '');

    // Convert hex to RGB
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // Calculate luminance - standard formula
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // Return black for bright colors, white for dark colors
    return luminance > 0.5 ? '#000000' : '#ffffff';
  };

  // Theme management
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', isDarkTheme ? 'dark' : 'light');

    // Add or remove dark-theme class from body
    if (isDarkTheme) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }, [isDarkTheme]);

  const toggleTheme = () => {
    setIsDarkTheme(prev => !prev);
  };

  // Use useEffect to determine subdomain and initialize
  useEffect(() => {
    debug.log('App component mounted');

    // Check for pending URL auto-configuration from localStorage
    const pendingUrl = localStorage.getItem('pendingUrlAutoConfig');
    const timestamp = localStorage.getItem('urlAutoConfigTimestamp');

    if (pendingUrl && timestamp) {
      // Only process if the timestamp is recent (within the last 5 seconds)
      const now = Date.now();
      const timestampNum = parseInt(timestamp, 10);

      if (!isNaN(timestampNum) && now - timestampNum < 5000) {
        console.log('Found pending URL auto-configuration:', pendingUrl);
        setFirmUrl(pendingUrl);

        // Clear the localStorage items
        localStorage.removeItem('pendingUrlAutoConfig');
        localStorage.removeItem('urlAutoConfigTimestamp');

        // Trigger the URL submission after a short delay
        setTimeout(() => {
          handleUrlSubmit();
        }, 500);
      } else {
        // Clear old items
        localStorage.removeItem('pendingUrlAutoConfig');
        localStorage.removeItem('urlAutoConfigTimestamp');
      }
    }

    // Initialize and verify Supabase configuration
    initializeSupabaseConfig();

    // Test Supabase connection with our new test function
    logSupabaseConfig();
    verifySupabaseConfig().then(result => {
      if (result.success) {
        console.log('Supabase is properly configured and connected!', result.data);
        if (result.usingFallback) {
          console.warn('Using fallback Supabase configuration. Update your .env.development file for a better development experience.');
        }
      } else {
        console.warn('Supabase connection failed:', result.error);

        if (result.useMockData) {
          console.info('The application will use mock data for development');
          console.info('This is fine for local development, but you should fix the Supabase connection for production');

          // Set a flag to indicate we're using mock data
          window.USING_MOCK_DATA = true;
        }
      }
    });

    // Initialize React DevTools global hook for LegalScout
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout = {};
    }

    // Determine if we're in development environment
    const isDev = import.meta.env.MODE === 'development' ||
                 window.location.hostname === 'localhost' ||
                 window.location.hostname === '127.0.0.1';
    setIsDevelopment(isDev);

    // Get subdomain using our utility
    const subdomainValue = getCurrentSubdomain();
    debug.log('Subdomain detected:', subdomainValue);
    setSubdomain(subdomainValue);

    // Check if this is an attorney subdomain using shared utility
    const isAttorneySub = checkIsAttorneySubdomain(subdomainValue);
    setIsAttorneySubdomain(isAttorneySub);
    debug.log('Is attorney subdomain:', isAttorneySub, 'Subdomain value:', subdomainValue);

    // If we're on the main domain (not a subdomain), clear any attorney profile
    if (!isAttorneySub) {
      setAttorneyProfile(null);
    }

    // Get attorney profile based on subdomain
    const loadAttorneyProfile = async () => {
      setIsLoading(true);
      console.log('🚀 [App.jsx] Starting attorney profile load for subdomain:', subdomainValue);
      debug.log('🚀 Starting attorney profile load for subdomain:', subdomainValue);

      try {
        console.log('📞 [App.jsx] Calling getAttorneyConfigAsync with subdomain:', subdomainValue);
        debug.log('📞 Calling getAttorneyConfigAsync with subdomain:', subdomainValue);

        // Add a check to make sure the function exists
        if (typeof getAttorneyConfigAsync !== 'function') {
          throw new Error('getAttorneyConfigAsync is not a function');
        }

        const profile = await getAttorneyConfigAsync(subdomainValue);
        console.log('✅ [App.jsx] Attorney profile loaded successfully:', {
          hasProfile: !!profile,
          firmName: profile?.firmName,
          id: profile?.id,
          subdomain: profile?.subdomain,
          isFallback: profile?.isFallback
        });
        debug.log('✅ Attorney profile loaded successfully:', {
          hasProfile: !!profile,
          firmName: profile?.firmName,
          id: profile?.id,
          subdomain: profile?.subdomain,
          isFallback: profile?.isFallback
        });
        setAttorneyProfile(profile);

        // If no valid attorney profile is found for this subdomain,
        // don't treat it as an attorney subdomain
        if (!profile || !profile.firmName) {
          console.log('⚠️ [App.jsx] No valid profile found, setting isAttorneySubdomain to false');
          debug.log('⚠️ No valid profile found, setting isAttorneySubdomain to false');
          setIsAttorneySubdomain(false);
        } else {
          console.log('✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true');
          debug.log('✅ Valid profile found, keeping isAttorneySubdomain as true');
        }
      } catch (error) {
        console.error('❌ [App.jsx] Error loading attorney profile:', {
          message: error.message,
          stack: error.stack,
          subdomain: subdomainValue,
          functionExists: typeof getAttorneyConfigAsync === 'function'
        });
        // If there's an error loading the profile, don't treat it as an attorney subdomain
        setIsAttorneySubdomain(false);
        setAttorneyProfile(null);
      } finally {
        setIsLoading(false);
        console.log('🏁 [App.jsx] Attorney profile loading complete');
        debug.log('🏁 Attorney profile loading complete');
      }
    };

    // Only load attorney profile if this is an attorney subdomain
    if (isAttorneySub) {
      console.log('🔍 [App.jsx] This is an attorney subdomain, loading profile');
      loadAttorneyProfile();
    } else {
      console.log('🏠 [App.jsx] This is not an attorney subdomain, skipping profile load');
      setIsLoading(false);
    }

    return () => {
      debug.log('App component unmounted');
    };
  }, []);

  // Setup subdomain testing UI in development
  useEffect(() => {
    if (!isDevelopment) return;

    // Fetch available subdomains from Supabase
    const fetchSubdomains = async () => {
      try {
        // Import supabase client
        const { supabase } = await import('./lib/supabase');

        // Fetch all attorney subdomains from the database
        const { data, error } = await supabase
          .from('attorneys')
          .select('subdomain')
          .not('subdomain', 'is', null);

        if (error) {
          console.warn('Error fetching attorney subdomains:', error);
          setAvailableSubdomains(['default']);
          return;
        }

        // Extract subdomain values from the data
        const subdomains = data.map(item => item.subdomain).filter(Boolean);

        debug.log('Available subdomains for testing:', subdomains);
        setAvailableSubdomains(['default', ...subdomains]);
      } catch (error) {
        console.warn('Error setting up subdomain testing, using default only:', error);
        setAvailableSubdomains(['default']);
      }
    };

    fetchSubdomains();
  }, [isDevelopment]);

  // Track if we're in the process of starting a call to prevent race conditions
  const [isStartingCall, setIsStartingCall] = useState(false);

  const startCall = () => {
    debug.log('Call started', { timestamp: new Date().toISOString() });
    trackUserJourney('call_started');

    // If we're already in the process of starting a call, don't start another one
    if (isStartingCall) {
      console.log('[App.jsx] Already in the process of starting a call, ignoring duplicate startCall');
      return;
    }

    // Set the flag to indicate we're in the process of starting a call
    setIsStartingCall(true);

    // Clear any previous states
    setShowAttorneyInfo(false);
    setShowCallSummary(false);

    // Clean up any lingering state from previous calls
    window.vapiCallActive = false;

    // Clean up any lingering Daily.co iframes before starting a new call
    try {
      const existingIframes = document.querySelectorAll('iframe[title*="daily"]');
      if (existingIframes.length > 0) {
        console.log(`[App] Found ${existingIframes.length} lingering Daily.co iframes. Removing before starting new call...`);
        existingIframes.forEach(iframe => {
          iframe.parentNode.removeChild(iframe);
        });
      }
    } catch (cleanupError) {
      console.warn("[App] Error cleaning up iframes before starting call:", cleanupError);
    }

    // Increment the VapiCall key to force a fresh component instance
    setVapiCallKey(prev => prev + 1);

    // Set call to active - this will trigger the VapiCall component to render
    console.log('[App] Starting call - setting callActive to true');
    setCallActive(true);

    // Force the call-card-container to be visible after a short delay
    setTimeout(() => {
      console.log('[App] Forcing call-card-container to be visible');
      const callCardContainer = document.querySelector('.call-card-container');
      if (callCardContainer) {
        callCardContainer.classList.add('active');
        callCardContainer.style.display = 'flex';
        callCardContainer.style.visibility = 'visible';
        callCardContainer.style.opacity = '1';
        callCardContainer.style.zIndex = '1000';
      }

      // Reset the starting call flag after a delay to allow for initialization
      setTimeout(() => {
        setIsStartingCall(false);
      }, 3000); // Increased to 3 seconds to allow for VapiCall initialization
    }, 500);
  }

  const handleGetStarted = () => {
    // Show the auth overlay when Get Started is clicked
    console.log('handleGetStarted called in App.jsx');
    setShowAuthOverlay(true);
  }

  // Expose handleGetStarted, handleUrlSubmit, and setShowAuthOverlay to window object for access from other components
  useEffect(() => {
    window.handleGetStarted = handleGetStarted;
    window.handleUrlSubmit = handleUrlSubmit;
    window.setShowAuthOverlay = setShowAuthOverlay;

    // Add event listener for urlAutoConfig event
    const handleUrlAutoConfig = (event) => {
      console.log('urlAutoConfig event received:', event.detail);
      if (event.detail && event.detail.url) {
        setFirmUrl(event.detail.url);
        handleUrlSubmit({ detail: { url: event.detail.url } });
      }
    };

    // Add event listener for autoConfigureClicked event
    const handleAutoConfigureClicked = (event) => {
      console.log('autoConfigureClicked event received:', event.detail);
      if (event.detail && event.detail.url) {
        setFirmUrl(event.detail.url);
        handleUrlSubmit({ detail: { url: event.detail.url } });
      }
    };

    // Add event listener for direct button clicks
    const handleAutoConfigButtonClick = (event) => {
      // Check if the clicked element is the Auto-Configure button
      if (event.target &&
          (event.target.textContent.includes('Auto-Configure') ||
           (event.target.parentElement && event.target.parentElement.textContent.includes('Auto-Configure')))) {
        console.log('Auto-Configure button click detected');

        // Find the URL input
        const urlInput = document.getElementById('firmUrl');
        if (urlInput && urlInput.value) {
          console.log('Found URL input with value:', urlInput.value);
          setFirmUrl(urlInput.value);
          handleUrlSubmit({ detail: { url: urlInput.value } });
        }
      }
    };

    document.addEventListener('urlAutoConfig', handleUrlAutoConfig);
    document.addEventListener('autoConfigureClicked', handleAutoConfigureClicked);
    document.addEventListener('click', handleAutoConfigButtonClick, true);

    return () => {
      // Clean up when component unmounts
      delete window.handleGetStarted;
      delete window.handleUrlSubmit;
      delete window.setShowAuthOverlay;
      document.removeEventListener('urlAutoConfig', handleUrlAutoConfig);
      document.removeEventListener('autoConfigureClicked', handleAutoConfigureClicked);
      document.removeEventListener('click', handleAutoConfigButtonClick, true);
    };
  }, [handleUrlSubmit]);

  const handleAuthSuccess = (attorneyData) => {
    // Handle successful authentication and attorney account creation
    console.log('Attorney account created:', attorneyData);

    // Check if this is development mode
    const isDevelopmentMode = attorneyData && attorneyData.id && attorneyData.id.toString().startsWith('dev-');

    if (isDevelopmentMode) {
      console.log('Development mode detected. Redirecting to dashboard...');
      // Close the auth overlay and redirect to dashboard
      setShowAuthOverlay(false);
      // Redirect to dashboard
      window.location.href = `/dashboard`;
    } else {
      // Redirect to dashboard
      window.location.href = `/dashboard`;
    }
  }

  // Track if we're in the process of ending a call to prevent race conditions
  const [isEndingCall, setIsEndingCall] = useState(false);
  // Add a stable key for VapiCall to prevent unnecessary unmounting
  const [vapiCallKey, setVapiCallKey] = useState(0);

  const endCall = (data) => {
    debug.log('Call ended', { timestamp: new Date().toISOString(), data });
    trackUserJourney('call_ended');

    // If we're already in the process of ending a call, don't start another cleanup
    if (isEndingCall) {
      console.log('[App.jsx] Already in the process of ending a call, ignoring duplicate endCall');
      return;
    }

    // Set the flag to indicate we're in the process of ending a call
    setIsEndingCall(true);

    // Check if the call is still active according to the global variable
    const isCallStillActive = window.vapiCallActive === true;

    // Check if this is a forced end while the assistant is still speaking
    const isForcedEndWhileSpeaking = data && data.forcedWhileSpeaking;

    // Clean up any lingering Daily.co iframes
    try {
      const existingIframes = document.querySelectorAll('iframe[title*="daily"]');
      if (existingIframes.length > 0) {
        console.log(`[App.jsx] Found ${existingIframes.length} lingering Daily.co iframes. Removing during endCall...`);
        existingIframes.forEach(iframe => {
          iframe.parentNode.removeChild(iframe);
        });
      }
    } catch (cleanupError) {
      console.warn("[App.jsx] Error cleaning up iframes during endCall:", cleanupError);
    }

    // If the call is empty or undefined, it's likely a premature call during initialization
    // In this case, we'll just reset the state and return to prevent the mount/unmount cycle
    if (!data || (Object.keys(data).length === 0 && data.constructor === Object)) {
      console.log('[App.jsx] Received empty data in endCall, likely during initialization - resetting state');

      // Reset the call state
      setCallActive(false);
      window.vapiCallActive = false;

      // Reset the ending call flag
      setTimeout(() => {
        setIsEndingCall(false);
      }, 500);

      return;
    }

    if (isCallStillActive || isForcedEndWhileSpeaking) {
      console.log('[App.jsx] Call is still active or ended while assistant was speaking - keeping interface visible');
      console.log('[App.jsx] window.vapiCallActive =', window.vapiCallActive);

      // Don't hide the call UI immediately if the call is still active
      // Instead, let the VapiCall component handle the cleanup when the call truly ends

      // Force the call-card-container to be visible
      const callCardContainer = document.querySelector('.call-card-container');
      if (callCardContainer) {
        callCardContainer.classList.add('active');
        callCardContainer.style.display = 'flex';
        callCardContainer.style.visibility = 'visible';
        callCardContainer.style.opacity = '1';
        callCardContainer.style.zIndex = '1000';
        console.log('[App.jsx] Forced call-card-container to be visible during active call');
      }

      // Set up an interval to check if the call is still active
      // Use a shorter interval and limit the number of checks to prevent infinite loops
      let checkCount = 0;
      const maxChecks = 10; // Maximum number of checks (10 seconds total)
      const checkCallInterval = setInterval(() => {
        checkCount++;
        console.log(`[App.jsx] Check ${checkCount}/${maxChecks} for call active status: ${window.vapiCallActive}`);

        if (window.vapiCallActive === false || checkCount >= maxChecks) {
          console.log('[App.jsx] Call is no longer active or max checks reached, cleaning up');
          clearInterval(checkCallInterval);

          // Force window.vapiCallActive to false if we've reached max checks
          if (checkCount >= maxChecks) {
            console.log('[App.jsx] Max checks reached, forcing window.vapiCallActive to false');
            window.vapiCallActive = false;
          }

          // Now we can safely end the call
          setCallActive(false);

          // Process the call data to show appropriate UI
          if (data && data.attorney) {
            console.log('[App.jsx] Showing attorney info');
            setShowAttorneyInfo(true);
            setShowCallSummary(false);
            setCallData(data);
          }
          else if (data && data.summary) {
            console.log('[App.jsx] Showing call summary');
            setShowAttorneyInfo(false);
            setShowCallSummary(true);
            setCallData(data);
          }
          else {
            console.log('[App.jsx] No attorney or summary data, returning to home state');
            setShowAttorneyInfo(false);
            setShowCallSummary(false);
          }

          // Clean up any lingering Daily.co iframes again
          try {
            const existingIframes = document.querySelectorAll('iframe[title*="daily"]');
            if (existingIframes.length > 0) {
              console.log(`[App.jsx] Found ${existingIframes.length} lingering Daily.co iframes after interval. Removing...`);
              existingIframes.forEach(iframe => {
                iframe.parentNode.removeChild(iframe);
              });
            }
          } catch (cleanupError) {
            console.warn("[App.jsx] Error cleaning up iframes after interval:", cleanupError);
          }

          // Reset the ending call flag
          setIsEndingCall(false);
        }
      }, 1000);

      return;
    }

    // For normal call endings, immediately set callActive to false to hide the call UI
    setCallActive(false);

    // Log detailed information about the call end
    console.log('[App.jsx] Call ended with data:', data);

    // Process the call data to show appropriate UI
    if (data && data.attorney) {
      console.log('[App.jsx] Showing attorney info');
      setShowAttorneyInfo(true);
      setShowCallSummary(false);
      setCallData(data);
    }
    else if (data && data.summary) {
      console.log('[App.jsx] Showing call summary');
      setShowAttorneyInfo(false);
      setShowCallSummary(true);
      setCallData(data);
    }
    else {
      console.log('[App.jsx] No attorney or summary data, returning to home state');
      setShowAttorneyInfo(false);
      setShowCallSummary(false);
    }

    // Force a re-render of the component tree to ensure the call UI is removed
    setTimeout(() => {
      console.log('[App.jsx] Forcing re-render after call end');
      setCallActive(state => {
        // Only set to false if it's not already false to avoid unnecessary re-renders
        if (state === true) return false;
        return state;
      });

      // Double-check that the global flag is set to false
      window.vapiCallActive = false;
      console.log('[App.jsx] Double-checked window.vapiCallActive is false at end of endCall');

      // Clean up any lingering Daily.co iframes one final time
      try {
        const existingIframes = document.querySelectorAll('iframe[title*="daily"]');
        if (existingIframes.length > 0) {
          console.log(`[App.jsx] Found ${existingIframes.length} lingering Daily.co iframes in final cleanup. Removing...`);
          existingIframes.forEach(iframe => {
            iframe.parentNode.removeChild(iframe);
          });
        }
      } catch (cleanupError) {
        console.warn("[App.jsx] Error cleaning up iframes in final cleanup:", cleanupError);
      }

      // Reset the ending call flag
      setIsEndingCall(false);
    }, 100);
  }

  // Debug the logo URL whenever it changes
  useEffect(() => {
    if (logoUrl) {
      console.log('[App.jsx] Current logoUrl:', logoUrl);
    }
  }, [logoUrl]);

  return (
    <div className="app-wrapper">
      <AnimatedBackground />
      <ToastContainer
        position="top-center"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme={isDarkTheme ? 'dark' : 'light'}
      />
      {/* Hide main app header for attorney subdomains and dashboard page to avoid duplication */}
      {!checkIsAttorneySubdomain(subdomain) && location.pathname !== '/dashboard' && (
        <header className="header">
          <div className="logo-container">
            <Link to="/" aria-label="Go to home page">
              <img src="/nav_logo.webp" alt="LegalScout Logo" className="logo" />
            </Link>
          </div>
          <Navbar isDarkTheme={isDarkTheme} />
          {/* Sign In Button - conditionally rendered based on current path */}
          {location.pathname === '/demo' && (
            <SignInButton onClick={() => setShowAuthOverlay(true)} />
          )}
          <ThemeToggle isDark={isDarkTheme} onToggle={toggleTheme} />
        </header>
      )}

      <main className="main-content-layer">
        <Routes>
          {/* Smart routing: attorney subdomains show homepage at root, others redirect based on auth */}
          <Route path="/" element={
            // Wait for subdomain detection to complete before routing
            subdomain === null || isLoading ? (
              <div className="loading-container" style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100vh',
                color: '#fff'
              }}>
                <div className="loading-spinner" style={{
                  border: '4px solid #f3f3f3',
                  borderTop: '4px solid #3498db',
                  borderRadius: '50%',
                  width: '40px',
                  height: '40px',
                  animation: 'spin 2s linear infinite',
                  marginBottom: '20px'
                }}></div>
                <p>Loading...</p>
              </div>
            ) : isAttorneySubdomain ? (
              <Home
                isLoading={isLoading}
                callActive={callActive}
                showAttorneyInfo={showAttorneyInfo}
                showCallSummary={showCallSummary}
                attorneyProfile={attorneyProfile}
                startCall={startCall}
                endCall={endCall}
                callData={callData}
                subdomain={subdomain}
                setShowAttorneyInfo={setShowAttorneyInfo}
                setShowCallSummary={setShowCallSummary}
                buttonText={buttonText}
                isAttorneySubdomain={isAttorneySubdomain}
                hideCreateAgentButton={true}
                isDarkTheme={isDarkTheme}
                vapiCallKey={vapiCallKey}
              />
            ) : (
              // FIXED: Simplified routing - authenticated users go to dashboard
              user ? <Navigate to="/dashboard" replace /> : <Navigate to="/home" replace />
            )
          } />

          {/* Primary home route - for main domain when not on attorney subdomain */}
          <Route
            path="/home"
            element={
              <Home
                isLoading={isLoading}
                callActive={callActive}
                showAttorneyInfo={showAttorneyInfo}
                showCallSummary={showCallSummary}
                attorneyProfile={attorneyProfile}
                startCall={startCall}
                endCall={endCall}
                callData={callData}
                subdomain={subdomain}
                setShowAttorneyInfo={setShowAttorneyInfo}
                setShowCallSummary={setShowCallSummary}
                buttonText={buttonText}
                isAttorneySubdomain={isAttorneySubdomain}
                hideCreateAgentButton={true}
                isDarkTheme={isDarkTheme}
                vapiCallKey={vapiCallKey}
              />
            }
          />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/contact" element={<div>Contact Page Coming Soon</div>} />
          <Route path="/auth/callback" element={<AuthCallback />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/test" element={<TestComponent />} />
          <Route path="/subdomain-test" element={<SubdomainTestPage />} />
          <Route path="/attorney-profile-test" element={<AttorneyProfileTest />} />
          <Route path="/vapi-test" element={<VapiTestPage />} />
          <Route path="/vapi-comparison" element={<VapiComparisonTest />} />
          <Route path="/complete-profile" element={<SimpleCompleteProfile />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/crm-demo" element={<CrmDemo />} />
          <Route path="/call-control" element={
            <Suspense fallback={
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>Loading call control interface...</p>
              </div>
            }>
              <CallControl />
            </Suspense>
          } />
          <Route path="/demo" element={
            <Suspense fallback={
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>Loading demo interface...</p>
              </div>
            }>
              <SimpleDemoPage
                firmName={firmName}
                logoUrl={logoUrl}
                state={state}
                primaryColor={primaryColor}
                secondaryColor={secondaryColor}
                buttonColor={buttonColor}
                setButtonColor={setButtonColor}
                backgroundColor={backgroundColor}
                backgroundOpacity={backgroundOpacity}
                welcomeMessage={welcomeMessage}
                informationGathering={informationGathering}
                practiceDescription={practiceDescription}
                previewHeight={previewHeight}
                setPreviewHeight={setPreviewHeight}
                attorneyName={attorneyName}
                selectedPracticeArea={selectedPracticeArea}
                handlePracticeAreaChange={handlePracticeAreaChange}
                showPreview={showPreview}
                setShowPreview={setShowPreview}
                handleLogoUpload={handleLogoUpload}
                handleRemoveLogo={handleRemoveLogo}
                practiceAreas={practiceAreas}
                activeConfigTab={activeConfigTab}
                setActiveConfigTab={setActiveConfigTab}
                buttonText={buttonText}
                setButtonText={setButtonText}
                buttonOpacity={buttonOpacity}
                setButtonOpacity={setButtonOpacity}
                practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
                setPracticeAreaBackgroundOpacity={setPracticeAreaBackgroundOpacity}
                textBackgroundColor={textBackgroundColor}
                setTextBackgroundColor={setTextBackgroundColor}
                goToPreview={goToPreview}
                setFirmName={setFirmName}
                setAttorneyName={setAttorneyName}
                setPracticeDescription={setPracticeDescription}
                setState={setState}
                setWelcomeMessage={setWelcomeMessage}
                setInformationGathering={setInformationGathering}
                setPrimaryColor={setPrimaryColor}
                setSecondaryColor={setSecondaryColor}
                setBackgroundColor={setBackgroundColor}
                setBackgroundOpacity={setBackgroundOpacity}
                iframeRef={iframeRef}
                firmUrl={firmUrl}
                setFirmUrl={setFirmUrl}
                isLoading={isUrlLoading}
                handleUrlSubmit={handleUrlSubmit}
                isDarkTheme={isDarkTheme}
                handleGetStarted={() => setShowAuthOverlay(true)}
              />
            </Suspense>
          } />
          <Route path="/demo/preview" element={
            <Suspense fallback={
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>Loading preview...</p>
              </div>
            }>
              <PreviewPage
                firmName={firmName}
                attorneyName={attorneyName}
                darkMode={isDarkTheme}
                onToggleDarkMode={() => setIsDarkTheme(!isDarkTheme)}
              />
            </Suspense>
          } />

          <Route path="/preview" element={
            <Suspense fallback={
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>Loading preview...</p>
              </div>
            }>
              <SimplifiedPreview
                firmName={firmName}
                primaryColor={primaryColor}
                secondaryColor={secondaryColor}
                buttonColor={buttonColor}
                backgroundColor={backgroundColor}
                backgroundOpacity={backgroundOpacity}
                practiceDescription={practiceDescription}
                welcomeMessage={welcomeMessage}
                informationGathering={informationGathering}
                theme={isDarkTheme ? 'dark' : 'light'}
                logoUrl={logoUrl || "/PRIMARY CLEAR.png"}
                buttonText={buttonText || "Start Consultation"}
                buttonOpacity={buttonOpacity}
                practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
                textBackgroundColor={textBackgroundColor}
                mascot="/PRIMARY CLEAR.png"
                vapiInstructions="You are a general legal assistant. Collect basic information about the client's legal needs and help them find an appropriate attorney."
              />
            </Suspense>
          } />

          <Route path="/simple-preview" element={<SimplePreviewPage />} />

          {/* Preview frame for dashboard */}
          <Route path="/preview-frame" element={
            <PreviewFrameLoader />
          } />

          {/* Test route for preview frame debugging */}
          <Route path="/preview-frame-test" element={
            <div style={{
              padding: '20px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '20px'
            }}>
              <h1>Simple Preview Test</h1>
              <p>This page is for testing the simple preview route directly.</p>
              <div style={{
                width: '100%',
                maxWidth: '800px',
                height: '600px',
                border: '2px solid #4B74AA',
                borderRadius: '8px',
                overflow: 'hidden'
              }}>
                <iframe
                  src="/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true"
                  title="Simple Preview Test"
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none'
                  }}
                />
              </div>
              <div>
                <h2>Troubleshooting</h2>
                <ul>
                  <li>If the preview appears here but not in the dashboard, there's likely a CSS or layout issue in the dashboard.</li>
                  <li>If the preview doesn't appear here either, there might be an issue with the preview component itself.</li>
                  <li>This test uses the same simple-preview route that all other previews now use.</li>
                </ul>
              </div>
            </div>
          } />

          {/* Simple test route */}
          <Route path="/test-route" element={
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <h1>Test Route Works!</h1>
              <p>If you can see this, routing is working correctly.</p>
            </div>
          } />

          {/* Development test routes removed for production */}
        </Routes>
      </main>

      {/* Development subdomain tools removed for production */}

      {/* Auth Overlay */}
      <AuthOverlay
        isOpen={showAuthOverlay}
        onClose={() => setShowAuthOverlay(false)}
        onSuccess={handleAuthSuccess}
      />

      {/* Mobile Activate Assistant - Global component for mobile devices */}
      <MobileActivateAssistant
        onActivated={(config) => {
          console.log('[App] Assistant activated:', config);
          // Optionally update attorney profile if needed
          if (config && config.id) {
            setAttorneyProfile(config);
          }
        }}
      />

      {/* Bug Report Button removed for production */}
    </div>
  );
}

export default withDevTools(App, {
  displayName: 'LegalScoutApp',
  type: 'container',
  description: 'Main application container that manages call state',
  responsibleFor: ['call initiation', 'layout management', 'state control']
});