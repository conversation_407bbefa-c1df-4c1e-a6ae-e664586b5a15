import React, { useState, useEffect, useRef } from 'react';

/**
 * Simple Vapi Call Button - Uses the same pattern as the working Official Vapi SDK Test
 * 
 * This component bypasses all the complex React state management and uses
 * the direct Vapi HTML script tag pattern that we know works.
 */
const SimpleVapiCallButton = ({ 
  assistantId = "f9b97d13-f9c4-40af-a660-62ba5925ff2a",
  apiKey = "6734febc-fc65-4669-93b0-929b31ff6564",
  buttonText = "Get Started",
  onCallStart,
  onCallEnd,
  onError
}) => {
  const [isCallActive, setIsCallActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [vapiInstance, setVapiInstance] = useState(null);
  const [sdkLoaded, setSdkLoaded] = useState(false);

  // Load the Vapi SDK using the exact same pattern as the working test
  useEffect(() => {
    if (window.vapiSDK) {
      setSdkLoaded(true);
      return;
    }

    console.log('[SimpleVapiCallButton] Loading Vapi SDK...');

    // Use the exact same loading method as the working Official Vapi SDK Test
    (function (d, t) {
      var g = document.createElement(t),
        s = d.getElementsByTagName(t)[0];
      g.src = "https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js";
      g.defer = true;
      g.async = true;
      s.parentNode.insertBefore(g, s);

      g.onload = function () {
        console.log('[SimpleVapiCallButton] ✅ Official Vapi SDK loaded successfully');
        setSdkLoaded(true);
      };

      g.onerror = function() {
        console.error('[SimpleVapiCallButton] ❌ Failed to load official Vapi SDK');
        setError('Failed to load voice SDK');
      };
    })(document, "script");
  }, []);

  // Start call using the exact same pattern as the working test
  const startCall = async () => {
    if (!sdkLoaded || !window.vapiSDK) {
      setError('Voice SDK not loaded');
      return;
    }

    if (isCallActive) {
      console.log('[SimpleVapiCallButton] Call already active');
      return;
    }

    try {
      console.log('[SimpleVapiCallButton] Starting call...');
      setIsLoading(true);
      setError(null);

      // Use the exact same configuration as the working test
      const buttonConfig = {
        position: "bottom-right",
        offset: "40px",
        width: "50px",
        height: "50px",
        idle: {
          color: `linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%)`,
          type: "pill",
          title: "Have a legal question?",
          subtitle: "Talk with our AI assistant",
          icon: `https://unpkg.com/lucide-static@0.321.0/icons/phone.svg`,
        },
        loading: {
          color: `linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%)`,
          type: "pill",
          title: "Connecting...",
          subtitle: "Please wait",
          icon: `https://unpkg.com/lucide-static@0.321.0/icons/loader-2.svg`,
        },
        active: {
          color: `linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%)`,
          type: "pill",
          title: "Call is in progress...",
          subtitle: "End the call.",
          icon: `https://unpkg.com/lucide-static@0.321.0/icons/phone-off.svg`,
        },
      };

      console.log('[SimpleVapiCallButton] Creating Vapi instance with assistant:', assistantId);

      // Create the Vapi instance using the exact same pattern as the working test
      const instance = window.vapiSDK.run({
        apiKey: apiKey,
        assistant: assistantId,
        config: buttonConfig,
      });

      if (instance) {
        console.log('[SimpleVapiCallButton] ✅ Vapi instance created successfully');
        setVapiInstance(instance);
        setIsCallActive(true);
        setIsLoading(false);

        // Set up event listeners if available
        if (instance.on && typeof instance.on === 'function') {
          instance.on('call-start', () => {
            console.log('[SimpleVapiCallButton] 🎉 Call started successfully!');
            setIsCallActive(true);
            if (onCallStart) onCallStart();
          });

          instance.on('call-end', () => {
            console.log('[SimpleVapiCallButton] 📞 Call ended');
            setIsCallActive(false);
            if (onCallEnd) onCallEnd();
          });

          instance.on('error', (error) => {
            console.error('[SimpleVapiCallButton] ❌ Call error:', error);
            setError(error.message || 'Call error occurred');
            setIsCallActive(false);
            if (onError) onError(error);
          });
        }

        if (onCallStart) onCallStart();
      } else {
        throw new Error('Failed to create Vapi instance');
      }
    } catch (error) {
      console.error('[SimpleVapiCallButton] Error starting call:', error);
      setError(error.message || 'Failed to start call');
      setIsCallActive(false);
      setIsLoading(false);
      if (onError) onError(error);
    }
  };

  // Stop call
  const stopCall = () => {
    if (vapiInstance && vapiInstance.stop) {
      vapiInstance.stop();
    }
    setIsCallActive(false);
    if (onCallEnd) onCallEnd();
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      gap: '10px' 
    }}>
      <button
        onClick={isCallActive ? stopCall : startCall}
        disabled={isLoading || (!sdkLoaded && !error)}
        style={{
          padding: '15px 30px',
          fontSize: '18px',
          fontWeight: 'bold',
          color: 'white',
          background: isCallActive 
            ? 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)'
            : 'linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%)',
          border: 'none',
          borderRadius: '50px',
          cursor: isLoading || (!sdkLoaded && !error) ? 'not-allowed' : 'pointer',
          opacity: isLoading || (!sdkLoaded && !error) ? 0.6 : 1,
          transition: 'all 0.3s ease',
          boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
          minWidth: '200px'
        }}
      >
        {isLoading ? (
          '🔄 Connecting...'
        ) : isCallActive ? (
          '📞 End Call'
        ) : !sdkLoaded ? (
          '⏳ Loading...'
        ) : (
          `🎤 ${buttonText}`
        )}
      </button>

      {error && (
        <div style={{
          color: '#e74c3c',
          fontSize: '14px',
          textAlign: 'center',
          maxWidth: '300px'
        }}>
          ❌ {error}
        </div>
      )}

      {isCallActive && (
        <div style={{
          color: '#27ae60',
          fontSize: '14px',
          textAlign: 'center'
        }}>
          ✅ Call is active - speak now!
        </div>
      )}

      {!sdkLoaded && !error && (
        <div style={{
          color: '#f39c12',
          fontSize: '12px',
          textAlign: 'center'
        }}>
          Loading voice SDK...
        </div>
      )}
    </div>
  );
};

export default SimpleVapiCallButton;
