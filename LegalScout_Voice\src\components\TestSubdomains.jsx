import React, { useEffect, useState } from 'react';
import { getTestSubdomain, setTestSubdomain, clearTestSubdomain } from '../utils/subdomainTester';
import '../TestSubdomains.css';

/**
 * TestSubdomains component for development environment
 * Allows switching between different subdomains for testing
 */
const TestSubdomains = ({ availableSubdomains = [] }) => {
  const [currentSubdomain, setCurrentSubdomain] = useState('default');
  
  // Initialize the current subdomain on component mount
  useEffect(() => {
    const testSubdomain = getTestSubdomain();
    if (testSubdomain) {
      setCurrentSubdomain(testSubdomain);
    }
  }, []);
  
  // Handler for subdomain button clicks
  const handleSubdomainClick = (subdomain) => {
    if (subdomain === 'default') {
      clearTestSubdomain();
    } else {
      setTestSubdomain(subdomain);
    }
    
    setCurrentSubdomain(subdomain);
    
    // Reload the page to apply the subdomain change
    window.location.reload();
  };
  
  return (
    <div className="test-subdomains">
      <div className="test-subdomains-title">Test Subdomains</div>
      <div className="test-subdomain-buttons">
        <button 
          className={`default ${currentSubdomain === 'default' ? 'active' : ''}`}
          onClick={() => handleSubdomainClick('default')}
        >
          default
        </button>
        
        {availableSubdomains.map(subdomain => (
          subdomain !== 'default' && (
            <button
              key={subdomain}
              className={`${subdomain} ${currentSubdomain === subdomain ? 'active' : ''}`}
              onClick={() => handleSubdomainClick(subdomain)}
            >
              {subdomain}
            </button>
          )
        ))}
      </div>
    </div>
  );
};

export default TestSubdomains; 