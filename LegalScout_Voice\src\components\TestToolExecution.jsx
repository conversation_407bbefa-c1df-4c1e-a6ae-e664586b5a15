import React, { useState, useEffect } from 'react';
import { VAPI_TOOL_IDS, MAKE_WEBHOOK_URLS } from '../constants/vapiConstants';
import { vapiService } from '../services/vapiService.jsx';

/**
 * A test component to manually trigger tool execution
 * This is for development and debugging purposes only
 */
const TestToolExecution = ({ vapi, dossierData = {} }) => {
  const [logs, setLogs] = useState([]);
  const [selectedTool, setSelectedTool] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testParams, setTestParams] = useState('');

  // Add a log entry with timestamp
  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toISOString();
    setLogs(prev => [
      ...prev,
      {
        id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        timestamp,
        message,
        type
      }
    ]);
  };

  // Clear all logs
  const clearLogs = () => {
    setLogs([]);
  };

  // Handle tool selection
  const handleToolSelect = (toolKey) => {
    setSelectedTool(toolKey);
    
    // Set default test parameters based on the selected tool
    if (toolKey === 'LIVE_DOSSIER') {
      setTestParams(JSON.stringify({
        "legalIssue": "I need help with a child custody case",
        "location": {
          "address": "New York, NY",
          "lat": 40.7128,
          "lng": -74.0060
        }
      }, null, 2));
    } else if (toolKey === 'CHECK_MATCHING_ATTORNEYS') {
      setTestParams(JSON.stringify({
        "practiceArea": "Family Law",
        "location": {
          "address": "New York, NY",
          "lat": 40.7128,
          "lng": -74.0060
        }
      }, null, 2));
    } else if (toolKey === 'LOCAL_ATTORNEY_SEARCH') {
      setTestParams(JSON.stringify({
        "practiceArea": "Family Law",
        "location": {
          "address": "New York, NY",
          "lat": 40.7128,
          "lng": -74.0060
        },
        "radius": 25
      }, null, 2));
    } else if (toolKey === 'GET_USER_INFO') {
      setTestParams(JSON.stringify({
        "subdomain": "default"
      }, null, 2));
    }
  };

  // Execute the selected tool
  const executeTool = async () => {
    if (!selectedTool) {
      addLog('No tool selected', 'error');
      return;
    }

    try {
      setIsLoading(true);
      addLog(`Executing tool: ${selectedTool}`, 'info');

      // Parse the test parameters
      let params = {};
      try {
        params = JSON.parse(testParams);
      } catch (e) {
        addLog(`Error parsing parameters: ${e.message}`, 'error');
        setIsLoading(false);
        return;
      }

      // Create tool execution data
      const toolId = VAPI_TOOL_IDS[selectedTool];
      const toolCallId = `test-tool-${Date.now()}`;
      
      const toolData = {
        tool: { id: toolId },
        toolCallId: toolCallId,
        ...params,
        existingDossier: dossierData
      };

      addLog(`Tool ID: ${toolId}`, 'info');
      addLog(`Tool Call ID: ${toolCallId}`, 'info');
      addLog(`Full Tool Data: ${JSON.stringify(toolData)}`, 'debug');

      // Get the webhook URL
      const webhookUrl = MAKE_WEBHOOK_URLS[selectedTool];
      if (!webhookUrl) {
        addLog(`No webhook URL found for tool: ${selectedTool}`, 'error');
        setIsLoading(false);
        return;
      }

      // Send the webhook request
      addLog(`Sending webhook request to: ${webhookUrl}`, 'info');
      const response = await vapiService.syncWithMakeWebhook(webhookUrl, toolData);
      
      // Process the response
      addLog(`Webhook response received: ${JSON.stringify(response)}`, 'success');

      // Check if the response follows the Vapi format
      if (response && response.results && Array.isArray(response.results)) {
        addLog('Response follows Vapi format with results array', 'success');
        
        // Check if the toolCallId matches
        const result = response.results.find(r => r.toolCallId === toolCallId);
        if (result) {
          addLog(`Found matching result for toolCallId: ${toolCallId}`, 'success');
          addLog(`Result: ${JSON.stringify(result.result)}`, 'success');
        } else {
          addLog(`No result found for toolCallId: ${toolCallId}`, 'warning');
        }
      } else {
        addLog('Response does not follow Vapi format with results array', 'warning');
        addLog('The response will be automatically reformatted by the webhook handler', 'info');
      }

      setIsLoading(false);
    } catch (error) {
      addLog(`Error executing tool: ${error.message}`, 'error');
      setIsLoading(false);
    }
  };

  return (
    <div className="test-tool-execution">
      <h2>Test Tool Execution</h2>
      
      <div className="tool-selection">
        <h3>1. Select a Tool</h3>
        <div className="tool-buttons">
          {Object.keys(VAPI_TOOL_IDS).map(toolKey => (
            <button 
              key={toolKey}
              onClick={() => handleToolSelect(toolKey)}
              className={selectedTool === toolKey ? 'selected' : ''}
              disabled={isLoading}
            >
              {toolKey}
            </button>
          ))}
        </div>
      </div>

      {selectedTool && (
        <div className="tool-params">
          <h3>2. Configure Parameters</h3>
          <textarea 
            value={testParams}
            onChange={(e) => setTestParams(e.target.value)}
            rows={10}
            disabled={isLoading}
          />
        </div>
      )}

      <div className="execute-section">
        <button 
          onClick={executeTool} 
          disabled={!selectedTool || isLoading}
          className="execute-button"
        >
          {isLoading ? 'Executing...' : 'Execute Tool'}
        </button>
      </div>

      <div className="logs-section">
        <div className="logs-header">
          <h3>Execution Logs</h3>
          <button onClick={clearLogs} disabled={isLoading}>Clear Logs</button>
        </div>
        <div className="logs-container">
          {logs.map(log => (
            <div key={log.id} className={`log-entry ${log.type}`}>
              <span className="timestamp">{new Date(log.timestamp).toLocaleTimeString()}</span>
              <span className="message">{log.message}</span>
            </div>
          ))}
          {logs.length === 0 && (
            <div className="empty-logs">No logs yet. Execute a tool to see logs.</div>
          )}
        </div>
      </div>

      <style jsx>{`
        .test-tool-execution {
          background: #f5f5f5;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
          max-width: 800px;
        }

        h2, h3 {
          color: #333;
          margin-top: 0;
        }

        .tool-selection, .tool-params, .execute-section, .logs-section {
          margin-bottom: 20px;
        }

        .tool-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-bottom: 15px;
        }

        button {
          background: #fff;
          border: 1px solid #ddd;
          padding: 8px 15px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          transition: all 0.2s;
        }

        button:hover {
          background: #f0f0f0;
        }

        button.selected {
          background: #007bff;
          color: white;
          border-color: #0069d9;
        }

        button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .execute-button {
          background: #28a745;
          color: white;
          border: none;
          padding: 10px 20px;
          font-size: 16px;
        }

        .execute-button:hover:not(:disabled) {
          background: #218838;
        }

        textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-family: monospace;
          font-size: 14px;
        }

        .logs-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .logs-container {
          background: #fff;
          border: 1px solid #ddd;
          border-radius: 4px;
          height: 300px;
          overflow-y: auto;
          padding: 10px;
        }

        .log-entry {
          margin-bottom: 5px;
          padding: 5px;
          border-radius: 3px;
          font-family: monospace;
          font-size: 12px;
          display: flex;
        }

        .log-entry.info {
          background: #e8f4fd;
        }

        .log-entry.error {
          background: #fee;
        }

        .log-entry.success {
          background: #e6fffa;
        }

        .log-entry.warning {
          background: #fff9db;
        }

        .log-entry.debug {
          color: #666;
          font-size: 11px;
        }

        .timestamp {
          color: #666;
          margin-right: 10px;
          flex-shrink: 0;
        }

        .empty-logs {
          color: #999;
          text-align: center;
          padding: 20px;
        }
      `}</style>
    </div>
  );
};

export default TestToolExecution; 