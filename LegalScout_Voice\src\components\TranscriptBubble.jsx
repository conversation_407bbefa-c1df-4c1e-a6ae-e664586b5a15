import React from 'react';

const TranscriptBubble = ({ transcript, isDarkTheme }) => {
  const hasContent = transcript && transcript.trim();
  
  const bubbleStyle = {
    display: hasContent ? 'flex' : 'none',
    flexDirection: 'column',
    maxWidth: '90%',
    marginBottom: '10px',
    alignSelf: 'flex-start',
    marginRight: 'auto',
    opacity: '0.9'
  };
  
  const labelStyle = {
    fontSize: '12px',
    marginBottom: '4px',
    fontWeight: 'bold',
    color: '#0ea5e9'
  };
  
  const contentStyle = {
    padding: '12px 16px',
    borderRadius: '18px',
    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
    wordBreak: 'break-word',
    fontStyle: 'italic',
    backgroundColor: isDarkTheme ? 'rgba(14, 165, 233, 0.2)' : 'rgba(14, 165, 233, 0.1)',
    borderLeft: isDarkTheme ? '3px solid rgba(14, 165, 233, 0.6)' : '3px solid rgba(14, 165, 233, 0.5)',
    color: isDarkTheme ? '#e0e0e0' : '#333'
  };
  
  return (
    <div style={bubbleStyle}>
      <div style={labelStyle}>
        🎤 Real-time transcript:
      </div>
      <div style={contentStyle}>
        {transcript || 'Listening...'}
      </div>
    </div>
  );
};

export default TranscriptBubble;
