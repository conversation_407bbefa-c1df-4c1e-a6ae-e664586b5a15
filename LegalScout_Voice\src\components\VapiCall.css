/* VapiCall Component Styles */

.vapi-call-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%; /* Fill parent container */
  position: relative;
  color: var(--text-primary, #ffffff);
  background-color: transparent; /* Allow animated background to show */
  overflow: hidden;
  max-width: none;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  z-index: 5;
}

/* Speech Particles Visualization */
.speech-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1; /* Above background but below content */
  pointer-events: none; /* Allow interaction with elements behind */
  background: transparent !important;
}

/* Globe Background (visible when connected) */
.globe-background {
  display: none; /* Hide the globe background completely */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -10; /* Move it behind all content */
  opacity: 0;
  pointer-events: none;
  overflow: hidden;
}

[data-theme="light"] .globe-background {
  display: none; /* Hide in light theme too */
}

.globe-visualization {
  display: none; /* Hide the globe visualization */
}

.globe-sphere {
  display: none; /* Hide the globe sphere */
}

.globe-highlight {
  display: none; /* Hide the highlight box */
}

.usa-highlight {
  display: none; /* Hide the USA highlight */
}

.location-label {
  display: none; /* Hide location labels */
}

/* Call interface - main container during an active call */
.call-interface {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: none;
  height: 100vh; /* Use viewport height to ensure full height */
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  padding-bottom: 80px; /* Add padding to account for the text input */
  background-color: transparent;
  z-index: 5;
}

/* Force visibility for call interface */
.call-interface.force-visible {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 1000 !important;
}

/* Persistent call container */
#persistent-call-container {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 10000 !important;
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  width: 400px !important;
  height: 200px !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
  border-radius: 10px !important;
  padding: 10px !important;
  color: white !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
  animation: fadeIn 0.5s ease-out !important;
}

/* Call card container - ensure it's always visible when active */
.call-card-container.active {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 1000 !important;
}

/* Three-column layout - adjusted for alignment with header */
.three-column-layout {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
  max-width: none;
  padding: 0;
  overflow: hidden;
  box-sizing: border-box;
  margin: 0;
  position: relative;
}

.column {
  padding: 10px;
  position: relative;
  box-sizing: border-box;
  height: 100%; /* Ensure full height in horizontal layout */
}

.left-column {
  width: 30%; /* Set width for horizontal layout */
  min-width: 300px;
  height: 100%; /* Full height */
  flex: 0 0 auto; /* Don't grow or shrink */
  overflow-y: auto; /* Allow scrolling if content is too large */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent rgba(59, 130, 246, 0.3); /* Firefox: thumb and track color with lighter blue */
}

/* Custom scrollbar for Webkit browsers (Chrome, Safari, Edge) */
.left-column::-webkit-scrollbar {
  width: 4px; /* Very thin scrollbar */
  background-color: transparent;
}

.left-column::-webkit-scrollbar-thumb {
  background-color: transparent; /* Completely transparent thumb */
  border: 1px solid rgba(59, 130, 246, 0.3); /* Thin light blue border matching dossier */
  border-radius: 10px;
  box-shadow: none; /* Remove glow for consistency */
}

.left-column::-webkit-scrollbar-thumb:hover {
  background-color: transparent; /* Keep transparent on hover */
  border: 1px solid rgba(59, 130, 246, 0.5); /* Slightly brighter border on hover */
  box-shadow: none; /* Remove glow for consistency */
}

.left-column::-webkit-scrollbar-track {
  background-color: transparent; /* Transparent track */
}

.middle-column {
  flex: 0 0 0; /* Don't grow, don't shrink, width 0 */
  background-color: transparent; /* Make completely transparent */
  border-radius: 10px;
  margin: 0;
  width: 0; /* Set width to 0 */
  min-width: 0; /* Ensure min-width is also 0 */
  opacity: 0;
  pointer-events: none; /* Prevent interaction */
  position: relative; /* Keep in the flow but invisible */
  z-index: -1;
}

/* Search loading spinner */
.search-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #D85722; /* Orange color for the spinner */
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.search-loading p {
  margin: 0;
  font-size: 16px;
  color: var(--text-primary);
  text-align: center;
}

/* Map placeholder */
.map-placeholder {
  width: 0;
  height: 0;
  background-color: transparent;
  opacity: 0;
  pointer-events: none;
  position: absolute;
  z-index: -1;
  display: none; /* Completely hide the map placeholder */
}

.right-column {
  width: 70%; /* Set width for horizontal layout */
  flex: 1 1 auto; /* Allow growing to fill available space */
  overflow-y: auto; /* Allow scrolling */
  height: 100%; /* Full height */
  min-width: 400px; /* Ensure minimum width */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent rgba(59, 130, 246, 0.9); /* Firefox: thumb and track color */
  display: flex !important; /* Force display on wide screens */
  visibility: visible !important; /* Force visibility on wide screens */
}

/* Custom scrollbar for Webkit browsers (Chrome, Safari, Edge) */
.right-column::-webkit-scrollbar {
  width: 4px; /* Very thin scrollbar */
  background-color: transparent;
}

.right-column::-webkit-scrollbar-thumb {
  background-color: transparent; /* Completely transparent thumb */
  border: 1px solid rgba(59, 130, 246, 0.9); /* Thin bright blue border */
  border-radius: 10px;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.9); /* Bright glowing effect */
}

.right-column::-webkit-scrollbar-thumb:hover {
  background-color: transparent; /* Keep transparent on hover */
  border: 1px solid rgba(59, 130, 246, 1); /* Brighter border on hover */
  box-shadow: 0 0 12px rgba(59, 130, 246, 1); /* Enhanced glow on hover */
}

.right-column::-webkit-scrollbar-track {
  background-color: transparent; /* Transparent track */
}

/* End Call Button - Updated styling */
.end-call-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.end-call-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #1a3363;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.end-call-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.end-call-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  opacity: 0.85;
  z-index: 2;
}

.end-call-text {
  margin-top: 5px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Dossier Component - Enhanced styling */
.dossier-component {
  width: 100%;
  background-color: transparent;
  color: var(--text-primary);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  border-radius: 16px;
  height: 100%;
  box-shadow: none; /* Remove shadow for transparency */
  margin-top: 5px;
  border: 1px solid rgba(59, 130, 246, 0.3); /* Light thin blue piping */
  backdrop-filter: none; /* Remove blur effect for transparency */
  -webkit-backdrop-filter: none; /* Remove blur effect for transparency */
  transition: all 0.3s ease;
}

[data-theme="light"] .dossier-component {
  background-color: transparent;
  border: 1px solid rgba(59, 130, 246, 0.3); /* Light thin blue piping */
  box-shadow: none; /* Remove shadow for transparency */
}

.dossier-title {
  color: var(--text-primary);
  font-size: 1.25rem;
  margin-bottom: 15px;
  text-align: left;
  font-weight: 600;
  letter-spacing: -0.02em;
  position: relative;
  padding-bottom: 12px;
}

.dossier-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(to right, var(--nav-text), transparent);
  border-radius: 2px;
}

.dossier-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  max-height: calc(100% - 50px);
  scrollbar-width: thin;
  padding: 4px;
}

.case-info-item {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  padding: 16px;
  background-color: transparent;
  border-radius: 12px;
  gap: 10px;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

[data-theme="light"] .case-info-item {
  background-color: transparent;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.case-info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
  background-color: rgba(var(--nav-hover-rgb), 0.1);
}

.case-info-item.status-item {
  background: linear-gradient(135deg, transparent, rgba(59, 130, 246, 0.05));
  border-left: 3px solid rgb(var(--color-interactive));
}

[data-theme="light"] .case-info-item.status-item {
  background: linear-gradient(135deg, transparent, rgba(59, 130, 246, 0.05));
}

.item-icon {
  font-size: 18px;
  margin-right: 8px;
  opacity: 0.9;
  transition: transform 0.3s ease;
}

.case-info-item:hover .item-icon {
  transform: scale(1.1);
}

.item-label {
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 6px;
  font-size: 0.75rem;
  text-transform: uppercase;
  width: 100%;
  letter-spacing: 0.05em;
}

.item-value {
  color: var(--text-primary);
  font-size: 0.9rem;
  line-height: 1.5;
  width: 100%;
  font-weight: 400;
}

/* Scrollbar styling */
.dossier-items::-webkit-scrollbar {
  width: 4px; /* Very thin scrollbar */
  background-color: transparent;
}

.dossier-items::-webkit-scrollbar-thumb {
  background-color: transparent; /* Completely transparent thumb */
  border: 1px solid rgba(59, 130, 246, 0.3); /* Thin light blue border matching dossier */
  border-radius: 10px;
  box-shadow: none; /* Remove glow for consistency */
}

.dossier-items::-webkit-scrollbar-thumb:hover {
  background-color: transparent; /* Keep transparent on hover */
  border: 1px solid rgba(59, 130, 246, 0.5); /* Slightly brighter border on hover */
  box-shadow: none; /* Remove glow for consistency */
}

.dossier-items::-webkit-scrollbar-track {
  background-color: transparent; /* Transparent track */
}

/* Firefox scrollbar styling */
.dossier-items {
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent rgba(59, 130, 246, 0.3); /* Firefox: thumb and track color with lighter blue */
}

/* Animation for updates */
@keyframes dossier-update {
  0% {
    opacity: 0.7;
    transform: scale(0.98) translateY(5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dossier-items.updating {
  animation: dossier-update 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Conversation Container - Transparent styling */
.conversation-container {
  height: 100%;
  display: flex !important; /* Force display on all screen sizes */
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  background-color: transparent;
  box-shadow: none;
  width: 100%;
  position: relative;
  flex: 1; /* Ensure it expands to fill available space */
  visibility: visible !important; /* Force visibility on all screen sizes */
  contain: layout style paint; /* Contain layout changes within this container */
  overscroll-behavior: contain; /* Prevent scroll chaining to parent elements */
}

/* Conversation area */
.conversation-area {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden; /* Prevent horizontal scroll */
  padding: 10px;
  padding-bottom: 100px; /* Reduced padding to minimize layout shifts */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent rgba(59, 130, 246, 0.9); /* Firefox: thumb and track color */
  position: relative;
  height: calc(100% - 80px); /* Adjust for input height */
  display: flex !important; /* Force display on all screen sizes */
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  background-color: transparent;
  width: 100%;
  margin-bottom: 20px; /* Reduced margin to minimize layout shifts */
  scroll-behavior: auto; /* Use auto instead of smooth to prevent global scroll interference */
  visibility: visible !important; /* Force visibility on all screen sizes */
  contain: layout style paint size; /* Contain layout, style, paint, and size changes */
  isolation: isolate; /* Create new stacking context to isolate scroll behavior */
  transform: translateZ(0); /* Force hardware acceleration and create new layer */
  overscroll-behavior: contain; /* Prevent scroll chaining to parent elements */
  -webkit-overflow-scrolling: touch; /* Enable momentum scrolling on iOS */
  scroll-snap-type: none; /* Disable scroll snapping to prevent interference */
}

/* Update message styles to remove source indicators */
.message {
  margin: 10px 0;
  display: flex !important; /* Force display on all screen sizes */
  width: 100%;
  visibility: visible !important; /* Force visibility on all screen sizes */
  opacity: 1 !important; /* Force opacity on all screen sizes */
}

.message.assistant {
  justify-content: flex-start;
}

.message.user {
  justify-content: flex-end;
}

.message-content {
  padding: 10px 12px;
  border-radius: 8px;
  max-width: 80%;
  margin: 0 10px;
  background-color: transparent;
  box-shadow: none;
  animation: stemIn 0.4s ease-out forwards;
  transform-origin: bottom left;
  display: block !important; /* Force display on all screen sizes */
  visibility: visible !important; /* Force visibility on all screen sizes */
  opacity: 1 !important; /* Force opacity on all screen sizes */
}

[data-theme="light"] .message-content {
  background-color: transparent;
  border: none;
}

[data-theme="dark"] .message-content {
  background-color: transparent;
  border: none;
}

.message-text {
  margin: 0;
  line-height: 1.5;
  color: var(--text-primary);
  font-size: 15px;
  letter-spacing: 0.01em;
  animation: textFadeIn 0.3s ease-out forwards;
  animation-delay: 0.2s; /* Start after the stem animation has begun */
  opacity: 1 !important; /* Force visible on all screen sizes */
  display: block !important; /* Force display on all screen sizes */
  visibility: visible !important; /* Force visibility on all screen sizes */
}

.message.assistant .message-text {
  font-weight: 400;
}

.message.user .message-text {
  font-weight: 500;
}

[data-theme="light"] .message-text {
  color: #000000;
}

.message.assistant .message-content {
  border-top-left-radius: 4px;
  border-left: 2px solid rgba(216, 87, 34, 0.6);
  background-color: rgba(216, 87, 34, 0.05); /* Much more transparent background */
  color: var(--text-primary);
  transform-origin: bottom left; /* Stem from bottom left */
}

.message.user .message-content {
  border-top-right-radius: 4px;
  border-right: 2px solid rgba(59, 130, 246, 0.6); /* Blue border for user messages */
  background-color: rgba(59, 130, 246, 0.05); /* Much more transparent background */
  color: var(--text-primary);
  transform-origin: bottom right; /* Stem from bottom right */
}

[data-theme="light"] .message.assistant .message-content {
  color: #000000;
  background-color: rgba(216, 87, 34, 0.03); /* Almost completely transparent */
  border-left: 2px solid rgba(216, 87, 34, 0.4);
}

[data-theme="light"] .message.user .message-content {
  color: #000000;
  background-color: rgba(59, 130, 246, 0.03); /* Almost completely transparent */
  border-right: 2px solid rgba(59, 130, 246, 0.4);
}

.message-timestamp {
  font-size: 0.8em;
  color: var(--text-secondary) !important;
  margin-top: 4px;
  text-align: right;
}

[data-theme="light"] .message-timestamp {
  color: rgba(0, 0, 0, 0.5) !important;
}

/* Remove old message source styles */
.message-source {
  display: none;
}

/* Animation for new messages */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Text entry styling */
.text-input-container-wrapper {
  position: fixed !important; /* Force fixed positioning with !important */
  bottom: 0 !important; /* Force anchor to bottom with !important */
  left: 0 !important;
  right: 0 !important;
  width: 100vw !important; /* Use viewport width to ensure full width */
  max-width: 100vw !important; /* Ensure it doesn't exceed viewport width */
  display: flex !important;
  justify-content: center !important;
  padding: 10px 20px !important;
  z-index: 10000 !important; /* Extremely high z-index to ensure it's always on top */
  margin: 0 !important;
  box-sizing: border-box !important;
  background-color: rgba(0, 0, 0, 0.3) !important; /* Semi-transparent background */
  backdrop-filter: blur(5px) !important; /* Add blur effect */
  -webkit-backdrop-filter: blur(5px) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  transform: translateZ(0) !important; /* Force hardware acceleration */
}

/* Fixed text input container with class-based styles */
.fixed-text-input-container {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 999999 !important;
  margin: 0 !important;
  padding: 10px 20px !important;
  box-sizing: border-box !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.text-input-inner-container {
  width: 100% !important;
  max-width: 800px !important;
  margin: 0 auto !important;
  display: flex !important;
  background-color: rgba(30, 30, 30, 0.5) !important;
  border-radius: 25px !important;
  padding: 5px !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.text-input-field {
  flex: 1 !important;
  background: transparent !important;
  border: none !important;
  padding: 12px 15px !important;
  color: white !important;
  font-size: 16px !important;
  outline: none !important;
  border-radius: 25px !important;
}

.text-input-send-button {
  background-color: #2e7bf3 !important;
  color: white !important;
  border: none !important;
  border-radius: 25px !important;
  padding: 8px 20px !important;
  margin: 5px !important;
  cursor: pointer !important;
  font-weight: 600 !important;
}

.text-input-send-button:disabled {
  opacity: 0.6 !important;
  pointer-events: none !important;
}

/* Remove the text entry label by setting it to display: none */
.text-entry-label {
  display: none; /* Hide the red "text entry" text */
}

.text-input-area {
  width: 100%;
  max-width: 800px;
  position: relative;
}

.text-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 12px 15px;
  color: var(--text-primary);
  font-size: 16px;
  outline: none;
  border-radius: 25px;
  transition: color 0.3s ease;
}

.text-input::placeholder {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

[data-theme="light"] .text-input {
  color: var(--text-primary);
}

[data-theme="light"] .text-input::placeholder {
  color: var(--text-secondary);
}

/* Text input container */
.text-input-container {
  display: flex !important;
  background-color: rgba(30, 30, 30, 0.5) !important; /* More transparent background */
  border-radius: 25px !important;
  padding: 5px !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s ease !important;
  width: 100% !important;
  max-width: 800px !important;
  position: relative !important;
  z-index: 10001 !important; /* Even higher z-index than the wrapper */
}

[data-theme="light"] .text-input-container {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.text-input-container:focus-within {
  box-shadow: 0 0 25px rgba(59, 130, 246, 0.15);
  background-color: var(--nav-hover);
  border-color: rgba(59, 130, 246, 0.3);
}

[data-theme="light"] .text-input-container:focus-within {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 25px rgba(59, 130, 246, 0.2);
}

.message-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 12px 15px;
  color: var(--text-primary);
  font-size: 16px;
  outline: none;
  border-radius: 25px;
}

.message-input::placeholder {
  color: var(--text-secondary);
}

.send-button {
  background-color: #2e7bf3;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 8px 20px;
  margin: 5px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.send-button:hover {
  background-color: #3d8aff;
  transform: scale(1.05);
}

.send-button:disabled {
  background-color: #475366;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Loading and error states */
.call-status {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 30px;
  color: white;
}

.call-status.connecting h2,
.call-status.idle h2,
.call-status.error h2 {
  margin-bottom: 20px;
  font-size: 18px;
  color: white;
}

.connecting-animation {
  display: flex;
  gap: 5px;
  margin-top: 10px;
}

.connecting-animation .dot {
  width: 10px;
  height: 10px;
  background-color: #2e7bf3;
  border-radius: 50%;
  animation: dot-fade 1.4s infinite;
  opacity: 0;
}

.connecting-animation .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.connecting-animation .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-fade {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

/* Media Queries */
@media (max-width: 1200px) {
  .three-column-layout {
    grid-template-columns: 1fr 2fr 1fr;
    gap: 15px;
  }

  .left-column {
    padding: 15px;
  }

  .right-column {
    padding: 15px;
  }
}

@media (max-width: 992px) {
  .three-column-layout {
    display: flex;
    flex-direction: column;
    gap: 10px;
    position: relative;
    height: 100%;
  }

  .left-column,
  .middle-column,
  .right-column {
    width: 100%;
  }

  .middle-column {
    order: 3;
    display: none; /* Hide the middle column on mobile */
  }

  .right-column {
    order: 2;
    flex: 1 1 auto; /* Allow growing but maintain proportion */
    overflow-y: auto; /* Allow scrolling */
    display: block !important;
    visibility: visible !important;
  }

  .left-column {
    order: 1; /* Show the dossier first on mobile */
    margin-bottom: 10px;
    min-height: 200px; /* Ensure minimum height */
    flex: 0 0 auto; /* Don't allow shrinking */
    display: block !important; /* Force display */
    visibility: visible !important; /* Ensure visibility */
  }

  .dossier-component {
    max-height: 300px; /* Limit height but allow scrolling */
    min-height: 150px; /* Ensure minimum height */
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    overflow-y: auto; /* Allow scrolling */
    display: block !important; /* Force display */
    visibility: visible !important; /* Ensure visibility */
  }

  .conversation-container {
    height: 100%;
    display: flex !important;
    flex-direction: column;
    overflow-y: auto; /* Allow scrolling */
    visibility: visible !important;
  }

  /* Ensure the conversation area takes most of the space */
  .conversation-area {
    flex: 1;
    min-height: 200px; /* Increased minimum height */
    overflow-y: auto; /* Allow scrolling */
    display: block !important; /* Force display */
    visibility: visible !important; /* Ensure visibility */
    position: relative;
    z-index: 50;
    max-height: calc(100vh - 300px); /* Set a maximum height based on viewport */
  }

  /* Ensure messages are visible */
  .message {
    display: flex !important;
    visibility: visible !important;
    margin: 8px 0;
    position: relative;
    z-index: 60;
    opacity: 1 !important;
    width: 100%;
  }

  .message.assistant {
    justify-content: flex-start;
  }

  .message.user {
    justify-content: flex-end;
  }

  .message-content {
    display: block !important;
    visibility: visible !important;
    max-width: 90%;
    opacity: 1 !important;
    color: white !important;
  }

  .message.assistant .message-content {
    background-color: rgba(216, 87, 34, 0.05) !important;
    border-left: 2px solid rgba(216, 87, 34, 0.6) !important;
    transform-origin: bottom left !important;
    animation: stemIn 0.4s ease-out forwards !important;
  }

  .message.assistant .message-text {
    color: rgba(255, 255, 255, 1) !important;
    font-weight: 400;
    animation: textFadeIn 0.3s ease-out forwards !important;
    animation-delay: 0.2s !important;
  }

  .message.user .message-content {
    background-color: rgba(59, 130, 246, 0.05) !important;
    border-right: 2px solid rgba(59, 130, 246, 0.6) !important;
    transform-origin: bottom right !important;
    animation: stemIn 0.4s ease-out forwards !important;
  }

  .message.user .message-text {
    color: rgba(255, 255, 255, 1) !important;
    font-weight: 500;
    animation: textFadeIn 0.3s ease-out forwards !important;
    animation-delay: 0.2s !important;
  }

  .message-text {
    display: block !important;
    visibility: visible !important;
    font-size: 14px;
    opacity: 1 !important;
    color: white !important;
  }

  .voice-status {
    flex-direction: row;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  /* Ensure the call interface properly stacks elements */
  .call-interface {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    position: relative; /* For absolute positioning */
    padding-bottom: 60px; /* Space for text input */
  }

  /* Switch to column layout on mobile */
  .three-column-layout {
    flex-direction: column;
  }

  /* Position the dossier at the top of the frame */
  .left-column {
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    width: 100%; /* Full width on mobile */
    z-index: 100;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 0;
    max-height: 200px;
    min-height: 100px;
    overflow-y: auto;
  }

  /* Ensure the dossier is visible and compact on mobile */
  .dossier-component {
    max-height: 180px;
    overflow-y: auto;
    margin-bottom: 0;
    border: 1px solid rgba(59, 130, 246, 0.3); /* Light thin blue piping */
    padding: 10px;
    box-shadow: none; /* Remove shadow for transparency */
    background-color: transparent !important; /* Make background completely transparent */
  }

  /* Make the dossier title smaller */
  .dossier-title {
    font-size: 1rem;
    margin-bottom: 10px;
    padding-bottom: 8px;
  }

  /* Make the case info items more compact */
  .case-info-item {
    padding: 5px;
    margin-bottom: 3px;
    font-size: 0.9rem;
  }

  .item-label {
    font-size: 0.7rem;
    margin-bottom: 3px;
  }

  .item-value {
    font-size: 0.8rem;
  }

  /* Position the conversation area between dossier and text input */
  .right-column {
    position: relative;
    flex: 1;
    overflow-y: auto;
    z-index: 99;
    padding: 0 10px;
    width: 100%; /* Full width on mobile */
    margin-top: 0; /* No margin needed in flex layout */
    margin-bottom: 0; /* No margin needed in flex layout */
    height: auto; /* Let flex layout determine height */
    min-height: 300px; /* Ensure minimum height */
    min-width: 0; /* Reset min-width for mobile */
  }

  /* Ensure the conversation area takes up available space */
  .conversation-area {
    flex: 1;
    height: auto; /* Let the container determine the height */
    min-height: 300px; /* Ensure minimum height */
    overflow-y: auto;
    padding-bottom: 20px;
    display: flex;
    flex-direction: column;
  }

  /* Anchor the text input area to the bottom of the frame */
  .fixed-text-input-container {
    padding: 10px !important;
    height: auto !important; /* Auto height for text input area */
  }

  .text-input-inner-container {
    padding: 3px !important;
  }

  .text-input-field {
    padding: 8px 12px !important;
    font-size: 14px !important;
  }

  .text-input-send-button {
    padding: 6px 12px !important;
    font-size: 14px !important;
    margin: 3px !important;
  }

  /* Make the text input more compact */
  .text-input-container {
    padding: 5px;
    display: flex !important;
    visibility: visible !important;
    width: 100% !important;
  }

  .message-input {
    padding: 8px 10px;
    font-size: 14px;
    display: block !important;
    visibility: visible !important;
    width: 100% !important;
  }

  .send-button {
    padding: 6px 12px;
    font-size: 14px;
    display: flex !important;
    visibility: visible !important;
  }

  .three-column-layout {
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .end-call-button {
    padding: 8px 12px;
    font-size: 14px;
    margin: 5px;
  }

  .end-call-button img {
    width: 16px;
    height: 16px;
    margin-right: 5px;
  }

  .voice-indicators {
    padding: 8px;
  }

  .voice-status {
    font-size: 14px;
  }

  .speaking-indicator, .volume-label {
    font-size: 12px;
  }

  .volume-bars {
    gap: 2px;
  }

  .volume-bar {
    width: 3px;
  }

  .conversation-area,
  .message-input {
    font-size: 14px;
  }

  .message-content {
    padding: 8px 12px;
  }

  .column {
    padding: 10px;
  }

  .text-input-container-wrapper {
    padding: 8px;
  }

  .globe-sphere {
    width: 200px;
    height: 200px;
  }

  .message {
    margin: 8px 0;
  }

  .text-input-container {
    padding: 8px;
  }

  .message-input {
    padding: 6px;
  }

  .send-button {
    padding: 6px 12px;
    font-size: 14px;
  }
}

/* Main Vapi Call Container - Ensure scroll containment */
.vapi-call-container {
  contain: layout style paint; /* Contain layout changes within this container */
  overscroll-behavior: contain; /* Prevent scroll chaining to parent elements */
  isolation: isolate; /* Create new stacking context */
  position: relative; /* Ensure proper positioning context */
}

/* Conversation scroll customization for WebKit browsers */
.conversation-area::-webkit-scrollbar {
  width: 4px; /* Very thin scrollbar */
  background-color: transparent;
}

.conversation-area::-webkit-scrollbar-thumb {
  background-color: transparent; /* Completely transparent thumb */
  border: 1px solid rgba(59, 130, 246, 0.9); /* Thin bright blue border */
  border-radius: 10px;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.9); /* Bright glowing effect */
}

.conversation-area::-webkit-scrollbar-thumb:hover {
  background-color: transparent; /* Keep transparent on hover */
  border: 1px solid rgba(59, 130, 246, 1); /* Brighter border on hover */
  box-shadow: 0 0 12px rgba(59, 130, 246, 1); /* Enhanced glow on hover */
}

.conversation-area::-webkit-scrollbar-track {
  background-color: transparent; /* Transparent track */
}

/* Firefox scrollbar styling */
.conversation-area {
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent rgba(59, 130, 246, 0.9); /* Firefox: thumb and track color */
}

/* Voice indicators */
.voice-indicators {
  padding: 10px 20px;
  background: transparent; /* Completely transparent background */
  border-radius: 10px 10px 0 0;
  margin-bottom: 5px;
  z-index: 50;
}

[data-theme="light"] .voice-indicators {
  background: transparent; /* Completely transparent in light mode too */
}

.voice-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.speaking-indicator {
  color: var(--text-secondary);
  display: none; /* Hide the speaking indicator text */
}

.speaking-indicator.active {
  color: #ff6eb4;
  display: none; /* Hide even when active */
}

[data-theme="light"] .speaking-indicator.active {
  color: #e91e63;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
  margin-right: 8px;
  transition: all 0.3s ease;
}

.speaking-indicator.active .indicator-dot {
  background-color: #ff6eb4;
  box-shadow: 0 0 10px #ff6eb4;
  animation: pulse 1.5s infinite;
}

.volume-level-container {
  display: flex;
  align-items: center;
  background: transparent;
  z-index: 999999; /* Extremely high z-index to ensure visibility */
  position: relative;
}

.volume-label {
  display: inline-block; /* Show the volume label */
  font-size: 14px;
  color: rgba(216, 87, 34, 0.9); /* Scout orange color */
  margin-right: 10px;
  font-weight: 600; /* Make it slightly bolder */
  text-shadow: 0 0 5px rgba(216, 87, 34, 0.5); /* Add a subtle glow */
}

.volume-bars {
  display: flex;
  align-items: flex-end; /* Align to bottom for better visualization */
  gap: 2px;
  height: 24px;
  background: transparent;
  padding: 0 5px;
}

.volume-bar {
  width: 4px;
  height: 5px;
  background-color: rgba(59, 130, 246, 0.1); /* Slightly visible when inactive */
  border-radius: 2px;
  transition: all 0.1s ease;
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 0 3px rgba(59, 130, 246, 0.1);
}

.volume-bar.active {
  background-color: rgba(59, 130, 246, 0.7); /* More visible active state */
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.8); /* Add glow */
  border: 1px solid rgba(59, 130, 246, 0.9);
}

.volume-bar:nth-child(1).active { height: 5px; }
.volume-bar:nth-child(2).active { height: 7px; }
.volume-bar:nth-child(3).active { height: 9px; }
.volume-bar:nth-child(4).active { height: 11px; }
.volume-bar:nth-child(5).active { height: 13px; }
.volume-bar:nth-child(6).active { height: 15px; }
.volume-bar:nth-child(7).active { height: 17px; }
.volume-bar:nth-child(8).active { height: 19px; }
.volume-bar:nth-child(9).active { height: 21px; }
.volume-bar:nth-child(10).active { height: 23px; }

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Message source indicators */
.message-source {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  opacity: 0.8;
  display: flex;
  align-items: center;
}

.message-source::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.user-source {
  color: #66c6ff;
}

.user-source::before {
  background-color: #66c6ff;
}

.assistant-source {
  color: #7effa7;
}

.assistant-source::before {
  background-color: #7effa7;
}

/* Specific source types */
.source-webhook .message-source {
  color: #ffcc66;
}

.source-webhook .message-source::before {
  background-color: #ffcc66;
}

.source-transcript .message-source {
  color: #ff9eee;
}

.source-transcript .message-source::before {
  background-color: #ff9eee;
}

/* Message timestamp */
.message-timestamp {
  font-size: 0.8em;
  color: var(--text-secondary) !important;
  margin-top: 4px;
  text-align: right;
}

/* Enhance animation for webhook messages */
.source-webhook .message-content {
  animation: fadeIn 0.5s ease-out;
  border-left: 2px solid #ffcc66;
}

/* Stem animation for messages */
@keyframes stemIn {
  0% {
    opacity: 0;
    transform: scaleY(0.1) translateY(10px);
  }
  40% {
    opacity: 0.4;
    transform: scaleY(0.8) translateY(5px);
  }
  100% {
    opacity: 1;
    transform: scaleY(1) translateY(0);
  }
}

/* Text fade in animation */
@keyframes textFadeIn {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add missing globePulse animation */
@keyframes globePulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

/* Text input form styles */
.text-input-form {
  display: flex;
  width: 100%;
  padding: 10px;
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
}

.text-input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 16px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  margin-right: 10px;
}

.text-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.text-input:disabled {
  background-color: var(--bg-disabled);
  cursor: not-allowed;
}

.send-button {
  padding: 10px 20px;
  border: none;
  border-radius: 20px;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-button:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-1px);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Transcribing message style */
.message.transcribing {
  opacity: 0.7;
  font-style: italic;
  animation: pulse 1.5s infinite;
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-indicator {
  display: inline-block;
  animation: typingDots 1.4s infinite;
  font-weight: bold;
}

@keyframes typingDots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60% { content: '...'; }
  80%, 100% { content: ''; }
}

@keyframes pulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.5;
  }
}