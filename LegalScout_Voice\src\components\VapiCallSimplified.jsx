import React, { useEffect, useState } from 'react';
import useVapiCallSimplified from '../hooks/useVapiCallSimplified';
import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';
import { getAttorneyConfigAsync } from '../config/attorneys';

/**
 * Simplified VapiCall Component - Following Official Vapi Web SDK Pattern
 * 
 * This component uses the simplified Vapi hook that follows the official
 * Vapi Web SDK pattern as documented at: https://docs.vapi.ai/quickstart/web
 * 
 * @param {Object} props - Component props
 * @param {string} props.assistantId - The assistant ID to use
 * @param {Function} props.onEndCall - Function to call when the call ends
 * @param {Object} props.assistantOverrides - Overrides for the assistant
 * @param {string} props.subdomain - Attorney subdomain for configuration
 * @param {boolean} props.autoStart - Whether to start the call automatically
 * @param {boolean} props.showControls - Whether to show call controls
 * @param {boolean} props.showMessages - Whether to show message history
 */
const VapiCallSimplified = ({
  assistantId = DEFAULT_ASSISTANT_ID,
  onEndCall,
  assistantOverrides = {},
  subdomain = 'default',
  autoStart = false,
  showControls = true,
  showMessages = true
}) => {
  const [attorneyConfig, setAttorneyConfig] = useState(null);
  const [finalAssistantId, setFinalAssistantId] = useState(assistantId);
  const [finalOverrides, setFinalOverrides] = useState(assistantOverrides);

  // Load attorney configuration
  useEffect(() => {
    const loadAttorneyConfig = async () => {
      try {
        console.log('[VapiCallSimplified] Loading attorney config for subdomain:', subdomain);
        const config = await getAttorneyConfigAsync(subdomain);
        
        if (config) {
          setAttorneyConfig(config);
          
          // Use attorney's assistant ID if available
          if (config.vapi_assistant_id && !assistantId) {
            setFinalAssistantId(config.vapi_assistant_id);
            console.log('[VapiCallSimplified] Using attorney assistant ID:', config.vapi_assistant_id);
          }
          
          // Prepare assistant overrides from attorney config
          const configOverrides = {
            recordingEnabled: true,
            ...assistantOverrides
          };
          
          // Add attorney-specific overrides if available
          if (config.customGreeting) {
            configOverrides.firstMessage = config.customGreeting;
          }
          
          if (config.voiceId) {
            configOverrides.voice = {
              provider: config.voiceProvider || '11labs',
              voiceId: config.voiceId
            };
          }
          
          setFinalOverrides(configOverrides);
          console.log('[VapiCallSimplified] Final assistant overrides:', configOverrides);
        }
      } catch (error) {
        console.error('[VapiCallSimplified] Failed to load attorney config:', error);
      }
    };

    loadAttorneyConfig();
  }, [subdomain, assistantId, assistantOverrides]);

  // Use the simplified Vapi hook
  const {
    status,
    isCallActive,
    isMuted,
    volumeLevel,
    messages,
    error,
    startCall,
    stopCall,
    sendMessage,
    toggleMute,
    say,
    isConnected,
    isConnecting,
    hasError
  } = useVapiCallSimplified({
    assistantId: finalAssistantId,
    onEndCall,
    assistantOverrides: finalOverrides
  });

  // Auto-start call if requested
  useEffect(() => {
    if (autoStart && status === 'idle' && finalAssistantId) {
      console.log('[VapiCallSimplified] Auto-starting call');
      startCall();
    }
  }, [autoStart, status, finalAssistantId, startCall]);

  // Handle call end
  const handleEndCall = () => {
    stopCall();
    if (typeof onEndCall === 'function') {
      onEndCall({
        messages,
        attorneyConfig,
        subdomain
      });
    }
  };

  // Render status indicator
  const renderStatus = () => {
    const statusConfig = {
      idle: { color: '#6b7280', text: 'Ready to call' },
      connecting: { color: '#f59e0b', text: 'Connecting...' },
      connected: { color: '#10b981', text: 'Call active' },
      ended: { color: '#6b7280', text: 'Call ended' },
      error: { color: '#ef4444', text: 'Error occurred' }
    };

    const config = statusConfig[status] || statusConfig.idle;

    return (
      <div className="flex items-center space-x-2 mb-4">
        <div 
          className="w-3 h-3 rounded-full"
          style={{ backgroundColor: config.color }}
        />
        <span className="text-sm font-medium" style={{ color: config.color }}>
          {config.text}
        </span>
        {volumeLevel > 0 && isConnected && (
          <div className="flex items-center space-x-1">
            <span className="text-xs text-gray-500">Volume:</span>
            <div className="w-16 h-2 bg-gray-200 rounded">
              <div 
                className="h-full bg-green-500 rounded transition-all duration-100"
                style={{ width: `${Math.min(volumeLevel * 100, 100)}%` }}
              />
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render call controls
  const renderControls = () => {
    if (!showControls) return null;

    return (
      <div className="flex space-x-3 mb-4">
        {!isCallActive ? (
          <button
            onClick={startCall}
            disabled={isConnecting}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isConnecting ? 'Connecting...' : 'Start Call'}
          </button>
        ) : (
          <>
            <button
              onClick={handleEndCall}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              End Call
            </button>
            <button
              onClick={toggleMute}
              className={`px-4 py-2 rounded-lg ${
                isMuted 
                  ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {isMuted ? 'Unmute' : 'Mute'}
            </button>
          </>
        )}
      </div>
    );
  };

  // Render messages
  const renderMessages = () => {
    if (!showMessages || messages.length === 0) return null;

    return (
      <div className="border rounded-lg p-4 max-h-64 overflow-y-auto">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Conversation</h3>
        <div className="space-y-2">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`p-2 rounded text-sm ${
                message.role === 'user'
                  ? 'bg-blue-50 text-blue-900 ml-4'
                  : 'bg-gray-50 text-gray-900 mr-4'
              }`}
            >
              <div className="font-medium text-xs text-gray-500 mb-1">
                {message.role === 'user' ? 'You' : 'Assistant'}
                {message.type === 'transcript' && ' (transcript)'}
              </div>
              <div>{message.content}</div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render error message
  const renderError = () => {
    if (!hasError) return null;

    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
        <div className="flex items-center">
          <div className="text-red-400 mr-2">⚠️</div>
          <div>
            <h4 className="text-sm font-medium text-red-800">Call Error</h4>
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="vapi-call-simplified p-4 border rounded-lg bg-white">
      <h2 className="text-lg font-semibold mb-4">Voice Assistant</h2>
      
      {renderStatus()}
      {renderError()}
      {renderControls()}
      {renderMessages()}
      
      {/* Debug info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-4 text-xs text-gray-500">
          <summary className="cursor-pointer">Debug Info</summary>
          <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto">
            {JSON.stringify({
              status,
              assistantId: finalAssistantId,
              subdomain,
              messageCount: messages.length,
              hasAttorneyConfig: !!attorneyConfig
            }, null, 2)}
          </pre>
        </details>
      )}
    </div>
  );
};

export default VapiCallSimplified;
