import React, { useState, useEffect } from 'react';

/**
 * Debug test component to verify Vapi SDK loading and initialization
 */
const VapiDebugTest = () => {
  const [status, setStatus] = useState('initializing');
  const [error, setError] = useState(null);
  const [vapi, setVapi] = useState(null);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
    console.log(`[VapiDebugTest] ${message}`);
  };

  useEffect(() => {
    const testVapiLoading = async () => {
      try {
        addLog('Starting Vapi SDK test...', 'info');
        
        // Test 1: Check if @vapi-ai/web package is available
        addLog('Test 1: Attempting to import @vapi-ai/web package', 'info');
        
        try {
          const VapiModule = await import('@vapi-ai/web');
          addLog('✅ Successfully imported @vapi-ai/web module', 'success');
          addLog(`Module keys: ${Object.keys(VapiModule).join(', ')}`, 'info');
          addLog(`Default export type: ${typeof VapiModule.default}`, 'info');
          
          // Test 2: Extract Vapi constructor
          const VapiConstructor = VapiModule.default || VapiModule.Vapi || VapiModule;
          
          if (typeof VapiConstructor === 'function') {
            addLog('✅ Found Vapi constructor function', 'success');
            
            // Test 3: Create Vapi instance
            const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
            addLog(`Test 3: Creating Vapi instance with API key: ${apiKey.substring(0, 8)}...`, 'info');
            
            try {
              // Constructor: new Vapi(apiToken, apiBaseUrl, dailyCallConfig, dailyCallObject)
              const vapiInstance = new VapiConstructor(apiKey, 'https://api.vapi.ai');
              addLog('✅ Successfully created Vapi instance', 'success');
              addLog(`Instance methods: ${Object.getOwnPropertyNames(Object.getPrototypeOf(vapiInstance)).join(', ')}`, 'info');
              
              // Test 4: Check required methods
              const requiredMethods = ['start', 'stop', 'on'];
              const missingMethods = requiredMethods.filter(method => typeof vapiInstance[method] !== 'function');
              
              if (missingMethods.length === 0) {
                addLog('✅ All required methods are available', 'success');
                setVapi(vapiInstance);
                setStatus('ready');
              } else {
                addLog(`❌ Missing required methods: ${missingMethods.join(', ')}`, 'error');
                setStatus('error');
                setError('Missing required methods');
              }
              
            } catch (instanceError) {
              addLog(`❌ Failed to create Vapi instance: ${instanceError.message}`, 'error');
              setStatus('error');
              setError(instanceError.message);
            }
            
          } else {
            addLog(`❌ Vapi constructor is not a function. Got: ${typeof VapiConstructor}`, 'error');
            setStatus('error');
            setError('Invalid Vapi constructor');
          }
          
        } catch (importError) {
          addLog(`❌ Failed to import @vapi-ai/web: ${importError.message}`, 'error');
          setStatus('error');
          setError(importError.message);
        }
        
      } catch (generalError) {
        addLog(`❌ General error: ${generalError.message}`, 'error');
        setStatus('error');
        setError(generalError.message);
      }
    };

    testVapiLoading();
  }, []);

  const testCall = async () => {
    if (!vapi) {
      addLog('❌ No Vapi instance available', 'error');
      return;
    }

    try {
      const assistantId = import.meta.env.VITE_VAPI_DEFAULT_ASSISTANT_ID || '310f0d43-27c2-47a5-a76d-e55171d024f7';
      addLog(`Testing call with assistant ID: ${assistantId}`, 'info');
      
      // Set up event listeners
      vapi.on('call-start', () => {
        addLog('✅ Call started successfully', 'success');
      });
      
      vapi.on('call-end', () => {
        addLog('Call ended', 'info');
      });
      
      vapi.on('error', async (error) => {
        let errorMessage = 'Unknown error';

        try {
          // Handle Response objects
          if (error && typeof error === 'object' && error.constructor.name === 'Response') {
            const responseText = await error.text();
            const status = error.status;
            const statusText = error.statusText;
            errorMessage = `HTTP ${status} ${statusText}: ${responseText}`;
            addLog(`❌ Call error (Response): ${errorMessage}`, 'error');
          } else if (error && error.message) {
            errorMessage = error.message;
            addLog(`❌ Call error: ${errorMessage}`, 'error');
          } else if (typeof error === 'string') {
            errorMessage = error;
            addLog(`❌ Call error: ${errorMessage}`, 'error');
          } else {
            errorMessage = JSON.stringify(error);
            addLog(`❌ Call error (object): ${errorMessage}`, 'error');
          }
        } catch (parseError) {
          addLog(`❌ Call error (unparseable): ${error}`, 'error');
          addLog(`❌ Parse error: ${parseError.message}`, 'error');
        }
      });
      
      // Start the call
      await vapi.start(assistantId);
      addLog('Call start request sent', 'info');
      
    } catch (callError) {
      addLog(`❌ Failed to start call: ${callError.message}`, 'error');
    }
  };

  const stopCall = () => {
    if (vapi) {
      try {
        vapi.stop();
        addLog('Stop call request sent', 'info');
      } catch (stopError) {
        addLog(`❌ Failed to stop call: ${stopError.message}`, 'error');
      }
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace',
      backgroundColor: '#f5f5f5',
      border: '1px solid #ddd',
      borderRadius: '8px',
      margin: '20px'
    }}>
      <h2>Vapi SDK Debug Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <strong>Status:</strong> 
        <span style={{ 
          color: status === 'ready' ? 'green' : status === 'error' ? 'red' : 'orange',
          marginLeft: '10px'
        }}>
          {status}
        </span>
      </div>

      {error && (
        <div style={{ 
          backgroundColor: '#ffe6e6', 
          color: 'red', 
          padding: '10px', 
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}

      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testCall}
          disabled={status !== 'ready'}
          style={{
            padding: '10px 20px',
            backgroundColor: status === 'ready' ? '#007bff' : '#ccc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            marginRight: '10px',
            cursor: status === 'ready' ? 'pointer' : 'not-allowed'
          }}
        >
          Test Call
        </button>
        
        <button 
          onClick={stopCall}
          disabled={status !== 'ready'}
          style={{
            padding: '10px 20px',
            backgroundColor: status === 'ready' ? '#dc3545' : '#ccc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: status === 'ready' ? 'pointer' : 'not-allowed'
          }}
        >
          Stop Call
        </button>
      </div>

      <div style={{ 
        backgroundColor: 'white', 
        border: '1px solid #ddd', 
        borderRadius: '4px',
        padding: '10px',
        maxHeight: '400px',
        overflowY: 'auto'
      }}>
        <h3>Debug Logs:</h3>
        {logs.map((log, index) => (
          <div 
            key={index} 
            style={{ 
              marginBottom: '5px',
              color: log.type === 'error' ? 'red' : log.type === 'success' ? 'green' : 'black'
            }}
          >
            <span style={{ color: '#666' }}>[{log.timestamp}]</span> {log.message}
          </div>
        ))}
      </div>
    </div>
  );
};

export default VapiDebugTest;
