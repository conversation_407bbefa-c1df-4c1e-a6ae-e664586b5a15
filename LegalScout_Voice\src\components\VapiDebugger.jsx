import React, { useState, useEffect } from 'react';

/**
 * VapiDebugger component
 * 
 * A simple component that directly calls the VAPI MCP server
 * and displays the raw results for debugging purposes.
 */
const VapiDebugger = ({ assistantId }) => {
  const [assistants, setAssistants] = useState([]);
  const [currentAssistant, setCurrentAssistant] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch assistants on mount
  useEffect(() => {
    fetchAssistants();
  }, []);

  // Fetch assistant details when assistantId changes
  useEffect(() => {
    if (assistantId) {
      fetchAssistantDetails(assistantId);
    }
  }, [assistantId]);

  // Fetch all assistants
  const fetchAssistants = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!window.mcp) {
        setError('MCP server not available');
        return;
      }

      console.log('VapiDebugger: Fetching assistants');
      const result = await window.mcp.invoke('list_assistants_vapi-mcp-server', {});
      console.log('VapiDebugger: Raw result:', result);

      if (result && Array.isArray(result)) {
        setAssistants(result);
      } else {
        setError('Unexpected response format');
      }
    } catch (error) {
      console.error('VapiDebugger: Error fetching assistants:', error);
      setError(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Fetch assistant details
  const fetchAssistantDetails = async (id) => {
    try {
      if (!window.mcp) {
        return;
      }

      console.log(`VapiDebugger: Fetching assistant details for ${id}`);
      const result = await window.mcp.invoke('get_assistant_vapi-mcp-server', {
        assistantId: id
      });
      console.log('VapiDebugger: Assistant details:', result);

      if (result) {
        setCurrentAssistant(result);
      }
    } catch (error) {
      console.error('VapiDebugger: Error fetching assistant details:', error);
    }
  };

  // Styles
  const styles = {
    container: {
      padding: '15px',
      backgroundColor: '#f5f5f5',
      border: '1px solid #ddd',
      borderRadius: '4px',
      marginBottom: '20px'
    },
    heading: {
      fontSize: '16px',
      fontWeight: 'bold',
      marginBottom: '10px'
    },
    list: {
      maxHeight: '200px',
      overflowY: 'auto',
      border: '1px solid #ddd',
      borderRadius: '4px',
      padding: '10px',
      backgroundColor: '#fff'
    },
    item: {
      padding: '5px',
      borderBottom: '1px solid #eee',
      fontSize: '14px'
    },
    error: {
      color: 'red',
      marginTop: '10px'
    },
    button: {
      padding: '8px 12px',
      backgroundColor: '#4B74AA',
      color: 'white',
      border: 'none',
      borderRadius: '4px',
      cursor: 'pointer',
      marginTop: '10px'
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.heading}>VAPI Debugger</div>
      
      {loading && <div>Loading...</div>}
      
      {error && <div style={styles.error}>{error}</div>}
      
      <div style={styles.heading}>Assistants ({assistants.length})</div>
      <div style={styles.list}>
        {assistants.length === 0 ? (
          <div>No assistants found</div>
        ) : (
          assistants.map(assistant => (
            <div key={assistant.id} style={styles.item}>
              <strong>{assistant.name}</strong> ({assistant.id})
              {assistant.id === assistantId && ' ← Current'}
            </div>
          ))
        )}
      </div>
      
      {currentAssistant && (
        <>
          <div style={styles.heading}>Current Assistant Details</div>
          <div style={styles.list}>
            <div><strong>Name:</strong> {currentAssistant.name}</div>
            <div><strong>ID:</strong> {currentAssistant.id}</div>
            <div><strong>First Message:</strong> {currentAssistant.firstMessage || 'None'}</div>
            <div><strong>Voice:</strong> {currentAssistant.voice?.provider} ({currentAssistant.voice?.voiceId})</div>
          </div>
        </>
      )}
      
      <button style={styles.button} onClick={fetchAssistants}>
        Refresh Assistants
      </button>
    </div>
  );
};

export default VapiDebugger;
