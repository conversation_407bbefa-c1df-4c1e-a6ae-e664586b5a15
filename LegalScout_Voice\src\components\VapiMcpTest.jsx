import { useState, useEffect } from 'react';
import { vapiMcpService } from '../services/vapiMcpService';

/**
 * Test component for Vapi MCP integration
 * This component demonstrates basic functionality of the Vapi MCP service
 */
const VapiMcpTest = () => {
  const [connected, setConnected] = useState(false);
  const [tools, setTools] = useState([]);
  const [assistants, setAssistants] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Connect to Vapi MCP server on component mount
  useEffect(() => {
    const connectToMcp = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Get API key from environment variables
        const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
        
        if (!apiKey) {
          throw new Error('Vapi API key not found in environment variables');
        }
        
        // Connect to MCP server
        const success = await vapiMcpService.connect(apiKey);
        setConnected(success);
        
        if (success) {
          // List available tools
          const availableTools = await vapiMcpService.listTools();
          setTools(availableTools);
          
          // List assistants
          const assistantsList = await vapiMcpService.listAssistants();
          setAssistants(assistantsList);
        }
      } catch (err) {
        console.error('Error in VapiMcpTest:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    connectToMcp();
    
    // Disconnect when component unmounts
    return () => {
      vapiMcpService.disconnect();
    };
  }, []);

  // Create a new call with the first assistant
  const handleCreateCall = async () => {
    if (!connected || assistants.length === 0) {
      setError('Cannot create call: Not connected or no assistants available');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Get the first assistant
      const assistantId = assistants[0].id;
      
      // This is just a test - in a real app, you would get this from user input
      const phoneNumber = '+11234567890'; // Replace with a real phone number for testing
      
      // Create a call
      const call = await vapiMcpService.createCall(assistantId, phoneNumber);
      
      alert(`Call created successfully! Call ID: ${call.id}`);
    } catch (err) {
      console.error('Error creating call:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg shadow-md max-w-2xl mx-auto my-8">
      <h2 className="text-2xl font-bold mb-4">Vapi MCP Integration Test</h2>
      
      <div className="mb-4">
        <p className="font-semibold">Connection Status:</p>
        <p className={`${connected ? 'text-green-600' : 'text-red-600'}`}>
          {connected ? 'Connected to Vapi MCP Server' : 'Not Connected'}
        </p>
      </div>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p className="font-semibold">Error:</p>
          <p>{error}</p>
        </div>
      )}
      
      {loading && (
        <div className="mb-4">
          <p className="text-blue-600">Loading...</p>
        </div>
      )}
      
      {tools.length > 0 && (
        <div className="mb-4">
          <p className="font-semibold">Available Tools:</p>
          <ul className="list-disc pl-5">
            {tools.map((tool, index) => (
              <li key={index}>{tool.name}: {tool.description}</li>
            ))}
          </ul>
        </div>
      )}
      
      {assistants.length > 0 && (
        <div className="mb-4">
          <p className="font-semibold">Available Assistants:</p>
          <ul className="list-disc pl-5">
            {assistants.map((assistant, index) => (
              <li key={index}>{assistant.name} ({assistant.id})</li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="mt-6">
        <button
          onClick={handleCreateCall}
          disabled={!connected || assistants.length === 0 || loading}
          className={`px-4 py-2 rounded ${
            !connected || assistants.length === 0 || loading
              ? 'bg-gray-300 cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
        >
          Create Test Call
        </button>
        <p className="text-sm text-gray-500 mt-2">
          Note: This will create a real call with the first assistant in your list.
          Make sure to use a real phone number in the code before testing.
        </p>
      </div>
    </div>
  );
};

export default VapiMcpTest;
