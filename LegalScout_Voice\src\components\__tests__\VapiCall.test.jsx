import React from 'react';
import { render, screen, act, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import VapiCall from '../VapiCall';
import useVapiCall from '../../hooks/useVapiCall';
import { CALL_STATUS } from '../../constants/vapiConstants';

// Mock the useVapiCall hook
jest.mock('../../hooks/useVapiCall');

// Mock the CallController component
jest.mock('../CallController', () => ({ 
  __esModule: true, 
  default: ({ dossierData, onEndCall }) => (
    <div data-testid="call-controller">
      <button onClick={() => onEndCall(dossierData)}>End Call</button>
      <div data-testid="dossier-data">{JSON.stringify(dossierData)}</div>
    </div>
  )
}));

describe('VapiCall Component', () => {
  const mockOnEndCall = jest.fn();
  
  const mockHookReturn = {
    status: CALL_STATUS.IDLE,
    dossierData: { practiceArea: 'Family Law' },
    volumeLevel: 0,
    assistantIsSpeaking: false,
    errorMessage: null,
    subdomainConfig: { firmName: 'Test Firm' },
    startCall: jest.fn(),
    stopCall: jest.fn(),
    vapi: {}
  };
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Default mock implementation
    useVapiCall.mockReturnValue(mockHookReturn);
  });
  
  test('renders idle state correctly', () => {
    useVapiCall.mockReturnValue({
      ...mockHookReturn,
      status: CALL_STATUS.IDLE
    });
    
    render(<VapiCall onEndCall={mockOnEndCall} subdomain="testfirm" />);
    
    // Check for idle state elements
    expect(screen.getByText('Ready to start call')).toBeInTheDocument();
    expect(screen.getByText('Start Call')).toBeInTheDocument();
    expect(screen.getByText('Firm: Test Firm')).toBeInTheDocument();
  });
  
  test('renders connecting state correctly', () => {
    useVapiCall.mockReturnValue({
      ...mockHookReturn,
      status: CALL_STATUS.CONNECTING
    });
    
    render(<VapiCall onEndCall={mockOnEndCall} />);
    
    // Check for connecting state elements
    expect(screen.getByText('Connecting...')).toBeInTheDocument();
    expect(screen.getAllByClass('dot')).toHaveLength(3);
  });
  
  test('renders connected state with CallController', () => {
    useVapiCall.mockReturnValue({
      ...mockHookReturn,
      status: CALL_STATUS.CONNECTED
    });
    
    render(<VapiCall onEndCall={mockOnEndCall} />);
    
    // Check for CallController
    expect(screen.getByTestId('call-controller')).toBeInTheDocument();
  });
  
  test('renders error state correctly', () => {
    useVapiCall.mockReturnValue({
      ...mockHookReturn,
      status: CALL_STATUS.ERROR,
      errorMessage: 'Test error message'
    });
    
    render(<VapiCall onEndCall={mockOnEndCall} />);
    
    // Check for error state elements
    expect(screen.getByText('Error')).toBeInTheDocument();
    expect(screen.getByText('Test error message')).toBeInTheDocument();
    expect(screen.getByText('Retry Call')).toBeInTheDocument();
  });
  
  test('renders completed state correctly', () => {
    useVapiCall.mockReturnValue({
      ...mockHookReturn,
      status: CALL_STATUS.COMPLETED
    });
    
    render(<VapiCall onEndCall={mockOnEndCall} />);
    
    // Check for completed state elements
    expect(screen.getByText('Call Completed')).toBeInTheDocument();
    expect(screen.getByText('View Results')).toBeInTheDocument();
  });
  
  test('calls startCall when start button is clicked', () => {
    useVapiCall.mockReturnValue({
      ...mockHookReturn,
      status: CALL_STATUS.IDLE
    });
    
    render(<VapiCall onEndCall={mockOnEndCall} />);
    
    // Click start call button
    fireEvent.click(screen.getByText('Start Call'));
    
    // Check if startCall was called
    expect(mockHookReturn.startCall).toHaveBeenCalledTimes(1);
  });
  
  test('calls stopCall and onEndCall when end call button is clicked', async () => {
    useVapiCall.mockReturnValue({
      ...mockHookReturn,
      status: CALL_STATUS.CONNECTED
    });
    
    render(<VapiCall onEndCall={mockOnEndCall} />);
    
    // Click end call button in CallController
    fireEvent.click(screen.getByText('End Call'));
    
    // Check if stopCall and onEndCall were called
    expect(mockHookReturn.stopCall).toHaveBeenCalledTimes(1);
    await waitFor(() => {
      expect(mockOnEndCall).toHaveBeenCalledTimes(1);
      expect(mockOnEndCall).toHaveBeenCalledWith({ practiceArea: 'Family Law' });
    });
  });
  
  test('calls onEndCall when view results button is clicked', () => {
    useVapiCall.mockReturnValue({
      ...mockHookReturn,
      status: CALL_STATUS.COMPLETED
    });
    
    render(<VapiCall onEndCall={mockOnEndCall} />);
    
    // Click view results button
    fireEvent.click(screen.getByText('View Results'));
    
    // Check if onEndCall was called
    expect(mockOnEndCall).toHaveBeenCalledTimes(1);
    expect(mockOnEndCall).toHaveBeenCalledWith({ practiceArea: 'Family Law' });
  });
}); 