.enhanced-call-controller {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: var(--card-background, #ffffff);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* Visualization container */
.visualization-container {
  width: 100%;
  height: 150px;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: var(--visualization-background, rgba(0, 0, 0, 0.02));
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

/* Status container */
.status-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  background-color: var(--status-background, rgba(0, 0, 0, 0.05));
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-indicator.idle .status-dot {
  background-color: var(--idle-color, #9ca3af);
}

.status-indicator.connecting .status-dot {
  background-color: var(--connecting-color, #fbbf24);
  animation: pulse 1s infinite;
}

.status-indicator.active .status-dot {
  background-color: var(--active-color, #10b981);
  animation: pulse 1.5s infinite;
}

.status-indicator.disconnecting .status-dot {
  background-color: var(--disconnecting-color, #f59e0b);
  animation: pulse 1s infinite;
}

.status-indicator.error .status-dot {
  background-color: var(--error-color, #ef4444);
}

.status-text {
  font-size: 0.875rem;
  color: var(--text-secondary, #4b5563);
}

/* Button container */
.button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.call-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  color: white;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.call-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.button-icon {
  margin-right: 0.5rem;
}

/* Transcript container */
.transcript-container {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
  border-radius: 0.5rem;
  background-color: var(--transcript-background, rgba(0, 0, 0, 0.02));
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
  margin-top: 1rem;
}

.transcript-message {
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: var(--message-background, #f9fafb);
  border-left: 3px solid transparent;
}

.transcript-message.assistant {
  border-left-color: var(--assistant-color, #3b82f6);
}

.transcript-message.user {
  border-left-color: var(--user-color, #10b981);
}

.message-role {
  font-weight: 600;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
  color: var(--text-secondary, #4b5563);
}

.message-text {
  font-size: 0.875rem;
  color: var(--text-primary, #1f2937);
  line-height: 1.4;
}

/* Animation */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Scrollbar styling */
.transcript-container::-webkit-scrollbar {
  width: 6px;
}

.transcript-container::-webkit-scrollbar-track {
  background: transparent;
}

.transcript-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .enhanced-call-controller {
    padding: 0.75rem;
  }
  
  .visualization-container {
    height: 120px;
  }
  
  .call-button {
    padding: 0.5rem 1rem;
  }
  
  .transcript-container {
    max-height: 150px;
  }
}
