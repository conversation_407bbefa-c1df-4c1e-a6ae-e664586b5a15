import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useVapi from '../../hooks/use-vapi';
import EnhancedSpeechParticles from '../EnhancedSpeechParticles';
import './EnhancedCallController.css';

/**
 * Enhanced Call Controller Component
 * 
 * This component provides a UI for controlling calls with Vapi,
 * including a call button, status indicators, and speech visualization.
 * It's inspired by Vapi Blocks but styled to match the LegalScout UI.
 */
const EnhancedCallController = ({ 
  assistantId, 
  className,
  showTranscript = true,
  showVisualization = true
}) => {
  const { 
    isSessionActive, 
    conversation, 
    currentSpeaker,
    toggleCall, 
    startCall, 
    stopCall 
  } = useVapi();
  
  const [status, setStatus] = useState('idle');
  const [statusMessage, setStatusMessage] = useState('Ready to start call');
  
  // Update status based on session state
  useEffect(() => {
    if (isSessionActive) {
      setStatus('active');
      setStatusMessage('Call in progress');
    } else {
      setStatus('idle');
      setStatusMessage('Ready to start call');
    }
  }, [isSessionActive]);
  
  // Handle starting a call
  const handleStartCall = async () => {
    try {
      setStatus('connecting');
      setStatusMessage('Connecting...');
      
      if (assistantId) {
        await startCall(assistantId);
      } else {
        await toggleCall();
      }
    } catch (error) {
      console.error('Error starting call:', error);
      setStatus('error');
      setStatusMessage('Error connecting');
    }
  };
  
  // Handle ending a call
  const handleEndCall = async () => {
    try {
      setStatus('disconnecting');
      setStatusMessage('Disconnecting...');
      await stopCall();
    } catch (error) {
      console.error('Error ending call:', error);
    }
  };
  
  // Get the last few messages for the transcript
  const getRecentMessages = () => {
    if (!conversation || conversation.length === 0) return [];
    return conversation.slice(-5); // Show last 5 messages
  };
  
  // Get appropriate button text based on status
  const getButtonText = () => {
    switch (status) {
      case 'connecting':
        return 'Connecting...';
      case 'disconnecting':
        return 'Disconnecting...';
      case 'active':
        return 'End Call';
      case 'error':
        return 'Retry';
      default:
        return 'Start Call';
    }
  };
  
  // Get appropriate button color based on status
  const getButtonColor = () => {
    switch (status) {
      case 'active':
        return 'bg-red-500 hover:bg-red-600';
      case 'error':
        return 'bg-yellow-500 hover:bg-yellow-600';
      default:
        return 'bg-blue-500 hover:bg-blue-600';
    }
  };
  
  // Get appropriate icon based on status
  const getButtonIcon = () => {
    switch (status) {
      case 'active':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 6a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zm0 6a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" />
          </svg>
        );
      case 'connecting':
      case 'disconnecting':
        return (
          <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
          </svg>
        );
    }
  };
  
  return (
    <div className={`enhanced-call-controller ${className || ''}`}>
      {/* Call visualization */}
      {showVisualization && (
        <div className="visualization-container">
          <EnhancedSpeechParticles 
            className={currentSpeaker ? `${currentSpeaker}-speaking` : ''}
          />
        </div>
      )}
      
      {/* Call status */}
      <div className="status-container">
        <div className={`status-indicator ${status}`}>
          <div className="status-dot"></div>
          <span className="status-text">{statusMessage}</span>
        </div>
      </div>
      
      {/* Call button */}
      <div className="button-container">
        <button
          className={`call-button ${getButtonColor()}`}
          onClick={isSessionActive ? handleEndCall : handleStartCall}
          disabled={status === 'connecting' || status === 'disconnecting'}
        >
          <span className="button-icon">{getButtonIcon()}</span>
          <span className="button-text">{getButtonText()}</span>
        </button>
      </div>
      
      {/* Transcript */}
      {showTranscript && (
        <AnimatePresence>
          <div className="transcript-container">
            {getRecentMessages().map((message, index) => (
              <motion.div
                key={index}
                className={`transcript-message ${message.role}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="message-role">
                  {message.role === 'assistant' ? 'Assistant' : 'You'}
                </div>
                <div className="message-text">{message.text}</div>
              </motion.div>
            ))}
          </div>
        </AnimatePresence>
      )}
    </div>
  );
};

export default EnhancedCallController;
