.json-editor {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.editor-toolbar button {
  padding: 6px 12px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-right: 8px;
}

.editor-toolbar button:hover {
  background-color: #f0f0f0;
}

.editor-toolbar button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.json-error {
  margin-left: auto;
  color: #c62828;
  font-size: 12px;
}

.json-textarea {
  width: 100%;
  padding: 12px;
  border: none;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  background-color: #fafafa;
}

.json-textarea.has-error {
  background-color: #fff8f8;
  border-color: #ffcdd2;
}

.json-textarea:focus {
  outline: none;
  background-color: #fff;
}
