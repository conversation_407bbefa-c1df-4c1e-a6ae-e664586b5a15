import React, { useState, useEffect } from 'react';
import './JsonEditor.css';

/**
 * JSON Editor Component
 * 
 * A simple JSON editor for editing structured data schemas
 */
const JsonEditor = ({ value, onChange, height = '300px', disabled = false }) => {
  const [jsonText, setJsonText] = useState('');
  const [error, setError] = useState(null);
  
  // Initialize editor with value
  useEffect(() => {
    if (value) {
      try {
        const formatted = JSON.stringify(value, null, 2);
        setJsonText(formatted);
        setError(null);
      } catch (err) {
        setJsonText(JSON.stringify(value));
        setError('Error formatting JSON');
      }
    } else {
      setJsonText('');
    }
  }, [value]);
  
  // Handle text changes
  const handleTextChange = (e) => {
    const newText = e.target.value;
    setJsonText(newText);
    
    try {
      // Try to parse the JSON
      const parsedJson = JSON.parse(newText);
      setError(null);
      
      // Call onChange with parsed JSON
      if (onChange) {
        onChange(parsedJson);
      }
    } catch (err) {
      setError('Invalid JSON: ' + err.message);
    }
  };
  
  // Format the JSON
  const handleFormat = () => {
    try {
      const parsedJson = JSON.parse(jsonText);
      const formatted = JSON.stringify(parsedJson, null, 2);
      setJsonText(formatted);
      setError(null);
    } catch (err) {
      setError('Invalid JSON: ' + err.message);
    }
  };
  
  return (
    <div className="json-editor">
      <div className="editor-toolbar">
        <button
          type="button"
          onClick={handleFormat}
          disabled={disabled}
          title="Format JSON"
        >
          Format
        </button>
        
        {error && <div className="json-error">{error}</div>}
      </div>
      
      <textarea
        className={`json-textarea ${error ? 'has-error' : ''}`}
        value={jsonText}
        onChange={handleTextChange}
        style={{ height }}
        disabled={disabled}
        spellCheck="false"
      />
    </div>
  );
};

export default JsonEditor;
