import React from 'react';
import { FaGavel } from 'react-icons/fa';
import './PracticeAreaSelector.css';

/**
 * Practice Area Selector Component
 * Provides a dropdown for selecting legal practice areas with AI CRM template application
 */
const PracticeAreaSelector = ({ 
  value, 
  onChange, 
  onApplyTemplate, 
  showApplyButton = true,
  className = '',
  disabled = false,
  label = 'Legal Practice Area'
}) => {
  const practiceAreas = [
    { value: '', label: 'Select a practice area' },
    { value: 'General Purpose', label: 'General Purpose Legal CRM' },
    { value: 'Personal Injury', label: 'Personal Injury' },
    { value: 'Family Law', label: 'Family Law' },
    { value: 'Estate Planning', label: 'Estate Planning' },
    { value: 'Criminal Defense', label: 'Criminal Defense' },
    { value: 'Business Law', label: 'Business Law' },
    { value: 'Immigration', label: 'Immigration' },
    { value: 'Intellectual Property', label: 'Intellectual Property' },
    { value: 'Real Estate', label: 'Real Estate' },
    { value: 'Employment Law', label: 'Employment Law' }
  ];

  const handleChange = (e) => {
    const selectedValue = e.target.value;
    onChange(selectedValue);
  };

  const handleApplyTemplate = () => {
    if (value && onApplyTemplate) {
      onApplyTemplate(value);
    }
  };

  return (
    <div className={`practice-area-selector ${className}`}>
      <div className="selector-group">
        <label htmlFor="practiceAreaSelect" className="selector-label">
          <FaGavel className="label-icon" />
          {label}
        </label>
        
        <div className="selector-controls">
          <select
            id="practiceAreaSelect"
            value={value}
            onChange={handleChange}
            disabled={disabled}
            className="practice-area-select"
          >
            {practiceAreas.map(area => (
              <option key={area.value} value={area.value}>
                {area.label}
              </option>
            ))}
          </select>
          
          {showApplyButton && (
            <button
              type="button"
              className="apply-template-btn"
              onClick={handleApplyTemplate}
              disabled={!value || disabled}
              title="Load the recommended AI CRM configuration for this practice area"
            >
              Apply AI CRM Template
            </button>
          )}
        </div>
      </div>
      
      {value && (
        <div className="practice-area-info">
          <p className="info-text">
            <strong>{practiceAreas.find(area => area.value === value)?.label}</strong> selected.
            {showApplyButton && ' Click "Apply AI CRM Template" to load recommended custom fields and prompts.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default PracticeAreaSelector;
