import React, { useState } from 'react';
import './CrmViews.css';

const ConsultationMapView = () => {
  const [activeMarker, setActiveMarker] = useState('philadelphia');
  
  return (
    <div className="crm-map-view">
      <div className="map-container">
        {/* This would be replaced with an actual map component like Google Maps or Mapbox */}
        <div className="map-placeholder">
          <div className="map-background"></div>
          <div className="map-overlay">
            <div 
              className={`map-marker ${activeMarker === 'philadelphia' ? 'active' : ''}`} 
              style={{ top: '40%', left: '30%' }} 
              data-count="3"
              onClick={() => setActiveMarker('philadelphia')}
            >
              {activeMarker === 'philadelphia' && (
                <div className="marker-popup">
                  <div className="popup-header">
                    <h3>Philadelphia Area</h3>
                    <span>3 consultations</span>
                  </div>
                  <div className="popup-list">
                    <div className="popup-item">
                      <div className="popup-client"><PERSON></div>
                      <div className="popup-detail">Personal Injury • Apr 21</div>
                      <div className="popup-actions">
                        <button className="popup-action-btn">
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                          </svg>
                        </button>
                        <button className="popup-action-btn">
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                          </svg>
                        </button>
                      </div>
                    </div>
                    <div className="popup-item">
                      <div className="popup-client">Michael Johnson</div>
                      <div className="popup-detail">Business Law • Apr 19</div>
                      <div className="popup-actions">
                        <button className="popup-action-btn">
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                          </svg>
                        </button>
                        <button className="popup-action-btn">
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                          </svg>
                        </button>
                      </div>
                    </div>
                    <div className="popup-item">
                      <div className="popup-client">Sarah Williams</div>
                      <div className="popup-detail">Estate Planning • Apr 15</div>
                      <div className="popup-actions">
                        <button className="popup-action-btn">
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                          </svg>
                        </button>
                        <button className="popup-action-btn">
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            <div 
              className={`map-marker ${activeMarker === 'pittsburgh' ? 'active' : ''}`} 
              style={{ top: '60%', left: '70%' }} 
              data-count="1"
              onClick={() => setActiveMarker('pittsburgh')}
            >
              {activeMarker === 'pittsburgh' && (
                <div className="marker-popup">
                  <div className="popup-header">
                    <h3>Pittsburgh Area</h3>
                    <span>1 consultation</span>
                  </div>
                  <div className="popup-list">
                    <div className="popup-item">
                      <div className="popup-client">Jane Smith</div>
                      <div className="popup-detail">Divorce • Apr 20</div>
                      <div className="popup-actions">
                        <button className="popup-action-btn">
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                          </svg>
                        </button>
                        <button className="popup-action-btn">
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <div className="map-sidebar">
        <div className="sidebar-header">
          <h3>Consultations by Location</h3>
          <select className="time-filter">
            <option value="all">All Time</option>
            <option value="month">Last 30 Days</option>
            <option value="week">Last 7 Days</option>
          </select>
        </div>
        
        <div className="location-list">
          <div 
            className={`location-item ${activeMarker === 'philadelphia' ? 'active' : ''}`}
            onClick={() => setActiveMarker('philadelphia')}
          >
            <div className="location-name">Philadelphia, PA</div>
            <div className="location-count">3 consultations</div>
          </div>
          <div 
            className={`location-item ${activeMarker === 'pittsburgh' ? 'active' : ''}`}
            onClick={() => setActiveMarker('pittsburgh')}
          >
            <div className="location-name">Pittsburgh, PA</div>
            <div className="location-count">1 consultation</div>
          </div>
          <div className="location-item">
            <div className="location-name">Harrisburg, PA</div>
            <div className="location-count">0 consultations</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsultationMapView;
