import React from 'react';
import './CrmViews.css';

const ConsultationTableView = () => {
  return (
    <div className="crm-table-view">
      <table className="consultation-table">
        <thead>
          <tr>
            <th>Client</th>
            <th>Date & Time</th>
            <th>Duration</th>
            <th>Topic</th>
            <th>Status</th>
            <th>Location</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr className="consultation-row">
            <td className="client-cell">
              <div className="client-info">
                <div className="client-avatar">JD</div>
                <div className="client-details">
                  <div className="client-name">John <PERSON></div>
                  <div className="client-email"><EMAIL></div>
                </div>
              </div>
            </td>
            <td>Apr 21, 2025 • 2:30 PM</td>
            <td>12 min</td>
            <td>Personal Injury</td>
            <td><span className="status-badge new">New Lead</span></td>
            <td>Philadelphia, PA</td>
            <td className="actions-cell">
              <div className="row-actions">
                <button className="action-button view-btn" title="View Details">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                </button>
                <button className="action-button schedule-btn" title="Schedule Meeting">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                </button>
                <button className="action-button scout-btn" title="Send Scout">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                  </svg>
                </button>
                <button className="action-button more-btn" title="More Options">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="1"></circle>
                    <circle cx="12" cy="5" r="1"></circle>
                    <circle cx="12" cy="19" r="1"></circle>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
          
          <tr className="consultation-row">
            <td className="client-cell">
              <div className="client-info">
                <div className="client-avatar">JS</div>
                <div className="client-details">
                  <div className="client-name">Jane Smith</div>
                  <div className="client-email"><EMAIL></div>
                </div>
              </div>
            </td>
            <td>Apr 20, 2025 • 10:15 AM</td>
            <td>18 min</td>
            <td>Divorce Consultation</td>
            <td><span className="status-badge follow-up">Follow-up</span></td>
            <td>Pittsburgh, PA</td>
            <td className="actions-cell">
              <div className="row-actions">
                <button className="action-button view-btn" title="View Details">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                </button>
                <button className="action-button schedule-btn" title="Schedule Meeting">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                </button>
                <button className="action-button scout-btn" title="Send Scout">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                  </svg>
                </button>
                <button className="action-button more-btn" title="More Options">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="1"></circle>
                    <circle cx="12" cy="5" r="1"></circle>
                    <circle cx="12" cy="19" r="1"></circle>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
          
          <tr className="consultation-row">
            <td className="client-cell">
              <div className="client-info">
                <div className="client-avatar">MJ</div>
                <div className="client-details">
                  <div className="client-name">Michael Johnson</div>
                  <div className="client-email"><EMAIL></div>
                </div>
              </div>
            </td>
            <td>Apr 19, 2025 • 3:45 PM</td>
            <td>22 min</td>
            <td>Business Law</td>
            <td><span className="status-badge completed">Completed</span></td>
            <td>Philadelphia, PA</td>
            <td className="actions-cell">
              <div className="row-actions">
                <button className="action-button view-btn" title="View Details">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                </button>
                <button className="action-button schedule-btn" title="Schedule Meeting">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                </button>
                <button className="action-button scout-btn" title="Send Scout">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                  </svg>
                </button>
                <button className="action-button more-btn" title="More Options">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="1"></circle>
                    <circle cx="12" cy="5" r="1"></circle>
                    <circle cx="12" cy="19" r="1"></circle>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      
      <div className="table-pagination">
        <button className="pagination-btn" disabled>Previous</button>
        <div className="pagination-info">Showing 1-10 of 24</div>
        <button className="pagination-btn">Next</button>
      </div>
    </div>
  );
};

export default ConsultationTableView;
