/* CRM Panel Styles */
.crm-panel {
  grid-column: 2;
  background-color: #f5f7fa;
  height: calc(100vh - 64px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-width: 100%;
}

/* View Toggle Styles */
.crm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.view-toggle {
  display: flex;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: var(--radius-medium);
  padding: 0.25rem;
}

.view-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 0.875rem;
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-default);
}

.view-toggle-btn.active {
  background-color: white;
  color: var(--primary-color);
  box-shadow: var(--shadow-soft);
}

/* Filters */
.crm-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background-color: white;
  color: var(--text-primary);
}

.filter-select {
  width: 200px;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background-color: white;
  color: var(--text-primary);
}

/* Table View Styles */
.crm-table-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1.5rem;
  overflow: auto;
}

.consultation-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: white;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-soft);
  overflow: hidden;
}

.consultation-table thead {
  background-color: rgba(0, 0, 0, 0.02);
}

.consultation-table th {
  padding: 1rem;
  text-align: left;
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
}

.consultation-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.consultation-row {
  transition: var(--transition-default);
}

.consultation-row:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.client-cell {
  width: 250px;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.client-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
}

.client-details {
  display: flex;
  flex-direction: column;
}

.client-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.client-email {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
}

.status-badge.new {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.status-badge.follow-up {
  background-color: rgba(230, 126, 34, 0.1);
  color: #e67e22;
}

.status-badge.completed {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.actions-cell {
  width: 150px;
}

.row-actions {
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: var(--transition-default);
}

.consultation-row:hover .row-actions {
  opacity: 1;
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-default);
}

.action-button:hover {
  background-color: var(--primary-color);
  color: white;
}

.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  margin-top: 1rem;
}

.pagination-btn {
  padding: 0.5rem 1rem;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition-default);
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Card View Styles */
.crm-card-view {
  padding: 1.5rem;
  overflow-y: auto;
  height: 100%;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.consultation-card {
  background-color: white;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-soft);
  overflow: hidden;
  transition: var(--transition-default);
}

.consultation-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.card-header {
  padding: 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid var(--border-color);
}

.card-body {
  padding: 1.25rem;
}

.consultation-detail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.detail-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.detail-value {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.consultation-summary {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.summary-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.summary-text {
  color: var(--text-primary);
  line-height: 1.5;
  font-size: 0.9375rem;
}

.card-footer {
  padding: 1rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.01);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 0.75rem;
}

.card-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: none;
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
  border-radius: var(--radius-small);
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition-default);
}

.card-action-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.card-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  margin-top: 1rem;
}

/* Map View Styles */
.crm-map-view {
  display: grid;
  grid-template-columns: 1fr 300px;
  height: 100%;
}

.map-container {
  position: relative;
  overflow: hidden;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #f0f0f0;
}

.map-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://maps.googleapis.com/maps/api/staticmap?center=Pennsylvania,USA&zoom=7&size=800x800&scale=2&maptype=roadmap&style=feature:administrative|element:geometry|visibility:off&style=feature:administrative.land_parcel|visibility:off&style=feature:administrative.neighborhood|visibility:off&style=feature:poi|visibility:off&style=feature:road|element:labels.icon|visibility:off&style=feature:transit|visibility:off&key=YOUR_API_KEY');
  background-size: cover;
  background-position: center;
  opacity: 0.7;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.map-marker {
  position: absolute;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: var(--transition-default);
  z-index: 1;
}

.map-marker::after {
  content: attr(data-count);
}

.map-marker:hover {
  transform: scale(1.1);
}

.map-marker.active {
  background-color: var(--accent-color);
  z-index: 2;
}

.marker-popup {
  position: absolute;
  top: 45px;
  left: -150px;
  width: 300px;
  background-color: white;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-medium);
  z-index: 3;
}

.marker-popup::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 150px;
  width: 16px;
  height: 16px;
  background-color: white;
  transform: rotate(45deg);
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05);
}

.popup-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.popup-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.popup-header span {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
}

.popup-list {
  max-height: 300px;
  overflow-y: auto;
}

.popup-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.popup-item:last-child {
  border-bottom: none;
}

.popup-client {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.popup-detail {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.popup-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.popup-action-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-default);
}

.popup-action-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.map-sidebar {
  background-color: white;
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
}

.sidebar-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.time-filter {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background-color: white;
  color: var(--text-primary);
}

.location-list {
  padding: 0.5rem 0;
}

.location-item {
  padding: 0.75rem 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: var(--transition-default);
}

.location-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.location-item.active {
  background-color: rgba(var(--primary-color-rgb), 0.05);
  border-left: 3px solid var(--primary-color);
}

.location-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.location-count {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
}

/* Dark theme styles */
[data-theme="dark"] .crm-panel {
  background-color: #121212;
}

[data-theme="dark"] .consultation-table {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .consultation-table thead {
  background-color: rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .consultation-table th,
[data-theme="dark"] .consultation-table td {
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .consultation-row:hover {
  background-color: rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .client-name {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .client-email {
  color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .action-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .consultation-card {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .card-header,
[data-theme="dark"] .card-footer {
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .card-footer {
  background-color: rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .detail-label,
[data-theme="dark"] .summary-label {
  color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .detail-value,
[data-theme="dark"] .summary-text {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .card-action-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .map-sidebar {
  background-color: #121212;
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .marker-popup {
  background-color: #1e1e1e;
}

[data-theme="dark"] .marker-popup::before {
  background-color: #1e1e1e;
}

[data-theme="dark"] .popup-header {
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .popup-header h3 {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .popup-item {
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .popup-client {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .popup-detail {
  color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .popup-action-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .crm-map-view {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 300px;
  }

  .card-grid {
    grid-template-columns: 1fr;
  }
}
