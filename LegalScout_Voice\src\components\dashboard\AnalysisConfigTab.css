.analysis-config-tab {
  padding: 20px;
}

.analysis-config-tab h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.8rem;
}

.analysis-config-tab .description {
  margin-bottom: 25px;
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

.config-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #444;
  font-size: 1.4rem;
}

.section-description {
  margin-bottom: 20px;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-family: monospace;
  resize: vertical;
}

.error-message {
  padding: 12px;
  margin-bottom: 20px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  border-left: 4px solid #c62828;
}

.success-message {
  padding: 12px;
  margin-bottom: 20px;
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: 4px;
  border-left: 4px solid #2e7d32;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.save-button,
.reset-button {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-button {
  background-color: #4B74AA;
  color: white;
  margin-left: 10px;
}

.reset-button {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.save-button:hover {
  background-color: #3A5F8A;
}

.reset-button:hover {
  background-color: #e0e0e0;
}

.save-button:disabled,
.reset-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
