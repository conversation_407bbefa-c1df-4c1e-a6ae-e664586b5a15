import React, { useState, useEffect } from 'react';
import { useAttorneyState } from '../../contexts/AttorneyStateContext';
import { vapiAssistantService } from '../../services/vapiAssistantService';
import TemplateManager from './TemplateManager';
import <PERSON>sonEditor from '../common/JsonEditor';
import {
  DEFAULT_STRUCTURED_DATA_SCHEMA,
  DEFAULT_SUCCESS_EVALUATION_PROMPT,
  DEFAULT_SUMMARY_PROMPT,
  DEFAULT_STRUCTURED_DATA_PROMPT
} from '../../config/defaultTemplates';
import './AnalysisConfigTab.css';

/**
 * Analysis Configuration Tab
 *
 * Allows attorneys to configure analysis settings for their Vapi assistant
 */
const AnalysisConfigTab = () => {
  const { attorney, updateAttorney, saveAttorney } = useAttorneyState();

  // Form state
  const [formData, setFormData] = useState({
    success_evaluation_prompt: '',
    summary_prompt: '',
    structured_data_prompt: '',
    structured_data_schema: null
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [assistant, setAssistant] = useState(null);

  // Initialize form data from attorney
  useEffect(() => {
    if (attorney) {
      setFormData({
        success_evaluation_prompt: attorney.success_evaluation_prompt || DEFAULT_SUCCESS_EVALUATION_PROMPT,
        summary_prompt: attorney.summary_prompt || DEFAULT_SUMMARY_PROMPT,
        structured_data_prompt: attorney.structured_data_prompt || DEFAULT_STRUCTURED_DATA_PROMPT,
        structured_data_schema: attorney.structured_data_schema || DEFAULT_STRUCTURED_DATA_SCHEMA
      });

      // Load assistant data
      loadAssistantData();
    }
  }, [attorney]);

  // Load assistant data from Vapi
  const loadAssistantData = async () => {
    if (!attorney?.vapi_assistant_id) {
      console.log('[AnalysisConfigTab] No assistant ID found for attorney:', attorney);
      return;
    }

    try {
      setLoading(true);
      console.log('[AnalysisConfigTab] Loading assistant data for ID:', attorney.vapi_assistant_id);
      const assistantData = await vapiAssistantService.getAssistant(attorney.vapi_assistant_id);
      console.log('[AnalysisConfigTab] Loaded assistant data:', assistantData);
      setAssistant(assistantData);

      // Update form with assistant data if available - prioritize Vapi data over Supabase
      if (assistantData?.analysis) {
        console.log('[AnalysisConfigTab] Found analysis data in assistant:', assistantData.analysis);
        const vapiAnalysisData = {
          success_evaluation_prompt: assistantData.analysis?.success?.prompt ||
                                    attorney.success_evaluation_prompt ||
                                    DEFAULT_SUCCESS_EVALUATION_PROMPT,
          summary_prompt: assistantData.analysis?.summary?.prompt ||
                         attorney.summary_prompt ||
                         DEFAULT_SUMMARY_PROMPT,
          structured_data_prompt: assistantData.analysis?.structuredData?.prompt ||
                                 attorney.structured_data_prompt ||
                                 DEFAULT_STRUCTURED_DATA_PROMPT,
          structured_data_schema: assistantData.analysis?.structuredData?.schema ||
                                 attorney.structured_data_schema ||
                                 DEFAULT_STRUCTURED_DATA_SCHEMA
        };

        setFormData(prevData => ({
          ...prevData,
          ...vapiAnalysisData
        }));

        // If Vapi has data that Supabase doesn't, sync it back to Supabase
        const needsSync = (
          (assistantData.analysis?.success?.prompt && !attorney.success_evaluation_prompt) ||
          (assistantData.analysis?.summary?.prompt && !attorney.summary_prompt) ||
          (assistantData.analysis?.structuredData?.prompt && !attorney.structured_data_prompt) ||
          (assistantData.analysis?.structuredData?.schema && !attorney.structured_data_schema)
        );

        if (needsSync) {
          console.log('Syncing analysis data from Vapi to Supabase');
          updateAttorney({
            ...attorney,
            ...vapiAnalysisData
          });
        }
      }
    } catch (error) {
      console.error('Error loading assistant data:', error);
      setError('Failed to load assistant data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: value
    }));
  };

  // Handle schema changes from JSON editor
  const handleSchemaChange = (schema) => {
    setFormData(prevData => ({
      ...prevData,
      structured_data_schema: schema
    }));
  };

  // Save changes
  const handleSaveChanges = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Update attorney with form data
      const updatedAttorney = {
        ...attorney,
        success_evaluation_prompt: formData.success_evaluation_prompt,
        summary_prompt: formData.summary_prompt,
        structured_data_prompt: formData.structured_data_prompt,
        structured_data_schema: formData.structured_data_schema
      };

      // Save to database
      await saveAttorney(updatedAttorney);

      // Update assistant if available
      if (attorney.vapi_assistant_id) {
        await vapiAssistantService.updateAssistantConfiguration(
          attorney.vapi_assistant_id,
          updatedAttorney
        );
      }

      setSuccess('Analysis configuration saved successfully');
    } catch (error) {
      console.error('Error saving analysis configuration:', error);

      // Check if it's a missing column error
      if (error.message?.includes('success_evaluation_prompt') ||
          error.message?.includes('summary_prompt') ||
          error.message?.includes('structured_data_prompt')) {
        setError('Database schema needs to be updated. Please run the migration: fix_analysis_columns.sql');
      } else {
        setError('Failed to save analysis configuration. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle template application
  const handleApplyTemplate = async (result) => {
    if (!result || !result.attorney) return;

    // Update form data with template values
    setFormData({
      success_evaluation_prompt: result.attorney.success_evaluation_prompt || DEFAULT_SUCCESS_EVALUATION_PROMPT,
      summary_prompt: result.attorney.summary_prompt || DEFAULT_SUMMARY_PROMPT,
      structured_data_prompt: result.attorney.structured_data_prompt || DEFAULT_STRUCTURED_DATA_PROMPT,
      structured_data_schema: result.attorney.structured_data_schema || DEFAULT_STRUCTURED_DATA_SCHEMA
    });

    // Update attorney state
    updateAttorney(result.attorney);

    // Show success message
    setSuccess('Template applied successfully');
  };

  // Reset to defaults
  const handleResetToDefaults = () => {
    if (!confirm('Are you sure you want to reset all fields to default values?')) {
      return;
    }

    setFormData({
      success_evaluation_prompt: DEFAULT_SUCCESS_EVALUATION_PROMPT,
      summary_prompt: DEFAULT_SUMMARY_PROMPT,
      structured_data_prompt: DEFAULT_STRUCTURED_DATA_PROMPT,
      structured_data_schema: DEFAULT_STRUCTURED_DATA_SCHEMA
    });
  };

  return (
    <div className="analysis-config-tab">
      <h2>Analysis Configuration</h2>
      <p className="description">
        Configure how your AI assistant analyzes conversations with potential clients.
        These settings control how the assistant evaluates success, generates summaries,
        and extracts structured data from conversations.
      </p>

      {/* Error and Success Messages */}
      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      {/* Template Manager */}
      <TemplateManager
        onApplyTemplate={handleApplyTemplate}
        onSaveSuccess={() => setSuccess('Template saved successfully')}
      />

      {/* Success Evaluation Configuration */}
      <div className="config-section">
        <h3>Success Evaluation</h3>
        <p className="section-description">
          Define how the AI assistant determines if a call was successful.
          This prompt guides the AI in evaluating whether key information was collected
          and client needs were addressed.
        </p>

        <div className="form-group">
          <label htmlFor="success_evaluation_prompt">Success Evaluation Prompt</label>
          <textarea
            id="success_evaluation_prompt"
            name="success_evaluation_prompt"
            value={formData.success_evaluation_prompt}
            onChange={handleInputChange}
            rows={10}
            placeholder="Enter success evaluation prompt..."
            disabled={loading}
          />
        </div>
      </div>

      {/* End of Call Report Configuration */}
      <div className="config-section">
        <h3>End of Call Report</h3>
        <p className="section-description">
          Configure how the AI assistant generates a summary report after each call.
          This report helps you quickly understand what was discussed and what actions are needed.
        </p>

        <div className="form-group">
          <label htmlFor="summary_prompt">Summary Prompt</label>
          <textarea
            id="summary_prompt"
            name="summary_prompt"
            value={formData.summary_prompt}
            onChange={handleInputChange}
            rows={10}
            placeholder="Enter summary prompt..."
            disabled={loading}
          />
        </div>
      </div>

      {/* Structured Data Configuration */}
      <div className="config-section">
        <h3>Structured Data Extraction</h3>
        <p className="section-description">
          Define how the AI assistant extracts structured data from conversations.
          This data can be used to automatically populate your CRM or case management system.
        </p>

        <div className="form-group">
          <label htmlFor="structured_data_prompt">Structured Data Prompt</label>
          <textarea
            id="structured_data_prompt"
            name="structured_data_prompt"
            value={formData.structured_data_prompt}
            onChange={handleInputChange}
            rows={6}
            placeholder="Enter structured data prompt..."
            disabled={loading}
          />
        </div>

        <div className="form-group">
          <label htmlFor="structured_data_schema">Structured Data Schema (JSON)</label>
          <JsonEditor
            value={formData.structured_data_schema}
            onChange={handleSchemaChange}
            height="400px"
            disabled={loading}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="action-buttons">
        <button
          className="reset-button"
          onClick={handleResetToDefaults}
          disabled={loading}
        >
          Reset to Defaults
        </button>

        <button
          className="save-button"
          onClick={handleSaveChanges}
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  );
};

export default AnalysisConfigTab;
