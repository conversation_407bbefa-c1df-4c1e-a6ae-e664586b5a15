import React, { useState, useEffect } from 'react';
import { FaRobot, FaTools, FaPlus, FaSync } from 'react-icons/fa';
import { supabase } from '../../lib/supabase';
import VoiceAssistantDiagnostics from './VoiceAssistantDiagnostics';

// Generate UUID function
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

/**
 * AssistantInfoSection Component
 *
 * Displays assistant information with name and ID, includes diagnostics toggle,
 * and provides create assistant functionality.
 */
const AssistantInfoSection = ({
  attorney,
  showDiagnostics,
  setShowDiagnostics,
  onCreateAssistant,
  setLoading,
  setError,
  setSuccess,
  onUpdate
}) => {
  const [assistantData, setAssistantData] = useState(null);
  const [loadingAssistant, setLoadingAssistant] = useState(false);

  // Load assistant data when component mounts or assistant ID changes
  useEffect(() => {
    if (attorney?.vapi_assistant_id && attorney.vapi_assistant_id !== 'null' && attorney.vapi_assistant_id !== 'undefined') {
      loadAssistantData();
    }
  }, [attorney?.vapi_assistant_id]);

  const loadAssistantData = async () => {
    if (!attorney?.vapi_assistant_id) return;

    setLoadingAssistant(true);
    try {
      // Import the Vapi service dynamically
      const { vapiMcpService } = await import('../../services/vapiMcpService');

      // Try to get assistant data
      const assistant = await vapiMcpService.getAssistant(attorney.vapi_assistant_id);

      if (assistant) {
        setAssistantData(assistant);

        // Check multiple possible fields for instructions
        const instructions = assistant.instructions ||
                           assistant.systemPrompt ||
                           assistant.model?.systemPrompt ||
                           assistant.llm?.systemPrompt ||
                           assistant.prompt;

        // Update the attorney's vapi_instructions with the data from Vapi
        if (instructions && onUpdate) {
          onUpdate({
            vapi_instructions: instructions,
            welcome_message: assistant.firstMessage || attorney.welcome_message
          });
        }
      }
    } catch (error) {
      console.error('[AssistantInfoSection] Error loading assistant data:', error);
      // Don't show error to user, just log it
    } finally {
      setLoadingAssistant(false);
    }
  };

  const handleCreateAssistant = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get the attorney from localStorage if not available
      let currentAttorney = attorney;
      if (!currentAttorney || !currentAttorney.id) {
        try {
          console.log('[AssistantInfoSection] Attorney object is missing or has no ID, checking localStorage');
          const storedAttorney = localStorage.getItem('attorney');
          if (storedAttorney) {
            currentAttorney = JSON.parse(storedAttorney);
            console.log('[AssistantInfoSection] Retrieved attorney from localStorage:', currentAttorney);
          }
        } catch (error) {
          console.error('[AssistantInfoSection] Error parsing attorney from localStorage:', error);
        }
      }

      // If still no valid attorney, create a development one
      if (!currentAttorney || (!currentAttorney.id && !currentAttorney.subdomain && !currentAttorney.email)) {
        currentAttorney = {
          id: generateUUID(), // Generate a valid UUID
          subdomain: 'dev-attorney',
          firm_name: 'Development Law Firm',
          name: 'Dev Attorney',
          email: '<EMAIL>',
          user_id: generateUUID(), // Generate a valid UUID
        };

        // Save to localStorage
        localStorage.setItem('attorney', JSON.stringify(currentAttorney));
      }

      // Import the Vapi assistant service dynamically
      const { vapiAssistantService } = await import('../../services/vapiAssistantService');

      // Create a new assistant
      const assistant = await vapiAssistantService.createAssistantForAttorney(currentAttorney);

      if (assistant && assistant.id) {

        // Update the attorney object with the new assistant ID
        const updatedAttorney = { ...currentAttorney, vapi_assistant_id: assistant.id };

        // Check if the attorney ID is a valid UUID
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        const isValidUuid = uuidRegex.test(currentAttorney.id);

        if (isValidUuid) {
          try {
            // Update the attorney in Supabase
            const { error } = await supabase
              .from('attorneys')
              .update({ vapi_assistant_id: assistant.id })
              .eq('id', currentAttorney.id);

            if (error) {
              console.error('[AssistantInfoSection] Error updating attorney in Supabase:', error);
              // Don't throw error, just log it and continue
            }
          } catch (updateError) {
            console.error('[AssistantInfoSection] Exception updating attorney in Supabase:', updateError);
            // Don't throw error, just log it and continue
          }
        }

        // Update localStorage
        localStorage.setItem('attorney', JSON.stringify(updatedAttorney));

        // Update the local attorney state
        onUpdate({ vapi_assistant_id: assistant.id });

        // Load the new assistant data
        setAssistantData(assistant);

        // Show success message
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        throw new Error('Failed to create assistant - no assistant ID returned');
      }
    } catch (error) {
      console.error('[AssistantInfoSection] Error creating new assistant:', error);
      setError('Error creating new assistant: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to check if assistant ID is valid (not null, undefined, or mock)
  const isValidAssistantId = (assistantId) => {
    return assistantId &&
           assistantId !== 'null' &&
           assistantId !== 'undefined' &&
           !assistantId.startsWith('mock-');
  };

  // If no valid assistant ID, show create assistant option
  if (!isValidAssistantId(attorney?.vapi_assistant_id)) {
    const isMockId = attorney?.vapi_assistant_id && attorney.vapi_assistant_id.startsWith('mock-');

    return (
      <div className="assistant-info-section">
        <div className="assistant-info">
          <div className="alert alert-warning">
            <span>
              {isMockId
                ? 'Voice assistant needs to be properly configured. Mock assistant detected.'
                : 'No assistant configured. Click below to create one.'}
            </span>
          </div>
          <button
            type="button"
            className="create-assistant-button"
            onClick={handleCreateAssistant}
          >
            <FaPlus /> {isMockId ? 'Fix Assistant' : 'Create Assistant'}
          </button>
        </div>
      </div>
    );
  }

  // Show assistant info with name and ID
  return (
    <div className="assistant-info-section">
      <div className="assistant-info">
        <div className="alert alert-info">
          <div className="assistant-display">
            <div className="assistant-main-info">
              <FaRobot className="assistant-icon" />
              <div className="assistant-details">
                <div className="assistant-name">
                  {loadingAssistant ? 'Loading...' : (assistantData?.name || 'Assistant')}
                </div>
                <div className="assistant-id">
                  ID: {attorney.vapi_assistant_id}
                </div>
              </div>
            </div>
            <div className="assistant-actions">
              <button
                type="button"
                className="diagnostics-toggle-button"
                onClick={() => setShowDiagnostics(!showDiagnostics)}
                title={showDiagnostics ? 'Hide Diagnostics' : 'Show Diagnostics'}
              >
                <FaTools />
              </button>
              <button
                type="button"
                className="refresh-assistant-button"
                onClick={loadAssistantData}
                title="Refresh Assistant Data"
              >
                <FaSync />
              </button>
            </div>
          </div>
        </div>

        <button
          type="button"
          className="create-assistant-button secondary"
          onClick={handleCreateAssistant}
        >
          <FaPlus /> Create New Assistant
        </button>
      </div>

      {/* Diagnostics Section */}
      {showDiagnostics && (
        <div className="diagnostics-section">
          <VoiceAssistantDiagnostics attorney={attorney} />
        </div>
      )}
    </div>
  );
};

export default AssistantInfoSection;
