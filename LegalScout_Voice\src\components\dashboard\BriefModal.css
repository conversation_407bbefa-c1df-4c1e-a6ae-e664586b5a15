/* Brief Modal Styles - Modern, Clean Layout */

.brief-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 1rem;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.brief-modal {
  background: var(--card-bg);
  border-radius: 24px;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px var(--border-color);
  max-width: 1200px;
  width: 95%;
  max-height: 95vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Header */
.brief-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem 2rem 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.brief-title-section {
  flex: 1;
}

.brief-title {
  font-size: 1.875rem;
  font-weight: 800;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.025em;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

.brief-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 600;
  opacity: 0.95;
  line-height: 1.4;
}

.brief-actions {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
  flex-wrap: wrap;
  min-width: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  position: relative;
  min-height: 44px;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 140px;
  justify-content: center;
}

.action-btn.primary {
  background: linear-gradient(135deg, #87ceeb 0%, #b0e0e6 100%);
  color: #2c3e50;
  box-shadow: 0 2px 4px rgba(135, 206, 235, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(135, 206, 235, 0.4);
}

.action-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.action-btn.secondary:hover {
  background: var(--card-hover);
  transform: translateY(-1px);
}

.action-btn.tertiary {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.action-btn.tertiary:hover {
  background: var(--card-hover);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: var(--card-hover);
  color: var(--text-primary);
  transform: scale(1.1);
}

/* Content */
.brief-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 2rem;
}

.brief-section {
  margin-bottom: 2.5rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
}

.brief-section:last-child {
  border-bottom: none;
  margin-bottom: 1rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.375rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--border-color);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-icon {
  background: linear-gradient(135deg, #f5f5dc 0%, #87ceeb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.1rem;
}

.section-icon.success {
  background: linear-gradient(135deg, #90ee90 0%, #32cd32 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-content {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item label {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.8;
}

.info-item value {
  font-size: 1.1rem;
  color: var(--text-primary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contact-link {
  color: #4682b4;
  text-decoration: none;
  transition: color 0.2s ease;
  font-weight: 500;
}

.contact-link:hover {
  color: #87ceeb;
  text-decoration: underline;
}

.practice-area-badge {
  background: #e8f5e8;
  color: #155724;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.location-icon {
  color: #6c757d;
}

/* Summary Content */
.summary-content {
  background: var(--bg-secondary);
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #87ceeb;
  font-size: 1rem;
  line-height: 1.7;
  color: var(--text-primary);
}

/* Evaluation Content */
.evaluation-content {
  background: var(--bg-secondary);
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #90ee90;
  font-size: 1rem;
  line-height: 1.7;
  color: var(--text-primary);
}

/* Custom Fields Grid */
.custom-fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.25rem;
}

.custom-field-item {
  background: var(--bg-secondary);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.custom-field-item label {
  display: block;
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.custom-field-item value {
  display: block;
  color: var(--text-primary);
  font-weight: 600;
}

.field-type-boolean {
  font-weight: 600;
}

/* Structured Data Grid */
.structured-data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.structured-data-item {
  background: #fff3cd;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #ffeaa7;
}

.structured-data-item label {
  display: block;
  font-weight: 600;
  color: #856404;
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
}

.structured-data-item value {
  display: block;
  color: #856404;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  white-space: pre-wrap;
}

/* Metadata Grid */
.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.metadata-item label {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.8;
}

.metadata-item value {
  color: var(--text-primary);
  font-weight: 600;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.new {
  background: #cce5ff;
  color: #004085;
}

.status-badge.in-progress {
  background: #fff3cd;
  color: #856404;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.follow-up {
  background: #f8d7da;
  color: #721c24;
}

/* Footer */
.brief-footer {
  padding: 2rem;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.footer-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  min-height: 44px;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 120px;
  justify-content: center;
}

.footer-btn.primary {
  background: linear-gradient(135deg, #87ceeb 0%, #b0e0e6 100%);
  color: #2c3e50;
  box-shadow: 0 2px 4px rgba(135, 206, 235, 0.3);
}

.footer-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(135, 206, 235, 0.4);
}

.footer-btn.secondary {
  background: var(--card-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.footer-btn.secondary:hover {
  background: var(--card-hover);
  transform: translateY(-1px);
}

/* Workflow Actions in Footer */
.workflow-actions {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.workflow-section {
  margin-bottom: 1.5rem;
}

.workflow-section:last-child {
  margin-bottom: 0;
}

.workflow-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.workflow-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.workflow-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
}

/* Pre-engagement workflow buttons - increasing blue gradient */
.workflow-btn.pre-engagement.qualify {
  background: linear-gradient(135deg, #dbeafe 0%, #93c5fd 100%);
  color: #1e40af;
  border: 1px solid #3b82f6;
}

.workflow-btn.pre-engagement.intake {
  background: linear-gradient(135deg, #bfdbfe 0%, #60a5fa 100%);
  color: #1d4ed8;
  border: 1px solid #2563eb;
}

.workflow-btn.pre-engagement.conflict-check {
  background: linear-gradient(135deg, #93c5fd 0%, #3b82f6 100%);
  color: white;
  border: 1px solid #1d4ed8;
}

.workflow-btn.pre-engagement.collect-info {
  background: linear-gradient(135deg, #60a5fa 0%, #2563eb 100%);
  color: white;
  border: 1px solid #1e40af;
}

.workflow-btn.pre-engagement.refer {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: 1px solid #1e40af;
}

/* Client engagement workflow buttons - increasing orange/tan gradient */
.workflow-btn.client-engagement.draft {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
  color: #9a3412;
  border: 1px solid #ea580c;
}

.workflow-btn.client-engagement.review {
  background: linear-gradient(135deg, #fdba74 0%, #fb923c 100%);
  color: #9a3412;
  border: 1px solid #dc2626;
}

.workflow-btn.client-engagement.research {
  background: linear-gradient(135deg, #fb923c 0%, #f97316 100%);
  color: white;
  border: 1px solid #c2410c;
}

.workflow-btn.client-engagement.file {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  color: white;
  border: 1px solid #9a3412;
}

.workflow-btn.client-engagement.forms {
  background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
  color: white;
  border: 1px solid #991b1b;
}

.workflow-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .brief-modal-overlay {
    padding: 0.5rem;
    align-items: flex-start;
    padding-top: 2rem;
  }

  .brief-modal {
    max-height: calc(100vh - 2rem);
    border-radius: 20px 20px 0 0;
    width: 98%;
  }

  .brief-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    padding: 1.5rem;
  }

  .brief-content {
    padding: 0 1.5rem;
  }

  .brief-actions {
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .action-btn {
    flex: 1;
    justify-content: center;
    min-width: 120px;
    font-size: 0.85rem;
    padding: 0.65rem 1rem;
  }

  .info-grid,
  .custom-fields-grid,
  .structured-data-grid,
  .metadata-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .footer-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .footer-btn {
    justify-content: center;
  }

  .brief-footer {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .brief-modal-overlay {
    padding: 0;
  }

  .brief-modal {
    border-radius: 0;
    max-height: 100vh;
  }

  .brief-header,
  .brief-content,
  .brief-footer {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .action-btn,
  .footer-btn {
    padding: 1rem;
    font-size: 0.875rem;
  }
}
