/* CSV Import Modal Styles */
.csv-import-modal {
  max-width: 800px;
  width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
}

.csv-import-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.csv-import-modal .modal-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1f2937;
  font-size: 1.25rem;
}

.csv-import-modal .close-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.csv-import-modal .close-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.csv-import-modal .modal-body {
  padding: 2rem;
}

/* Upload Step */
.upload-step .upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 3rem 2rem;
  text-align: center;
  margin-bottom: 2rem;
  transition: all 0.2s;
  cursor: pointer;
}

.upload-step .upload-area:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.upload-step .upload-content .upload-icon {
  font-size: 3rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.upload-step .upload-content h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
}

.upload-step .upload-content p {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
}

.upload-step .upload-content small {
  color: #9ca3af;
}

.sample-format {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

.sample-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.sample-format h4 {
  margin: 0;
  color: #1f2937;
}

.download-template-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.download-template-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.sample-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.sample-table th,
.sample-table td {
  padding: 0.5rem;
  text-align: left;
  border: 1px solid #d1d5db;
}

.sample-table th {
  background: #f3f4f6;
  font-weight: 600;
  color: #374151;
}

.sample-table td {
  color: #6b7280;
}

/* Mapping Step */
.mapping-step h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.mapping-step p {
  margin: 0 0 2rem 0;
  color: #6b7280;
}

.mapping-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.mapping-row {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}

.csv-header {
  text-align: center;
}

.csv-header strong {
  display: block;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.csv-header small {
  color: #6b7280;
  font-size: 0.75rem;
}

.arrow {
  font-size: 1.25rem;
  color: #6b7280;
  font-weight: bold;
}

.db-field select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
  color: #1f2937;
  font-size: 0.875rem;
}

.db-field select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.required-note {
  color: #6b7280;
  font-style: italic;
}

/* Preview Step */
.preview-step h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.preview-step p {
  margin: 0 0 2rem 0;
  color: #6b7280;
}

.preview-table {
  overflow-x: auto;
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}

.preview-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.preview-table th,
.preview-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.preview-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.preview-table td {
  color: #6b7280;
}

.import-summary {
  background: #dbeafe;
  border: 1px solid #93c5fd;
  border-radius: 0.5rem;
  padding: 1rem;
  text-align: center;
}

.import-summary p {
  margin: 0;
  color: #1e40af;
  font-weight: 500;
}

/* Results Step */
.results-step {
  text-align: center;
  padding: 2rem 0;
}

.success-icon {
  width: 4rem;
  height: 4rem;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem auto;
  color: white;
  font-size: 2rem;
}

.results-step h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.5rem;
}

.results-step p {
  margin: 0;
  color: #6b7280;
  font-size: 1.125rem;
}

/* Loading Message */
.loading-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 0.5rem;
  color: #0369a1;
  margin-top: 1rem;
  justify-content: center;
}

.loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #bae6fd;
  border-top: 2px solid #0369a1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  color: #dc2626;
  margin-top: 1rem;
}

/* Modal Footer */
.csv-import-modal .modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.csv-import-modal .modal-footer button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.cancel-button,
.back-button {
  background: white;
  color: #6b7280;
  border-color: #d1d5db;
}

.cancel-button:hover,
.back-button:hover {
  background: #f9fafb;
  color: #374151;
}

.next-button,
.import-button,
.complete-button {
  background: #3b82f6;
  color: white;
}

.next-button:hover,
.import-button:hover,
.complete-button:hover {
  background: #2563eb;
}

.import-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Import button in consultations table */
.import-csv-button {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.import-csv-button:hover {
  background: #059669;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .csv-import-modal {
    width: 95vw;
    margin: 1rem;
  }

  .mapping-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    text-align: center;
  }

  .arrow {
    transform: rotate(90deg);
  }

  .csv-import-modal .modal-footer {
    flex-direction: column;
  }

  .csv-import-modal .modal-footer button {
    width: 100%;
  }
}
