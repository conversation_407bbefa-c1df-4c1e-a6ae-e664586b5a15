import React, { useState, useRef } from 'react';
import { supabase } from '../../lib/supabase';
import { FaUpload, FaTimes, FaCheck, FaExclamationTriangle, FaFileImport, FaDownload } from 'react-icons/fa';
import <PERSON> from 'papaparse';
import * as XLSX from 'xlsx';
import './CSVImportModal.css';

/**
 * CSVImportModal component for importing consultation data from CSV/Excel files
 */
const CSVImportModal = ({ isOpen, onClose, attorney, onImportComplete }) => {
  const [file, setFile] = useState(null);
  const [csvData, setCsvData] = useState([]);
  const [headers, setHeaders] = useState([]);
  const [mapping, setMapping] = useState({});
  const [step, setStep] = useState(1); // 1: Upload, 2: Map, 3: Preview, 4: Import
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [importResults, setImportResults] = useState(null);
  const fileInputRef = useRef(null);

  // Available database fields for mapping
  const availableFields = [
    { key: 'client_name', label: 'Client Name', required: true },
    { key: 'client_email', label: 'Client Email', required: false },
    { key: 'client_phone', label: 'Client Phone', required: false },
    { key: 'summary', label: 'Summary', required: false },
    { key: 'practice_area', label: 'Practice Area', required: false },
    { key: 'location', label: 'Location', required: false },
    { key: 'status', label: 'Status', required: false },
    { key: 'transcript', label: 'Transcript', required: false },
    { key: 'duration', label: 'Duration (seconds)', required: false },
  ];

  // Handle file selection
  const handleFileSelect = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      setFile(selectedFile);
      setError(null);
      parseFile(selectedFile);
    }
  };

  // Parse CSV/Excel file
  const parseFile = async (file) => {
    try {
      setLoading(true);
      const fileName = file.name.toLowerCase();

      if (fileName.endsWith('.csv')) {
        // Parse CSV file
        Papa.parse(file, {
          header: true,
          skipEmptyLines: true,
          complete: (results) => {
            if (results.errors.length > 0) {
              setError(`Error parsing CSV file: ${results.errors[0].message}`);
              setLoading(false);
              return;
            }

            if (results.data.length === 0) {
              setError('CSV file is empty or contains no valid data');
              setLoading(false);
              return;
            }

            const headers = Object.keys(results.data[0]);
            setHeaders(headers);
            setCsvData(results.data);
            setStep(2);
            setLoading(false);
          },
          error: (error) => {
            setError(`Error parsing CSV file: ${error.message}`);
            setLoading(false);
          }
        });
      } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        // Parse Excel file
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            if (jsonData.length === 0) {
              setError('Excel file is empty or contains no valid data');
              setLoading(false);
              return;
            }

            const headers = Object.keys(jsonData[0]);
            setHeaders(headers);
            setCsvData(jsonData);
            setStep(2);
            setLoading(false);
          } catch (error) {
            setError(`Error parsing Excel file: ${error.message}`);
            setLoading(false);
          }
        };
        reader.onerror = () => {
          setError('Error reading Excel file');
          setLoading(false);
        };
        reader.readAsArrayBuffer(file);
      } else {
        setError('Unsupported file format. Please use CSV, XLS, or XLSX files.');
        setLoading(false);
      }
    } catch (error) {
      setError(`Error parsing file: ${error.message}`);
      setLoading(false);
    }
  };

  // Handle field mapping
  const handleMappingChange = (csvHeader, dbField) => {
    setMapping(prev => ({
      ...prev,
      [csvHeader]: dbField
    }));
  };

  // Validate mapping
  const validateMapping = () => {
    const requiredFields = availableFields.filter(f => f.required);
    const mappedFields = Object.values(mapping);

    for (const field of requiredFields) {
      if (!mappedFields.includes(field.key)) {
        return `Required field "${field.label}" is not mapped`;
      }
    }
    return null;
  };

  // Preview mapped data
  const previewData = () => {
    const validationError = validateMapping();
    if (validationError) {
      setError(validationError);
      return;
    }
    setError(null);
    setStep(3);
  };

  // Transform CSV data to database format
  const transformData = (row) => {
    const transformed = {
      attorney_id: attorney.id,
      created_at: new Date().toISOString(),
      status: 'imported'
    };

    Object.entries(mapping).forEach(([csvHeader, dbField]) => {
      if (dbField && row[csvHeader] !== undefined && row[csvHeader] !== null) {
        let value = row[csvHeader];

        // Clean up the value
        if (typeof value === 'string') {
          value = value.trim();
        }

        // Handle specific field transformations
        if (dbField === 'duration' && value) {
          // Convert duration to seconds if it's in minutes or other format
          const numValue = parseFloat(value);
          if (!isNaN(numValue)) {
            transformed[dbField] = Math.round(numValue);
          }
        } else if (value !== '') {
          transformed[dbField] = value;
        }
      }
    });

    return transformed;
  };

  // Import data to database
  const handleImport = async () => {
    try {
      setLoading(true);
      setError(null);

      const transformedData = csvData.map(transformData);

      const { data, error } = await supabase
        .from('consultations')
        .insert(transformedData)
        .select();

      if (error) throw error;

      setImportResults({
        success: true,
        imported: data.length,
        total: csvData.length
      });
      setStep(4);
    } catch (error) {
      setError(`Import failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Reset modal state
  const resetModal = () => {
    setFile(null);
    setCsvData([]);
    setHeaders([]);
    setMapping({});
    setStep(1);
    setError(null);
    setImportResults(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Close modal
  const handleClose = () => {
    resetModal();
    onClose();
  };

  // Complete import
  const handleComplete = () => {
    resetModal();
    onImportComplete();
  };

  // Download sample template
  const downloadTemplate = () => {
    const link = document.createElement('a');
    link.href = '/sample-consultations.csv';
    link.download = 'sample-consultations.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content csv-import-modal">
        <div className="modal-header">
          <h2>
            <FaFileImport /> Import Consultations from CSV/Excel
          </h2>
          <button className="close-button" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        <div className="modal-body">
          {/* Step 1: File Upload */}
          {step === 1 && (
            <div className="upload-step">
              <div className="upload-area">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  onChange={handleFileSelect}
                  style={{ display: 'none' }}
                />
                <div className="upload-content" onClick={() => fileInputRef.current?.click()}>
                  <FaUpload className="upload-icon" />
                  <h3>Choose CSV or Excel file</h3>
                  <p>Click to browse or drag and drop your file here</p>
                  <small>Supported formats: .csv, .xlsx, .xls</small>
                </div>
              </div>

              <div className="sample-format">
                <div className="sample-header">
                  <h4>Expected Format:</h4>
                  <button className="download-template-button" onClick={downloadTemplate}>
                    <FaDownload /> Download Sample Template
                  </button>
                </div>
                <div className="sample-table">
                  <table>
                    <thead>
                      <tr>
                        <th>Client Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Summary</th>
                        <th>Practice Area</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>John Doe</td>
                        <td><EMAIL></td>
                        <td>(*************</td>
                        <td>Personal injury case</td>
                        <td>Personal Injury</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Field Mapping */}
          {step === 2 && (
            <div className="mapping-step">
              <h3>Map CSV columns to database fields</h3>
              <p>Match your CSV headers with the appropriate database fields:</p>

              <div className="mapping-grid">
                {headers.map(header => (
                  <div key={header} className="mapping-row">
                    <div className="csv-header">
                      <strong>{header}</strong>
                      <small>CSV Column</small>
                    </div>
                    <div className="arrow">→</div>
                    <div className="db-field">
                      <select
                        value={mapping[header] || ''}
                        onChange={(e) => handleMappingChange(header, e.target.value)}
                      >
                        <option value="">Skip this column</option>
                        {availableFields.map(field => (
                          <option key={field.key} value={field.key}>
                            {field.label} {field.required ? '*' : ''}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                ))}
              </div>

              <div className="required-note">
                <small>* Required fields must be mapped</small>
              </div>
            </div>
          )}

          {/* Step 3: Preview */}
          {step === 3 && (
            <div className="preview-step">
              <h3>Preview Import Data</h3>
              <p>Review the first few rows before importing:</p>

              <div className="preview-table">
                <table>
                  <thead>
                    <tr>
                      {Object.values(mapping).filter(Boolean).map(field => (
                        <th key={field}>
                          {availableFields.find(f => f.key === field)?.label}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {csvData.slice(0, 3).map((row, index) => (
                      <tr key={index}>
                        {Object.entries(mapping).filter(([_, dbField]) => dbField).map(([csvHeader, dbField]) => (
                          <td key={dbField}>{row[csvHeader]}</td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="import-summary">
                <p><strong>Ready to import {csvData.length} consultations</strong></p>
              </div>
            </div>
          )}

          {/* Step 4: Results */}
          {step === 4 && importResults && (
            <div className="results-step">
              <div className="success-icon">
                <FaCheck />
              </div>
              <h3>Import Complete!</h3>
              <p>Successfully imported {importResults.imported} of {importResults.total} consultations.</p>
            </div>
          )}

          {/* Loading Display */}
          {loading && (
            <div className="loading-message">
              <div className="loading-spinner"></div>
              <span>
                {step === 1 ? 'Parsing file...' :
                 step === 3 ? 'Importing data...' : 'Processing...'}
              </span>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="error-message">
              <FaExclamationTriangle />
              <span>{error}</span>
            </div>
          )}
        </div>

        <div className="modal-footer">
          {step === 1 && (
            <button className="cancel-button" onClick={handleClose}>
              Cancel
            </button>
          )}

          {step === 2 && (
            <>
              <button className="back-button" onClick={() => setStep(1)}>
                Back
              </button>
              <button className="next-button" onClick={previewData}>
                Preview Data
              </button>
            </>
          )}

          {step === 3 && (
            <>
              <button className="back-button" onClick={() => setStep(2)}>
                Back
              </button>
              <button
                className="import-button"
                onClick={handleImport}
                disabled={loading}
              >
                {loading ? 'Importing...' : 'Import Data'}
              </button>
            </>
          )}

          {step === 4 && (
            <button className="complete-button" onClick={handleComplete}>
              Done
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CSVImportModal;
