.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.call-details-modal {
  background-color: var(--modal-background, #ffffff);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color, #eeeeee);
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  color: var(--text-primary, #333333);
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary, #666666);
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: var(--hover-background, #f5f5f5);
  color: var(--text-primary, #333333);
}

.modal-tabs {
  display: flex;
  overflow-x: auto;
  border-bottom: 1px solid var(--border-color, #eeeeee);
}

.tab-button {
  background: none;
  border: none;
  padding: 15px 20px;
  font-size: 14px;
  color: var(--text-secondary, #666666);
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.tab-button:hover {
  background-color: var(--hover-background, #f5f5f5);
}

.tab-button.active {
  color: var(--primary-color, #4a90e2);
  border-bottom: 2px solid var(--primary-color, #4a90e2);
  font-weight: 600;
}

.modal-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.loading,
.empty-state {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary, #666666);
  font-style: italic;
}

.error-message {
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 4px solid #ff0000;
  padding: 10px 15px;
  margin-bottom: 20px;
  color: #d32f2f;
  border-radius: 4px;
}

/* Overview styles */
.overview-section {
  margin-bottom: 30px;
}

.overview-section h3 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--text-primary, #333333);
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.overview-item {
  padding: 10px;
  background-color: var(--item-background, #f9f9f9);
  border-radius: 6px;
}

.item-label {
  font-size: 12px;
  color: var(--text-secondary, #666666);
  margin-bottom: 5px;
}

.item-value {
  font-size: 14px;
  color: var(--text-primary, #333333);
  word-break: break-all;
}

.summary {
  padding: 15px;
  background-color: var(--item-background, #f9f9f9);
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary, #333333);
}

/* Transcript styles */
.transcripts,
.messages {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.transcript-item,
.message-item {
  padding: 15px;
  border-radius: 8px;
  background-color: var(--item-background, #f9f9f9);
}

.transcript-item.assistant,
.message-item.assistant {
  background-color: var(--assistant-background, rgba(74, 144, 226, 0.1));
  border-left: 3px solid var(--primary-color, #4a90e2);
}

.transcript-item.user,
.message-item.user {
  background-color: var(--user-background, rgba(76, 175, 80, 0.1));
  border-left: 3px solid var(--success-color, #4caf50);
}

.transcript-header,
.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.transcript-role,
.message-role {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary, #333333);
}

.transcript-time,
.message-time {
  font-size: 12px;
  color: var(--text-secondary, #666666);
}

.transcript-text,
.message-content {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary, #333333);
  white-space: pre-wrap;
}

/* Tool execution styles */
.tool-executions {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tool-execution-item {
  padding: 15px;
  border-radius: 8px;
  background-color: var(--item-background, #f9f9f9);
  border-left: 3px solid var(--tool-color, #9c27b0);
}

.tool-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.tool-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary, #333333);
}

.tool-time {
  font-size: 12px;
  color: var(--text-secondary, #666666);
}

.tool-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.tool-input,
.tool-output {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.tool-input h4,
.tool-output h4 {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary, #666666);
}

.tool-input pre,
.tool-output pre,
.metadata pre {
  background-color: var(--code-background, #f5f5f5);
  padding: 10px;
  border-radius: 4px;
  font-size: 13px;
  overflow-x: auto;
  margin: 0;
  color: var(--code-color, #333333);
  max-height: 300px;
  overflow-y: auto;
}

/* Status badge styles */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.status-badge.green {
  background-color: rgba(76, 175, 80, 0.2);
  color: #2e7d32;
}

.status-badge.red {
  background-color: rgba(244, 67, 54, 0.2);
  color: #c62828;
}

.status-badge.blue {
  background-color: rgba(33, 150, 243, 0.2);
  color: #1565c0;
}

.status-badge.orange {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ef6c00;
}

.status-badge.purple {
  background-color: rgba(156, 39, 176, 0.2);
  color: #7b1fa2;
}

.status-badge.gray {
  background-color: rgba(158, 158, 158, 0.2);
  color: #616161;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .call-details-modal {
    width: 95%;
    max-height: 95vh;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-button {
    padding: 12px 15px;
    font-size: 13px;
  }
}
