import React, { useState, useEffect } from 'react';
import { callHistoryService } from '../../services/callHistoryService';
import './CallDetailsModal.css';

/**
 * Call Details Modal Component
 * 
 * Displays detailed information about a call, including transcripts and tool executions.
 * 
 * @param {Object} props
 * @param {string} props.callId - The call ID to display details for
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Callback when the modal is closed
 */
const CallDetailsModal = ({ callId, isOpen, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [callDetails, setCallDetails] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch call details
  useEffect(() => {
    const fetchCallDetails = async () => {
      if (!callId || !isOpen) return;

      try {
        setLoading(true);
        const details = await callHistoryService.getCallDetails(callId);
        setCallDetails(details);
        setError(null);
      } catch (error) {
        console.error('Error fetching call details:', error);
        setError('Failed to load call details. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchCallDetails();
  }, [callId, isOpen]);

  // Handle close
  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Render status badge
  const renderStatusBadge = (status) => {
    if (!status) return null;
    
    const formattedStatus = callHistoryService.formatStatus(status);
    return (
      <span className={`status-badge ${formattedStatus.color}`}>
        {formattedStatus.label}
      </span>
    );
  };

  // Render call duration
  const renderDuration = (seconds) => {
    if (!seconds) return '0:00';
    return callHistoryService.formatDuration(seconds);
  };

  // Render date and time
  const renderDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
  };

  // Render transcripts
  const renderTranscripts = () => {
    if (!callDetails || !callDetails.transcripts || !Array.isArray(callDetails.transcripts) || callDetails.transcripts.length === 0) {
      return <div className="empty-state">No transcripts available.</div>;
    }

    return (
      <div className="transcripts">
        {callDetails.transcripts.map((transcript, index) => (
          <div key={index} className={`transcript-item ${transcript.role}`}>
            <div className="transcript-header">
              <span className="transcript-role">
                {transcript.role === 'assistant' ? 'Assistant' : 'Client'}
              </span>
              <span className="transcript-time">
                {renderDateTime(transcript.timestamp)}
              </span>
            </div>
            <div className="transcript-text">{transcript.text}</div>
          </div>
        ))}
      </div>
    );
  };

  // Render messages
  const renderMessages = () => {
    if (!callDetails || !callDetails.messages || !Array.isArray(callDetails.messages) || callDetails.messages.length === 0) {
      return <div className="empty-state">No messages available.</div>;
    }

    return (
      <div className="messages">
        {callDetails.messages.map((message, index) => (
          <div key={index} className={`message-item ${message.role}`}>
            <div className="message-header">
              <span className="message-role">
                {message.role === 'assistant' ? 'Assistant' : 'Client'}
              </span>
              <span className="message-time">
                {renderDateTime(message.timestamp)}
              </span>
            </div>
            <div className="message-content">{message.content}</div>
          </div>
        ))}
      </div>
    );
  };

  // Render tool executions
  const renderToolExecutions = () => {
    if (!callDetails || !callDetails.tool_executions || !Array.isArray(callDetails.tool_executions) || callDetails.tool_executions.length === 0) {
      return <div className="empty-state">No tool executions available.</div>;
    }

    return (
      <div className="tool-executions">
        {callDetails.tool_executions.map((tool, index) => (
          <div key={index} className="tool-execution-item">
            <div className="tool-header">
              <span className="tool-name">{tool.tool_name || 'Unknown Tool'}</span>
              <span className="tool-time">{renderDateTime(tool.timestamp)}</span>
            </div>
            <div className="tool-details">
              <div className="tool-input">
                <h4>Input:</h4>
                <pre>{typeof tool.input === 'string' ? tool.input : JSON.stringify(tool.input, null, 2)}</pre>
              </div>
              {tool.output && (
                <div className="tool-output">
                  <h4>Output:</h4>
                  <pre>{typeof tool.output === 'string' ? tool.output : JSON.stringify(tool.output, null, 2)}</pre>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render metadata
  const renderMetadata = () => {
    if (!callDetails || !callDetails.metadata) {
      return <div className="empty-state">No metadata available.</div>;
    }

    return (
      <div className="metadata">
        <pre>{JSON.stringify(callDetails.metadata, null, 2)}</pre>
      </div>
    );
  };

  // Render overview
  const renderOverview = () => {
    if (!callDetails) return null;

    return (
      <div className="overview">
        <div className="overview-section">
          <h3>Call Information</h3>
          <div className="overview-grid">
            <div className="overview-item">
              <div className="item-label">Call ID</div>
              <div className="item-value">{callDetails.call_id}</div>
            </div>
            <div className="overview-item">
              <div className="item-label">Status</div>
              <div className="item-value">{renderStatusBadge(callDetails.status)}</div>
            </div>
            <div className="overview-item">
              <div className="item-label">Duration</div>
              <div className="item-value">{renderDuration(callDetails.duration)}</div>
            </div>
            <div className="overview-item">
              <div className="item-label">Start Time</div>
              <div className="item-value">{renderDateTime(callDetails.start_time)}</div>
            </div>
            <div className="overview-item">
              <div className="item-label">End Time</div>
              <div className="item-value">{renderDateTime(callDetails.end_time)}</div>
            </div>
            <div className="overview-item">
              <div className="item-label">Customer Phone</div>
              <div className="item-value">{callDetails.customer_phone || 'Unknown'}</div>
            </div>
            <div className="overview-item">
              <div className="item-label">Assistant ID</div>
              <div className="item-value">{callDetails.assistant_id}</div>
            </div>
          </div>
        </div>

        <div className="overview-section">
          <h3>Summary</h3>
          <div className="summary">
            {callDetails.metadata?.summary || 'No summary available.'}
          </div>
        </div>
      </div>
    );
  };

  // Render active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'transcripts':
        return renderTranscripts();
      case 'messages':
        return renderMessages();
      case 'tools':
        return renderToolExecutions();
      case 'metadata':
        return renderMetadata();
      default:
        return renderOverview();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="call-details-modal">
        <div className="modal-header">
          <h2>Call Details</h2>
          <button className="close-button" onClick={handleClose}>×</button>
        </div>

        {loading ? (
          <div className="loading">Loading call details...</div>
        ) : error ? (
          <div className="error-message">{error}</div>
        ) : (
          <>
            <div className="modal-tabs">
              <button
                className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
                onClick={() => handleTabChange('overview')}
              >
                Overview
              </button>
              <button
                className={`tab-button ${activeTab === 'transcripts' ? 'active' : ''}`}
                onClick={() => handleTabChange('transcripts')}
              >
                Transcripts
              </button>
              <button
                className={`tab-button ${activeTab === 'messages' ? 'active' : ''}`}
                onClick={() => handleTabChange('messages')}
              >
                Messages
              </button>
              <button
                className={`tab-button ${activeTab === 'tools' ? 'active' : ''}`}
                onClick={() => handleTabChange('tools')}
              >
                Tool Executions
              </button>
              <button
                className={`tab-button ${activeTab === 'metadata' ? 'active' : ''}`}
                onClick={() => handleTabChange('metadata')}
              >
                Metadata
              </button>
            </div>

            <div className="modal-content">
              {renderTabContent()}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CallDetailsModal;
