/* Call Forwarding Rules Styles */
.call-forwarding-rules {
  width: 100%;
}

.loading-indicator {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary, #6c757d);
}

.error-message {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: #dc3545;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.retry-button {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.retry-button:hover {
  background-color: #c82333;
}

.rules-section {
  margin-top: 1rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  background-color: var(--background-light, #f8f9fa);
  border-radius: 8px;
  color: var(--text-secondary, #6c757d);
}

.empty-state p {
  margin: 0.5rem 0;
}

.rules-list {
  margin-bottom: 1.5rem;
}

.rule-item {
  background-color: var(--card-background, #ffffff);
  border: 1px solid var(--border-color, #e9ecef);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.rule-info h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary, #212529);
  font-size: 1.1rem;
}

.rule-info p {
  margin: 0.25rem 0;
  color: var(--text-secondary, #6c757d);
  font-size: 0.9rem;
}

.condition-details {
  font-size: 0.85rem;
  color: var(--text-muted, #6c757d);
}

.rule-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.condition-badge {
  background-color: var(--primary-color, #007bff);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.delete-rule-button {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  line-height: 1;
}

.delete-rule-button:hover {
  background-color: #c82333;
}

.add-rule-button {
  background-color: var(--primary-color, #007bff);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.add-rule-button:hover {
  background-color: var(--primary-color-dark, #0056b3);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--card-background, #ffffff);
  border-radius: 8px;
  padding: 0;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e9ecef);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-primary, #212529);
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary, #6c757d);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: var(--text-primary, #212529);
}

.modal-content form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary, #212529);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color, #ced4da);
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: var(--input-background, #ffffff);
  color: var(--text-primary, #212529);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color, #007bff);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.condition-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.radio-option,
.checkbox-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.radio-option input,
.checkbox-option input {
  width: auto;
  margin: 0;
}

.condition-details {
  background-color: var(--background-light, #f8f9fa);
  border: 1px solid var(--border-color, #e9ecef);
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
}

.condition-details h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary, #212529);
  font-size: 1rem;
}

.days-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.5rem;
}

.time-range {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.keyword-input input {
  margin-bottom: 0.5rem;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.keyword-tag {
  background-color: var(--primary-color, #007bff);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.remove-keyword {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  line-height: 1;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-keyword:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color, #e9ecef);
}

.cancel-button,
.save-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: var(--secondary-color, #6c757d);
  color: white;
}

.cancel-button:hover {
  background-color: var(--secondary-color-dark, #5a6268);
}

.save-button {
  background-color: var(--primary-color, #007bff);
  color: white;
}

.save-button:hover {
  background-color: var(--primary-color-dark, #0056b3);
}

.save-button:disabled,
.cancel-button:disabled {
  background-color: var(--disabled-color, #6c757d);
  cursor: not-allowed;
}

.success-message {
  background-color: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.2);
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: #28a745;
}

/* Dark theme support */
[data-theme="dark"] .call-forwarding-rules {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .empty-state {
  background-color: var(--dark-background-light, #2c2c2c);
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .rule-item {
  background-color: var(--dark-card-background, #1e1e1e);
  border-color: var(--dark-border-color, #444);
}

[data-theme="dark"] .rule-info h4 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .rule-info p {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .modal-content {
  background-color: var(--dark-card-background, #1e1e1e);
}

[data-theme="dark"] .modal-header {
  border-bottom-color: var(--dark-border-color, #444);
}

[data-theme="dark"] .modal-header h3 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .form-group label {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group select,
[data-theme="dark"] .form-group textarea {
  background-color: var(--dark-input-background, #2c2c2c);
  border-color: var(--dark-border-color, #444);
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .condition-details {
  background-color: var(--dark-background-light, #2c2c2c);
  border-color: var(--dark-border-color, #444);
}

[data-theme="dark"] .condition-details h4 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .modal-actions {
  border-top-color: var(--dark-border-color, #444);
}
