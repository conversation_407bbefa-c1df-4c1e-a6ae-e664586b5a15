import React, { useState, useEffect } from 'react';
import { FaPlus, FaSync } from 'react-icons/fa';
import { supabase } from '../../lib/supabase';
import './CallForwardingRules.css';

/**
 * CallForwardingRules component for managing call forwarding rules
 * Extracted from ToolsTab to be used in the Calls section
 */
const CallForwardingRules = ({ attorney, onUpdate }) => {
  // State for forwarding rules
  const [forwardingRules, setForwardingRules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForwardingRuleModal, setShowForwardingRuleModal] = useState(false);
  const [forwardingRuleForm, setForwardingRuleForm] = useState({
    name: '',
    forward_to_name: '',
    forward_to_phone: '',
    condition_type: 'always', // 'always', 'time-based', 'keyword-based'
    days: [],
    start_time: '09:00',
    end_time: '17:00',
    timezone: 'EST',
    keywords: []
  });
  const [timeConditionEnabled, setTimeConditionEnabled] = useState(false);
  const [keywordConditionEnabled, setKeywordConditionEnabled] = useState(false);
  const [formError, setFormError] = useState('');
  const [formSuccess, setFormSuccess] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load forwarding rules on component mount
  useEffect(() => {
    if (attorney?.id) {
      fetchForwardingRules();
    }
  }, [attorney]);

  // Fetch forwarding rules from Supabase
  const fetchForwardingRules = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('forwarding_rules')
        .select('*')
        .eq('attorney_id', attorney.id);

      if (error) throw error;

      setForwardingRules(data || []);
    } catch (err) {
      console.error('Error fetching forwarding rules:', err);
      setError('Failed to load forwarding rules');
    } finally {
      setLoading(false);
    }
  };

  // Handle adding a new forwarding rule
  const handleAddForwardingRule = () => {
    // Reset form state
    setForwardingRuleForm({
      name: '',
      forward_to_name: '',
      forward_to_phone: '',
      condition_type: 'always',
      days: [],
      start_time: '09:00',
      end_time: '17:00',
      timezone: 'EST',
      keywords: []
    });
    setTimeConditionEnabled(false);
    setKeywordConditionEnabled(false);
    setFormError('');
    setFormSuccess('');
    setIsSubmitting(false);

    // Show modal
    setShowForwardingRuleModal(true);
  };

  // Handle form input changes
  const handleForwardingFormChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      if (name === 'days') {
        // Handle days array
        setForwardingRuleForm(prev => {
          const days = [...prev.days];
          if (checked) {
            days.push(value);
          } else {
            const index = days.indexOf(value);
            if (index !== -1) {
              days.splice(index, 1);
            }
          }
          return { ...prev, days };
        });
      } else {
        setForwardingRuleForm(prev => ({ ...prev, [name]: checked }));
      }
    } else {
      setForwardingRuleForm(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle condition type change
  const handleConditionTypeChange = (type) => {
    setForwardingRuleForm(prev => ({ ...prev, condition_type: type }));

    if (type === 'time-based') {
      setTimeConditionEnabled(true);
      setKeywordConditionEnabled(false);
    } else if (type === 'keyword-based') {
      setTimeConditionEnabled(false);
      setKeywordConditionEnabled(true);
    } else {
      setTimeConditionEnabled(false);
      setKeywordConditionEnabled(false);
    }
  };

  // Handle adding a keyword
  const handleAddKeyword = (keyword) => {
    if (!keyword.trim()) return;

    setForwardingRuleForm(prev => ({
      ...prev,
      keywords: [...prev.keywords, keyword.trim()]
    }));
  };

  // Handle removing a keyword
  const handleRemoveKeyword = (index) => {
    setForwardingRuleForm(prev => {
      const keywords = [...prev.keywords];
      keywords.splice(index, 1);
      return { ...prev, keywords };
    });
  };

  // Handle form submission for forwarding rules
  const handleSubmitForwardingRule = async (e) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);
      setFormError('');

      // Validate form
      if (!forwardingRuleForm.name) {
        setFormError('Rule name is required');
        return;
      }

      if (!forwardingRuleForm.forward_to_name || !forwardingRuleForm.forward_to_phone) {
        setFormError('Forward to name and phone are required');
        return;
      }

      // Validate condition-specific fields
      if (forwardingRuleForm.condition_type === 'time-based') {
        if (forwardingRuleForm.days.length === 0) {
          setFormError('Please select at least one day');
          return;
        }
      } else if (forwardingRuleForm.condition_type === 'keyword-based') {
        if (forwardingRuleForm.keywords.length === 0) {
          setFormError('Please add at least one keyword');
          return;
        }
      }

      // Insert into Supabase
      const { data, error } = await supabase
        .from('forwarding_rules')
        .insert({
          attorney_id: attorney.id,
          name: forwardingRuleForm.name,
          forward_to_name: forwardingRuleForm.forward_to_name,
          forward_to_phone: forwardingRuleForm.forward_to_phone,
          condition_type: forwardingRuleForm.condition_type,
          days: forwardingRuleForm.days,
          start_time: forwardingRuleForm.start_time,
          end_time: forwardingRuleForm.end_time,
          timezone: forwardingRuleForm.timezone,
          keywords: forwardingRuleForm.keywords
        });

      if (error) throw error;

      // Success
      setFormSuccess('Forwarding rule added successfully');

      // Refresh rules
      await fetchForwardingRules();

      // Notify parent component if callback provided
      if (onUpdate) {
        onUpdate();
      }

      // Close modal after a delay
      setTimeout(() => {
        setShowForwardingRuleModal(false);
      }, 1500);

    } catch (err) {
      console.error('Error adding forwarding rule:', err);
      setFormError('Failed to add forwarding rule');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting a forwarding rule
  const handleDeleteRule = async (ruleId) => {
    if (!confirm('Are you sure you want to delete this forwarding rule?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('forwarding_rules')
        .delete()
        .eq('id', ruleId);

      if (error) throw error;

      // Refresh rules
      await fetchForwardingRules();

      // Notify parent component if callback provided
      if (onUpdate) {
        onUpdate();
      }
    } catch (err) {
      console.error('Error deleting forwarding rule:', err);
      setError('Failed to delete forwarding rule');
    }
  };

  if (loading) {
    return (
      <div className="call-forwarding-rules">
        <div className="loading-indicator">Loading forwarding rules...</div>
      </div>
    );
  }

  return (
    <div className="call-forwarding-rules">
      {error && (
        <div className="error-message">
          {error}
          <button onClick={fetchForwardingRules} className="retry-button">
            <FaSync />
            Retry
          </button>
        </div>
      )}

      <div className="rules-section">
        {forwardingRules.length === 0 ? (
          <div className="empty-state">
            <p>No forwarding rules configured yet.</p>
            <p>Add rules to automatically forward calls to the right person when specific conditions are met.</p>
          </div>
        ) : (
          <div className="rules-list">
            {forwardingRules.map((rule) => (
              <div key={rule.id} className="rule-item">
                <div className="rule-info">
                  <h4>{rule.name}</h4>
                  <p>Forward to: <strong>{rule.forward_to_name}</strong> ({rule.forward_to_phone})</p>

                  {rule.condition_type === 'time-based' && (
                    <p className="condition-details">
                      Active: {rule.days.join(', ')} from {rule.start_time} to {rule.end_time} ({rule.timezone})
                    </p>
                  )}

                  {rule.condition_type === 'keyword-based' && (
                    <p className="condition-details">
                      Keywords: {rule.keywords.join(', ')}
                    </p>
                  )}
                </div>

                <div className="rule-actions">
                  <span className="condition-badge">
                    {rule.condition_type === 'always' && 'Always Forward'}
                    {rule.condition_type === 'time-based' && 'Time-Based'}
                    {rule.condition_type === 'keyword-based' && 'Keyword-Based'}
                  </span>
                  <button
                    className="delete-rule-button"
                    onClick={() => handleDeleteRule(rule.id)}
                    title="Delete rule"
                  >
                    ×
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        <button className="add-rule-button" onClick={handleAddForwardingRule}>
          <FaPlus />
          <span>Add Forwarding Rule</span>
        </button>
      </div>

      {/* Forwarding Rule Modal */}
      {showForwardingRuleModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Add Forwarding Rule</h3>
              <button
                className="close-button"
                onClick={() => setShowForwardingRuleModal(false)}
              >
                ×
              </button>
            </div>

            {formError && <div className="error-message">{formError}</div>}
            {formSuccess && <div className="success-message">{formSuccess}</div>}

            <form onSubmit={handleSubmitForwardingRule}>
              <div className="form-group">
                <label htmlFor="name">Rule Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={forwardingRuleForm.name}
                  onChange={handleForwardingFormChange}
                  placeholder="e.g., After Hours, Personal Injury Cases"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="forward_to_name">Forward To (Name)</label>
                <input
                  type="text"
                  id="forward_to_name"
                  name="forward_to_name"
                  value={forwardingRuleForm.forward_to_name}
                  onChange={handleForwardingFormChange}
                  placeholder="e.g., John Smith"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="forward_to_phone">Forward To (Phone)</label>
                <input
                  type="tel"
                  id="forward_to_phone"
                  name="forward_to_phone"
                  value={forwardingRuleForm.forward_to_phone}
                  onChange={handleForwardingFormChange}
                  placeholder="e.g., +1234567890"
                  required
                />
              </div>

              <div className="form-group">
                <label>Forwarding Condition</label>
                <div className="condition-options">
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="condition_type"
                      value="always"
                      checked={forwardingRuleForm.condition_type === 'always'}
                      onChange={() => handleConditionTypeChange('always')}
                    />
                    <span>Always forward calls</span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="condition_type"
                      value="time-based"
                      checked={forwardingRuleForm.condition_type === 'time-based'}
                      onChange={() => handleConditionTypeChange('time-based')}
                    />
                    <span>Forward during specific times</span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="condition_type"
                      value="keyword-based"
                      checked={forwardingRuleForm.condition_type === 'keyword-based'}
                      onChange={() => handleConditionTypeChange('keyword-based')}
                    />
                    <span>Forward when specific keywords are mentioned</span>
                  </label>
                </div>
              </div>

              {/* Time-based conditions */}
              {timeConditionEnabled && (
                <div className="condition-details">
                  <h4>Time-Based Conditions</h4>

                  <div className="form-group">
                    <label>Days of the Week</label>
                    <div className="days-selector">
                      {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => (
                        <label key={day} className="checkbox-option">
                          <input
                            type="checkbox"
                            name="days"
                            value={day}
                            checked={forwardingRuleForm.days.includes(day)}
                            onChange={handleForwardingFormChange}
                          />
                          <span>{day}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="time-range">
                    <div className="form-group">
                      <label htmlFor="start_time">Start Time</label>
                      <input
                        type="time"
                        id="start_time"
                        name="start_time"
                        value={forwardingRuleForm.start_time}
                        onChange={handleForwardingFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="end_time">End Time</label>
                      <input
                        type="time"
                        id="end_time"
                        name="end_time"
                        value={forwardingRuleForm.end_time}
                        onChange={handleForwardingFormChange}
                      />
                    </div>
                  </div>

                  <div className="form-group">
                    <label htmlFor="timezone">Timezone</label>
                    <select
                      id="timezone"
                      name="timezone"
                      value={forwardingRuleForm.timezone}
                      onChange={handleForwardingFormChange}
                    >
                      <option value="EST">Eastern Time (EST)</option>
                      <option value="CST">Central Time (CST)</option>
                      <option value="MST">Mountain Time (MST)</option>
                      <option value="PST">Pacific Time (PST)</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Keyword-based conditions */}
              {keywordConditionEnabled && (
                <div className="condition-details">
                  <h4>Keyword-Based Conditions</h4>

                  <div className="form-group">
                    <label>Keywords</label>
                    <div className="keyword-input">
                      <input
                        type="text"
                        placeholder="Enter a keyword and press Enter"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleAddKeyword(e.target.value);
                            e.target.value = '';
                          }
                        }}
                      />
                    </div>

                    <div className="keywords-list">
                      {forwardingRuleForm.keywords.map((keyword, index) => (
                        <div key={index} className="keyword-tag">
                          <span>{keyword}</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveKeyword(index)}
                            className="remove-keyword"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              <div className="modal-actions">
                <button
                  type="button"
                  className="cancel-button"
                  onClick={() => setShowForwardingRuleModal(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="save-button"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Saving...' : 'Save Rule'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default CallForwardingRules;
