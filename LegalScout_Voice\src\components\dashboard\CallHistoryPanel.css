.call-history-panel {
  background-color: var(--card-background, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.panel-header h2 {
  margin: 0;
  font-size: 18px;
  color: var(--text-primary, #333333);
}

/* Dark theme adjustments */
[data-theme="dark"] .call-history-panel {
  background-color: var(--dark-card-background, #1e1e1e);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .panel-header h2 {
  color: var(--dark-text-primary, #f8f9fa);
}

.refresh-button {
  background-color: var(--primary-color, #4a90e2);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background-color: var(--primary-color-dark, #3a7bc8);
}

.refresh-button:disabled {
  background-color: var(--disabled-color, #cccccc);
  cursor: not-allowed;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: var(--text-secondary, #666666);
}

.filter-group select,
.filter-group input {
  padding: 8px;
  border: 1px solid var(--border-color, #dddddd);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-background, #ffffff);
  color: var(--text-primary, #333333);
}

/* Dark theme adjustments for filters */
[data-theme="dark"] .filter-group label {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .filter-group select,
[data-theme="dark"] .filter-group input {
  background-color: var(--dark-input-background, #2c2c2c);
  border-color: var(--dark-border-color, #444);
  color: var(--dark-text-primary, #e9ecef);
}

.call-statistics {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--stats-background, rgba(74, 144, 226, 0.1));
  border-radius: 8px;
}

.stat-item {
  text-align: center;
  min-width: 80px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--primary-color, #4a90e2);
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary, #666666);
  margin-top: 4px;
}

/* Dark theme adjustments for statistics */
[data-theme="dark"] .call-statistics {
  background-color: var(--dark-stats-background, rgba(13, 110, 253, 0.05));
}

[data-theme="dark"] .stat-value {
  color: var(--dark-primary-color, #0d6efd);
}

[data-theme="dark"] .stat-label {
  color: var(--dark-text-secondary, #adb5bd);
}

.loading,
.empty-state {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary, #666666);
  font-style: italic;
}

.error-message {
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 4px solid #ff0000;
  padding: 10px 15px;
  margin-bottom: 20px;
  color: #d32f2f;
  border-radius: 4px;
}

/* Dark theme adjustments for loading and error states */
[data-theme="dark"] .loading,
[data-theme="dark"] .empty-state {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .error-message {
  background-color: rgba(255, 0, 0, 0.05);
  border-left: 4px solid #c62828;
  color: #ef5350;
}

.call-history-table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

.call-history-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.call-history-table th,
.call-history-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color, #eeeeee);
}

.call-history-table th {
  font-weight: 600;
  color: var(--text-secondary, #666666);
  background-color: var(--table-header-background, #f9f9f9);
}

.call-history-table tr:hover {
  background-color: var(--table-hover-background, #f5f5f5);
}

/* Dark theme adjustments for table */
[data-theme="dark"] .call-history-table th,
[data-theme="dark"] .call-history-table td {
  border-bottom-color: var(--dark-border-color, #2c2c2c);
  color: var(--dark-text-primary, #e9ecef);
}

[data-theme="dark"] .call-history-table th {
  color: var(--dark-text-secondary, #adb5bd);
  background-color: var(--dark-table-header-background, #2c2c2c);
}

[data-theme="dark"] .call-history-table tr:hover {
  background-color: var(--dark-table-hover-background, #2c2c2c);
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.status-badge.green {
  background-color: rgba(76, 175, 80, 0.2);
  color: #2e7d32;
}

.status-badge.red {
  background-color: rgba(244, 67, 54, 0.2);
  color: #c62828;
}

.status-badge.blue {
  background-color: rgba(33, 150, 243, 0.2);
  color: #1565c0;
}

.status-badge.orange {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ef6c00;
}

.status-badge.purple {
  background-color: rgba(156, 39, 176, 0.2);
  color: #7b1fa2;
}

.status-badge.gray {
  background-color: rgba(158, 158, 158, 0.2);
  color: #616161;
}

.view-details-button {
  background-color: transparent;
  color: var(--primary-color, #4a90e2);
  border: 1px solid var(--primary-color, #4a90e2);
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-details-button:hover {
  background-color: var(--primary-color, #4a90e2);
  color: white;
}

/* Dark theme adjustments for buttons */
[data-theme="dark"] .view-details-button {
  color: var(--dark-primary-color, #0d6efd);
  border-color: var(--dark-primary-color, #0d6efd);
}

[data-theme="dark"] .view-details-button:hover {
  background-color: var(--dark-primary-color, #0d6efd);
  color: white;
}

[data-theme="dark"] .refresh-button {
  background-color: var(--dark-primary-color, #0d6efd);
}

[data-theme="dark"] .refresh-button:hover {
  background-color: var(--dark-primary-color-dark, #0b5ed7);
}

[data-theme="dark"] .refresh-button:disabled {
  background-color: var(--dark-disabled-color, #343a40);
  color: #6c757d;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}

.pagination button {
  background-color: var(--primary-color, #4a90e2);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pagination button:hover {
  background-color: var(--primary-color-dark, #3a7bc8);
}

.pagination button:disabled {
  background-color: var(--disabled-color, #cccccc);
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: var(--text-secondary, #666666);
}

/* Dark theme adjustments for pagination */
[data-theme="dark"] .pagination button {
  background-color: var(--dark-primary-color, #0d6efd);
}

[data-theme="dark"] .pagination button:hover {
  background-color: var(--dark-primary-color-dark, #0b5ed7);
}

[data-theme="dark"] .pagination button:disabled {
  background-color: var(--dark-disabled-color, #343a40);
  color: #6c757d;
}

[data-theme="dark"] .page-info {
  color: var(--dark-text-secondary, #adb5bd);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .call-statistics {
    gap: 10px;
  }

  .stat-item {
    min-width: 70px;
  }

  .stat-value {
    font-size: 20px;
  }

  .filters {
    flex-direction: column;
    gap: 10px;
  }

  .filter-group {
    width: 100%;
  }

  .filter-group select,
  .filter-group input {
    flex: 1;
  }
}
