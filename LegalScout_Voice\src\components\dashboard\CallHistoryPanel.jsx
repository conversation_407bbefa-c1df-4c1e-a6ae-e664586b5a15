import React, { useState, useEffect } from 'react';
import { callHistoryService } from '../../services/callHistoryService';
import './CallHistoryPanel.css';

/**
 * Call History Panel Component
 * 
 * Displays call history for an attorney in the dashboard.
 * 
 * @param {Object} props
 * @param {string} props.attorneyId - The attorney ID
 * @param {number} props.limit - Maximum number of calls to display
 * @param {Function} props.onViewDetails - Callback when user clicks to view call details
 */
const CallHistoryPanel = ({ attorneyId, limit = 10, onViewDetails }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [callHistory, setCallHistory] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState(null);
  const [dateRange, setDateRange] = useState({
    startDate: null,
    endDate: null
  });

  // Fetch call history
  const fetchCallHistory = async () => {
    try {
      setLoading(true);
      
      // Get call history
      const result = await callHistoryService.getCallHistory(attorneyId, {
        limit,
        page,
        status: statusFilter,
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });
      
      setCallHistory(result.calls);
      setTotalPages(Math.ceil(result.total / limit));
      
      // Get call statistics
      const stats = await callHistoryService.getCallStatistics(attorneyId, {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });
      
      setStatistics(stats);
      setError(null);
    } catch (error) {
      console.error('Error fetching call history:', error);
      setError('Failed to load call history. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch call history when component mounts or filters change
  useEffect(() => {
    if (attorneyId) {
      fetchCallHistory();
    }
  }, [attorneyId, page, statusFilter, dateRange.startDate, dateRange.endDate]);

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  // Handle status filter change
  const handleStatusFilterChange = (event) => {
    setStatusFilter(event.target.value === 'all' ? null : event.target.value);
    setPage(1); // Reset to first page when filter changes
  };

  // Handle date range change
  const handleDateRangeChange = (type, event) => {
    setDateRange(prev => ({
      ...prev,
      [type]: event.target.value ? new Date(event.target.value).toISOString() : null
    }));
    setPage(1); // Reset to first page when filter changes
  };

  // Handle view details click
  const handleViewDetails = (callId) => {
    if (onViewDetails) {
      onViewDetails(callId);
    }
  };

  // Render call status badge
  const renderStatusBadge = (status) => {
    const formattedStatus = callHistoryService.formatStatus(status);
    return (
      <span className={`status-badge ${formattedStatus.color}`}>
        {formattedStatus.label}
      </span>
    );
  };

  // Render call duration
  const renderDuration = (seconds) => {
    return callHistoryService.formatDuration(seconds);
  };

  // Render statistics
  const renderStatistics = () => {
    if (!statistics) return null;

    return (
      <div className="call-statistics">
        <div className="stat-item">
          <div className="stat-value">{statistics.totalCalls}</div>
          <div className="stat-label">Total Calls</div>
        </div>
        <div className="stat-item">
          <div className="stat-value">{statistics.completedCalls}</div>
          <div className="stat-label">Completed</div>
        </div>
        <div className="stat-item">
          <div className="stat-value">{statistics.failedCalls}</div>
          <div className="stat-label">Failed</div>
        </div>
        <div className="stat-item">
          <div className="stat-value">{renderDuration(statistics.averageDuration)}</div>
          <div className="stat-label">Avg. Duration</div>
        </div>
        <div className="stat-item">
          <div className="stat-value">{statistics.completionRate.toFixed(1)}%</div>
          <div className="stat-label">Completion Rate</div>
        </div>
      </div>
    );
  };

  return (
    <div className="call-history-panel">
      <div className="panel-header">
        <h2>Call History</h2>
        <button className="refresh-button" onClick={fetchCallHistory} disabled={loading}>
          {loading ? 'Loading...' : 'Refresh'}
        </button>
      </div>

      {/* Filters */}
      <div className="filters">
        <div className="filter-group">
          <label htmlFor="status-filter">Status:</label>
          <select
            id="status-filter"
            value={statusFilter || 'all'}
            onChange={handleStatusFilterChange}
            disabled={loading}
          >
            <option value="all">All</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="in_progress">In Progress</option>
            <option value="queued">Queued</option>
            <option value="scheduled">Scheduled</option>
          </select>
        </div>
        <div className="filter-group">
          <label htmlFor="start-date">From:</label>
          <input
            id="start-date"
            type="date"
            value={dateRange.startDate ? new Date(dateRange.startDate).toISOString().split('T')[0] : ''}
            onChange={(e) => handleDateRangeChange('startDate', e)}
            disabled={loading}
          />
        </div>
        <div className="filter-group">
          <label htmlFor="end-date">To:</label>
          <input
            id="end-date"
            type="date"
            value={dateRange.endDate ? new Date(dateRange.endDate).toISOString().split('T')[0] : ''}
            onChange={(e) => handleDateRangeChange('endDate', e)}
            disabled={loading}
          />
        </div>
      </div>

      {/* Statistics */}
      {renderStatistics()}

      {/* Error message */}
      {error && <div className="error-message">{error}</div>}

      {/* Call history table */}
      {loading ? (
        <div className="loading">Loading call history...</div>
      ) : callHistory.length === 0 ? (
        <div className="empty-state">No calls found.</div>
      ) : (
        <div className="call-history-table-container">
          <table className="call-history-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Time</th>
                <th>Phone</th>
                <th>Duration</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {callHistory.map((call) => (
                <tr key={call.call_id}>
                  <td>{new Date(call.start_time).toLocaleDateString()}</td>
                  <td>{new Date(call.start_time).toLocaleTimeString()}</td>
                  <td>{call.customer_phone || 'Unknown'}</td>
                  <td>{renderDuration(call.duration)}</td>
                  <td>{renderStatusBadge(call.status)}</td>
                  <td>
                    <button
                      className="view-details-button"
                      onClick={() => handleViewDetails(call.call_id)}
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => handlePageChange(page - 1)}
            disabled={page === 1 || loading}
          >
            Previous
          </button>
          <span className="page-info">
            Page {page} of {totalPages}
          </span>
          <button
            onClick={() => handlePageChange(page + 1)}
            disabled={page === totalPages || loading}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default CallHistoryPanel;
