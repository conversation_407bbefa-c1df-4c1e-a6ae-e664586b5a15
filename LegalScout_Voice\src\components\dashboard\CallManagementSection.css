.call-management-section {
  margin-bottom: 30px;
}

.section-header {
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 24px;
  color: var(--text-primary, #333333);
  margin: 0;
}

.outbound-call-panel {
  background-color: var(--card-background, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

/* Dark theme adjustments */
[data-theme="dark"] .section-header h2 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .outbound-call-panel {
  background-color: var(--dark-card-background, #1e1e1e);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.outbound-call-panel h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  color: var(--text-primary, #333333);
}

/* Assistant Status Display */
.assistant-status {
  background-color: var(--card-background-secondary, #f8f9fa);
  border: 1px solid var(--border-color-light, #e9ecef);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.assistant-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.success {
  background-color: #28a745;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

.status-indicator.error {
  background-color: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

.assistant-text {
  color: var(--text-secondary, #495057);
  font-size: 0.95rem;
}

.assistant-text strong {
  color: var(--text-primary, #212529);
}

.assistant-id {
  color: var(--text-muted, #6c757d);
  font-size: 0.85rem;
  font-family: 'Courier New', monospace;
  background-color: var(--background-muted, #e9ecef);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* Phone number loading and error states */
.loading-message {
  color: var(--text-secondary, #6c757d);
  font-style: italic;
  padding: 0.5rem;
  background-color: var(--background-light, #f8f9fa);
  border-radius: 4px;
}

.error-message {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: 4px;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.retry-button {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #c82333;
}

.no-phone-numbers {
  color: var(--text-secondary, #6c757d);
  background-color: var(--background-warning, #fff3cd);
  border: 1px solid var(--border-warning, #ffeaa7);
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.9rem;
}

.outbound-call-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: flex-end;
}

.form-group {
  flex: 1;
  min-width: 250px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary, #666666);
}

.form-group input, .form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color, #dddddd);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-background, #ffffff);
  color: var(--text-primary, #333333);
}

/* Dark theme adjustments for assistant status */
[data-theme="dark"] .assistant-status {
  background-color: var(--dark-card-background-secondary, #2c2c2c);
  border-color: var(--dark-border-color-light, #444);
}

[data-theme="dark"] .assistant-text {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .assistant-text strong {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .assistant-id {
  color: var(--dark-text-muted, #6c757d);
  background-color: var(--dark-background-muted, #444);
}

/* Dark theme for phone number states */
[data-theme="dark"] .loading-message {
  color: var(--dark-text-secondary, #adb5bd);
  background-color: var(--dark-background-light, #2c2c2c);
}

[data-theme="dark"] .error-message {
  color: #f5c6cb;
  background-color: rgba(220, 53, 69, 0.05);
  border-color: rgba(220, 53, 69, 0.1);
}

[data-theme="dark"] .no-phone-numbers {
  color: var(--dark-text-secondary, #adb5bd);
  background-color: var(--dark-background-warning, #3d3d00);
  border-color: var(--dark-border-warning, #666600);
}

/* Dark theme adjustments for form elements */
[data-theme="dark"] .outbound-call-panel h3 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .form-group label {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .form-group input, [data-theme="dark"] .form-group select {
  background-color: var(--dark-input-background, #2c2c2c);
  border-color: var(--dark-border-color, #444);
  color: var(--dark-text-primary, #e9ecef);
}

.form-actions {
  display: flex;
  gap: 10px;
}

.call-button,
.schedule-button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.call-button {
  background-color: var(--primary-color, #4a90e2);
  color: white;
}

.call-button:hover {
  background-color: var(--primary-color-dark, #3a7bc8);
}

.schedule-button {
  background-color: var(--secondary-color, #6c757d);
  color: white;
}

.schedule-button:hover {
  background-color: var(--secondary-color-dark, #5a6268);
}

.call-button:disabled,
.schedule-button:disabled {
  background-color: var(--disabled-color, #cccccc);
  cursor: not-allowed;
}

.call-result {
  margin-top: 15px;
  padding: 10px 15px;
  border-radius: 4px;
  font-size: 14px;
}

.call-result.success {
  background-color: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4caf50;
  color: #2e7d32;
}

.call-result.error {
  background-color: rgba(244, 67, 54, 0.1);
  border-left: 4px solid #f44336;
  color: #c62828;
}

/* Dark theme adjustments for buttons and call results */
[data-theme="dark"] .call-button {
  background-color: var(--dark-primary-color, #0d6efd);
}

[data-theme="dark"] .call-button:hover {
  background-color: var(--dark-primary-color-dark, #0b5ed7);
}

[data-theme="dark"] .schedule-button {
  background-color: var(--dark-secondary-color, #495057);
}

[data-theme="dark"] .schedule-button:hover {
  background-color: var(--dark-secondary-color-dark, #343a40);
}

[data-theme="dark"] .call-button:disabled,
[data-theme="dark"] .schedule-button:disabled {
  background-color: var(--dark-disabled-color, #343a40);
  color: #6c757d;
}

[data-theme="dark"] .call-result.success {
  background-color: rgba(76, 175, 80, 0.05);
  border-left: 4px solid #2e7d32;
  color: #81c784;
}

[data-theme="dark"] .call-result.error {
  background-color: rgba(244, 67, 54, 0.05);
  border-left: 4px solid #c62828;
  color: #e57373;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .outbound-call-form {
    flex-direction: column;
    gap: 15px;
  }

  .form-group {
    width: 100%;
  }

  .form-actions {
    width: 100%;
  }

  .call-button,
  .schedule-button {
    flex: 1;
  }
}
