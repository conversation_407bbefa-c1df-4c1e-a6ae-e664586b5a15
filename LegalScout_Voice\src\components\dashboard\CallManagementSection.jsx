import React, { useState, useEffect } from 'react';
import CallHistoryPanel from './CallHistoryPanel';
import CallDetailsModal from './CallDetailsModal';
import LiveCallMonitor from './LiveCallMonitor';
import { vapiMcpService } from '../../services/vapiMcpService';
import { supabase } from '../../lib/supabase';
import { smsNotificationService } from '../../services/SmsNotificationService';
import './CallManagementSection.css';

/**
 * Call Management Section Component
 *
 * Integrates call history and call details components into the dashboard.
 * Also provides functionality to make outbound calls.
 *
 * @param {Object} props
 * @param {Object} props.attorney - The attorney object
 * @param {Function} props.onError - Callback when an error occurs
 */
const CallManagementSection = ({ attorney, onError }) => {
  const [selectedCallId, setSelectedCallId] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [makingCall, setMakingCall] = useState(false);
  const [callResult, setCallResult] = useState(null);
  const [phoneNumbers, setPhoneNumbers] = useState([]);
  const [selectedPhoneNumberId, setSelectedPhoneNumberId] = useState(null);
  const [phoneNumbersLoading, setPhoneNumbersLoading] = useState(true);
  const [phoneNumbersError, setPhoneNumbersError] = useState(null);
  const [activeCallId, setActiveCallId] = useState(null);
  const [showLiveMonitor, setShowLiveMonitor] = useState(false);

  // Debug logging for attorney data
  console.log('[CallManagementSection] Component rendered with attorney:', attorney);
  console.log('[CallManagementSection] Attorney ID:', attorney?.id);
  console.log('[CallManagementSection] Attorney Vapi Assistant ID:', attorney?.vapi_assistant_id);

  // Ensure attorney has a valid Vapi assistant ID
  useEffect(() => {
    const checkAssistant = async () => {
      if (attorney && !attorney.vapi_assistant_id) {
        try {
          // Get the assistant from Vapi
          await vapiMcpService.ensureConnection();

          // Create a new assistant if needed
          const assistantConfig = {
            name: attorney.firm_name || 'Legal Assistant',
            firstMessage: attorney.welcome_message || 'Hello, how can I help you with your legal needs today?',
            instructions: attorney.vapi_instructions || 'You are a legal assistant helping potential clients understand their legal needs.',
            voice: {
              provider: attorney.voice_provider || '11labs',
              voiceId: attorney.voice_id || 'sarah'
            },
            llm: {
              provider: "openai",
              model: attorney.ai_model || "gpt-4o"
            },
            transcriber: {
              provider: "deepgram",
              model: "nova-3"
            }
          };

          const assistant = await vapiMcpService.createAssistant(assistantConfig);

          // Update attorney record with new assistant ID
          const { error } = await supabase
            .from('attorneys')
            .update({
              vapi_assistant_id: assistant.id,
              updated_at: new Date().toISOString()
            })
            .eq('id', attorney.id);

          if (error) {
            throw error;
          }

          console.log(`Created and linked new assistant ${assistant.id} for attorney ${attorney.id}`);
        } catch (error) {
          console.error('Error ensuring attorney has assistant:', error);
          if (onError) {
            onError('Failed to ensure attorney has a valid assistant. Please try again.');
          }
        }
      }
    };

    checkAssistant();

    // Get available phone numbers
    const getPhoneNumbers = async () => {
      try {
        setPhoneNumbersLoading(true);
        setPhoneNumbersError(null);

        console.log('[CallManagementSection] Loading phone numbers...');
        await vapiMcpService.ensureConnection();
        const numbers = await vapiMcpService.listPhoneNumbers();

        console.log('[CallManagementSection] Phone numbers loaded:', numbers);
        setPhoneNumbers(numbers || []);

        if (numbers && numbers.length > 0) {
          setSelectedPhoneNumberId(numbers[0].id);
          console.log('[CallManagementSection] Selected first phone number:', numbers[0].id);
        } else {
          console.log('[CallManagementSection] No phone numbers available');
          setPhoneNumbersError('No phone numbers available. Please add a phone number in your Vapi account.');
        }
      } catch (error) {
        console.error('[CallManagementSection] Error getting phone numbers:', error);
        setPhoneNumbersError(`Failed to load phone numbers: ${error.message}`);
      } finally {
        setPhoneNumbersLoading(false);
      }
    };

    getPhoneNumbers();
  }, [attorney]);

  // Handle view call details
  const handleViewCallDetails = (callId) => {
    setSelectedCallId(callId);
    setIsModalOpen(true);
  };

  // Handle close modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedCallId(null);
  };

  // Handle phone number change
  const handlePhoneNumberChange = (event) => {
    // Only allow digits, plus sign, and parentheses
    const value = event.target.value.replace(/[^\d+()]/g, '');
    setPhoneNumber(value);
  };

  // Handle phone number selection change
  const handlePhoneNumberSelection = (event) => {
    setSelectedPhoneNumberId(event.target.value);
  };

  // Handle make call
  const handleMakeCall = async () => {
    console.log('[CallManagementSection] handleMakeCall called');
    console.log('[CallManagementSection] Phone number:', phoneNumber);
    console.log('[CallManagementSection] Selected phone number ID:', selectedPhoneNumberId);
    console.log('[CallManagementSection] Attorney:', attorney);

    if (!phoneNumber) {
      console.log('[CallManagementSection] No phone number provided');
      if (onError) {
        onError('Please enter a phone number.');
      }
      return;
    }

    if (!attorney.vapi_assistant_id) {
      console.log('[CallManagementSection] No assistant ID found');
      setCallResult({
        success: false,
        message: 'No assistant configured. Please configure your assistant in the Agent tab first.'
      });
      if (onError) {
        onError('No assistant configured. Please configure an assistant first.');
      }
      return;
    }

    try {
      setMakingCall(true);
      setCallResult(null);

      // Debug logging for assistant ID
      console.log('[CallManagementSection] Making call with assistant ID:', attorney.vapi_assistant_id);
      console.log('[CallManagementSection] Full attorney object:', attorney);
      console.log('[CallManagementSection] Phone number:', phoneNumber);
      console.log('[CallManagementSection] Selected phone number ID:', selectedPhoneNumberId);

      // Ensure connection
      await vapiMcpService.ensureConnection();

      // Create call
      const callOptions = selectedPhoneNumberId
        ? { phoneNumberId: selectedPhoneNumberId }
        : {}; // Allow call without phone number ID for testing

      const call = await vapiMcpService.createCall(
        attorney.vapi_assistant_id,
        phoneNumber,
        callOptions
      );

      if (!call || !call.id) {
        throw new Error('Call creation failed - no call ID returned');
      }

      setCallResult({
        success: true,
        message: `Call initiated successfully. Call ID: ${call.id}. Monitoring call progress...`,
        callId: call.id
      });

      // Set active call for live monitoring
      setActiveCallId(call.id);
      setShowLiveMonitor(true);

      // Send SMS notification to attorney
      try {
        console.log('[CallManagementSection] Sending SMS notification to attorney...');
        await smsNotificationService.sendCallStartedNotification(call.id, attorney.id);
        console.log('[CallManagementSection] SMS notification sent successfully');
      } catch (smsError) {
        console.warn('[CallManagementSection] Failed to send SMS notification:', smsError.message);
        // Don't fail the call if SMS fails
      }

      // Monitor call progress
      try {
        const finalCallData = await vapiMcpService.monitorCall(
          call.id,
          (callData) => {
            // Update UI with call status
            setCallResult(prev => ({
              ...prev,
              message: `Call ${call.id} status: ${callData.status}. ${callData.duration ? `Duration: ${Math.round(callData.duration)}s` : ''}`
            }));
          },
          300000 // 5 minutes timeout
        );

        // Update with final result
        setCallResult({
          success: finalCallData.status === 'completed',
          message: `Call ${finalCallData.status}. ${finalCallData.duration ? `Duration: ${Math.round(finalCallData.duration)}s` : ''}`,
          callId: call.id,
          finalStatus: finalCallData.status
        });

      } catch (monitorError) {
        console.warn('[CallManagementSection] Call monitoring failed:', monitorError);
        setCallResult(prev => ({
          ...prev,
          message: `${prev.message} (Monitoring failed: ${monitorError.message})`
        }));
      }

      // Clear phone number
      setPhoneNumber('');
    } catch (error) {
      console.error('Error making call:', error);
      setCallResult({
        success: false,
        message: `Failed to make call: ${error.message}`
      });

      if (onError) {
        onError(`Failed to make call: ${error.message}`);
      }
    } finally {
      setMakingCall(false);
    }
  };

  // Handle schedule call
  const handleScheduleCall = async () => {
    if (!phoneNumber) {
      if (onError) {
        onError('Please enter a phone number.');
      }
      return;
    }

    if (!attorney.vapi_assistant_id) {
      if (onError) {
        onError('No assistant configured. Please configure an assistant first.');
      }
      return;
    }

    try {
      setMakingCall(true);
      setCallResult(null);

      // Get scheduled time (5 minutes from now)
      const scheduledTime = new Date();
      scheduledTime.setMinutes(scheduledTime.getMinutes() + 5);

      // Ensure connection
      await vapiMcpService.ensureConnection();

      // Create scheduled call
      const callOptions = {
        scheduledAt: scheduledTime.toISOString()
      };

      if (selectedPhoneNumberId) {
        callOptions.phoneNumberId = selectedPhoneNumberId;
      }

      const call = await vapiMcpService.createCall(
        attorney.vapi_assistant_id,
        phoneNumber,
        callOptions
      );

      setCallResult({
        success: true,
        message: `Call scheduled successfully for ${scheduledTime.toLocaleTimeString()}. Call ID: ${call.id}`,
        callId: call.id
      });

      // Clear phone number
      setPhoneNumber('');
    } catch (error) {
      console.error('Error scheduling call:', error);
      setCallResult({
        success: false,
        message: `Failed to schedule call: ${error.message}`
      });

      if (onError) {
        onError(`Failed to schedule call: ${error.message}`);
      }
    } finally {
      setMakingCall(false);
    }
  };

  return (
    <div className="call-management-section">
      <div className="section-header">
        <h2>Call Management</h2>
      </div>

      <div className="outbound-call-panel">
        <h3>Make Outbound Call</h3>

        {/* Assistant Status Display */}
        <div className="assistant-status">
          {attorney?.vapi_assistant_id && !attorney.vapi_assistant_id.startsWith('mock-') ? (
            <div className="assistant-info">
              <span className="status-indicator success"></span>
              <span className="assistant-text">
                Using Assistant: <strong>{attorney.firm_name || 'Your AI Assistant'}</strong>
              </span>
              <span className="assistant-id">ID: {attorney.vapi_assistant_id}</span>
            </div>
          ) : (
            <div className="assistant-info">
              <span className="status-indicator error"></span>
              <span className="assistant-text">
                {attorney?.vapi_assistant_id && attorney.vapi_assistant_id.startsWith('mock-')
                  ? 'Voice assistant needs to be properly configured. Mock assistant detected.'
                  : 'No assistant configured. Please configure your assistant in the Agent tab first.'}
              </span>
            </div>
          )}
        </div>

        <div className="outbound-call-form">
          <div className="form-group">
            <label htmlFor="phone-number">Client Phone Number:</label>
            <input
              id="phone-number"
              type="tel"
              value={phoneNumber}
              onChange={handlePhoneNumberChange}
              placeholder="Enter client phone number"
              disabled={makingCall}
            />
          </div>

          <div className="form-group">
            <label htmlFor="caller-id">Caller ID:</label>
            {phoneNumbersLoading ? (
              <div className="loading-message">Loading phone numbers...</div>
            ) : phoneNumbersError ? (
              <div className="error-message">
                {phoneNumbersError}
                <button
                  className="retry-button"
                  onClick={() => {
                    // Retry loading phone numbers
                    const getPhoneNumbers = async () => {
                      try {
                        setPhoneNumbersLoading(true);
                        setPhoneNumbersError(null);
                        await vapiMcpService.ensureConnection();
                        const numbers = await vapiMcpService.listPhoneNumbers();
                        setPhoneNumbers(numbers || []);
                        if (numbers && numbers.length > 0) {
                          setSelectedPhoneNumberId(numbers[0].id);
                        }
                      } catch (error) {
                        setPhoneNumbersError(`Failed to load phone numbers: ${error.message}`);
                      } finally {
                        setPhoneNumbersLoading(false);
                      }
                    };
                    getPhoneNumbers();
                  }}
                >
                  Retry
                </button>
              </div>
            ) : phoneNumbers.length > 0 ? (
              <select
                id="caller-id"
                value={selectedPhoneNumberId || ''}
                onChange={handlePhoneNumberSelection}
                disabled={makingCall}
              >
                {phoneNumbers.map(number => (
                  <option key={number.id} value={number.id}>
                    {number.phone_number} ({number.friendly_name || 'Vapi Number'})
                  </option>
                ))}
              </select>
            ) : (
              <div className="no-phone-numbers">
                No phone numbers available. You can still test calls, but they may not work properly.
              </div>
            )}
          </div>

          <div className="form-actions">
            <button
              className="call-button"
              onClick={() => {
                console.log('[CallManagementSection] Call button clicked');
                console.log('[CallManagementSection] Button state - makingCall:', makingCall, 'phoneNumber:', phoneNumber, 'selectedPhoneNumberId:', selectedPhoneNumberId);
                handleMakeCall();
              }}
              disabled={makingCall || !phoneNumber}
            >
              {makingCall ? 'Initiating Call...' : 'Call Now'}
            </button>
            <button
              className="schedule-button"
              onClick={handleScheduleCall}
              disabled={makingCall || !phoneNumber}
            >
              Schedule Call
            </button>
          </div>
        </div>

        {callResult && (
          <div className={`call-result ${callResult.success ? 'success' : 'error'}`}>
            {callResult.message}
          </div>
        )}
      </div>

      <CallHistoryPanel
        attorneyId={attorney?.id}
        limit={10}
        onViewDetails={handleViewCallDetails}
      />

      <CallDetailsModal
        callId={selectedCallId}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />

      {/* Live Call Monitor */}
      {showLiveMonitor && activeCallId && (
        <LiveCallMonitor
          callId={activeCallId}
          attorney={attorney}
          onClose={() => {
            setShowLiveMonitor(false);
            setActiveCallId(null);
          }}
        />
      )}
    </div>
  );
};

export default CallManagementSection;
