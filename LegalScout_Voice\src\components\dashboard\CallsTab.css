/* Calls Tab Styles */

.calls-tab {
  padding: 20px;
  font-family: 'Inter', sans-serif;
}

.calls-tab h2 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

/* Dark theme adjustments */
[data-theme="dark"] .calls-tab h2 {
  color: #f8f9fa;
}

.tab-description {
  margin-bottom: 20px;
  color: #6c757d;
  font-size: 14px;
}

/* Section Tabs */
.section-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.section-tab {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.section-tab svg {
  margin-right: 8px;
  font-size: 16px;
}

.section-tab.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.section-tab:hover:not(.active) {
  color: #495057;
  background-color: #f8f9fa;
}

.section-tab .coming-soon {
  position: absolute;
  top: 0;
  right: 0;
  transform: translateY(-50%);
  padding: 2px 6px;
  background-color: #ffc107;
  color: #212529;
  font-size: 10px;
  font-weight: 600;
  border-radius: 10px;
}

/* Section Content */
.section-content {
  margin-top: 20px;
}

/* Dashboard Card */
.dashboard-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}

.dashboard-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.card-description {
  margin-bottom: 20px;
  color: #6c757d;
  font-size: 14px;
}

/* Dark theme adjustments for dashboard card */
[data-theme="dark"] .dashboard-card {
  background-color: #1e1e1e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .dashboard-card h3 {
  color: #f8f9fa;
}

[data-theme="dark"] .card-description {
  color: #adb5bd;
}

/* Coming Soon Placeholder */
.coming-soon-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.placeholder-icon {
  font-size: 48px;
  color: #adb5bd;
  margin-bottom: 20px;
}

.coming-soon-placeholder p {
  color: #6c757d;
  font-size: 16px;
  margin: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .section-tabs {
    flex-direction: column;
    border-bottom: none;
  }

  .section-tab {
    border-bottom: 1px solid #e9ecef;
    border-left: 3px solid transparent;
  }

  .section-tab.active {
    border-bottom-color: #e9ecef;
    border-left-color: #007bff;
    background-color: #f8f9fa;
  }
}
