.consultation-stats {
  margin-bottom: 2rem;
}

.stats-loading {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
  font-style: italic;
  font-size: 1.1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #87ceeb 0%, #b0e0e6 50%, #87ceeb 100%);
  border-radius: 20px 20px 0 0;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(135, 206, 235, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.stat-card:hover {
  transform: translateY(-2px) scale(1.01);
  box-shadow: var(--shadow-medium);
  border-color: #87ceeb;
}

.stat-card:hover::after {
  opacity: 1;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f5dc 0%, #87ceeb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.3rem;
  flex-shrink: 0;
  transition: all 0.3s ease;
  position: relative;
}

/* Remove icon background effects */

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: 800;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 0.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: 1.2;
  opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Dark theme support */
[data-theme="dark"] .consultation-stats {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .stats-loading {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .stat-card {
  background: var(--dark-card-background, #1e1e1e);
  border-color: var(--dark-border-color, #444);
}

[data-theme="dark"] .stat-card:hover {
  border-color: var(--dark-primary-color-light, rgba(216, 87, 34, 0.4));
  box-shadow: 0 4px 12px rgba(216, 87, 34, 0.15);
}

[data-theme="dark"] .stat-value {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .stat-label {
  color: var(--dark-text-secondary, #adb5bd);
}
