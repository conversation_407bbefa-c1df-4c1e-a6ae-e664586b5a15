import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { FaPhone, FaClock, FaUsers, FaChartLine, FaCalendarWeek } from 'react-icons/fa';
import './ConsultationStats.css';

/**
 * ConsultationStats component to display consultation statistics
 */
const ConsultationStats = ({ attorney }) => {
  const [stats, setStats] = useState({
    totalConsultations: 0,
    totalDuration: 0,
    averageDuration: 0,
    recentConsultations: 0,
    loading: true
  });

  useEffect(() => {
    const fetchStats = async () => {
      if (!attorney || !attorney.id) {
        setStats(prev => ({ ...prev, loading: false }));
        return;
      }

      try {
        // Get all consultations for this attorney
        const { data: consultations, error } = await supabase
          .from('consultations')
          .select('duration, created_at')
          .eq('attorney_id', attorney.id);

        if (error) throw error;

        const now = new Date();
        const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        const totalConsultations = consultations.length;
        const totalDuration = consultations.reduce((sum, c) => sum + (c.duration || 0), 0);
        const averageDuration = totalConsultations > 0 ? totalDuration / totalConsultations : 0;
        const recentConsultations = consultations.filter(c => 
          new Date(c.created_at) >= last7Days
        ).length;

        setStats({
          totalConsultations,
          totalDuration,
          averageDuration,
          recentConsultations,
          loading: false
        });
      } catch (error) {
        console.error('Error fetching consultation stats:', error);
        setStats(prev => ({ ...prev, loading: false }));
      }
    };

    fetchStats();
  }, [attorney]);

  const formatDuration = (seconds) => {
    if (!seconds) return '0 min';
    const minutes = Math.round(seconds / 60);
    return `${minutes} min`;
  };

  if (stats.loading) {
    return (
      <div className="consultation-stats">
        <div className="stats-loading">Loading stats...</div>
      </div>
    );
  }

  return (
    <div className="consultation-stats">
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">
            <FaPhone />
          </div>
          <div className="stat-content">
            <div className="stat-value">{stats.totalConsultations}</div>
            <div className="stat-label">Total Consultations</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <FaClock />
          </div>
          <div className="stat-content">
            <div className="stat-value">{formatDuration(stats.totalDuration)}</div>
            <div className="stat-label">Total Duration</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <FaChartLine />
          </div>
          <div className="stat-content">
            <div className="stat-value">{formatDuration(stats.averageDuration)}</div>
            <div className="stat-label">Average Duration</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <FaCalendarWeek />
          </div>
          <div className="stat-content">
            <div className="stat-value">{stats.recentConsultations}</div>
            <div className="stat-label">Last 7 Days</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsultationStats;
