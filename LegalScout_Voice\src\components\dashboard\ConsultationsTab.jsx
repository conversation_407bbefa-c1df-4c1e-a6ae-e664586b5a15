import React, { useState, useEffect, useRef } from 'react';
import { supabase } from '../../lib/supabase';
import { FaPhone, FaEnvelope, FaCalendarAlt, FaList, FaThLarge, FaMapMarkedAlt, FaFilter, FaSearch, FaEllipsisH, FaPlus, FaCog, FaTrash, FaEdit, FaFileImport, FaEye, FaUsers, FaChartLine, FaClock, FaCheckSquare, FaSquare, FaUserCheck, FaClipboardCheck, FaExclamationTriangle, FaInfoCircle, FaShareAlt, FaPlay, FaCalendarPlus, FaFileAlt, FaSearchPlus, FaGavel, FaUniversity, FaWpforms } from 'react-icons/fa';
import ConsultationStats from './ConsultationStats';
import CSVImportModal from './CSVImportModal';
import ManageColumnsModal from './ManageColumnsModal';
import BriefModal from './BriefModal';
import './ConsultationsTab.css';

/**
 * ConsultationsTab component for the attorney dashboard
 * Displays consultation history and allows management of client interactions
 */
const ConsultationsTab = ({ attorney }) => {
  // State for consultations data
  const [consultations, setConsultations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for UI
  const [viewMode, setViewMode] = useState('table'); // table, card, map
  const [searchTerm, setSearchTerm] = useState('');
  const [customColumns, setCustomColumns] = useState([]);
  const [showColumnModal, setShowColumnModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [editingColumn, setEditingColumn] = useState(null);
  const [showActionMenu, setShowActionMenu] = useState(null); // Track which consultation's menu is open
  const [showManageColumnsModal, setShowManageColumnsModal] = useState(false);
  const [selectedConsultation, setSelectedConsultation] = useState(null);
  const [showBriefModal, setShowBriefModal] = useState(false);
  const [selectedConsultations, setSelectedConsultations] = useState(new Set());
  const [showBatchActions, setShowBatchActions] = useState(false);
  const actionMenuRefs = useRef({});
  const [newColumn, setNewColumn] = useState({
    name: '',
    display_name: '',
    field_type: 'text',
    source: 'metadata',
    source_path: '',
    default_value: '',
    options: [],
    is_visible: true,
    display_order: 0
  });
  const [isAddingColumn, setIsAddingColumn] = useState(false);

  // Fetch custom columns
  useEffect(() => {
    const fetchCustomColumns = async () => {
      try {
        if (!attorney || !attorney.id) return;

        const { data, error } = await supabase
          .from('custom_columns')
          .select('*')
          .eq('attorney_id', attorney.id)
          .order('display_order', { ascending: true });

        if (error) throw error;

        setCustomColumns(data || []);
      } catch (error) {
        console.error('Error fetching custom columns:', error);
      }
    };

    fetchCustomColumns();
  }, [attorney]);

  // Fetch consultations data
  useEffect(() => {
    const fetchConsultations = async () => {
      try {
        setLoading(true);

        // Check if attorney exists
        if (!attorney) {
          console.log('Attorney object is null or undefined');
          setConsultations([]);
          setLoading(false);
          return;
        }

        console.log('Fetching consultations for attorney:', attorney.id);

        // Fetch consultations from Supabase using attorney_id
        if (!attorney.id) {
          console.log('No attorney ID found for attorney');
          setConsultations([]);
          setLoading(false);
          return;
        }

        const { data, error } = await supabase
          .from('consultations')
          .select('*')
          .eq('attorney_id', attorney.id)
          .order('created_at', { ascending: false });

        if (error) throw error;

        // Parse metadata for each consultation
        const consultationsWithParsedMetadata = (data || []).map(consultation => {
          // Parse metadata if it's a string
          let metadata = null;
          if (consultation.metadata) {
            try {
              metadata = typeof consultation.metadata === 'string'
                ? JSON.parse(consultation.metadata)
                : consultation.metadata;
            } catch (e) {
              console.error('Error parsing metadata:', e);
              metadata = consultation.metadata;
            }
          }

          return {
            ...consultation,
            metadata: metadata
          };
        });

        setConsultations(consultationsWithParsedMetadata);
      } catch (error) {
        console.error('Error fetching consultations:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    // Fetch custom columns
    const fetchCustomColumns = async () => {
      try {
        // Check if attorney exists
        if (!attorney || !attorney.id) {
          console.log('Attorney object is null or undefined');
          setCustomColumns([]);
          return;
        }

        const { data, error } = await supabase
          .from('custom_columns')
          .select('*')
          .eq('attorney_id', attorney.id)
          .order('created_at', { ascending: true });

        if (error) throw error;

        setCustomColumns(data || []);
      } catch (error) {
        console.error('Error fetching custom columns:', error);
      }
    };

    fetchConsultations();
    fetchCustomColumns();
  }, [attorney]);

  // Close action menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showActionMenu && actionMenuRefs.current[showActionMenu]) {
        const menuElement = actionMenuRefs.current[showActionMenu];
        if (menuElement && !menuElement.contains(event.target)) {
          setShowActionMenu(null);
        }
      }
    };

    if (showActionMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showActionMenu]);

  // Filter consultations based on search term
  const filteredConsultations = consultations.filter(consultation => {
    if (!searchTerm) return true;

    const searchString = searchTerm.toLowerCase();
    return (
      (consultation.client_name && consultation.client_name.toLowerCase().includes(searchString)) ||
      (consultation.client_email && consultation.client_email?.toLowerCase().includes(searchString)) ||
      (consultation.client_phone && consultation.client_phone?.includes(searchString)) ||
      (consultation.summary && consultation.summary.toLowerCase().includes(searchString)) ||
      (consultation.status && consultation.status.toLowerCase().includes(searchString))
    );
  });

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Toggle view mode
  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Open modal to add a new custom column
  const handleAddColumn = () => {
    // Check if attorney exists and has a valid ID
    if (!attorney || !attorney.id) {
      alert('Attorney information is missing. Please refresh the page or contact support.');
      return;
    }

    setEditingColumn(null);
    setNewColumn({
      name: '',
      display_name: '',
      field_type: 'text',
      source: 'metadata',
      source_path: '',
      default_value: '',
      options: [],
      is_visible: true,
      display_order: customColumns.length
    });
    setIsAddingColumn(true);
    setShowColumnModal(true);
  };

  // Open modal to edit an existing custom column
  const handleEditColumn = (column) => {
    // Check if attorney exists and has a valid ID
    if (!attorney || !attorney.id) {
      alert('Attorney information is missing. Please refresh the page or contact support.');
      return;
    }

    setEditingColumn(column);
    setNewColumn({
      ...column,
      options: column.options || []
    });
    setIsAddingColumn(false);
    setShowColumnModal(true);
  };

  // Save a custom column (new or edited)
  const handleSaveColumn = async () => {
    try {
      // Validate inputs
      if (!newColumn.name || !newColumn.display_name) {
        alert('Name and Display Name are required');
        return;
      }

      // Check if attorney exists
      if (!attorney || !attorney.id) {
        alert('Attorney information is missing. Please refresh the page or contact support.');
        return;
      }

      // Format column data
      const columnData = {
        ...newColumn,
        attorney_id: attorney.id,
        options: Array.isArray(newColumn.options) ? newColumn.options : []
      };

      let result;

      if (isAddingColumn) {
        // Insert new column
        const { data, error } = await supabase
          .from('custom_columns')
          .insert([columnData])
          .select();

        if (error) throw error;
        result = data[0];
      } else {
        // Update existing column
        const { data, error } = await supabase
          .from('custom_columns')
          .update(columnData)
          .eq('id', editingColumn.id)
          .select();

        if (error) throw error;
        result = data[0];
      }

      // Update local state
      if (isAddingColumn) {
        setCustomColumns([...customColumns, result]);
      } else {
        setCustomColumns(customColumns.map(col =>
          col.id === result.id ? result : col
        ));
      }

      // Close modal
      setShowColumnModal(false);
    } catch (error) {
      console.error('Error saving custom column:', error);
      alert(`Error saving column: ${error.message}`);
    }
  };

  // Delete a custom column
  const handleDeleteColumn = async (columnId) => {
    // Check if attorney exists and has a valid ID
    if (!attorney || !attorney.id) {
      alert('Attorney information is missing. Please refresh the page or contact support.');
      return;
    }

    if (!confirm('Are you sure you want to delete this column?')) return;

    try {
      const { error } = await supabase
        .from('custom_columns')
        .delete()
        .eq('id', columnId);

      if (error) throw error;

      // Update local state
      setCustomColumns(customColumns.filter(col => col.id !== columnId));
    } catch (error) {
      console.error('Error deleting custom column:', error);
      alert(`Error deleting column: ${error.message}`);
    }
  };

  // Get value from consultation data based on custom column configuration
  const getColumnValue = (consultation, column) => {
    try {
      if (column.source === 'metadata') {
        // Extract from metadata using source_path
        if (consultation.metadata && column.source_path) {
          const path = column.source_path.split('.');
          let value = consultation.metadata;

          for (const key of path) {
            if (value === undefined || value === null) break;
            value = value[key];
          }

          return value !== undefined ? value : column.default_value || '';
        }
      } else if (column.source === 'transcript') {
        // For transcript-based fields (e.g., extracted from transcript text)
        return consultation[column.name] || column.default_value || '';
      } else {
        // For direct fields
        return consultation[column.name] || column.default_value || '';
      }

      return column.default_value || '';
    } catch (error) {
      console.error(`Error getting value for column ${column.name}:`, error);
      return column.default_value || '';
    }
  };

  // Get value from consultation data based on custom field configuration
  const getCustomFieldValue = (consultation, field) => {
    try {
      // Try to get value from structured data first
      if (consultation.metadata?.structured_data) {
        const value = consultation.metadata.structured_data[field.name];
        if (value !== undefined && value !== null) {
          return value;
        }
      }

      // Try to get value from metadata
      if (consultation.metadata) {
        const value = consultation.metadata[field.name];
        if (value !== undefined && value !== null) {
          return value;
        }
      }

      // Try to get value directly from consultation
      const value = consultation[field.name];
      if (value !== undefined && value !== null) {
        return value;
      }

      // Return empty string if no value found
      return '';
    } catch (error) {
      console.error(`Error getting value for custom field ${field.name}:`, error);
      return '';
    }
  };

  // Handle action menu toggle
  const handleActionMenuToggle = (consultationId) => {
    setShowActionMenu(showActionMenu === consultationId ? null : consultationId);
  };

  // Handle call client action
  const handleCallClient = async (consultation) => {
    if (!consultation.client_phone) {
      alert('No phone number available for this client.');
      return;
    }

    if (!attorney?.vapi_assistant_id) {
      alert('No voice assistant configured. Please configure your assistant in the Agent tab.');
      return;
    }

    try {
      // Import the Vapi service
      const { vapiMcpService } = await import('../../services/vapiMcpService');

      // Ensure connection
      await vapiMcpService.ensureConnection();

      // Create the call
      const call = await vapiMcpService.createCall(
        attorney.vapi_assistant_id,
        consultation.client_phone,
        {}
      );

      alert(`Call initiated successfully! Call ID: ${call.id}`);
      setShowActionMenu(null); // Close menu
    } catch (error) {
      console.error('Error making call:', error);
      alert(`Failed to make call: ${error.message}`);
    }
  };

  // Handle schedule call action
  const handleScheduleCall = (consultation) => {
    if (!consultation.client_phone) {
      alert('No phone number available for this client.');
      return;
    }

    // For now, just show an alert. In the future, this could open a scheduling modal
    alert('Call scheduling feature coming soon! For now, you can use the Calls tab to schedule calls.');
    setShowActionMenu(null); // Close menu
  };

  // Handle update consultation status
  const handleUpdateStatus = async (consultation, newStatus) => {
    try {
      const { error } = await supabase
        .from('consultations')
        .update({ status: newStatus })
        .eq('id', consultation.id);

      if (error) throw error;

      // Update local state
      setConsultations(consultations.map(c =>
        c.id === consultation.id ? { ...c, status: newStatus } : c
      ));

      setShowActionMenu(null); // Close menu
    } catch (error) {
      console.error('Error updating consultation status:', error);
      alert(`Failed to update status: ${error.message}`);
    }
  };

  // Handle consultation click to open Brief modal
  const handleConsultationClick = (consultation) => {
    setSelectedConsultation(consultation);
    setShowBriefModal(true);
  };

  // Batch selection handlers
  const handleSelectConsultation = (consultationId, event) => {
    event.stopPropagation();
    const newSelected = new Set(selectedConsultations);
    if (newSelected.has(consultationId)) {
      newSelected.delete(consultationId);
    } else {
      newSelected.add(consultationId);
    }
    setSelectedConsultations(newSelected);
    setShowBatchActions(newSelected.size > 0);
  };

  const handleSelectAll = (event) => {
    event.stopPropagation();
    if (selectedConsultations.size === filteredConsultations.length) {
      setSelectedConsultations(new Set());
      setShowBatchActions(false);
    } else {
      setSelectedConsultations(new Set(filteredConsultations.map(c => c.id)));
      setShowBatchActions(true);
    }
  };

  const clearSelection = () => {
    setSelectedConsultations(new Set());
    setShowBatchActions(false);
  };

  // Workflow action handlers
  const handleWorkflowAction = async (consultation, action) => {
    try {
      const updates = { workflow_stage: action };

      // Add specific updates based on action
      switch (action) {
        case 'qualify':
          updates.status = 'in-progress';
          break;
        case 'intake':
          updates.status = 'in-progress';
          break;
        case 'conflict-check':
          updates.status = 'in-progress';
          break;
        case 'collect-info':
          updates.status = 'in-progress';
          break;
        case 'refer':
          updates.status = 'completed';
          break;
        case 'draft':
          updates.status = 'in-progress';
          break;
        case 'review':
          updates.status = 'in-progress';
          break;
        case 'research':
          updates.status = 'in-progress';
          break;
        case 'file':
          updates.status = 'completed';
          break;
        case 'forms':
          updates.status = 'in-progress';
          break;
        default:
          break;
      }

      const { error } = await supabase
        .from('consultations')
        .update(updates)
        .eq('id', consultation.id);

      if (error) throw error;

      // Update local state
      setConsultations(consultations.map(c =>
        c.id === consultation.id ? { ...c, ...updates } : c
      ));

      setShowActionMenu(null);
    } catch (error) {
      console.error('Error updating consultation workflow:', error);
      alert(`Failed to update workflow: ${error.message}`);
    }
  };

  // Batch workflow actions
  const handleBatchWorkflowAction = async (action) => {
    if (selectedConsultations.size === 0) return;

    try {
      const updates = { workflow_stage: action };

      switch (action) {
        case 'qualify':
          updates.status = 'in-progress';
          break;
        case 'intake':
          updates.status = 'in-progress';
          break;
        case 'conflict-check':
          updates.status = 'in-progress';
          break;
        case 'collect-info':
          updates.status = 'in-progress';
          break;
        case 'refer':
          updates.status = 'completed';
          break;
        case 'draft':
          updates.status = 'in-progress';
          break;
        case 'review':
          updates.status = 'in-progress';
          break;
        case 'research':
          updates.status = 'in-progress';
          break;
        case 'file':
          updates.status = 'completed';
          break;
        case 'forms':
          updates.status = 'in-progress';
          break;
        default:
          break;
      }

      const { error } = await supabase
        .from('consultations')
        .update(updates)
        .in('id', Array.from(selectedConsultations));

      if (error) throw error;

      // Update local state
      setConsultations(consultations.map(c =>
        selectedConsultations.has(c.id) ? { ...c, ...updates } : c
      ));

      clearSelection();
    } catch (error) {
      console.error('Error batch updating consultations:', error);
      alert(`Failed to batch update: ${error.message}`);
    }
  };

  // Handle toggle column visibility
  const handleToggleColumnVisibility = async (columnId, isVisible) => {
    try {
      const { error } = await supabase
        .from('custom_columns')
        .update({ is_visible: isVisible })
        .eq('id', columnId);

      if (error) throw error;

      // Update local state
      setCustomColumns(customColumns.map(col =>
        col.id === columnId ? { ...col, is_visible: isVisible } : col
      ));
    } catch (error) {
      console.error('Error updating column visibility:', error);
      alert(`Failed to update column visibility: ${error.message}`);
    }
  };

  // Handle reorder columns
  const handleReorderColumns = async (draggedColumnId, targetColumnId, position) => {
    try {
      // Get the columns to reorder
      const draggedColumn = customColumns.find(col => col.id === draggedColumnId);
      const targetColumn = customColumns.find(col => col.id === targetColumnId);

      if (!draggedColumn || !targetColumn) return;

      // Calculate new display orders
      const sortedColumns = [...customColumns].sort((a, b) => a.display_order - b.display_order);
      const updates = [];

      if (position === 'before') {
        // Move dragged column before target
        const newOrder = targetColumn.display_order;
        updates.push({ id: draggedColumnId, display_order: newOrder });

        // Shift other columns
        sortedColumns.forEach(col => {
          if (col.id !== draggedColumnId && col.display_order >= newOrder) {
            updates.push({ id: col.id, display_order: col.display_order + 1 });
          }
        });
      } else {
        // Move dragged column after target
        const newOrder = targetColumn.display_order + 1;
        updates.push({ id: draggedColumnId, display_order: newOrder });

        // Shift other columns
        sortedColumns.forEach(col => {
          if (col.id !== draggedColumnId && col.display_order >= newOrder) {
            updates.push({ id: col.id, display_order: col.display_order + 1 });
          }
        });
      }

      // Update database
      for (const update of updates) {
        const { error } = await supabase
          .from('custom_columns')
          .update({ display_order: update.display_order })
          .eq('id', update.id);

        if (error) throw error;
      }

      // Refresh columns
      const { data, error } = await supabase
        .from('custom_columns')
        .select('*')
        .eq('attorney_id', attorney.id)
        .order('display_order', { ascending: true });

      if (error) throw error;
      setCustomColumns(data || []);
    } catch (error) {
      console.error('Error reordering columns:', error);
      alert(`Failed to reorder columns: ${error.message}`);
    }
  };

  // Handle bulk delete columns
  const handleBulkDeleteColumns = async (columnIds) => {
    if (!confirm(`Are you sure you want to delete ${columnIds.length} column(s)? This action cannot be undone.`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('custom_columns')
        .delete()
        .in('id', columnIds);

      if (error) throw error;

      // Update local state
      setCustomColumns(customColumns.filter(col => !columnIds.includes(col.id)));
    } catch (error) {
      console.error('Error deleting columns:', error);
      alert(`Failed to delete columns: ${error.message}`);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className="consultations-tab">
        <h2>Consultations</h2>
        <div className="loading-spinner"></div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="consultations-tab">
        <h2>Consultations</h2>
        <div className="alert alert-error">
          <p>Error loading consultations: {error}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="consultations-tab">
      <h2>Briefs</h2>
      <p className="tab-description">
        View and manage your client briefs. Track client information and consultation history.
      </p>

      {/* Consultation Statistics */}
      <ConsultationStats attorney={attorney} />

      <div className="dashboard-card">
        <div className="consultations-header">
          <div className="search-filter">
            <div className="search-box">
              <label htmlFor="consultations-search" className="sr-only">Search consultations</label>
              <FaSearch className="search-icon" />
              <input
                type="text"
                id="consultations-search"
                placeholder="Search consultations..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="form-control"
                aria-label="Search consultations"
              />
            </div>
            <button className="filter-button">
              <FaFilter />
              <span>Filter</span>
            </button>
          </div>

          <div className="view-options">
            <button
              className={`view-option ${viewMode === 'table' ? 'active' : ''}`}
              onClick={() => handleViewModeChange('table')}
              title="Table View"
            >
              <FaList />
            </button>
            <button
              className={`view-option ${viewMode === 'card' ? 'active' : ''}`}
              onClick={() => handleViewModeChange('card')}
              title="Card View"
            >
              <FaThLarge />
            </button>
            <button
              className={`view-option ${viewMode === 'map' ? 'active' : ''}`}
              onClick={() => handleViewModeChange('map')}
              title="Map View"
            >
              <FaMapMarkedAlt />
            </button>
          </div>
        </div>

        {/* Always show the table structure */}
        {viewMode === 'table' && (
              <div className="consultations-table">
                {/* Batch Actions Bar */}
                {showBatchActions && (
                  <div className="batch-actions-bar">
                    <div className="batch-actions-left">
                      <div className="batch-info">
                        <span>{selectedConsultations.size} selected</span>
                        <button className="clear-selection-btn" onClick={clearSelection}>
                          Clear Selection
                        </button>
                      </div>
                      <div className="batch-workflow-actions">
                        {/* Pre-engagement workflow actions */}
                        <div className="workflow-group pre-engagement">
                          <button
                            className="batch-action-btn qualify pre-engagement"
                            onClick={() => handleBatchWorkflowAction('qualify')}
                          >
                            <FaUserCheck /> Qualify
                          </button>
                          <button
                            className="batch-action-btn intake pre-engagement"
                            onClick={() => handleBatchWorkflowAction('intake')}
                          >
                            <FaClipboardCheck /> Intake
                          </button>
                          <button
                            className="batch-action-btn conflict-check pre-engagement"
                            onClick={() => handleBatchWorkflowAction('conflict-check')}
                          >
                            <FaExclamationTriangle /> Conflict Check
                          </button>
                          <button
                            className="batch-action-btn collect-info pre-engagement"
                            onClick={() => handleBatchWorkflowAction('collect-info')}
                          >
                            <FaInfoCircle /> Collect Info
                          </button>
                          <button
                            className="batch-action-btn refer pre-engagement"
                            onClick={() => handleBatchWorkflowAction('refer')}
                          >
                            <FaShareAlt /> Refer
                          </button>
                        </div>

                        {/* Visual separator */}
                        <div className="workflow-separator"></div>

                        {/* Client engagement workflow actions */}
                        <div className="workflow-group client-engagement">
                          <button
                            className="batch-action-btn draft client-engagement"
                            onClick={() => handleBatchWorkflowAction('draft')}
                          >
                            <FaFileAlt /> Draft
                          </button>
                          <button
                            className="batch-action-btn review client-engagement"
                            onClick={() => handleBatchWorkflowAction('review')}
                          >
                            <FaSearchPlus /> Review
                          </button>
                          <button
                            className="batch-action-btn research client-engagement"
                            onClick={() => handleBatchWorkflowAction('research')}
                          >
                            <FaGavel /> Research
                          </button>
                          <button
                            className="batch-action-btn file client-engagement"
                            onClick={() => handleBatchWorkflowAction('file')}
                          >
                            <FaUniversity /> File
                          </button>
                          <button
                            className="batch-action-btn forms client-engagement"
                            onClick={() => handleBatchWorkflowAction('forms')}
                          >
                            <FaWpforms /> Forms
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="table-header-actions">
                  <div className="table-management-buttons">
                    <button className="table-mgmt-btn import" onClick={() => setShowImportModal(true)}>
                      <FaFileImport /> Import CSV/Excel
                    </button>
                    <button className="table-mgmt-btn add-column" onClick={handleAddColumn}>
                      <FaPlus /> Add Custom Column
                    </button>
                    <button className="table-mgmt-btn manage" onClick={() => setShowManageColumnsModal(true)}>
                      <FaCog /> Manage Columns
                    </button>
                  </div>
                </div>
                <table>
                  <thead>
                    <tr>
                      <th className="select-column">
                        <button
                          className="select-all-btn"
                          onClick={handleSelectAll}
                          title={selectedConsultations.size === filteredConsultations.length ? "Deselect All" : "Select All"}
                        >
                          {selectedConsultations.size === filteredConsultations.length ?
                            <FaCheckSquare /> : <FaSquare />
                          }
                        </button>
                      </th>
                      <th>Date</th>
                      <th>Client</th>
                      <th>Contact</th>
                      <th>Summary</th>
                      <th>Success Evaluation</th>
                      <th>Structured Data</th>
                      {/* Show custom fields from attorney configuration */}
                      {attorney?.custom_fields && Array.isArray(attorney.custom_fields) ?
                        attorney.custom_fields.map((field, index) => (
                          <th key={`custom-field-${index}`} className="custom-column custom-field-column">
                            <div className="column-header">
                              <span>{field.label || field.name}</span>
                              <span className="field-source-badge">Custom Field</span>
                            </div>
                          </th>
                        )) : null
                      }
                      {/* Render additional custom column headers */}
                      {customColumns
                        .filter(col => col.is_visible)
                        .sort((a, b) => a.display_order - b.display_order)
                        .map(column => (
                          <th key={column.id} className="custom-column">
                            <div className="column-header">
                              <span>{column.display_name}</span>
                              <div className="column-actions">
                                <button
                                  className="column-action-button"
                                  onClick={() => handleEditColumn(column)}
                                  title="Edit Column"
                                >
                                  <FaEdit />
                                </button>
                                <button
                                  className="column-action-button"
                                  onClick={() => handleDeleteColumn(column.id)}
                                  title="Delete Column"
                                >
                                  <FaTrash />
                                </button>
                              </div>
                            </div>
                          </th>
                        ))}
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredConsultations.length > 0 ? (
                      filteredConsultations.map((consultation) => (
                        <tr key={consultation.id} className={selectedConsultations.has(consultation.id) ? 'selected-row' : ''}>
                          <td className="select-column">
                            <button
                              className="select-row-btn"
                              onClick={(e) => handleSelectConsultation(consultation.id, e)}
                              title={selectedConsultations.has(consultation.id) ? "Deselect" : "Select"}
                            >
                              {selectedConsultations.has(consultation.id) ?
                                <FaCheckSquare /> : <FaSquare />
                              }
                            </button>
                          </td>
                          <td
                            className="consultation-row-clickable"
                            onClick={() => handleConsultationClick(consultation)}
                            style={{ cursor: 'pointer' }}
                          >
                            {formatDate(consultation.created_at)}
                          </td>
                          <td
                            className="consultation-row-clickable"
                            onClick={() => handleConsultationClick(consultation)}
                            style={{ cursor: 'pointer' }}
                          >
                            {consultation.client_name || 'Anonymous'}
                          </td>
                          <td>
                            {consultation.client_email && (
                              <a href={`mailto:${consultation.client_email}`} className="contact-link">
                                <FaEnvelope />
                              </a>
                            )}
                            {consultation.client_phone && (
                              <a href={`tel:${consultation.client_phone}`} className="contact-link">
                                <FaPhone />
                              </a>
                            )}
                          </td>
                          <td
                            className="summary-cell consultation-row-clickable"
                            onClick={() => handleConsultationClick(consultation)}
                            style={{ cursor: 'pointer' }}
                          >
                            {consultation.summary || 'No summary available'}
                          </td>
                          <td
                            className="success-evaluation-cell consultation-row-clickable"
                            onClick={() => handleConsultationClick(consultation)}
                            style={{ cursor: 'pointer' }}
                          >
                            {consultation.metadata?.success_evaluation || 'Not evaluated'}
                          </td>
                          <td
                            className="structured-data-cell consultation-row-clickable"
                            onClick={() => handleConsultationClick(consultation)}
                            style={{ cursor: 'pointer' }}
                          >
                            {consultation.metadata?.structured_data ?
                              Object.keys(consultation.metadata.structured_data).length + ' fields' :
                              'No data'
                            }
                          </td>
                          {/* Render custom field values */}
                          {attorney?.custom_fields && Array.isArray(attorney.custom_fields) ?
                            attorney.custom_fields.map((field, index) => (
                              <td key={`consultation-field-${consultation.id}-${index}`} className="custom-column">
                                {getCustomFieldValue(consultation, field)}
                              </td>
                            )) : null
                          }
                          {/* Render additional custom column values */}
                          {customColumns
                            .filter(col => col.is_visible)
                            .sort((a, b) => a.display_order - b.display_order)
                            .map(column => (
                              <td key={column.id} className="custom-column">
                                {getColumnValue(consultation, column)}
                              </td>
                            ))}
                          <td>
                            <div className="action-buttons">
                              <button
                                className="action-button"
                                title="Schedule Meeting"
                                onClick={() => handleScheduleCall(consultation)}
                              >
                                <FaCalendarAlt />
                              </button>
                              <div
                                className="action-dropdown"
                                ref={el => actionMenuRefs.current[consultation.id] = el}
                              >
                                <button
                                  className="action-button"
                                  title="More Options"
                                  onClick={() => handleActionMenuToggle(consultation.id)}
                                >
                                  <FaEllipsisH />
                                </button>
                                {showActionMenu === consultation.id && (
                                  <div className="action-menu">
                                    {/* Communication Actions */}
                                    {consultation.client_phone && (
                                      <button
                                        className="action-menu-item"
                                        onClick={() => handleCallClient(consultation)}
                                      >
                                        <FaPhone /> Call Now
                                      </button>
                                    )}
                                    <button
                                      className="action-menu-item"
                                      onClick={() => handleScheduleCall(consultation)}
                                    >
                                      <FaCalendarAlt /> Schedule Call
                                    </button>
                                    <button
                                      className="action-menu-item"
                                      onClick={() => handleScheduleCall(consultation)}
                                    >
                                      <FaCalendarPlus /> Start/Schedule Session
                                    </button>

                                    <div className="action-menu-divider"></div>

                                    {/* Pre-transition Workflow Actions */}
                                    <div className="action-menu-section-title">Pre-Engagement</div>
                                    <button
                                      className="action-menu-item workflow-action pre-transition"
                                      onClick={() => handleWorkflowAction(consultation, 'qualify')}
                                    >
                                      <FaUserCheck /> Qualify
                                    </button>
                                    <button
                                      className="action-menu-item workflow-action pre-transition"
                                      onClick={() => handleWorkflowAction(consultation, 'intake')}
                                    >
                                      <FaClipboardCheck /> Intake
                                    </button>
                                    <button
                                      className="action-menu-item workflow-action pre-transition"
                                      onClick={() => handleWorkflowAction(consultation, 'conflict-check')}
                                    >
                                      <FaExclamationTriangle /> Conflict Check
                                    </button>
                                    <button
                                      className="action-menu-item workflow-action pre-transition"
                                      onClick={() => handleWorkflowAction(consultation, 'collect-info')}
                                    >
                                      <FaInfoCircle /> Collect Info
                                    </button>
                                    <button
                                      className="action-menu-item workflow-action pre-transition"
                                      onClick={() => handleWorkflowAction(consultation, 'refer')}
                                    >
                                      <FaShareAlt /> Refer
                                    </button>

                                    <div className="action-menu-divider"></div>

                                    {/* Client Engagement Workflow Actions */}
                                    <div className="action-menu-section-title">Client Engagement</div>
                                    <button
                                      className="action-menu-item workflow-action client-engagement"
                                      onClick={() => handleWorkflowAction(consultation, 'draft')}
                                    >
                                      <FaFileAlt /> Draft
                                    </button>
                                    <button
                                      className="action-menu-item workflow-action client-engagement"
                                      onClick={() => handleWorkflowAction(consultation, 'review')}
                                    >
                                      <FaSearchPlus /> Review
                                    </button>
                                    <button
                                      className="action-menu-item workflow-action client-engagement"
                                      onClick={() => handleWorkflowAction(consultation, 'research')}
                                    >
                                      <FaGavel /> Research
                                    </button>
                                    <button
                                      className="action-menu-item workflow-action client-engagement"
                                      onClick={() => handleWorkflowAction(consultation, 'file')}
                                    >
                                      <FaUniversity /> File
                                    </button>
                                    <button
                                      className="action-menu-item workflow-action client-engagement"
                                      onClick={() => handleWorkflowAction(consultation, 'forms')}
                                    >
                                      <FaWpforms /> Forms
                                    </button>

                                    <div className="action-menu-divider"></div>

                                    {/* Status Actions */}
                                    <div className="action-menu-section-title">Status</div>
                                    <button
                                      className="action-menu-item"
                                      onClick={() => handleUpdateStatus(consultation, 'in-progress')}
                                    >
                                      Mark In Progress
                                    </button>
                                    <button
                                      className="action-menu-item"
                                      onClick={() => handleUpdateStatus(consultation, 'completed')}
                                    >
                                      Mark Completed
                                    </button>
                                    <button
                                      className="action-menu-item"
                                      onClick={() => handleUpdateStatus(consultation, 'follow-up')}
                                    >
                                      Needs Follow-up
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr className="empty-row">
                        <td colSpan={
                          6 +
                          (attorney?.custom_fields ? attorney.custom_fields.length : 0) +
                          customColumns.filter(col => col.is_visible).length +
                          1
                        } className="empty-cell">
                          <div className="empty-state-inline">
                            <FaPhone className="empty-icon" />
                            <div className="empty-content">
                              <h4>No consultations yet</h4>
                              <p>When clients interact with your AI assistant, their consultations will appear here with the custom fields you've configured.</p>
                              {(!attorney?.custom_fields || attorney.custom_fields.length === 0) && customColumns.length === 0 && (
                                <p className="setup-hint">
                                  <strong>Tip:</strong> Go to the <strong>Custom Fields</strong> tab to configure what data to collect from clients.
                                </p>
                              )}
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

        {viewMode === 'card' && (
          <div className="consultations-cards">
            {filteredConsultations.length > 0 ? (
              filteredConsultations.map((consultation) => (
                  <div key={consultation.id} className="consultation-card">
                    <div className="card-header">
                      <div className="client-info">
                        <div className="client-avatar">
                          {(consultation.client_name || 'A').charAt(0).toUpperCase()}
                        </div>
                        <div className="client-details">
                          <h3>{consultation.client_name || 'Anonymous Client'}</h3>
                          <span className="consultation-date">{formatDate(consultation.created_at)}</span>
                        </div>
                      </div>
                      <div className="consultation-status">
                        <span className={`status-badge ${consultation.status || 'completed'}`}>
                          {consultation.status || 'Completed'}
                        </span>
                      </div>
                    </div>

                    <div className="card-body">
                      <div className="consultation-summary">
                        <h4>Summary</h4>
                        <p>{consultation.summary || 'No summary available'}</p>
                      </div>

                      {consultation.practice_area && (
                        <div className="practice-area">
                          <span className="practice-area-tag">{consultation.practice_area}</span>
                        </div>
                      )}

                      <div className="consultation-metadata">
                        {consultation.metadata && (
                          <>
                            {consultation.metadata.call_duration && (
                              <div className="metadata-item">
                                <span className="metadata-label">Duration:</span>
                                <span className="metadata-value">{Math.round(consultation.metadata.call_duration / 60)}m {consultation.metadata.call_duration % 60}s</span>
                              </div>
                            )}
                            {consultation.metadata.end_reason && (
                              <div className="metadata-item">
                                <span className="metadata-label">End Reason:</span>
                                <span className="metadata-value">{consultation.metadata.end_reason}</span>
                              </div>
                            )}
                          </>
                        )}
                      </div>

                      <div className="contact-info">
                        {consultation.client_email && (
                          <div className="contact-item">
                            <FaEnvelope className="contact-icon" />
                            <span>{consultation.client_email}</span>
                          </div>
                        )}
                        {consultation.client_phone && (
                          <div className="contact-item">
                            <FaPhone className="contact-icon" />
                            <span>{consultation.client_phone}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="card-footer">
                      <button className="card-action-button primary">
                        <FaCalendarAlt />
                        <span>Schedule Follow-up</span>
                      </button>
                      <button className="card-action-button secondary">
                        <FaEnvelope />
                        <span>Send Email</span>
                      </button>
                      <button className="card-action-button secondary">
                        <FaEllipsisH />
                      </button>
                    </div>
                  </div>
                ))
            ) : (
              <div className="empty-state-cards">
                <div className="empty-state-inline">
                  <FaPhone className="empty-icon" />
                  <div className="empty-content">
                    <h4>No consultations yet</h4>
                    <p>When clients interact with your AI assistant, their consultations will appear here as cards with the custom fields you've configured.</p>
                    {(!attorney?.custom_fields || attorney.custom_fields.length === 0) && customColumns.length === 0 && (
                      <p className="setup-hint">
                        <strong>Tip:</strong> Go to the <strong>Custom Fields</strong> tab to configure what data to collect from clients.
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {viewMode === 'map' && (
          <div className="map-view">
            <div className="map-placeholder">
              <p>Map view is coming soon. This will display your consultations geographically.</p>
            </div>
          </div>
        )}
      </div>






      {/* Custom Column Modal */}
      {showColumnModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>{isAddingColumn ? 'Add Custom Column' : 'Edit Column'}</h2>

            <div className="form-group">
              <label htmlFor="column-name">Column Name (Internal)</label>
              <input
                id="column-name"
                type="text"
                value={newColumn.name}
                onChange={(e) => setNewColumn({...newColumn, name: e.target.value})}
                placeholder="e.g., client_age"
              />
            </div>

            <div className="form-group">
              <label htmlFor="display-name">Display Name</label>
              <input
                id="display-name"
                type="text"
                value={newColumn.display_name}
                onChange={(e) => setNewColumn({...newColumn, display_name: e.target.value})}
                placeholder="e.g., Client Age"
              />
            </div>

            <div className="form-group">
              <label htmlFor="field-type">Field Type</label>
              <select
                id="field-type"
                value={newColumn.field_type}
                onChange={(e) => setNewColumn({...newColumn, field_type: e.target.value})}
              >
                <option value="text">Text</option>
                <option value="number">Number</option>
                <option value="date">Date</option>
                <option value="boolean">Yes/No</option>
                <option value="select">Select (Dropdown)</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="source">Data Source</label>
              <select
                id="source"
                value={newColumn.source}
                onChange={(e) => setNewColumn({...newColumn, source: e.target.value})}
              >
                <option value="metadata">Metadata</option>
                <option value="transcript">Transcript</option>
                <option value="custom">Custom</option>
              </select>
            </div>

            {newColumn.source === 'metadata' && (
              <div className="form-group">
                <label htmlFor="source-path">Metadata Path</label>
                <input
                  id="source-path"
                  type="text"
                  value={newColumn.source_path}
                  onChange={(e) => setNewColumn({...newColumn, source_path: e.target.value})}
                  placeholder="e.g., client.age or structured_data.age"
                />
                <small>Use dot notation to access nested properties</small>
              </div>
            )}

            <div className="form-group">
              <label htmlFor="default-value">Default Value</label>
              <input
                id="default-value"
                type="text"
                value={newColumn.default_value}
                onChange={(e) => setNewColumn({...newColumn, default_value: e.target.value})}
                placeholder="Value to show if data is missing"
              />
            </div>

            {newColumn.field_type === 'select' && (
              <div className="form-group">
                <label htmlFor="options-list">Options</label>
                <div className="options-list" id="options-list">
                  {Array.isArray(newColumn.options) && newColumn.options.map((option, index) => (
                    <div key={index} className="option-item">
                      <input
                        type="text"
                        id={`option-${index}`}
                        value={option}
                        onChange={(e) => {
                          const newOptions = [...newColumn.options];
                          newOptions[index] = e.target.value;
                          setNewColumn({...newColumn, options: newOptions});
                        }}
                        aria-label={`Option ${index + 1}`}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const newOptions = [...newColumn.options];
                          newOptions.splice(index, 1);
                          setNewColumn({...newColumn, options: newOptions});
                        }}
                        aria-label={`Remove option ${index + 1}`}
                      >
                        <FaTrash />
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    className="add-option-button"
                    onClick={() => {
                      const options = Array.isArray(newColumn.options) ? [...newColumn.options, ''] : [''];
                      setNewColumn({...newColumn, options});
                    }}
                  >
                    <FaPlus /> Add Option
                  </button>
                </div>
              </div>
            )}

            <div className="form-group">
              <label htmlFor="is-visible">
                <input
                  id="is-visible"
                  type="checkbox"
                  checked={newColumn.is_visible}
                  onChange={(e) => setNewColumn({...newColumn, is_visible: e.target.checked})}
                />
                Visible in Table
              </label>
            </div>

            <div className="modal-actions">
              <button type="button" className="cancel-button" onClick={() => setShowColumnModal(false)}>
                Cancel
              </button>
              <button type="button" className="save-button" onClick={handleSaveColumn}>
                Save Column
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSV Import Modal */}
      {showImportModal && (
        <CSVImportModal
          isOpen={showImportModal}
          onClose={() => setShowImportModal(false)}
          attorney={attorney}
          onImportComplete={() => {
            setShowImportModal(false);
            // Refresh consultations after import
            const fetchConsultations = async () => {
              try {
                const { data, error } = await supabase
                  .from('consultations')
                  .select('*')
                  .eq('attorney_id', attorney.id)
                  .order('created_at', { ascending: false });

                if (error) throw error;
                setConsultations(data || []);
              } catch (error) {
                console.error('Error refreshing consultations:', error);
              }
            };
            fetchConsultations();
          }}
        />
      )}

      {/* Manage Columns Modal */}
      {showManageColumnsModal && (
        <ManageColumnsModal
          isOpen={showManageColumnsModal}
          onClose={() => setShowManageColumnsModal(false)}
          columns={[
            // Convert attorney custom fields to column format
            ...(attorney?.custom_fields ? attorney.custom_fields.map((field, index) => ({
              id: `custom-field-${field.name}`,
              name: field.name,
              display_name: field.label || field.name,
              field_type: field.type || 'text',
              source: 'custom_field',
              source_path: field.name,
              is_visible: true,
              display_order: index,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })) : []),
            // Add custom columns from database
            ...customColumns
          ]}
          onToggleVisibility={handleToggleColumnVisibility}
          onDeleteColumn={handleDeleteColumn}
          onBulkDelete={handleBulkDeleteColumns}
          onReorderColumns={handleReorderColumns}
          onEditColumn={(column) => {
            setShowManageColumnsModal(false);
            handleEditColumn(column);
          }}
        />
      )}

      {/* Brief Modal */}
      {showBriefModal && selectedConsultation && (
        <BriefModal
          consultation={selectedConsultation}
          attorney={attorney}
          isOpen={showBriefModal}
          onClose={() => {
            setShowBriefModal(false);
            setSelectedConsultation(null);
          }}
        />
      )}
    </div>
  );
};

export default ConsultationsTab;
