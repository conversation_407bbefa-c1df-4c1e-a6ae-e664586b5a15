.custom-fields-tab {
  width: 100%;
}

.custom-field-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  position: relative;
}

.advanced-options-toggle {
  margin: 15px 0;
  text-align: right;
}

.advanced-options-toggle button {
  color: #4B74AA;
  text-decoration: underline;
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px 10px;
}

.advanced-options {
  margin-top: 15px;
  padding: 15px;
  background-color: rgba(75, 116, 170, 0.05);
  border-radius: 8px;
  border-left: 3px solid #4B74AA;
}

.custom-field-row .form-group {
  flex: 1;
  min-width: 200px;
}

.custom-field-row .checkbox-group {
  display: flex;
  align-items: center;
  min-width: 100px;
  flex: 0 0 auto;
}

.custom-field-row .checkbox-group input {
  margin-right: 5px;
}

.remove-field-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 5px 10px;
  font-size: 0.8rem;
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: 4px;
  cursor: pointer;
}

.remove-field-btn:hover {
  background-color: rgba(220, 53, 69, 0.2);
}

.field-actions {
  margin: 20px 0;
}

.form-actions {
  margin-top: 30px;
  display: flex;
  align-items: center;
}

.save-btn {
  min-width: 120px;
}

.success-message {
  margin-left: 15px;
  color: #28a745;
  font-weight: 500;
}

.error-message {
  margin-left: 15px;
  color: #dc3545;
  font-weight: 500;
}

.template-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 8px;
}

.template-selector label {
  margin-bottom: 0;
  font-weight: 500;
}

.template-selector select {
  flex: 1;
}

.prompts-section {
  margin-bottom: 30px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 8px;
}

.field-description {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 10px;
}

.card-description {
  margin-bottom: 20px;
  font-size: 1rem;
  color: #495057;
}

/* Button styles */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-primary {
  background-color: #4B74AA;
  color: white;
}

.btn-primary:hover {
  background-color: #3a5d8a;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}
