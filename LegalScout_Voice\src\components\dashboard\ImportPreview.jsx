import React, { useState } from 'react';
import { FaCheck, FaTimes, FaEdit, FaPlus, FaMinus, FaEye, FaEyeSlash } from 'react-icons/fa';
import './ImportPreview.css';

/**
 * Import Preview Component
 * Shows extracted data and lets attorney review/edit before applying
 */
const ImportPreview = ({ extractedData, onConfirm, onCancel, onEdit }) => {
  const [editedData, setEditedData] = useState(extractedData);
  const [expandedSections, setExpandedSections] = useState({
    profile: true,
    agent: false,
    custom: false,
    deduced: false
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleFieldChange = (field, value) => {
    setEditedData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayFieldChange = (field, index, value) => {
    setEditedData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field, defaultValue = '') => {
    setEditedData(prev => ({
      ...prev,
      [field]: [...(prev[field] || []), defaultValue]
    }));
  };

  const removeArrayItem = (field, index) => {
    setEditedData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const handleConfirm = () => {
    onConfirm(editedData);
  };

  return (
    <div className="import-preview-overlay">
      <div className="import-preview-modal">
        <div className="preview-header">
          <h3>Review Imported Data</h3>
          <p>We extracted this information from your website. Review and edit as needed:</p>
          <div className="confidence-indicator">
            <span>Extraction Confidence: </span>
            <div className="confidence-bar">
              <div 
                className="confidence-fill" 
                style={{ width: `${(editedData.extractionConfidence || 0.8) * 100}%` }}
              />
            </div>
            <span>{Math.round((editedData.extractionConfidence || 0.8) * 100)}%</span>
          </div>
        </div>

        <div className="preview-content">
          {/* Profile Information */}
          <div className="preview-section">
            <div 
              className="section-header" 
              onClick={() => toggleSection('profile')}
            >
              <h4>Profile Information</h4>
              {expandedSections.profile ? <FaEyeSlash /> : <FaEye />}
            </div>
            
            {expandedSections.profile && (
              <div className="section-content">
                <div className="field-grid">
                  <div className="field-group">
                    <label>Firm Name</label>
                    <input
                      type="text"
                      value={editedData.firmName || ''}
                      onChange={(e) => handleFieldChange('firmName', e.target.value)}
                    />
                  </div>
                  <div className="field-group">
                    <label>Attorney Name</label>
                    <input
                      type="text"
                      value={editedData.attorneyName || ''}
                      onChange={(e) => handleFieldChange('attorneyName', e.target.value)}
                    />
                  </div>
                  <div className="field-group">
                    <label>Phone</label>
                    <input
                      type="tel"
                      value={editedData.phone || ''}
                      onChange={(e) => handleFieldChange('phone', e.target.value)}
                    />
                  </div>
                  <div className="field-group">
                    <label>Email</label>
                    <input
                      type="email"
                      value={editedData.email || ''}
                      onChange={(e) => handleFieldChange('email', e.target.value)}
                    />
                  </div>
                </div>

                <div className="field-group">
                  <label>Practice Areas</label>
                  <div className="array-field">
                    {(editedData.practiceAreas || []).map((area, index) => (
                      <div key={index} className="array-item">
                        <input
                          type="text"
                          value={area}
                          onChange={(e) => handleArrayFieldChange('practiceAreas', index, e.target.value)}
                        />
                        <button 
                          type="button"
                          onClick={() => removeArrayItem('practiceAreas', index)}
                          className="remove-btn"
                        >
                          <FaMinus />
                        </button>
                      </div>
                    ))}
                    <button 
                      type="button"
                      onClick={() => addArrayItem('practiceAreas', '')}
                      className="add-btn"
                    >
                      <FaPlus /> Add Practice Area
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Agent Configuration */}
          <div className="preview-section">
            <div 
              className="section-header" 
              onClick={() => toggleSection('agent')}
            >
              <h4>Agent Configuration</h4>
              {expandedSections.agent ? <FaEyeSlash /> : <FaEye />}
            </div>
            
            {expandedSections.agent && (
              <div className="section-content">
                <div className="field-group">
                  <label>Welcome Message</label>
                  <textarea
                    value={editedData.welcomeMessage || ''}
                    onChange={(e) => handleFieldChange('welcomeMessage', e.target.value)}
                    rows="3"
                  />
                </div>
                
                <div className="field-group">
                  <label>Button Text</label>
                  <input
                    type="text"
                    value={editedData.buttonText || ''}
                    onChange={(e) => handleFieldChange('buttonText', e.target.value)}
                  />
                </div>

                <div className="color-fields">
                  <div className="field-group">
                    <label>Primary Color</label>
                    <div className="color-input">
                      <input
                        type="color"
                        value={editedData.primaryColor || '#1e40af'}
                        onChange={(e) => handleFieldChange('primaryColor', e.target.value)}
                      />
                      <input
                        type="text"
                        value={editedData.primaryColor || '#1e40af'}
                        onChange={(e) => handleFieldChange('primaryColor', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="field-group">
                    <label>Secondary Color</label>
                    <div className="color-input">
                      <input
                        type="color"
                        value={editedData.secondaryColor || '#3b82f6'}
                        onChange={(e) => handleFieldChange('secondaryColor', e.target.value)}
                      />
                      <input
                        type="text"
                        value={editedData.secondaryColor || '#3b82f6'}
                        onChange={(e) => handleFieldChange('secondaryColor', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Custom Fields Suggestions */}
          <div className="preview-section">
            <div 
              className="section-header" 
              onClick={() => toggleSection('custom')}
            >
              <h4>Suggested Custom Fields</h4>
              {expandedSections.custom ? <FaEyeSlash /> : <FaEye />}
            </div>
            
            {expandedSections.custom && (
              <div className="section-content">
                <p className="section-description">
                  Based on your practice areas, we suggest these intake form fields:
                </p>
                {(editedData.customFields || []).map((field, index) => (
                  <div key={index} className="custom-field-preview">
                    <strong>{field.name}</strong> ({field.type})
                    {field.required && <span className="required">*</span>}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="preview-actions">
          <button 
            className="cancel-btn" 
            onClick={onCancel}
          >
            <FaTimes /> Cancel
          </button>
          <button 
            className="confirm-btn" 
            onClick={handleConfirm}
          >
            <FaCheck /> Apply Configuration
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImportPreview;
