import React, { useState } from 'react';
import { FaPlug, FaExternalLinkAlt, FaCheck, FaLock, FaCalendarAlt, FaFileAlt, FaDatabase, FaEnvelope, FaPhone, FaSms } from 'react-icons/fa';

/**
 * IntegrationsTab component for the attorney dashboard
 * Displays available integrations with third-party services
 */
const IntegrationsTab = ({ attorney }) => {
  // State for UI
  const [activeIntegration, setActiveIntegration] = useState(null);

  // List of available integrations
  const integrations = [
    {
      id: 'clio',
      name: 'Clio',
      description: 'Connect your Clio account to automatically sync client information and case details.',
      icon: <FaDatabase />,
      status: 'coming-soon',
      category: 'crm'
    },
    {
      id: 'calendly',
      name: 'Calendly',
      description: 'Allow clients to schedule appointments directly through your AI assistant.',
      icon: <FaCalendarAlt />,
      status: 'coming-soon',
      category: 'scheduling'
    },
    {
      id: 'docusign',
      name: 'DocuSign',
      description: 'Send documents for electronic signature directly from your dashboard.',
      icon: <FaFileAlt />,
      status: 'coming-soon',
      category: 'documents'
    },
    {
      id: 'gmail',
      name: 'Gmail',
      description: 'Connect your Gmail account to send follow-up emails to potential clients.',
      icon: <FaEnvelope />,
      status: 'coming-soon',
      category: 'communication'
    },
    {
      id: 'twilio',
      name: 'Twilio',
      description: 'Enable SMS notifications and reminders for your clients.',
      icon: <FaSms />,
      status: 'coming-soon',
      category: 'communication'
    },
    {
      id: 'zoom',
      name: 'Zoom',
      description: 'Schedule and start video consultations directly from your dashboard.',
      icon: <FaPhone />,
      status: 'coming-soon',
      category: 'communication'
    }
  ];

  // Group integrations by category
  const groupedIntegrations = integrations.reduce((acc, integration) => {
    if (!acc[integration.category]) {
      acc[integration.category] = [];
    }
    acc[integration.category].push(integration);
    return acc;
  }, {});

  // Category labels
  const categoryLabels = {
    crm: 'CRM & Practice Management',
    scheduling: 'Scheduling & Calendar',
    documents: 'Document Management',
    communication: 'Communication Tools'
  };

  return (
    <div className="integrations-tab">
      <div className="coming-soon-header">
        <h2>Integrations</h2>
        <span className="coming-soon-badge">Coming Soon</span>
      </div>
      <p className="tab-description">
        Connect your LegalScout assistant with your favorite tools and services to streamline your workflow.
      </p>

      <div className="dashboard-card">
        <h3>Your Integrations</h3>
        <div className="empty-state">
          <div className="empty-state-icon">
            <FaPlug />
          </div>
          <h3>No active integrations</h3>
          <p>Connect your favorite tools below to enhance your LegalScout experience.</p>
        </div>
      </div>

      {Object.entries(groupedIntegrations).map(([category, categoryIntegrations]) => (
        <div key={category} className="dashboard-card">
          <h3>{categoryLabels[category]}</h3>
          <div className="integrations-grid">
            {categoryIntegrations.map((integration) => (
              <div
                key={integration.id}
                className={`integration-card ${integration.status === 'coming-soon' ? 'coming-soon' : ''}`}
                onClick={() => setActiveIntegration(integration.id)}
              >
                <div className="integration-logo">
                  {integration.icon}
                </div>
                <div className="integration-info">
                  <h3>{integration.name}</h3>
                  <p>{integration.description}</p>
                </div>
                <div className="integration-status">
                  {integration.status === 'connected' ? (
                    <span className="status-badge connected">
                      <FaCheck /> Connected
                    </span>
                  ) : integration.status === 'coming-soon' ? (
                    <span className="status-badge coming-soon">
                      <FaLock /> Coming Soon
                    </span>
                  ) : (
                    <button className="connect-button">
                      Connect <FaExternalLinkAlt />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      <div className="integration-cta">
        <p>Don't see the integration you need? Let us know what tools you'd like to connect with LegalScout.</p>
        <button className="dashboard-button">
          Request Integration
        </button>
      </div>

      {/* Integration Modal (for future implementation) */}
      {activeIntegration && (
        <div className="integration-modal">
          <div className="modal-content">
            <h3>Connect {integrations.find(i => i.id === activeIntegration)?.name}</h3>
            <p>This integration is coming soon. We'll notify you when it's available.</p>
            <div className="modal-actions">
              <button
                className="dashboard-button secondary"
                onClick={() => setActiveIntegration(null)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IntegrationsTab;
