import React, { useState, useEffect } from 'react';
import { useCallMonitoring } from '../../hooks/useCallMonitoring';
import { vapiMcpService } from '../../services/vapiMcpService';
import './LiveCallMonitor.css';

/**
 * Live Call Monitor Component
 * 
 * Provides real-time monitoring of active calls with attorney intervention capabilities
 */
const LiveCallMonitor = ({ callId, onClose, attorney }) => {
  const [interventionMode, setInterventionMode] = useState(false);
  const [notes, setNotes] = useState('');
  const [callControlUrl, setCallControlUrl] = useState(null);

  // Use the call monitoring hook for real-time updates
  const {
    call: callData,
    transcripts,
    status,
    loading,
    error: monitoringError,
    endCall,
    sendMessage
  } = useCallMonitoring(callId);

  useEffect(() => {
    if (callId) {
      console.log('[LiveCallMonitor] Monitoring call:', callId);
      // Generate call control URL for attorney
      generateCallControlUrl();
    }
  }, [callId]);

  const generateCallControlUrl = async () => {
    try {
      // Generate a secure token for call control
      const token = btoa(JSON.stringify({
        callId,
        attorneyId: attorney?.id,
        timestamp: Date.now(),
        expires: Date.now() + (60 * 60 * 1000) // 1 hour
      }));

      const controlUrl = `${window.location.origin}/call-control?token=${token}`;
      setCallControlUrl(controlUrl);
    } catch (error) {
      console.error('[LiveCallMonitor] Failed to generate call control URL:', error);
    }
  };

  const handleInterventionToggle = async () => {
    try {
      if (!interventionMode) {
        // Start intervention mode
        console.log('[LiveCallMonitor] Starting intervention mode for call:', callId);
        setInterventionMode(true);
        
        // You could add logic here to notify the AI assistant that attorney is monitoring
        // or to enable special intervention features
      } else {
        // Stop intervention mode
        console.log('[LiveCallMonitor] Stopping intervention mode for call:', callId);
        setInterventionMode(false);
      }
    } catch (error) {
      console.error('[LiveCallMonitor] Failed to toggle intervention mode:', error);
    }
  };

  const handleEndCall = async () => {
    try {
      console.log('[LiveCallMonitor] Attorney ending call:', callId);

      // End the call using the hook's endCall function
      const success = await endCall();

      if (success) {
        // Close the monitor
        onClose();
      }
    } catch (error) {
      console.error('[LiveCallMonitor] Failed to end call:', error);
    }
  };

  const handleTakeOver = () => {
    // Open call control in new window/tab
    if (callControlUrl) {
      window.open(callControlUrl, '_blank', 'width=800,height=600');
    }
  };

  const copyControlUrl = () => {
    if (callControlUrl) {
      navigator.clipboard.writeText(callControlUrl);
      // You could add a toast notification here
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="live-call-monitor">
      <div className="monitor-header">
        <h3>Live Call Monitor</h3>
        <div className="call-info">
          <span className="call-id">Call ID: {callId}</span>
          <span className={`connection-status ${!loading && !monitoringError ? 'connected' : 'disconnected'}`}>
            {!loading && !monitoringError ? '🟢 Connected' : '🔴 Disconnected'}
          </span>
        </div>
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      {monitoringError && (
        <div className="error-message">
          Monitoring Error: {monitoringError}
        </div>
      )}

      <div className="call-status-panel">
        <div className="status-grid">
          <div className="status-item">
            <label>Status:</label>
            <span className={`status-value ${status || callData?.status}`}>
              {status || callData?.status || 'Unknown'}
            </span>
          </div>
          <div className="status-item">
            <label>Duration:</label>
            <span className="status-value">
              {formatDuration(callData?.duration)}
            </span>
          </div>
          <div className="status-item">
            <label>Client:</label>
            <span className="status-value">
              {callData?.customer?.number || 'Unknown'}
            </span>
          </div>
          <div className="status-item">
            <label>Assistant:</label>
            <span className="status-value">
              {callData?.assistant?.name || attorney?.firm_name || 'AI Assistant'}
            </span>
          </div>
        </div>
      </div>

      <div className="transcript-panel">
        <h4>Live Transcript</h4>
        <div className="transcript-content">
          {transcripts && transcripts.length > 0 ? (
            transcripts.map((entry, index) => (
              <div key={index} className={`transcript-entry ${entry.role}`}>
                <span className="speaker">{entry.role === 'assistant' ? 'AI' : 'Client'}:</span>
                <span className="message">{entry.message || entry.text}</span>
                <span className="timestamp">{new Date(entry.timestamp || Date.now()).toLocaleTimeString()}</span>
              </div>
            ))
          ) : (
            <div className="no-transcript">
              {!loading && !monitoringError ? 'Waiting for transcript...' : 'Not connected to call'}
            </div>
          )}
        </div>
      </div>

      <div className="intervention-panel">
        <h4>Attorney Controls</h4>
        
        <div className="control-buttons">
          <button
            className={`intervention-button ${interventionMode ? 'active' : ''}`}
            onClick={handleInterventionToggle}
          >
            {interventionMode ? '🔴 Stop Monitoring' : '👁️ Start Monitoring'}
          </button>
          
          <button
            className="takeover-button"
            onClick={handleTakeOver}
            disabled={!callControlUrl}
          >
            📞 Take Over Call
          </button>
          
          <button
            className="end-call-button"
            onClick={handleEndCall}
          >
            ⏹️ End Call
          </button>
        </div>

        {callControlUrl && (
          <div className="control-url-section">
            <label>Call Control URL:</label>
            <div className="url-input-group">
              <input
                type="text"
                value={callControlUrl}
                readOnly
                className="control-url-input"
              />
              <button onClick={copyControlUrl} className="copy-button">
                📋 Copy
              </button>
            </div>
            <small>Share this URL to allow remote call control</small>
          </div>
        )}

        <div className="notes-section">
          <label htmlFor="call-notes">Call Notes:</label>
          <textarea
            id="call-notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add notes about this call..."
            rows={4}
          />
        </div>
      </div>
    </div>
  );
};

export default LiveCallMonitor;
