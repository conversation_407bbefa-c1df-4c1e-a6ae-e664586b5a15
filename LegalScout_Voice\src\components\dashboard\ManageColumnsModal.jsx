import React, { useState, useEffect } from 'react';
import { FaTimes, FaEye, FaEyeSlash, FaEdit, FaTrash, FaGripVertical, FaPlus, FaCheck, FaUndo } from 'react-icons/fa';
import './ManageColumnsModal.css';

/**
 * ManageColumnsModal component for managing custom columns
 * Allows viewing, hiding/showing, deleting, and reordering columns
 */
const ManageColumnsModal = ({
  isOpen,
  onClose,
  columns,
  onToggleVisibility,
  onDeleteColumn,
  onBulkDelete,
  onReorderColumns,
  onEditColumn
}) => {
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [draggedColumn, setDraggedColumn] = useState(null);
  const [filter, setFilter] = useState('all'); // all, visible, hidden
  const [sortBy, setSortBy] = useState('order'); // order, name, created, type

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedColumns([]);
      setDraggedColumn(null);
      console.log('ManageColumnsModal opened with columns:', columns);
      console.log('Columns length:', columns?.length);
      console.log('Columns data:', JSON.stringify(columns, null, 2));
    }
  }, [isOpen, columns]);

  if (!isOpen) return null;

  // Ensure columns is an array
  const safeColumns = Array.isArray(columns) ? columns : [];

  // Filter and sort columns
  const filteredColumns = safeColumns
    .filter(col => {
      if (filter === 'visible') return col.is_visible;
      if (filter === 'hidden') return !col.is_visible;
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.display_name.localeCompare(b.display_name);
        case 'created':
          return new Date(b.created_at) - new Date(a.created_at);
        case 'type':
          return a.field_type.localeCompare(b.field_type);
        case 'order':
        default:
          return a.display_order - b.display_order;
      }
    });

  console.log('Filtered columns:', filteredColumns);
  console.log('Filter:', filter, 'SortBy:', sortBy);

  // Handle column selection
  const handleColumnSelect = (columnId) => {
    setSelectedColumns(prev =>
      prev.includes(columnId)
        ? prev.filter(id => id !== columnId)
        : [...prev, columnId]
    );
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedColumns.length === filteredColumns.length) {
      setSelectedColumns([]);
    } else {
      setSelectedColumns(filteredColumns.map(col => col.id));
    }
  };

  // Handle drag start
  const handleDragStart = (e, column) => {
    setDraggedColumn(column);
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over
  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // Handle drop
  const handleDrop = (e, targetColumn) => {
    e.preventDefault();
    if (draggedColumn && draggedColumn.id !== targetColumn.id) {
      const rect = e.currentTarget.getBoundingClientRect();
      const midpoint = rect.top + rect.height / 2;
      const position = e.clientY < midpoint ? 'before' : 'after';
      onReorderColumns(draggedColumn.id, targetColumn.id, position);
    }
    setDraggedColumn(null);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get field type display name
  const getFieldTypeDisplay = (fieldType) => {
    const types = {
      text: 'Text',
      number: 'Number',
      date: 'Date',
      boolean: 'Yes/No',
      select: 'Dropdown'
    };
    return types[fieldType] || fieldType;
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content manage-columns-modal">
        <div className="modal-header">
          <h2>
            <FaGripVertical /> Manage Columns
          </h2>
          <button className="close-button" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <div className="modal-body">
          {/* Controls */}
          <div className="column-controls">
            <div className="filter-controls">
              <label>Show:</label>
              <select value={filter} onChange={(e) => setFilter(e.target.value)}>
                <option value="all">All Columns</option>
                <option value="visible">Visible Only</option>
                <option value="hidden">Hidden Only</option>
              </select>
            </div>

            <div className="sort-controls">
              <label>Sort by:</label>
              <select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
                <option value="order">Display Order</option>
                <option value="name">Name</option>
                <option value="created">Date Created</option>
                <option value="type">Field Type</option>
              </select>
            </div>

            <div className="bulk-actions">
              {selectedColumns.length > 0 && (
                <>
                  <button
                    className="bulk-action-button delete"
                    onClick={() => onBulkDelete(selectedColumns)}
                  >
                    <FaTrash /> Delete Selected ({selectedColumns.length})
                  </button>
                  <button
                    className="bulk-action-button clear"
                    onClick={() => setSelectedColumns([])}
                  >
                    Clear Selection
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Column Statistics */}
          <div className="column-stats">
            <div className="stat-item">
              <span className="stat-label">Total Columns:</span>
              <span className="stat-value">{safeColumns.length}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Visible:</span>
              <span className="stat-value">{safeColumns.filter(col => col.is_visible).length}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Hidden:</span>
              <span className="stat-value">{safeColumns.filter(col => !col.is_visible).length}</span>
            </div>
          </div>

          {/* Column List */}
          <div className="columns-list">
            <div className="list-header">
              <div className="select-all">
                <input
                  type="checkbox"
                  checked={selectedColumns.length === filteredColumns.length && filteredColumns.length > 0}
                  onChange={handleSelectAll}
                />
                <span>Select All</span>
              </div>
              <div className="header-labels">
                <span>Column</span>
                <span>Type</span>
                <span>Status</span>
                <span>Created</span>
                <span>Actions</span>
              </div>
            </div>

            {filteredColumns.length === 0 ? (
              <div className="empty-state">
                <p>No columns found matching the current filter.</p>
              </div>
            ) : (
              filteredColumns.map((column) => (
                <div
                  key={column.id}
                  className={`column-item ${selectedColumns.includes(column.id) ? 'selected' : ''} ${
                    draggedColumn?.id === column.id ? 'dragging' : ''
                  }`}
                  draggable
                  onDragStart={(e) => handleDragStart(e, column)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, column)}
                >
                  <div className="column-select">
                    <input
                      type="checkbox"
                      checked={selectedColumns.includes(column.id)}
                      onChange={() => handleColumnSelect(column.id)}
                    />
                  </div>

                  <div className="drag-handle">
                    <FaGripVertical />
                  </div>

                  <div className="column-info">
                    <div className="column-name">
                      <strong>{column.display_name}</strong>
                      <small>{column.name}</small>
                    </div>
                    <div className="column-type">
                      {getFieldTypeDisplay(column.field_type)}
                    </div>
                    <div className="column-status">
                      <span className={`status-badge ${column.is_visible ? 'visible' : 'hidden'}`}>
                        {column.is_visible ? (
                          <>
                            <FaEye /> Visible
                          </>
                        ) : (
                          <>
                            <FaEyeSlash /> Hidden
                          </>
                        )}
                      </span>
                    </div>
                    <div className="column-created">
                      {formatDate(column.created_at)}
                    </div>
                    <div className="column-actions">
                      <button
                        className="action-btn toggle"
                        onClick={() => onToggleVisibility(column.id, !column.is_visible)}
                        title={column.is_visible ? 'Hide Column' : 'Show Column'}
                      >
                        {column.is_visible ? <FaEyeSlash /> : <FaEye />}
                      </button>
                      <button
                        className="action-btn edit"
                        onClick={() => onEditColumn(column)}
                        title="Edit Column"
                      >
                        <FaEdit />
                      </button>
                      <button
                        className="action-btn delete"
                        onClick={() => onDeleteColumn(column.id)}
                        title="Delete Column"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        <div className="modal-footer">
          <div className="footer-info">
            <small>
              Drag and drop to reorder columns. Changes are saved automatically.
            </small>
          </div>
          <div className="footer-actions">
            <button className="close-button-footer" onClick={onClose}>
              Done
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManageColumnsModal;
