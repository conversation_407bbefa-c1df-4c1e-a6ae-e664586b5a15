import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { FaUpload, FaTrash, Fa<PERSON>heck, FaExclamationTriangle, FaToggleOn, FaToggleOff } from 'react-icons/fa';
import ConfigValidator from '../ConfigValidator';
import SubdomainEditor from './SubdomainEditor';
import PracticeAreaSelector from '../common/PracticeAreaSelector';
import WebsiteImporter from './WebsiteImporter';
import { useAuth } from '../../contexts/AuthContext';
import { filterUpdateData } from '../../utils/attorneyUtils';
import { v4 as uuidv4 } from 'uuid';
import { isRealAuthEnabled, toggleAuthMode } from '../../utils/authModeToggle';
import { getTemplateByPracticeArea } from '../../config/defaultTemplates';

// Note: We're now importing filterUpdateData from attorneyUtils.js

/**
 * ProfileTab component for the attorney dashboard
 * Allows attorneys to update their profile information
 */
const ProfileTab = ({ attorney, onUpdate, previewConfig }) => {
  // Get attorney from auth context
  const { attorney: authAttorney } = useAuth();

  // Local state for attorney to handle immediate updates
  const [localAttorney, setLocalAttorney] = useState(attorney || authAttorney);

  // Update local attorney when props change
  useEffect(() => {
    setLocalAttorney(attorney || authAttorney);
  }, [attorney, authAttorney]);

  // Use local attorney state for immediate updates
  const effectiveAttorney = localAttorney;

  // Get user from auth context
  const { user } = useAuth();

  // State for form data
  const [formData, setFormData] = useState({
    firmName: previewConfig.firmName || '',
    attorneyName: previewConfig.attorneyName || '',
    practiceArea: attorney?.practice_area || '', // Single practice area selection
    practiceAreas: previewConfig.practiceAreas || [],
    state: previewConfig.state || '',
    practiceDescription: previewConfig.practiceDescription || '',
    email: attorney?.email || user?.email || '',
    phone: attorney?.phone || '',
    officeAddress: attorney?.office_address || '',
    schedulingLink: attorney?.scheduling_link || '',
  });

  // Update form data when attorney or user changes
  React.useEffect(() => {
    console.log('Attorney object in ProfileTab:', attorney);
    console.log('User object in ProfileTab:', user);

    // Update all form fields from attorney object and user data
    setFormData(prev => {
      const updatedData = {
        ...prev,
        firmName: attorney?.firm_name || prev.firmName,
        attorneyName: attorney?.name || prev.attorneyName,
        phone: attorney?.phone || prev.phone,
        officeAddress: attorney?.office_address || prev.officeAddress,
        schedulingLink: attorney?.scheduling_link || prev.schedulingLink
      };

      // Priority for email: attorney email > OAuth user email > previewConfig email
      // Log all possible sources for debugging
      console.log('Email sources:', {
        userEmail: user?.email,
        userMetadataEmail: user?.user_metadata?.email,
        userIdentityEmail: user?.identities?.[0]?.identity_data?.email,
        attorneyEmail: attorney?.email,
        previewConfigEmail: previewConfig.email
      });

      // First priority: Use attorney email from database if available
      if (attorney?.email) {
        updatedData.email = attorney.email;
        console.log('Updated form data with attorney email from database:', attorney.email);
      }
      // Second priority: Use OAuth user email if available
      else if (user?.email) {
        updatedData.email = user.email;
        console.log('Updated form data with OAuth user email:', user.email);
      }
      // Third priority: Check user metadata
      else if (user?.user_metadata?.email) {
        updatedData.email = user.user_metadata.email;
        console.log('Updated form data with OAuth user metadata email:', user.user_metadata.email);
      }
      // Fourth priority: Check identity data
      else if (user?.identities?.[0]?.identity_data?.email) {
        updatedData.email = user.identities[0].identity_data.email;
        console.log('Updated form data with OAuth identity email:', user.identities[0].identity_data.email);
      }
      // Last resort: Use previewConfig or previous state
      else {
        updatedData.email = previewConfig.email || prev.email || '';
        console.log('Using email from previewConfig or previous state:', updatedData.email);
      }

      return updatedData;
    });
  }, [attorney, user, previewConfig.email]);

  // State for UI
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [practiceAreaInput, setPracticeAreaInput] = useState('');

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Update preview config for real-time preview
    if (['firmName', 'attorneyName', 'state', 'practiceDescription'].includes(name)) {
      onUpdate({ [name]: value });
    }
  };

  // Handle practice area input
  const handlePracticeAreaInputChange = (e) => {
    setPracticeAreaInput(e.target.value);
  };

  // Add practice area
  const handleAddPracticeArea = () => {
    if (practiceAreaInput.trim() === '') return;

    const updatedAreas = [...formData.practiceAreas, practiceAreaInput.trim()];
    setFormData(prev => ({ ...prev, practiceAreas: updatedAreas }));
    onUpdate({ practiceAreas: updatedAreas });
    setPracticeAreaInput('');
  };

  // Remove practice area
  const handleRemovePracticeArea = (index) => {
    const updatedAreas = formData.practiceAreas.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, practiceAreas: updatedAreas }));
    onUpdate({ practiceAreas: updatedAreas });
  };



  // State for validation
  const [validationResult, setValidationResult] = useState(null);

  // Validate configuration
  const validateConfig = async () => {
    if (!effectiveAttorney?.id) {
      return {
        valid: false,
        message: 'Attorney ID is missing. Please refresh the page.'
      };
    }

    try {
      // Import the validateConfiguration function
      const { validateConfiguration } = await import('../../services/syncTools');

      // Validate the configuration
      const result = await validateConfiguration({
        attorneyId: effectiveAttorney.id,
        configData: {
          name: formData.attorneyName,
          email: formData.email,
          firm_name: formData.firmName,
          welcome_message: previewConfig.welcomeMessage || 'Welcome to my legal practice',
          vapi_instructions: previewConfig.vapiInstructions || 'You are a legal assistant',
          voice_provider: previewConfig.voiceProvider || 'playht',
          voice_id: previewConfig.voiceId || 'ranger'
        }
      });

      setValidationResult(result);
      return result;
    } catch (error) {
      console.error('Error validating configuration:', error);
      return {
        valid: false,
        error: error.message,
        message: `Error validating configuration: ${error.message}`
      };
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    // Store state in localStorage for preview purposes
    try {
      const previewData = JSON.parse(localStorage.getItem('previewData') || '{}');
      previewData.state = formData.state;
      localStorage.setItem('previewData', JSON.stringify(previewData));
      console.log('Stored state in localStorage for preview:', formData.state);
    } catch (e) {
      console.error('Error storing state in localStorage:', e);
    }

    // Validate configuration before saving
    const validationResult = await validateConfig();

    if (!validationResult.valid) {
      setError(validationResult.message || 'Configuration validation failed');
      setLoading(false);
      return;
    }

    try {
      // Get attorney data from various sources in order of priority
      let currentAttorney = attorney;

      // If attorney is not available or has no ID, try to get it from auth context
      if (!currentAttorney || !currentAttorney.id) {
        if (authAttorney && authAttorney.id) {
          currentAttorney = authAttorney;
          console.log('Using attorney from auth context:', currentAttorney);
        } else {
          // Try localStorage as a fallback
          const storedAttorney = localStorage.getItem('attorney');
          if (storedAttorney) {
            try {
              currentAttorney = JSON.parse(storedAttorney);
              console.log('Retrieved attorney from localStorage:', currentAttorney);
            } catch (parseError) {
              console.error('Error parsing attorney from localStorage:', parseError);
            }
          }
        }
      }

      // If we still don't have an attorney with an ID, create a development one
      if (!currentAttorney || !currentAttorney.id) {
        console.log('No attorney found with ID, creating development attorney');

        // Create a development attorney using the user's ID if available
        currentAttorney = {
          id: user?.id || 'dev-attorney-id',
          name: formData.attorneyName || user?.user_metadata?.name || 'Development Attorney',
          email: formData.email || user?.email || '<EMAIL>',
          firm_name: formData.firmName || 'Development Law Firm',
          user_id: user?.id || 'dev-user-id'
        };

        console.log('Created development attorney:', currentAttorney);
      }

      // In development mode, create a temporary ID
      if (import.meta.env.DEV) {
        console.warn('Development mode: Using temporary attorney ID');
        currentAttorney = {
          ...currentAttorney,
          id: uuidv4()
        };
      } else {
        throw new Error('Attorney ID is missing. Please refresh the page.');
      }



      // Check if attorney object and ID exist
      if (!currentAttorney || !currentAttorney.id) {
        // In development mode, create a temporary ID
        if (import.meta.env.DEV) {
          console.warn('Development mode: Using temporary attorney ID');
          currentAttorney = {
            ...currentAttorney,
            id: uuidv4()
          };
        } else {
          throw new Error('Attorney ID is missing. Please refresh the page.');
        }
      }

      // Update attorney profile in database
      if (import.meta.env.DEV) {
        console.log('Development mode: Skipping database update');

        // Update the attorney object in localStorage
        const updatedAttorney = {
          ...currentAttorney,
          firm_name: formData.firmName,
          name: formData.attorneyName,
          practice_areas: formData.practiceAreas,
          state: formData.state,
          practice_description: formData.practiceDescription,
          phone: formData.phone,
          office_address: formData.officeAddress,
          scheduling_link: formData.schedulingLink,
          updated_at: new Date().toISOString()
        };

        // Save to localStorage
        localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
        // Persist the attorney ID for future sessions
        localStorage.setItem('attorney_id', updatedAttorney.id);
        localStorage.setItem('currentAttorneyId', updatedAttorney.id);
        console.log('Updated attorney in localStorage:', updatedAttorney);
      } else {
        // Production mode - update the database
        console.log('Updating attorney in database with ID:', currentAttorney.id);

        // Prepare update data
        const updateData = {
          firm_name: formData.firmName,
          name: formData.attorneyName,
          practice_areas: formData.practiceAreas,
          practice_description: formData.practiceDescription,
          phone: formData.phone,
          office_address: formData.officeAddress,
          scheduling_link: formData.schedulingLink,
          updated_at: new Date()
        };

        // Store state in localStorage for preview purposes, but don't send to database
        try {
          const previewData = JSON.parse(localStorage.getItem('previewData') || '{}');
          previewData.state = formData.state;
          localStorage.setItem('previewData', JSON.stringify(previewData));
        } catch (e) {
          console.error('Error storing state in localStorage:', e);
        }

        // Add email and user_id if available
        if (formData.email) {
          updateData.email = formData.email;
        }

        if (user?.id) {
          updateData.user_id = user.id;
        }

        // Filter update data to only include fields that exist in the database
        const filteredUpdateData = await filterUpdateData('attorneys', updateData);

        console.log('Filtered update data for attorneys:', filteredUpdateData);

        // Update the database
        const { error: updateError } = await supabase
          .from('attorneys')
          .update(filteredUpdateData)
          .eq('id', currentAttorney.id);

        if (updateError) throw updateError;
      }

      // Sync attorney profile with Vapi
      try {
        // Import the syncAttorneyProfile function
        const { syncAttorneyProfile } = await import('../../services/syncTools');

        // Sync the attorney profile
        await syncAttorneyProfile({
          attorneyId: currentAttorney.id,
          forceUpdate: true
        });

        console.log('Attorney profile synced with Vapi');
      } catch (syncError) {
        console.error('Error syncing attorney profile:', syncError);
        // Don't fail the whole operation if sync fails
      }

      // Show success message
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error('Error updating profile:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle website import completion
   */
  const handleWebsiteImport = async (importedData) => {
    try {
      console.log('Website import completed:', importedData);

      // Update form data with imported information
      setFormData(prev => ({
        ...prev,
        firmName: importedData.firmName || prev.firmName,
        attorneyName: importedData.attorneyName || prev.attorneyName,
        practiceDescription: importedData.practiceDescription || prev.practiceDescription,
        phone: importedData.phone || prev.phone,
        email: importedData.email || prev.email,
        officeAddress: importedData.address || prev.officeAddress,
        state: importedData.state || prev.state,
        practiceAreas: importedData.practiceAreas || prev.practiceAreas,
        // Visual elements for other tabs
        primaryColor: importedData.primaryColor || prev.primaryColor,
        secondaryColor: importedData.secondaryColor || prev.secondaryColor,
        accentColor: importedData.accentColor || prev.accentColor,
        welcomeMessage: importedData.welcomeMessage || prev.welcomeMessage,
        buttonText: importedData.buttonText || prev.buttonText,
        vapiInstructions: importedData.vapiInstructions || prev.vapiInstructions
      }));

      // Show success message
      setSuccess('Profile imported successfully from website!');
      setError('');

      // Auto-save the imported data
      setTimeout(() => {
        const form = document.querySelector('.profile-tab form');
        if (form) {
          form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
        }
      }, 1000);

    } catch (error) {
      console.error('Error handling website import:', error);
      setError('Failed to process imported data');
    }
  };

  return (
    <div className="profile-tab">
      <h2>Profile Information</h2>
      <p className="tab-description">
        Update your profile information to customize your LegalScout experience.
      </p>

      {error && (
        <div className="alert alert-error">
          <FaExclamationTriangle />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="alert alert-success">
          <FaCheck />
          <span>Profile updated successfully!</span>
        </div>
      )}

      {/* Config Validator */}
      <ConfigValidator
        attorneyId={effectiveAttorney?.id}
        configData={formData}
        autoValidate={false}
      />

      <form onSubmit={handleSubmit}>
        {/* Website Importer - Prominently placed at top */}
        <WebsiteImporter
          onImportComplete={handleWebsiteImport}
          disabled={loading}
        />

        <div className="dashboard-card">
          <h3>Basic Information</h3>

          <div className="form-group">
            <label>Your Subdomain</label>
            <SubdomainEditor
              currentSubdomain={effectiveAttorney?.subdomain}
              firmName={formData.firmName}
              onUpdate={async (data) => {
                // Update the attorney in the database
                if (effectiveAttorney?.id && data.subdomain) {
                  console.log('Updating subdomain in Supabase:', data.subdomain);

                  const { error } = await supabase
                    .from('attorneys')
                    .update({ subdomain: data.subdomain })
                    .eq('id', effectiveAttorney.id);

                  if (error) {
                    console.error('Supabase subdomain update failed:', error);
                    throw new Error(`Failed to update subdomain: ${error.message}`);
                  }

                  console.log('Subdomain successfully updated in Supabase');

                  // Immediately update local attorney state to reflect the change
                  setLocalAttorney(prev => ({
                    ...prev,
                    subdomain: data.subdomain
                  }));

                  // Update parent component state if onUpdate callback is provided
                  if (onUpdate) {
                    onUpdate({ subdomain: data.subdomain });
                  }

                  // Note: Vapi sync may happen separately and could fail without affecting subdomain update
                }
              }}
              disabled={loading}
            />
          </div>

          <div className="form-group">
            <label htmlFor="firmName">Firm Name</label>
            <input
              type="text"
              id="firmName"
              name="firmName"
              className="form-control"
              value={formData.firmName}
              onChange={handleChange}
              placeholder="Enter your firm name"
            />
          </div>

          <div className="form-group">
            <label htmlFor="attorneyName">Attorney Name</label>
            <input
              type="text"
              id="attorneyName"
              name="attorneyName"
              className="form-control"
              value={formData.attorneyName}
              onChange={handleChange}
              placeholder="Enter your full name"
            />
          </div>

          <div className="form-group">
            <label htmlFor="barId">Bar ID</label>
            <input
              type="text"
              id="barId"
              name="barId"
              className="form-control"
              value={formData.barId}
              onChange={handleChange}
              placeholder="Enter your bar ID"
            />
          </div>

          <div className="form-group">
            <label htmlFor="state">State</label>
            <input
              type="text"
              id="state"
              name="state"
              className="form-control"
              value={formData.state}
              onChange={handleChange}
              placeholder="Enter your state (e.g., CA, NY)"
            />
          </div>

          <div className="form-group">
            <label htmlFor="email">Email (from OAuth)</label>
            <input
              type="email"
              id="email"
              name="email"
              className="form-control"
              value={formData.email || ''}
              onChange={handleChange}
              placeholder="Email from your OAuth account"
              disabled
            />
            <small className="form-text">
              {formData.email
                ? `Email is set from your OAuth login (${formData.email}) and cannot be changed`
                : 'Email will be automatically set from your OAuth login when you sign in'}
            </small>
            {!formData.email && user && (
              <div className="alert alert-warning" style={{ marginTop: '10px', fontSize: '12px' }}>
                <strong>OAuth Email Not Found</strong>
                <p>We couldn't retrieve your email from the OAuth provider. This might be due to permission settings.</p>
                <p>User ID: {user.id}</p>
              </div>
            )}

            {/* Debug information for OAuth email */}
            <div className="debug-info" style={{ marginTop: '10px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px', fontSize: '12px' }}>
              <details>
                <summary>Debug OAuth Information</summary>
                <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                  {JSON.stringify({
                    userEmail: user?.email,
                    userMetadataEmail: user?.user_metadata?.email,
                    userIdentityEmail: user?.identities?.[0]?.identity_data?.email,
                    attorneyEmail: attorney?.email,
                    formDataEmail: formData.email,
                    isDevelopment: import.meta.env.DEV || import.meta.env.MODE === 'development',
                    userObject: user
                  }, null, 2)}
                </pre>
              </details>
              <p>Provider: {user?.app_metadata?.provider || 'Unknown'}</p>
              <p>Auth Mode: {(import.meta.env.DEV || import.meta.env.MODE === 'development') ? 'Development (Mock Auth)' : 'Production'}</p>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="phone">Phone</label>
            <input
              type="tel"
              id="phone"
              name="phone"
              className="form-control"
              value={formData.phone}
              onChange={handleChange}
              placeholder="Enter your phone number"
            />
          </div>

          <div className="form-group">
            <label htmlFor="officeAddress">Office Address</label>
            <textarea
              id="officeAddress"
              name="officeAddress"
              className="form-control"
              value={formData.officeAddress}
              onChange={handleChange}
              placeholder="Enter your office address"
              rows="3"
            />
          </div>

          <div className="form-group">
            <label htmlFor="schedulingLink">Scheduling Link</label>
            <input
              type="url"
              id="schedulingLink"
              name="schedulingLink"
              className="form-control"
              value={formData.schedulingLink}
              onChange={handleChange}
              placeholder="https://calendly.com/your-link"
            />
            <small className="form-text">
              Add a link to your scheduling system to allow clients to book appointments directly.
            </small>
          </div>
        </div>

        <div className="dashboard-card">
          <h3>Practice Information</h3>

          <div className="form-group">
            <label htmlFor="practiceDescription">Practice Description</label>
            <textarea
              id="practiceDescription"
              name="practiceDescription"
              className="form-control"
              value={formData.practiceDescription}
              onChange={handleChange}
              placeholder="Describe your practice"
              rows="4"
            />
            <small className="form-text">
              This description will be displayed to potential clients. You can use basic markdown formatting.
            </small>
          </div>

          <div className="form-group">
            <label htmlFor="practiceAreaInput">Practice Areas</label>
            <div className="practice-areas-container">
              {formData.practiceAreas.length > 0 ? (
                <div className="practice-areas-list">
                  {formData.practiceAreas.map((area, index) => (
                    <div key={index} className="practice-area-tag">
                      <span>{area}</span>
                      <button
                        type="button"
                        onClick={() => handleRemovePracticeArea(index)}
                        className="remove-tag-button"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="empty-state small">
                  No practice areas added yet. Add your areas of expertise below.
                </p>
              )}

              <div className="add-practice-area">
                <input
                  type="text"
                  id="practiceAreaInput"
                  value={practiceAreaInput}
                  onChange={handlePracticeAreaInputChange}
                  placeholder="Add a practice area"
                  className="form-control"
                />
                <button
                  type="button"
                  onClick={handleAddPracticeArea}
                  className="add-button"
                >
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>



        <div className="action-buttons">
          <button
            type="submit"
            className="dashboard-button"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProfileTab;
