/**
 * Session Template Manager
 * Manages multi-agent, multi-human session templates for attorneys
 */

import React, { useState, useEffect } from 'react';
import { 
  FaPlus, FaEdit, FaTrash, FaCopy, FaPlay, FaUsers, FaRobot, 
  FaClock, FaDollarSign, FaCheckCircle, FaExclamationTriangle 
} from 'react-icons/fa';
import { DEFAULT_SESSION_TEMPLATES } from '../../config/sessionTemplates';
import './SessionTemplateManager.css';

const SessionTemplateManager = ({ attorney, onCreateSession }) => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showTemplateDetails, setShowTemplateDetails] = useState(false);

  useEffect(() => {
    loadSessionTemplates();
  }, [attorney]);

  const loadSessionTemplates = async () => {
    try {
      setLoading(true);
      
      // Load attorney's custom templates
      const customTemplates = await fetchCustomTemplates(attorney.id);
      
      // Combine with default templates
      const allTemplates = [
        ...Object.values(DEFAULT_SESSION_TEMPLATES).map(template => ({
          ...template,
          id: template.name.toLowerCase().replace(/\s+/g, '_'),
          is_default: true,
          attorney_id: null
        })),
        ...customTemplates
      ];
      
      setTemplates(allTemplates);
    } catch (error) {
      console.error('Error loading session templates:', error);
      setError('Failed to load session templates');
    } finally {
      setLoading(false);
    }
  };

  const fetchCustomTemplates = async (attorneyId) => {
    // This would fetch from your API
    // For now, return empty array
    return [];
  };

  const handleCreateSession = async (template) => {
    try {
      setLoading(true);
      
      // Create session from template
      const sessionConfig = {
        template_id: template.id,
        attorney_id: attorney.id,
        name: `${template.name} - ${new Date().toLocaleDateString()}`,
        participants: {
          ai_agents: template.ai_agents,
          human_roles: template.human_roles.filter(role => role.role !== 'client')
        }
      };

      await onCreateSession(sessionConfig);
      
    } catch (error) {
      console.error('Error creating session:', error);
      setError('Failed to create session from template');
    } finally {
      setLoading(false);
    }
  };

  const renderTemplateCard = (template) => (
    <div key={template.id} className="session-template-card">
      <div className="template-header">
        <h3>{template.name}</h3>
        <div className="template-badges">
          {template.is_default && <span className="badge default">Default</span>}
          <span className={`badge complexity ${template.session_config?.complexity_level}`}>
            {template.session_config?.complexity_level || 'Medium'}
          </span>
        </div>
      </div>

      <div className="template-description">
        {template.description}
      </div>

      <div className="template-stats">
        <div className="stat">
          <FaClock />
          <span>{template.session_config?.estimated_duration || 'Variable'}</span>
        </div>
        <div className="stat">
          <FaUsers />
          <span>{template.human_roles?.length || 0} Human Roles</span>
        </div>
        <div className="stat">
          <FaRobot />
          <span>{template.ai_agents?.length || 0} AI Agents</span>
        </div>
        <div className="stat">
          <FaDollarSign />
          <span>${template.session_config?.pricing?.base_price || 'Custom'}</span>
        </div>
      </div>

      <div className="template-participants">
        <div className="participants-section">
          <h4>AI Agents</h4>
          <div className="participant-list">
            {template.ai_agents?.map((agent, index) => (
              <div key={index} className="participant-item ai">
                <FaRobot />
                <span>{agent.name}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="participants-section">
          <h4>Human Roles</h4>
          <div className="participant-list">
            {template.human_roles?.map((role, index) => (
              <div key={index} className={`participant-item human ${role.required ? 'required' : 'optional'}`}>
                <FaUsers />
                <span>{role.name}</span>
                {role.required && <FaExclamationTriangle className="required-icon" />}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="template-workflow">
        <h4>Workflow Phases</h4>
        <div className="workflow-phases">
          {template.workflow?.phases?.map((phase, index) => (
            <div key={index} className="workflow-phase">
              <div className="phase-number">{index + 1}</div>
              <div className="phase-info">
                <span className="phase-name">{phase.name}</span>
                <span className="phase-duration">{phase.estimated_duration}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="template-actions">
        <button 
          className="action-btn primary"
          onClick={() => handleCreateSession(template)}
          disabled={loading}
        >
          <FaPlay /> Create Session
        </button>
        
        <button 
          className="action-btn secondary"
          onClick={() => {
            setSelectedTemplate(template);
            setShowTemplateDetails(true);
          }}
        >
          <FaEdit /> View Details
        </button>

        {!template.is_default && (
          <>
            <button className="action-btn secondary">
              <FaCopy /> Duplicate
            </button>
            <button className="action-btn danger">
              <FaTrash /> Delete
            </button>
          </>
        )}
      </div>
    </div>
  );

  const renderTemplateDetails = () => {
    if (!selectedTemplate) return null;

    return (
      <div className="template-details-modal">
        <div className="modal-content">
          <div className="modal-header">
            <h2>{selectedTemplate.name}</h2>
            <button 
              className="close-btn"
              onClick={() => setShowTemplateDetails(false)}
            >
              ×
            </button>
          </div>

          <div className="modal-body">
            <div className="details-section">
              <h3>Session Configuration</h3>
              <div className="config-grid">
                <div className="config-item">
                  <label>Duration:</label>
                  <span>{selectedTemplate.session_config?.estimated_duration}</span>
                </div>
                <div className="config-item">
                  <label>Complexity:</label>
                  <span>{selectedTemplate.session_config?.complexity_level}</span>
                </div>
                <div className="config-item">
                  <label>Max Participants:</label>
                  <span>{selectedTemplate.session_config?.max_participants}</span>
                </div>
                <div className="config-item">
                  <label>Base Price:</label>
                  <span>${selectedTemplate.session_config?.pricing?.base_price}</span>
                </div>
              </div>
            </div>

            <div className="details-section">
              <h3>AI Agents</h3>
              {selectedTemplate.ai_agents?.map((agent, index) => (
                <div key={index} className="agent-details">
                  <h4>{agent.name}</h4>
                  <p><strong>Role:</strong> {agent.role}</p>
                  <p><strong>Description:</strong> {agent.description}</p>
                  <p><strong>Model:</strong> {agent.config?.model}</p>
                  <p><strong>Tools:</strong> {agent.config?.tools?.join(', ')}</p>
                  <p><strong>Activation:</strong> {agent.activation_conditions?.phase}</p>
                </div>
              ))}
            </div>

            <div className="details-section">
              <h3>Human Roles</h3>
              {selectedTemplate.human_roles?.map((role, index) => (
                <div key={index} className="role-details">
                  <h4>{role.name} {role.required && <span className="required">(Required)</span>}</h4>
                  <p><strong>Role:</strong> {role.role}</p>
                  <p><strong>Description:</strong> {role.description}</p>
                  <p><strong>Participation:</strong> {role.participation?.mode}</p>
                  <p><strong>Response Time:</strong> {role.availability?.response_time}</p>
                  <p><strong>Expertise:</strong> {role.availability?.expertise_required?.join(', ')}</p>
                </div>
              ))}
            </div>

            <div className="details-section">
              <h3>Workflow</h3>
              {selectedTemplate.workflow?.phases?.map((phase, index) => (
                <div key={index} className="phase-details">
                  <h4>Phase {index + 1}: {phase.name}</h4>
                  <p><strong>Description:</strong> {phase.description}</p>
                  <p><strong>Duration:</strong> {phase.estimated_duration}</p>
                  <p><strong>Steps:</strong> {phase.steps?.length || 0}</p>
                  
                  <div className="phase-steps">
                    {phase.steps?.map((step, stepIndex) => (
                      <div key={stepIndex} className="step-item">
                        <span className="step-number">{stepIndex + 1}</span>
                        <div className="step-info">
                          <span className="step-name">{step.name}</span>
                          <span className="step-type">({step.type})</span>
                          <span className="step-assigned">→ {step.assigned_to}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="modal-footer">
            <button 
              className="btn primary"
              onClick={() => {
                setShowTemplateDetails(false);
                handleCreateSession(selectedTemplate);
              }}
            >
              Create Session from Template
            </button>
            <button 
              className="btn secondary"
              onClick={() => setShowTemplateDetails(false)}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="session-template-manager">
      <div className="manager-header">
        <h2>Session Templates</h2>
        <button 
          className="create-template-btn"
          onClick={() => setShowCreateModal(true)}
        >
          <FaPlus /> Create Custom Template
        </button>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {loading ? (
        <div className="loading">Loading session templates...</div>
      ) : (
        <div className="templates-grid">
          {templates.map(renderTemplateCard)}
        </div>
      )}

      {showTemplateDetails && renderTemplateDetails()}
    </div>
  );
};

export default SessionTemplateManager;
