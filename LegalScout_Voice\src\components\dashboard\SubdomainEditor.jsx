import React, { useState, useEffect } from 'react';
import { FaEdit, FaTimes, FaCheck, FaSpinner, FaExclamationTriangle } from 'react-icons/fa';
import { supabase } from '../../lib/supabase';
import './SubdomainEditor.css';

const SubdomainEditor = ({
  currentSubdomain,
  firmName,
  onUpdate,
  disabled = false
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newSubdomain, setNewSubdomain] = useState('');
  const [isChecking, setIsChecking] = useState(false);
  const [isAvailable, setIsAvailable] = useState(null);
  const [error, setError] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Generate suggested subdomain from firm name
  const generateSuggestedSubdomain = (name) => {
    if (!name) return '';
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters except spaces
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      .substring(0, 30); // Limit length
  };

  // Check subdomain availability
  const checkAvailability = async (subdomain) => {
    if (!subdomain || subdomain === currentSubdomain) {
      setIsAvailable(null);
      return;
    }

    // Validate subdomain format
    const subdomainRegex = /^[a-z0-9-]+$/;
    if (!subdomainRegex.test(subdomain)) {
      setError('Subdomain can only contain lowercase letters, numbers, and hyphens');
      setIsAvailable(false);
      return;
    }

    if (subdomain.length < 3) {
      setError('Subdomain must be at least 3 characters long');
      setIsAvailable(false);
      return;
    }

    if (subdomain.length > 30) {
      setError('Subdomain must be 30 characters or less');
      setIsAvailable(false);
      return;
    }

    if (subdomain.startsWith('-') || subdomain.endsWith('-')) {
      setError('Subdomain cannot start or end with a hyphen');
      setIsAvailable(false);
      return;
    }

    setIsChecking(true);
    setError('');

    try {
      console.log('Checking availability for subdomain:', subdomain);

      // Use a more reliable query method - get all matches instead of .single()
      const { data, error: queryError } = await supabase
        .from('attorneys')
        .select('subdomain')
        .eq('subdomain', subdomain);

      console.log('Supabase query result:', { data, queryError });

      // Handle query error
      if (queryError) {
        console.error('Query error:', queryError);
        throw queryError;
      }

      // Check if subdomain exists (data will be an array)
      const subdomainExists = data && data.length > 0;
      const available = !subdomainExists;

      console.log('Query returned data array:', data);
      console.log('Subdomain exists:', subdomainExists);
      console.log('Subdomain available:', available);

      setIsAvailable(available);

      if (!available) {
        setError('This subdomain is already taken');
      }
    } catch (err) {
      console.error('Error checking subdomain availability:', err);
      setError('Error checking availability. Please try again.');
      setIsAvailable(false);
    } finally {
      setIsChecking(false);
    }
  };

  // Handle subdomain input change
  const handleSubdomainChange = (e) => {
    const value = e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '');
    setNewSubdomain(value);
  };

  // Handle save
  const handleSave = async () => {
    if (!isAvailable || !newSubdomain) return;

    setIsSaving(true);
    try {
      console.log('SubdomainEditor: Attempting to save subdomain:', newSubdomain);
      await onUpdate({ subdomain: newSubdomain });

      console.log('SubdomainEditor: Subdomain save completed successfully');
      setIsEditing(false);
      setNewSubdomain('');
      setIsAvailable(null);
      setError('');

      // Show success feedback
      alert(`Subdomain successfully updated to: ${newSubdomain}.legalscout.net`);

    } catch (err) {
      console.error('SubdomainEditor: Error updating subdomain:', err);

      // Check if this is a Vapi-related error vs a Supabase error
      if (err.message && err.message.includes('Failed to update subdomain')) {
        // This is a Supabase error - show it
        setError('Failed to update subdomain. Please try again.');
      } else {
        // This might be a Vapi sync error after successful Supabase update
        setError('Subdomain updated, but there was an issue syncing with voice assistant. Your new URL should still work.');

        // Still close the editor since the main update likely succeeded
        setTimeout(() => {
          setIsEditing(false);
          setNewSubdomain('');
          setIsAvailable(null);
          setError('');
        }, 3000);
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setIsEditing(false);
    setNewSubdomain('');
    setIsAvailable(null);
    setError('');
  };

  // Handle edit click
  const handleEditClick = () => {
    setIsEditing(true);
    setNewSubdomain(generateSuggestedSubdomain(firmName));
  };

  // Debounced availability check
  useEffect(() => {
    if (!newSubdomain) {
      setIsAvailable(null);
      setError('');
      return;
    }

    const timeoutId = setTimeout(() => {
      checkAvailability(newSubdomain);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [newSubdomain]);

  if (!isEditing) {
    return (
      <div className="subdomain-display">
        <div className="subdomain-info">
          <div className="subdomain-url">
            <strong>{currentSubdomain || 'not-set'}</strong>.legalscout.net
          </div>
          <div className="subdomain-label">Your unique URL</div>
        </div>
        <button
          type="button"
          className="edit-subdomain-btn"
          onClick={handleEditClick}
          disabled={disabled}
          title="Edit subdomain"
        >
          <FaEdit />
        </button>
      </div>
    );
  }

  return (
    <div className="subdomain-editor">
      <div className="subdomain-editor-header">
        <h4>Edit Your Subdomain</h4>
        <p>Choose a unique subdomain for your LegalScout page. This will be your permanent URL.</p>
      </div>

      <div className="subdomain-input-group">
        <div className="subdomain-input-wrapper">
          <input
            type="text"
            value={newSubdomain}
            onChange={handleSubdomainChange}
            placeholder="your-firm-name"
            className={`subdomain-input ${
              isAvailable === true ? 'available' :
              isAvailable === false ? 'unavailable' : ''
            }`}
            disabled={isSaving}
          />
          <span className="subdomain-suffix">.legalscout.net</span>
        </div>

        <div className="availability-status">
          {isChecking && (
            <div className="status checking">
              <FaSpinner className="spinning" />
              <span>Checking availability...</span>
            </div>
          )}

          {!isChecking && isAvailable === true && (
            <div className="status available">
              <FaCheck />
              <span>Available!</span>
            </div>
          )}

          {!isChecking && isAvailable === false && (
            <div className="status unavailable">
              <FaExclamationTriangle />
              <span>Not available</span>
            </div>
          )}
        </div>
      </div>

      {error && (
        <div className="subdomain-error">
          <FaExclamationTriangle />
          <span>{error}</span>
        </div>
      )}

      <div className="subdomain-preview">
        <strong>Preview:</strong> https://{newSubdomain || 'your-subdomain'}.legalscout.net
      </div>

      <div className="subdomain-actions">
        <button
          type="button"
          className="cancel-btn"
          onClick={handleCancel}
          disabled={isSaving}
        >
          <FaTimes />
          Cancel
        </button>

        <button
          type="button"
          className="save-btn"
          onClick={handleSave}
          disabled={!isAvailable || isSaving || !newSubdomain}
        >
          {isSaving ? (
            <>
              <FaSpinner className="spinning" />
              Saving...
            </>
          ) : (
            <>
              <FaCheck />
              Save Subdomain
            </>
          )}
        </button>
      </div>

      <div className="subdomain-warning">
        <FaExclamationTriangle />
        <div>
          <strong>Important:</strong> Changing your subdomain will update your URL.
          Any existing links you've shared will no longer work.
        </div>
      </div>
    </div>
  );
};

export default SubdomainEditor;
