.template-manager {
  margin: 20px 0;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.template-manager h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 1.5rem;
}

.template-manager h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #444;
  font-size: 1.2rem;
}

.error-message {
  padding: 10px;
  margin-bottom: 15px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  border-left: 4px solid #c62828;
}

.success-message {
  padding: 10px;
  margin-bottom: 15px;
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: 4px;
  border-left: 4px solid #2e7d32;
}

.template-form {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-group input[type="text"],
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  min-height: 80px;
  resize: vertical;
}

.form-group.checkbox {
  display: flex;
  align-items: center;
}

.form-group.checkbox label {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.form-group.checkbox input {
  margin-right: 8px;
}

.save-button,
.apply-button {
  padding: 10px 20px;
  background-color: #4B74AA;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-button:hover,
.apply-button:hover {
  background-color: #3A5F8A;
}

.save-button:disabled,
.apply-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.template-list {
  padding: 20px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.template-select {
  display: flex;
  align-items: flex-end;
  margin-bottom: 20px;
}

.template-select label {
  margin-right: 10px;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.template-select select {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-right: 10px;
}

.template-table {
  overflow-x: auto;
}

.template-table table {
  width: 100%;
  border-collapse: collapse;
}

.template-table th,
.template-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.template-table th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #333;
}

.template-table tr:hover {
  background-color: #f9f9f9;
}

.apply-button-small,
.delete-button-small {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-right: 5px;
}

.apply-button-small {
  background-color: #4B74AA;
  color: white;
}

.delete-button-small {
  background-color: #e53935;
  color: white;
}

.apply-button-small:hover {
  background-color: #3A5F8A;
}

.delete-button-small:hover {
  background-color: #c62828;
}

.loading,
.no-templates {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}
