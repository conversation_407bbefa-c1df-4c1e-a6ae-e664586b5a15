import React, { useState, useEffect } from 'react';
import {
  getTemplates,
  getTemplateById,
  saveAsTemplate,
  applyTemplate,
  deleteTemplate
} from '../../services/templateService';
import { useAttorneyState } from '../../contexts/AttorneyStateContext';
import { vapiAssistantService } from '../../services/vapiAssistantService';
import './TemplateManager.css';

/**
 * Template Manager Component
 *
 * Allows attorneys to save, load, and manage assistant templates
 */
const TemplateManager = ({ onApplyTemplate, onSaveSuccess }) => {
  const { attorney } = useAttorneyState();
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [category, setCategory] = useState('general');
  const [isPublic, setIsPublic] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Load templates
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        setLoading(true);
        setError(null);

        const data = await getTemplates({
          includePublic: true,
          attorneyId: attorney?.id
        });

        setTemplates(data || []);
      } catch (error) {
        console.error('Error loading templates:', error);
        // Don't show error for missing table - just use empty templates
        if (error.message?.includes('relation "assistant_templates" does not exist')) {
          console.warn('Templates table not found - using empty template list');
          setTemplates([]);
        } else {
          setError('Failed to load templates. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    };

    if (attorney?.id) {
      loadTemplates();
    }
  }, [attorney?.id]);

  // Save current configuration as template
  const handleSaveTemplate = async () => {
    if (!templateName) {
      setError('Template name is required');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Get the current assistant
      const assistantId = attorney.vapi_assistant_id;
      if (!assistantId) {
        setError('No assistant found for this attorney');
        return;
      }

      // Get assistant details from Vapi
      const assistant = await vapiAssistantService.getAssistant(assistantId);

      // Save as template
      await saveAsTemplate(attorney, assistant, {
        name: templateName,
        description: templateDescription,
        category,
        isPublic
      });

      // Reset form
      setTemplateName('');
      setTemplateDescription('');
      setCategory('general');
      setIsPublic(false);

      // Show success message
      setSuccess('Template saved successfully');

      // Reload templates
      const data = await getTemplates({
        includePublic: true,
        attorneyId: attorney?.id
      });

      setTemplates(data || []);

      // Call onSaveSuccess callback if provided
      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (error) {
      console.error('Error saving template:', error);
      // Don't show error for missing table - just disable template saving
      if (error.message?.includes('relation "assistant_templates" does not exist')) {
        console.warn('Templates table not found - template saving disabled');
        setError('Template saving is not available. Please contact support.');
      } else {
        setError('Failed to save template. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Apply selected template
  const handleApplyTemplate = async () => {
    if (!selectedTemplate) {
      setError('Please select a template to apply');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Get template details
      const template = await getTemplateById(selectedTemplate);

      // Apply template to attorney
      const result = await applyTemplate(attorney, selectedTemplate);

      // Show success message
      setSuccess('Template applied successfully');

      // Call onApplyTemplate callback if provided
      if (onApplyTemplate) {
        onApplyTemplate(result);
      }
    } catch (error) {
      console.error('Error applying template:', error);
      setError('Failed to apply template. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Delete template
  const handleDeleteTemplate = async (templateId) => {
    if (!templateId) return;

    if (!confirm('Are you sure you want to delete this template?')) {
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Delete template
      await deleteTemplate(templateId);

      // Show success message
      setSuccess('Template deleted successfully');

      // Reload templates
      const data = await getTemplates({
        includePublic: true,
        attorneyId: attorney?.id
      });

      setTemplates(data || []);
    } catch (error) {
      console.error('Error deleting template:', error);
      setError('Failed to delete template. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="template-manager">
      <h3>Template Management</h3>

      {/* Error and Success Messages */}
      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      {/* Save Template Form */}
      <div className="template-form">
        <h4>Save Current Configuration as Template</h4>
        <div className="form-group">
          <label htmlFor="templateName">Template Name</label>
          <input
            id="templateName"
            type="text"
            value={templateName}
            onChange={(e) => setTemplateName(e.target.value)}
            placeholder="Enter template name"
            disabled={loading}
          />
        </div>

        <div className="form-group">
          <label htmlFor="templateDescription">Description</label>
          <textarea
            id="templateDescription"
            value={templateDescription}
            onChange={(e) => setTemplateDescription(e.target.value)}
            placeholder="Enter template description"
            disabled={loading}
          />
        </div>

        <div className="form-group">
          <label htmlFor="category">Category</label>
          <select
            id="category"
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            disabled={loading}
          >
            <option value="general">General</option>
            <option value="personal_injury">Personal Injury</option>
            <option value="family_law">Family Law</option>
            <option value="estate_planning">Estate Planning</option>
            <option value="criminal_defense">Criminal Defense</option>
            <option value="business_law">Business Law</option>
            <option value="immigration">Immigration</option>
            <option value="intellectual_property">Intellectual Property</option>
            <option value="real_estate">Real Estate</option>
            <option value="tax_law">Tax Law</option>
          </select>
        </div>

        <div className="form-group checkbox">
          <label>
            <input
              type="checkbox"
              checked={isPublic}
              onChange={(e) => setIsPublic(e.target.checked)}
              disabled={loading}
            />
            Make template public (visible to other attorneys)
          </label>
        </div>

        <button
          className="save-button"
          onClick={handleSaveTemplate}
          disabled={!templateName || loading}
        >
          {loading ? 'Saving...' : 'Save as Template'}
        </button>
      </div>

      {/* Template List */}
      <div className="template-list">
        <h4>Available Templates</h4>

        {loading && <div className="loading">Loading templates...</div>}

        {templates.length === 0 && !loading ? (
          <div className="no-templates">No templates available</div>
        ) : (
          <>
            <div className="template-select">
              <label htmlFor="templateSelect">Select a template</label>
              <select
                id="templateSelect"
                value={selectedTemplate || ''}
                onChange={(e) => setSelectedTemplate(e.target.value)}
                disabled={loading}
              >
                <option value="">-- Select Template --</option>
                {templates.map(template => (
                  <option key={template.id} value={template.id}>
                    {template.name} {template.attorney_id !== attorney?.id ? '(Public)' : ''}
                  </option>
                ))}
              </select>

              <button
                className="apply-button"
                onClick={handleApplyTemplate}
                disabled={!selectedTemplate || loading}
              >
                {loading ? 'Applying...' : 'Apply Template'}
              </button>
            </div>

            <div className="template-table">
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Category</th>
                    <th>Description</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {templates.map(template => (
                    <tr key={template.id}>
                      <td>{template.name}</td>
                      <td>{template.category || 'General'}</td>
                      <td>{template.description || 'No description'}</td>
                      <td>{new Date(template.created_at).toLocaleDateString()}</td>
                      <td>
                        <button
                          className="apply-button-small"
                          onClick={() => {
                            setSelectedTemplate(template.id);
                            handleApplyTemplate();
                          }}
                          disabled={loading}
                        >
                          Apply
                        </button>

                        {template.attorney_id === attorney?.id && (
                          <button
                            className="delete-button-small"
                            onClick={() => handleDeleteTemplate(template.id)}
                            disabled={loading}
                          >
                            Delete
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TemplateManager;
