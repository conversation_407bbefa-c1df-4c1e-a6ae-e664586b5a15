.tools-tab {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.tools-header {
  margin-bottom: 30px;
}

.tools-header h2 {
  color: var(--text-primary);
  margin-bottom: 8px;
  font-size: 1.8rem;
  font-weight: 600;
}

.tab-description {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.loading-state .spinning {
  font-size: 2rem;
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: var(--error-bg, #fee);
  color: var(--error-text, #c53030);
  border: 1px solid var(--error-border, #fed7d7);
  border-radius: 8px;
  margin-bottom: 20px;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.tool-card {
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--card-bg);
  position: relative;
}

.tool-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tool-card.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-bg, rgba(75, 116, 170, 0.1));
}

.tool-card.recommended {
  border-color: var(--success-color, #48bb78);
}

.tool-card.recommended.selected {
  border-color: var(--primary-color);
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.tool-info h4 {
  color: var(--text-primary);
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.tool-category {
  display: inline-block;
  background-color: var(--secondary-bg, rgba(44, 62, 80, 0.1));
  color: var(--text-secondary);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.recommended-badge {
  display: inline-block;
  background-color: var(--success-color, #48bb78);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 8px;
}

.auto-managed-badge {
  display: inline-block;
  background-color: var(--info-color, #3182ce);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 8px;
}

.configurable-badge {
  display: inline-block;
  background-color: var(--warning-color, #ed8936);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 8px;
}

.tool-card.auto-managed {
  border-color: var(--info-color, #3182ce);
  background-color: var(--info-bg, rgba(49, 130, 206, 0.1));
}

.tool-card.expanded {
  border-color: var(--primary-color);
}

.tool-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.tool-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  flex-shrink: 0;
}

.config-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.config-toggle:hover {
  background-color: var(--hover-bg, rgba(0, 0, 0, 0.1));
  color: var(--primary-color);
}

.config-toggle .expanded {
  transform: rotate(180deg);
}

.selected-icon {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.unselected-icon {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  transition: border-color 0.2s ease;
}

.tool-card:hover .unselected-icon {
  border-color: var(--primary-color);
}

.tool-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

.tool-config-panel {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

.tool-config-panel h5 {
  color: var(--text-primary);
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
}

.config-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.config-field label {
  color: var(--text-primary);
  font-size: 0.9rem;
  font-weight: 500;
}

.config-field .required {
  color: var(--error-color, #e53e3e);
  margin-left: 2px;
}

.config-field input,
.config-field select,
.config-field textarea {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--input-bg);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.config-field input:focus,
.config-field select:focus,
.config-field textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.config-field textarea {
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

.config-field .checkbox-label {
  flex-direction: row !important;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  margin: 0;
}

.call-forwarding-settings {
  margin-top: 20px;
}

.setting-group {
  margin-bottom: 20px;
}

.setting-group label {
  display: block;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 8px;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  cursor: pointer;
  font-weight: 500 !important;
  margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  margin-right: 12px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.setting-group input[type="tel"],
.setting-group select,
.setting-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--input-bg);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.2s ease;
  font-family: inherit;
}

.setting-group input[type="tel"]:focus,
.setting-group select:focus,
.setting-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.setting-group textarea {
  resize: vertical;
  min-height: 80px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.dashboard-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dashboard-button:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.dashboard-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Dark mode adjustments */
[data-theme="dark"] .tool-card {
  background-color: var(--card-bg-dark, #2d3748);
  border-color: var(--border-color-dark, #4a5568);
}

[data-theme="dark"] .tool-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .setting-group input[type="tel"],
[data-theme="dark"] .setting-group select,
[data-theme="dark"] .setting-group textarea {
  background-color: var(--input-bg-dark, #2d3748);
  border-color: var(--border-color-dark, #4a5568);
}

[data-theme="dark"] .checkmark {
  border-color: var(--border-color-dark, #4a5568);
}

[data-theme="dark"] .unselected-icon {
  border-color: var(--border-color-dark, #4a5568);
}

[data-theme="dark"] .config-toggle:hover {
  background-color: var(--hover-bg-dark, rgba(255, 255, 255, 0.1));
}

[data-theme="dark"] .tool-config-panel {
  border-top-color: var(--border-color-dark, #4a5568);
}

[data-theme="dark"] .config-field input,
[data-theme="dark"] .config-field select,
[data-theme="dark"] .config-field textarea {
  background-color: var(--input-bg-dark, #2d3748);
  border-color: var(--border-color-dark, #4a5568);
}

/* Forwarding Rules Styles (moved from AutomationTab) */
.rules-section {
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 30px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  margin-bottom: 20px;
}

.empty-state.small {
  padding: 20px;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border-left: 3px solid var(--primary-color);
}

.rule-info h4 {
  margin: 0 0 5px 0;
  font-size: 1rem;
}

.rule-info p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.condition-badge {
  background-color: var(--primary-color);
  color: white;
  font-size: 0.7rem;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.add-rule-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-rule-button:hover {
  background-color: var(--primary-color-dark);
}

/* Modal overlay for forwarding rule modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 32px;
  width: 90%;
  max-width: 650px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.forwarding-modal h3 {
  margin-top: 0;
  margin-bottom: 32px;
  font-size: 1.75rem;
  color: #1a202c;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-success,
.form-error {
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.form-error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2d3748;
  font-size: 0.95rem;
  letter-spacing: 0.025em;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background-color: #ffffff;
  color: #2d3748;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 4px rgba(66, 153, 225, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.form-row {
  display: flex;
  gap: 10px;
}

.form-row .form-group {
  flex: 1;
}

.condition-options {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.condition-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.condition-option:hover {
  border-color: #4299e1;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15);
}

.condition-option.active {
  border-color: #4299e1;
  background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(49, 130, 206, 0.05) 100%);
  box-shadow: 0 4px 15px rgba(66, 153, 225, 0.2);
}

.condition-option.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4299e1, #3182ce);
}

.condition-option svg {
  font-size: 1.75rem;
  color: #4299e1;
  transition: all 0.3s ease;
}

.condition-option:hover svg,
.condition-option.active svg {
  transform: scale(1.1);
  color: #3182ce;
}

.condition-option span {
  font-size: 0.95rem;
  font-weight: 600;
  color: #2d3748;
  letter-spacing: 0.025em;
}

.days-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.day-checkbox {
  display: flex;
  align-items: center;
  gap: 5px;
}

.day-checkbox input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.day-checkbox label {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-primary, #2d3748);
  cursor: pointer;
}

.condition-fields {
  margin-top: 15px;
  padding: 15px;
  background-color: var(--secondary-bg, rgba(0, 0, 0, 0.02));
  border-radius: 6px;
  border: 1px solid var(--border-color, #e2e8f0);
}

.field-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 10px;
}

.keyword-input-container {
  margin-bottom: 10px;
}

.keyword-input-container input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 4px;
  background-color: var(--input-background, #ffffff);
  color: var(--text-primary, #2d3748);
  font-size: 14px;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 40px;
  padding: 8px;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 4px;
  background-color: var(--input-background, #ffffff);
}

.keyword-tag {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: rgba(66, 153, 225, 0.1);
  border: 1px solid var(--primary-color, #4299e1);
  border-radius: 16px;
  padding: 4px 10px;
  font-size: 0.9rem;
  color: var(--primary-color, #4299e1);
}

.remove-keyword {
  background: none;
  border: none;
  color: var(--text-secondary, #718096);
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  padding: 0;
  margin-left: 4px;
}

.remove-keyword:hover {
  color: var(--error-color, #e53e3e);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.cancel-button,
.save-button {
  padding: 14px 28px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 15px;
  min-width: 120px;
  letter-spacing: 0.025em;
}

.cancel-button {
  background-color: #f7fafc;
  border: 2px solid #e2e8f0;
  color: #4a5568;
}

.save-button {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  border: 2px solid transparent;
  color: white;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.cancel-button:hover {
  background-color: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.save-button:hover {
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
}

.cancel-button:disabled,
.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
