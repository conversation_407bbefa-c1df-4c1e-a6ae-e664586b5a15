.vapi-assistant-config {
  background-color: var(--card-background, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.vapi-assistant-config h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  color: var(--text-primary, #333333);
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  font-style: italic;
  color: var(--text-secondary, #666666);
}

.error-message {
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 4px solid #ff0000;
  padding: 10px 15px;
  margin-bottom: 20px;
  color: #d32f2f;
  border-radius: 4px;
}

.success-message {
  background-color: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4caf50;
  padding: 10px 15px;
  margin-bottom: 20px;
  color: #388e3c;
  border-radius: 4px;
}

.no-assistant {
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  text-align: center;
}

.create-assistant-button {
  background-color: var(--primary-color, #4B74AA);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 10px;
  transition: background-color 0.2s;
}

.create-assistant-button:hover {
  background-color: var(--primary-color-dark, #3A5D88);
}

.create-assistant-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.assistant-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.assistant-field {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.assistant-field label {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-secondary, #666666);
}

.field-value {
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  color: var(--text-primary, #333333);
}

.assistant-field textarea {
  min-height: 100px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
}

.assistant-field textarea:focus {
  outline: none;
  border-color: var(--primary-color, #4B74AA);
  box-shadow: 0 0 0 2px rgba(75, 116, 170, 0.2);
}

.field-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 5px;
}

.field-actions button {
  background-color: transparent;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.field-actions button:last-child {
  background-color: var(--primary-color, #4B74AA);
  color: white;
  border-color: var(--primary-color, #4B74AA);
}

.field-actions button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.field-actions button:last-child:hover {
  background-color: var(--primary-color-dark, #3A5D88);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.save-button {
  background-color: var(--primary-color, #4B74AA);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-button:hover {
  background-color: var(--primary-color-dark, #3A5D88);
}

.save-button:disabled,
.cancel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Diagnostics section */
.toggle-diagnostics-button {
  background-color: transparent;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  margin-top: 10px;
  cursor: pointer;
  align-self: flex-start;
  transition: all 0.2s;
}

.toggle-diagnostics-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.diagnostics-section {
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  margin-bottom: 20px;
  border: 1px solid #eee;
}

.diagnostics-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: var(--text-primary, #333333);
}

.diagnostics-group {
  margin-bottom: 20px;
}

.diagnostics-group h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: var(--text-secondary, #666666);
}

.diagnostics-group pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: monospace;
  font-size: 12px;
  margin: 0;
}

.test-connection-button,
.test-assistant-button {
  background-color: var(--primary-color, #4B74AA);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-connection-button:hover,
.test-assistant-button:hover {
  background-color: var(--primary-color-dark, #3A5D88);
}

.test-connection-button:disabled,
.test-assistant-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.test-assistant-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.test-assistant-form input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}
