import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, Card, CardContent, Typography, CircularProgress, <PERSON><PERSON>, Di<PERSON>r, TextField } from '@mui/material';
import { testVapiDirectConnection, testGetAssistant, getVapiEnvironmentConfig } from '../../utils/vapiDiagnostics';

/**
 * Voice Assistant Diagnostics Component
 *
 * This component provides diagnostic tools for voice assistant integration.
 */
const VapiDiagnostics = ({ attorney }) => {
  const [loading, setLoading] = useState(false);
  const [directConnectionResult, setDirectConnectionResult] = useState(null);
  const [assistantResult, setAssistantResult] = useState(null);
  const [environmentConfig, setEnvironmentConfig] = useState(null);
  const [assistantId, setAssistantId] = useState(attorney?.vapi_assistant_id || '');

  // Load environment configuration on mount
  useEffect(() => {
    setEnvironmentConfig(getVapiEnvironmentConfig());
  }, []);

  // Update assistant ID when attorney changes
  useEffect(() => {
    if (attorney?.vapi_assistant_id) {
      setAssistantId(attorney.vapi_assistant_id);
    }
  }, [attorney]);

  // Test direct connection to Vapi API
  const handleTestDirectConnection = async () => {
    setLoading(true);
    try {
      const result = await testVapiDirectConnection();
      setDirectConnectionResult(result);
    } catch (error) {
      setDirectConnectionResult({
        success: false,
        error: `Unexpected error: ${error.message}`,
        details: error.stack
      });
    } finally {
      setLoading(false);
    }
  };

  // Test getting assistant by ID
  const handleTestGetAssistant = async () => {
    if (!assistantId) {
      setAssistantResult({
        success: false,
        error: 'No assistant ID provided'
      });
      return;
    }

    setLoading(true);
    try {
      const result = await testGetAssistant(assistantId);
      setAssistantResult(result);
    } catch (error) {
      setAssistantResult({
        success: false,
        error: `Unexpected error: ${error.message}`,
        details: error.stack
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card variant="outlined" sx={{ mt: 2 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Voice Assistant Diagnostics
        </Typography>

        {/* Environment Configuration */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Environment Configuration
          </Typography>
          {environmentConfig ? (
            <Box component="pre" sx={{
              bgcolor: 'background.paper',
              p: 2,
              borderRadius: 1,
              overflow: 'auto',
              fontSize: '0.875rem'
            }}>
              {JSON.stringify(environmentConfig, null, 2)}
            </Box>
          ) : (
            <CircularProgress size={20} />
          )}
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Direct Connection Test */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            API Connection Test
          </Typography>
          <Button
            variant="contained"
            onClick={handleTestDirectConnection}
            disabled={loading}
            sx={{ mb: 2 }}
          >
            Test Connection
          </Button>

          {directConnectionResult && (
            <Alert
              severity={directConnectionResult.success ? "success" : "error"}
              sx={{ mt: 1 }}
            >
              {directConnectionResult.success
                ? directConnectionResult.message
                : directConnectionResult.error}
            </Alert>
          )}

          {directConnectionResult && directConnectionResult.details && (
            <Box component="pre" sx={{
              bgcolor: 'background.paper',
              p: 2,
              borderRadius: 1,
              overflow: 'auto',
              fontSize: '0.875rem',
              mt: 1
            }}>
              {typeof directConnectionResult.details === 'string'
                ? directConnectionResult.details
                : JSON.stringify(directConnectionResult.details, null, 2)}
            </Box>
          )}
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Get Assistant Test */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Get Assistant Test
          </Typography>

          <TextField
            label="Assistant ID"
            value={assistantId}
            onChange={(e) => setAssistantId(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            sx={{ mb: 2 }}
          />

          <Button
            variant="contained"
            onClick={handleTestGetAssistant}
            disabled={loading || !assistantId}
            sx={{ mb: 2 }}
          >
            Test Get Assistant
          </Button>

          {assistantResult && (
            <Alert
              severity={assistantResult.success ? "success" : "error"}
              sx={{ mt: 1 }}
            >
              {assistantResult.success
                ? assistantResult.message
                : assistantResult.error}
            </Alert>
          )}

          {assistantResult && assistantResult.assistant && (
            <Box component="pre" sx={{
              bgcolor: 'background.paper',
              p: 2,
              borderRadius: 1,
              overflow: 'auto',
              fontSize: '0.875rem',
              mt: 1
            }}>
              {JSON.stringify(assistantResult.assistant, null, 2)}
            </Box>
          )}

          {assistantResult && assistantResult.details && !assistantResult.assistant && (
            <Box component="pre" sx={{
              bgcolor: 'background.paper',
              p: 2,
              borderRadius: 1,
              overflow: 'auto',
              fontSize: '0.875rem',
              mt: 1
            }}>
              {typeof assistantResult.details === 'string'
                ? assistantResult.details
                : JSON.stringify(assistantResult.details, null, 2)}
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default VapiDiagnostics;
