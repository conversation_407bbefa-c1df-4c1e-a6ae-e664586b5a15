import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

/**
 * VapiIntegration component for the Dashboard
 * 
 * This component handles the integration between the attorney's dashboard
 * and their Vapi assistant configuration.
 * 
 * @param {Object} props Component props
 * @param {Object} props.attorney The attorney data from Supabase
 * @param {Function} props.setAttorney Function to update attorney data
 * @param {Object} props.vapiAssistantData The Vapi assistant data
 * @param {Function} props.setVapiAssistantData Function to update Vapi assistant data
 * @param {Function} props.fetchVapiAssistantData Function to fetch Vapi assistant data
 * @param {Function} props.updateVapiAssistant Function to update Vapi assistant
 */
const VapiIntegration = ({
  attorney,
  setAttorney,
  vapiAssistantData,
  setVapiAssistantData,
  fetchVapiAssistantData,
  updateVapiAssistant
}) => {
  // State for tracking changes to Vapi fields
  const [vapiFieldsChanged, setVapiFieldsChanged] = useState({});
  const [isLoadingVapi, setIsLoadingVapi] = useState(false);
  const [hasVapiChanges, setHasVapiChanges] = useState(false);

  // Function to handle changes to Vapi fields
  const handleVapiFieldChange = (field, value) => {
    // Track the changed field
    setVapiFieldsChanged(prev => ({
      ...prev,
      [field]: value
    }));

    // Set the flag indicating there are changes
    setHasVapiChanges(true);
  };

  // Function to save Vapi field changes
  const handleSaveVapiChanges = async () => {
    try {
      setIsLoadingVapi(true);

      // Check if we have an assistant ID
      if (!attorney?.vapi_assistant_id) {
        throw new Error('No Vapi assistant ID found');
      }

      // Update the Vapi assistant directly
      await updateVapiAssistant(attorney.vapi_assistant_id, vapiFieldsChanged);

      // Show success message
      alert('Vapi assistant configuration updated successfully!');
    } catch (error) {
      console.error('Error saving Vapi changes:', error);
      alert(`Error saving Vapi changes: ${error.message}`);
    } finally {
      setIsLoadingVapi(false);
    }
  };

  // Function to abandon Vapi field changes
  const handleAbandonVapiChanges = () => {
    // Reset the changed fields
    setVapiFieldsChanged({});
    setHasVapiChanges(false);

    // Reset the form fields to the current assistant data
    if (vapiAssistantData) {
      // Reset fields based on what was changed
      Object.keys(vapiFieldsChanged).forEach(field => {
        switch (field) {
          case 'firstMessage':
            // Update the attorney state with the original welcome message
            setAttorney(prev => ({
              ...prev,
              welcome_message: vapiAssistantData.firstMessage || ''
            }));
            break;
          case 'voice':
            // Update the attorney state with the original voice ID
            setAttorney(prev => ({
              ...prev,
              voice_id: vapiAssistantData.voice?.voiceId || 'sarah'
            }));
            break;
          case 'llm':
            // Update the attorney state with the original AI model
            setAttorney(prev => ({
              ...prev,
              ai_model: vapiAssistantData.llm?.model || 'gpt-4o'
            }));
            break;
          case 'instructions':
            // Update the attorney state with the original instructions
            setAttorney(prev => ({
              ...prev,
              vapi_instructions: vapiAssistantData.instructions || ''
            }));
            break;
          default:
            break;
        }
      });
    }
  };

  // Render the Vapi integration status
  return (
    <div className="vapi-assistant-status">
      <h3>Vapi Assistant Status</h3>
      {isLoadingVapi ? (
        <div className="loading-spinner-small"></div>
      ) : (
        <>
          {vapiAssistantData ? (
            <div>
              <p>Assistant ID: {attorney?.vapi_assistant_id}</p>
              <p>Status: Active</p>
              <p>Last Updated: {new Date(vapiAssistantData.updatedAt).toLocaleString()}</p>
              
              {hasVapiChanges && (
                <div className="vapi-field-actions">
                  <button 
                    className="vapi-save-button"
                    onClick={handleSaveVapiChanges}
                    disabled={isLoadingVapi}
                  >
                    Save Changes
                  </button>
                  <button 
                    className="vapi-abandon-button"
                    onClick={handleAbandonVapiChanges}
                    disabled={isLoadingVapi}
                  >
                    Abandon Changes
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div>
              <p>No Vapi assistant found.</p>
              <button 
                className="vapi-save-button"
                onClick={() => fetchVapiAssistantData(attorney?.vapi_assistant_id)}
                disabled={isLoadingVapi || !attorney?.vapi_assistant_id}
              >
                Refresh Assistant Data
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default VapiIntegration;
