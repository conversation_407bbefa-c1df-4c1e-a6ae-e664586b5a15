.diagnostics-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 16px;
  margin-bottom: 24px;
  border: 1px solid #e0e0e0;
}

.diagnostics-content {
  padding: 20px;
}

.diagnostics-title {
  font-size: 18px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 16px;
  color: #333333;
}

.diagnostics-section {
  margin-bottom: 24px;
}

.diagnostics-subtitle {
  font-size: 16px;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 12px;
  color: #555555;
}

.diagnostics-pre {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow: auto;
  font-size: 14px;
  font-family: monospace;
  margin: 8px 0;
  max-height: 300px;
}

.diagnostics-divider {
  border: none;
  border-top: 1px solid #e0e0e0;
  margin: 20px 0;
}

.diagnostics-button {
  background-color: #4B74AA;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 16px;
  transition: background-color 0.2s;
}

.diagnostics-button:hover {
  background-color: #3A5D88;
}

.diagnostics-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.diagnostics-alert {
  padding: 12px 16px;
  border-radius: 4px;
  margin: 8px 0;
  font-size: 14px;
}

.diagnostics-alert.success {
  background-color: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4caf50;
  color: #388e3c;
}

.diagnostics-alert.error {
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 4px solid #ff0000;
  color: #d32f2f;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  font-style: italic;
  color: #666666;
}

.diagnostics-input-group {
  margin-bottom: 16px;
}

.diagnostics-input-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #555555;
}

.diagnostics-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dddddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 8px;
}

.diagnostics-input:focus {
  outline: none;
  border-color: #4B74AA;
  box-shadow: 0 0 0 2px rgba(75, 116, 170, 0.2);
}
