import React, { useState, useEffect } from 'react';
import { testDirectConnection, testGetAssistant, getEnvironmentConfig } from '../../utils/voiceAssistantDiagnostics';
import './VoiceAssistantDiagnostics.css';

/**
 * Voice Assistant Diagnostics Component
 *
 * This component provides diagnostic tools for voice assistant integration.
 */
const VoiceAssistantDiagnostics = ({ attorney }) => {
  const [loading, setLoading] = useState(false);
  const [directConnectionResult, setDirectConnectionResult] = useState(null);
  const [assistantResult, setAssistantResult] = useState(null);
  const [environmentConfig, setEnvironmentConfig] = useState(null);
  const [assistantId, setAssistantId] = useState(attorney?.vapi_assistant_id || '');

  // Load environment configuration on mount
  useEffect(() => {
    setEnvironmentConfig(getEnvironmentConfig());
  }, []);

  // Update assistant ID when attorney changes
  useEffect(() => {
    if (attorney?.vapi_assistant_id) {
      setAssistantId(attorney.vapi_assistant_id);
    }
  }, [attorney]);

  // Test direct connection to API
  const handleTestDirectConnection = async () => {
    setLoading(true);
    try {
      const result = await testDirectConnection();
      setDirectConnectionResult(result);
    } catch (error) {
      setDirectConnectionResult({
        success: false,
        error: `Unexpected error: ${error.message}`,
        details: error.stack
      });
    } finally {
      setLoading(false);
    }
  };

  // Test getting assistant by ID
  const handleTestGetAssistant = async () => {
    if (!assistantId) {
      setAssistantResult({
        success: false,
        error: 'No assistant ID provided'
      });
      return;
    }

    setLoading(true);
    try {
      const result = await testGetAssistant(assistantId);
      setAssistantResult(result);
    } catch (error) {
      setAssistantResult({
        success: false,
        error: `Unexpected error: ${error.message}`,
        details: error.stack
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="diagnostics-card">
      <div className="diagnostics-content">
        <h3 className="diagnostics-title">Voice Assistant Diagnostics</h3>

        {/* Environment Configuration */}
        <div className="diagnostics-section">
          <h4 className="diagnostics-subtitle">Environment Configuration</h4>
          {environmentConfig ? (
            <pre className="diagnostics-pre">
              {JSON.stringify(environmentConfig, null, 2)}
            </pre>
          ) : (
            <div className="loading-spinner">Loading...</div>
          )}
        </div>

        <hr className="diagnostics-divider" />

        {/* Direct Connection Test */}
        <div className="diagnostics-section">
          <h4 className="diagnostics-subtitle">API Connection Test</h4>
          <button
            className="diagnostics-button"
            onClick={handleTestDirectConnection}
            disabled={loading}
          >
            Test Connection
          </button>

          {directConnectionResult && (
            <div className={`diagnostics-alert ${directConnectionResult.success ? "success" : "error"}`}>
              {directConnectionResult.success
                ? directConnectionResult.message
                : directConnectionResult.error}
            </div>
          )}

          {directConnectionResult && directConnectionResult.details && (
            <pre className="diagnostics-pre">
              {typeof directConnectionResult.details === 'string'
                ? directConnectionResult.details
                : JSON.stringify(directConnectionResult.details, null, 2)}
            </pre>
          )}
        </div>

        <hr className="diagnostics-divider" />

        {/* Get Assistant Test */}
        <div className="diagnostics-section">
          <h4 className="diagnostics-subtitle">Get Assistant Test</h4>

          <div className="diagnostics-input-group">
            <label htmlFor="assistant-id">Assistant ID</label>
            <input
              id="assistant-id"
              type="text"
              value={assistantId}
              onChange={(e) => setAssistantId(e.target.value)}
              className="diagnostics-input"
            />
          </div>

          <button
            className="diagnostics-button"
            onClick={handleTestGetAssistant}
            disabled={loading || !assistantId}
          >
            Test Get Assistant
          </button>

          {assistantResult && (
            <div className={`diagnostics-alert ${assistantResult.success ? "success" : "error"}`}>
              {assistantResult.success
                ? assistantResult.message
                : assistantResult.error}
            </div>
          )}

          {assistantResult && assistantResult.assistant && (
            <pre className="diagnostics-pre">
              {JSON.stringify(assistantResult.assistant, null, 2)}
            </pre>
          )}

          {assistantResult && assistantResult.details && !assistantResult.assistant && (
            <pre className="diagnostics-pre">
              {typeof assistantResult.details === 'string'
                ? assistantResult.details
                : JSON.stringify(assistantResult.details, null, 2)}
            </pre>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceAssistantDiagnostics;
