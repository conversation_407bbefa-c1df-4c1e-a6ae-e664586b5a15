import React, { useState } from 'react';
import { FaGlobe, FaMagic, Fa<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import ImportPreview from './ImportPreview';
import mcpBlocker from '../../utils/mcpBlocker';
import './WebsiteImporter.css';

/**
 * Dead Simple Website Importer Component
 * 1-click attorney profile configuration from website URL
 */
const WebsiteImporter = ({ onImportComplete, disabled = false }) => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [extractedData, setExtractedData] = useState(null);

  const handleImport = async (e) => {
    e.preventDefault();

    if (!url.trim()) {
      setError('Please enter a website URL');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    // Start blocking MCP requests during import
    mcpBlocker.startBlocking();

    try {
      // Use the real website import API
      const response = await fetch('/api/website-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: url.trim() })
      });

      if (!response.ok) {
        throw new Error(`Import failed: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Show preview with extracted data
        setExtractedData(result.data);
        setShowPreview(true);
      } else {
        throw new Error(result.error || 'Import failed');
      }

    } catch (err) {
      console.error('Website import error:', err);
      setError(err.message || 'Failed to import website data');
    } finally {
      setLoading(false);
      // Stop blocking MCP requests when import is done
      mcpBlocker.stopBlocking();
    }
  };

  const handlePreviewConfirm = (finalData) => {
    setShowPreview(false);
    setSuccess('Profile imported successfully!');
    onImportComplete(finalData);
    setUrl(''); // Clear the input
    setExtractedData(null);
    // Ensure MCP blocking is stopped when import is complete
    mcpBlocker.stopBlocking();
  };

  const handlePreviewCancel = () => {
    setShowPreview(false);
    setExtractedData(null);
    // Stop blocking MCP requests when import is cancelled
    mcpBlocker.stopBlocking();
  };

  return (
    <>
      {showPreview && extractedData && (
        <ImportPreview
          extractedData={extractedData}
          onConfirm={handlePreviewConfirm}
          onCancel={handlePreviewCancel}
        />
      )}

      <div className="website-importer">
      <div className="importer-header">
        <FaGlobe className="importer-icon" />
        <div>
          <h4>1-Click Website Import</h4>
          <p>Automatically configure your profile from your law firm website</p>
        </div>
      </div>

      <form onSubmit={handleImport} className="importer-form">
        <div className="url-input-group">
          <input
            type="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://yourfirm.com"
            className="url-input"
            disabled={loading || disabled}
          />
          <button
            type="submit"
            className="import-button"
            disabled={loading || disabled || !url.trim()}
          >
            {loading ? (
              <>
                <FaSpinner className="spinning" />
                Importing...
              </>
            ) : (
              <>
                <FaMagic />
                Import
              </>
            )}
          </button>
        </div>

        {error && (
          <div className="import-message error">
            <FaExclamationTriangle />
            {error}
          </div>
        )}

        {success && (
          <div className="import-message success">
            <FaCheck />
            {success}
          </div>
        )}
      </form>

      <div className="import-features">
        <small>
          ✨ Automatically extracts: Firm name, colors, logo, practice areas, contact info, and more
        </small>
      </div>
      </div>
    </>
  );
};

export default WebsiteImporter;
