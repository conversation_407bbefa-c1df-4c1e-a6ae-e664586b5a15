/* Sessions/Workflow Tab Styling */

/* Full-width layout for Sessions tab */
.workflow-tab {
  width: 100%;
  max-width: none;
  padding: 1.5rem;
}

.workflow-tab .tab-header {
  margin-bottom: 1.5rem;
}

.workflow-tab .tab-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary, #212529);
  font-size: 1.75rem;
  font-weight: 600;
}

.workflow-tab .tab-header p {
  margin: 0;
  color: var(--text-secondary, #6c757d);
  font-size: 1rem;
  line-height: 1.5;
}

/* Section tabs with improved styling */
.section-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  background-color: var(--background-light, #f8f9fa);
  border-radius: 8px;
  padding: 0.25rem;
}

.section-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: var(--text-secondary, #6c757d);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.section-tab:hover {
  background-color: var(--background-hover, #e9ecef);
  color: var(--text-primary, #212529);
}

.section-tab.active {
  background-color: var(--primary-color, #D85722);
  color: white;
  box-shadow: 0 2px 4px rgba(216, 87, 34, 0.2);
}

.section-tab svg {
  font-size: 1rem;
}

/* Section content with full-width utilization */
.section-content {
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.section-header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary, #212529);
  font-size: 1.5rem;
  font-weight: 600;
}

.section-header p {
  margin: 0;
  color: var(--text-secondary, #6c757d);
  font-size: 1rem;
  line-height: 1.5;
  flex: 1;
}

/* Template grid with better spacing */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

/* My Sessions specific styles */
.my-sessions .sessions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1rem;
}

.session-category {
  background: var(--card-background, #ffffff);
  border: 1px solid var(--border-color-light, #e9ecef);
  border-radius: 8px;
  padding: 1.5rem;
}

.session-category h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary, #212529);
  font-size: 1.2rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color, #D85722);
}

.session-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.session-card {
  background: var(--background-light, #f8f9fa);
  border: 1px solid var(--border-color, #ced4da);
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.session-card:hover {
  border-color: var(--primary-color-light, rgba(216, 87, 34, 0.3));
  box-shadow: 0 2px 8px rgba(216, 87, 34, 0.1);
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.session-header h5 {
  margin: 0;
  color: var(--text-primary, #212529);
  font-size: 1rem;
  font-weight: 600;
}

.session-actions {
  display: flex;
  gap: 0.25rem;
}

.session-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: var(--text-secondary, #6c757d);
}

.session-members {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary, #6c757d);
}

/* Session History styles */
.session-history .history-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.history-card {
  background: var(--card-background, #ffffff);
  border: 1px solid var(--border-color-light, #e9ecef);
  border-radius: 8px;
  padding: 1.25rem;
  transition: all 0.2s ease;
}

.history-card:hover {
  border-color: var(--primary-color-light, rgba(216, 87, 34, 0.3));
  box-shadow: 0 4px 12px rgba(216, 87, 34, 0.1);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.history-header h5 {
  margin: 0;
  color: var(--text-primary, #212529);
  font-size: 1.1rem;
  font-weight: 600;
}

.session-date {
  font-size: 0.85rem;
  color: var(--text-secondary, #6c757d);
}

.history-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.85rem;
  color: var(--text-secondary, #6c757d);
}

.history-actions {
  display: flex;
  gap: 0.5rem;
}

/* Empty states */
.empty-state-inline {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--background-light, #f8f9fa);
  border: 1px dashed var(--border-color, #ced4da);
  border-radius: 6px;
  color: var(--text-secondary, #6c757d);
  font-size: 0.9rem;
}

.empty-state-inline svg {
  color: var(--primary-color, #D85722);
  font-size: 1.2rem;
}

/* Button improvements */
.btn-primary, .btn-secondary, .btn-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--primary-color, #D85722);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-color-dark, #c44a1a);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(216, 87, 34, 0.2);
}

.btn-secondary {
  background-color: var(--background-light, #f8f9fa);
  color: var(--text-secondary, #6c757d);
  border: 1px solid var(--border-color, #ced4da);
}

.btn-secondary:hover {
  background-color: var(--background-hover, #e9ecef);
  color: var(--text-primary, #212529);
}

.btn-icon {
  padding: 0.5rem;
  background-color: transparent;
  color: var(--text-secondary, #6c757d);
}

.btn-icon:hover {
  background-color: var(--background-light, #f8f9fa);
  color: var(--text-primary, #212529);
}

.btn-icon.danger:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

/* Responsive design for full-width */
@media (max-width: 1200px) {
  .my-sessions .sessions-grid {
    grid-template-columns: 1fr;
  }

  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .workflow-tab {
    padding: 1rem;
  }

  .section-tabs {
    flex-wrap: wrap;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
  }

  .template-grid {
    grid-template-columns: 1fr;
  }

  .history-list {
    grid-template-columns: 1fr;
  }
}

/* Dark theme support */
[data-theme="dark"] .workflow-tab {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .workflow-tab .tab-header h2 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .workflow-tab .tab-header p {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .section-tabs {
  background-color: var(--dark-background-light, #2c2c2c);
}

[data-theme="dark"] .section-tab {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .section-tab:hover {
  background-color: var(--dark-background-hover, #3c3c3c);
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .section-tab.active {
  background-color: var(--primary-color, #D85722);
  color: white;
}

[data-theme="dark"] .section-header h3 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .section-header p {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .session-category {
  background: var(--dark-card-background, #1e1e1e);
  border-color: var(--dark-border-color, #444);
}

[data-theme="dark"] .session-category h4 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .session-card {
  background: var(--dark-background-light, #2c2c2c);
  border-color: var(--dark-border-color, #444);
}

[data-theme="dark"] .session-card:hover {
  border-color: var(--dark-primary-color-light, rgba(216, 87, 34, 0.4));
  box-shadow: 0 2px 8px rgba(216, 87, 34, 0.15);
}

[data-theme="dark"] .session-header h5 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .session-meta,
[data-theme="dark"] .session-members,
[data-theme="dark"] .session-date,
[data-theme="dark"] .history-details {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .history-card {
  background: var(--dark-card-background, #1e1e1e);
  border-color: var(--dark-border-color, #444);
}

[data-theme="dark"] .history-card:hover {
  border-color: var(--dark-primary-color-light, rgba(216, 87, 34, 0.4));
  box-shadow: 0 4px 12px rgba(216, 87, 34, 0.15);
}

[data-theme="dark"] .history-header h5 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .empty-state-inline {
  background-color: var(--dark-background-light, #2c2c2c);
  border-color: var(--dark-border-color, #444);
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .btn-secondary {
  background-color: var(--dark-background-light, #2c2c2c);
  color: var(--dark-text-secondary, #adb5bd);
  border-color: var(--dark-border-color, #444);
}

[data-theme="dark"] .btn-secondary:hover {
  background-color: var(--dark-background-hover, #3c3c3c);
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .btn-icon {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .btn-icon:hover {
  background-color: var(--dark-background-light, #2c2c2c);
  color: var(--dark-text-primary, #f8f9fa);
}

/* Sessions/Workflow Tab Modal Styling */

/* Modal Overlay with proper theme support */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  pointer-events: auto;
}

/* Modern Modal Content with theme support */
.modal-content {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid rgba(100, 181, 246, 0.2);
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  z-index: 10000;
  pointer-events: auto;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
}

/* Dark theme modal styling */
[data-theme="dark"] .modal-content {
  background: linear-gradient(135deg,
    rgba(18, 18, 20, 0.95) 0%,
    rgba(24, 24, 28, 0.95) 100%);
  border: 1px solid rgba(100, 181, 246, 0.3);
  color: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(100, 181, 246, 0.1);
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid rgba(100, 181, 246, 0.15);
  background: linear-gradient(90deg,
    rgba(100, 181, 246, 0.05) 0%,
    rgba(144, 202, 249, 0.05) 100%);
  border-radius: 16px 16px 0 0;
}

[data-theme="dark"] .modal-header {
  border-bottom: 1px solid rgba(100, 181, 246, 0.2);
  background: linear-gradient(90deg,
    rgba(100, 181, 246, 0.08) 0%,
    rgba(144, 202, 249, 0.08) 100%);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #1976d2;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[data-theme="dark"] .modal-header h3 {
  color: #64b5f6;
  background: linear-gradient(135deg, #64b5f6, #90caf9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Close Button */
.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: rgba(100, 181, 246, 0.1);
  color: #1976d2;
  transform: scale(1.1);
}

[data-theme="dark"] .modal-close {
  color: #aaa;
}

[data-theme="dark"] .modal-close:hover {
  background-color: rgba(100, 181, 246, 0.15);
  color: #64b5f6;
}

/* Modal Body */
.modal-body {
  padding: 24px 28px;
}

/* Template Info Section */
.template-info {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg,
    rgba(100, 181, 246, 0.05) 0%,
    rgba(144, 202, 249, 0.05) 100%);
  border: 1px solid rgba(100, 181, 246, 0.15);
  border-radius: 12px;
}

[data-theme="dark"] .template-info {
  background: linear-gradient(135deg,
    rgba(100, 181, 246, 0.08) 0%,
    rgba(144, 202, 249, 0.08) 100%);
  border: 1px solid rgba(100, 181, 246, 0.2);
}

.template-info h4 {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1976d2;
}

[data-theme="dark"] .template-info h4 {
  color: #64b5f6;
}

.template-info p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

[data-theme="dark"] .template-info p {
  color: rgba(255, 255, 255, 0.7);
}

/* Human Roles Section */
.human-roles {
  margin-bottom: 24px;
}

.human-roles h5 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

[data-theme="dark"] .human-roles h5 {
  color: rgba(255, 255, 255, 0.9);
}

.role-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(100, 181, 246, 0.05);
  border: 1px solid rgba(100, 181, 246, 0.1);
  border-radius: 10px;
  transition: all 0.2s ease;
}

[data-theme="dark"] .role-item {
  background: rgba(100, 181, 246, 0.08);
  border: 1px solid rgba(100, 181, 246, 0.15);
}

.role-item svg {
  color: #1976d2;
  font-size: 1.2rem;
}

[data-theme="dark"] .role-item svg {
  color: #64b5f6;
}

.role-item div {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-item strong {
  font-weight: 600;
  color: #333;
}

[data-theme="dark"] .role-item strong {
  color: rgba(255, 255, 255, 0.9);
}

.role-item span {
  font-size: 0.9rem;
  color: #666;
}

[data-theme="dark"] .role-item span {
  color: rgba(255, 255, 255, 0.6);
}

/* Invite Section */
.invite-section {
  margin-bottom: 24px;
}

.invite-section h5 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

[data-theme="dark"] .invite-section h5 {
  color: rgba(255, 255, 255, 0.9);
}

.invite-input {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.invite-input input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid rgba(100, 181, 246, 0.3);
  border-radius: 8px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.invite-input input:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
  background: rgba(255, 255, 255, 1);
}

[data-theme="dark"] .invite-input input {
  background: rgba(24, 24, 28, 0.6);
  border: 1px solid rgba(100, 181, 246, 0.3);
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .invite-input input:focus {
  border-color: #64b5f6;
  box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.15);
  background: rgba(24, 24, 28, 0.8);
}

/* Buttons */
.btn-secondary, .btn-primary {
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 100px;
}

.btn-secondary {
  background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
  color: #666;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #e0e0e0, #d5d5d5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .btn-secondary {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .btn-secondary:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1));
  color: rgba(255, 255, 255, 0.9);
}

.btn-primary {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  border: 1px solid rgba(25, 118, 210, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #1565c0, #1976d2);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(25, 118, 210, 0.3);
}

.btn-primary:disabled {
  background: linear-gradient(135deg, #ccc, #bbb);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

[data-theme="dark"] .btn-primary {
  background: linear-gradient(135deg, #64b5f6, #90caf9);
  color: #121212;
}

[data-theme="dark"] .btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #42a5f5, #64b5f6);
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 28px 24px;
  border-top: 1px solid rgba(100, 181, 246, 0.15);
  background: linear-gradient(90deg,
    rgba(100, 181, 246, 0.02) 0%,
    rgba(144, 202, 249, 0.02) 100%);
  border-radius: 0 0 16px 16px;
}

[data-theme="dark"] .modal-footer {
  border-top: 1px solid rgba(100, 181, 246, 0.2);
  background: linear-gradient(90deg,
    rgba(100, 181, 246, 0.05) 0%,
    rgba(144, 202, 249, 0.05) 100%);
}

/* Participant Type Selector */
.participant-type-selector {
  margin-bottom: 24px;
}

.participant-type-selector h5 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

[data-theme="dark"] .participant-type-selector h5 {
  color: rgba(255, 255, 255, 0.9);
}

.type-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: rgba(100, 181, 246, 0.05);
  border: 2px solid rgba(100, 181, 246, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.type-option:hover {
  background: rgba(100, 181, 246, 0.08);
  border-color: rgba(100, 181, 246, 0.2);
  transform: translateY(-1px);
}

.type-option.active {
  background: linear-gradient(135deg,
    rgba(25, 118, 210, 0.1) 0%,
    rgba(66, 165, 245, 0.1) 100%);
  border-color: #1976d2;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
}

[data-theme="dark"] .type-option {
  background: rgba(100, 181, 246, 0.08);
  border: 2px solid rgba(100, 181, 246, 0.15);
}

[data-theme="dark"] .type-option:hover {
  background: rgba(100, 181, 246, 0.12);
  border-color: rgba(100, 181, 246, 0.25);
}

[data-theme="dark"] .type-option.active {
  background: linear-gradient(135deg,
    rgba(100, 181, 246, 0.15) 0%,
    rgba(144, 202, 249, 0.15) 100%);
  border-color: #64b5f6;
  box-shadow: 0 4px 12px rgba(100, 181, 246, 0.2);
}

.type-option svg {
  font-size: 1.5rem;
  color: #1976d2;
  flex-shrink: 0;
}

[data-theme="dark"] .type-option svg {
  color: #64b5f6;
}

.type-option span {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

[data-theme="dark"] .type-option span {
  color: rgba(255, 255, 255, 0.9);
}

.type-option small {
  display: block;
  color: #666;
  font-size: 0.9rem;
  margin-top: 4px;
}

[data-theme="dark"] .type-option small {
  color: rgba(255, 255, 255, 0.6);
}

/* Participant Form */
.participant-form {
  margin-top: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

[data-theme="dark"] .form-group label {
  color: rgba(255, 255, 255, 0.9);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(100, 181, 246, 0.3);
  border-radius: 8px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
  background: rgba(255, 255, 255, 1);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group select,
[data-theme="dark"] .form-group textarea {
  background: rgba(24, 24, 28, 0.6);
  border: 1px solid rgba(100, 181, 246, 0.3);
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .form-group input:focus,
[data-theme="dark"] .form-group select:focus,
[data-theme="dark"] .form-group textarea:focus {
  border-color: #64b5f6;
  box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.15);
  background: rgba(24, 24, 28, 0.8);
}

.form-note {
  margin-top: 12px;
  padding: 12px 16px;
  background: rgba(100, 181, 246, 0.05);
  border: 1px solid rgba(100, 181, 246, 0.1);
  border-radius: 8px;
}

[data-theme="dark"] .form-note {
  background: rgba(100, 181, 246, 0.08);
  border: 1px solid rgba(100, 181, 246, 0.15);
}

.form-note small {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

[data-theme="dark"] .form-note small {
  color: rgba(255, 255, 255, 0.7);
}

/* Email List */
.invited-emails {
  margin-top: 16px;
}

.invited-emails h6 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

[data-theme="dark"] .invited-emails h6 {
  color: rgba(255, 255, 255, 0.9);
}

.email-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.email-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(100, 181, 246, 0.05);
  border: 1px solid rgba(100, 181, 246, 0.1);
  border-radius: 8px;
}

[data-theme="dark"] .email-item {
  background: rgba(100, 181, 246, 0.08);
  border: 1px solid rgba(100, 181, 246, 0.15);
}

.email-item span {
  color: #333;
  font-weight: 500;
}

[data-theme="dark"] .email-item span {
  color: rgba(255, 255, 255, 0.9);
}

.remove-email {
  background: none;
  border: none;
  color: #f44336;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.remove-email:hover {
  background: rgba(244, 67, 54, 0.1);
  transform: scale(1.1);
}
