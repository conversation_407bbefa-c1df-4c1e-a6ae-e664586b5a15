import React, { useState, useEffect } from 'react';
import { FaPlus, FaPlay, FaStop, FaPause, FaEdit, FaTrash, FaUsers, FaRobot, FaCog, FaProjectDiagram, FaPhone, FaComments, FaGavel, FaFileContract, FaSearch, FaShieldAlt, FaEnvelope } from 'react-icons/fa';
import { vapiMcpService } from '../../services/vapiMcpService';
import './WorkflowTab.css';

const SessionsTab = ({ attorney, onUpdate, previewConfig }) => {
  const [activeSection, setActiveSection] = useState('templates');
  const [mySessions, setMySessions] = useState([]);
  const [activeSessions, setActiveSessions] = useState([]);
  const [sessionHistory, setSessionHistory] = useState([]);
  const [savedTemplates, setSavedTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [activeSession, setActiveSession] = useState(null);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [selectedTemplateForInvite, setSelectedTemplateForInvite] = useState(null);
  const [inviteEmails, setInviteEmails] = useState([]);
  const [currentInviteEmail, setCurrentInviteEmail] = useState('');
  const [showAddParticipantModal, setShowAddParticipantModal] = useState(false);
  const [participantType, setParticipantType] = useState('email'); // 'email', 'phone', 'ai'
  const [phoneNumber, setPhoneNumber] = useState('');
  const [participantName, setParticipantName] = useState('');
  const [participantRole, setParticipantRole] = useState('');
  const [selectedAiModel, setSelectedAiModel] = useState('gpt-4o');
  const [aiInstructions, setAiInstructions] = useState('');
  const [templateFilter, setTemplateFilter] = useState('all'); // 'all', 'attorney-focused', 'client-communication'

  // Legal session templates inspired by NexusMind collaborative model
  const sessionTemplates = [
    {
      id: 'legal-consultation-nexus',
      name: 'Legal Consultation Nexus',
      description: '2 Humans + 2 AIs collaborative legal consultation session',
      icon: <FaGavel />,
      type: 'collaborative',
      category: 'attorney-focused',
      humanRoles: [
        {
          name: 'Lead Attorney',
          role: 'Session facilitator and final decision maker',
          canInvite: true
        },
        {
          name: 'Legal Associate',
          role: 'Research support and case analysis',
          canInvite: true
        }
      ],
      agents: [
        {
          name: 'Legal Analyst AI',
          role: 'Case research, precedent analysis, and legal strategy',
          model: 'claude-3-opus',
          voice: 'andrew',
          tools: ['legal-research', 'precedent-finder', 'case-analysis']
        },
        {
          name: 'Client Advocate AI',
          role: 'Client perspective analysis and risk assessment',
          model: 'gpt-4o',
          voice: 'sarah',
          tools: ['client-analysis', 'risk-assessment', 'outcome-prediction']
        }
      ]
    },
    {
      id: 'contract-negotiation-nexus',
      name: 'Contract Negotiation Nexus',
      description: '2 Humans + 2 AIs for strategic contract analysis and negotiation',
      icon: <FaFileContract />,
      type: 'collaborative',
      category: 'attorney-focused',
      humanRoles: [
        {
          name: 'Contract Attorney',
          role: 'Lead negotiator and contract strategy',
          canInvite: true
        },
        {
          name: 'Business Advisor',
          role: 'Commercial terms and business impact analysis',
          canInvite: true
        }
      ],
      agents: [
        {
          name: 'Contract Analyzer AI',
          role: 'Document analysis, clause extraction, and risk identification',
          model: 'claude-3-opus',
          voice: 'andrew',
          tools: ['document-parser', 'clause-extractor', 'risk-analysis']
        },
        {
          name: 'Negotiation Strategist AI',
          role: 'Negotiation tactics, precedent analysis, and deal optimization',
          model: 'gpt-4o',
          voice: 'sarah',
          tools: ['negotiation-strategy', 'precedent-finder', 'deal-optimizer']
        }
      ]
    },
    {
      id: 'litigation-strategy-nexus',
      name: 'Litigation Strategy Nexus',
      description: '2 Humans + 2 AIs for comprehensive litigation planning',
      icon: <FaShieldAlt />,
      type: 'collaborative',
      category: 'attorney-focused',
      humanRoles: [
        {
          name: 'Lead Litigator',
          role: 'Case strategy and courtroom tactics',
          canInvite: true
        },
        {
          name: 'Paralegal Specialist',
          role: 'Evidence coordination and case management',
          canInvite: true
        }
      ],
      agents: [
        {
          name: 'Case Research AI',
          role: 'Legal research, precedent analysis, and case law discovery',
          model: 'claude-3-opus',
          voice: 'andrew',
          tools: ['case-law-search', 'precedent-analysis', 'legal-research']
        },
        {
          name: 'Evidence Strategist AI',
          role: 'Evidence analysis, timeline construction, and argument development',
          model: 'gpt-4o',
          voice: 'sarah',
          tools: ['evidence-analyzer', 'timeline-builder', 'argument-mapper']
        }
      ]
    },
    {
      id: 'client-problem-diagnosis',
      name: 'Client Problem Diagnosis Squad',
      description: '2 Humans + 2 AIs for complex legal problem analysis',
      icon: <FaSearch />,
      type: 'collaborative',
      category: 'attorney-focused',
      humanRoles: [
        {
          name: 'Senior Attorney',
          role: 'Problem assessment and solution strategy',
          canInvite: true
        },
        {
          name: 'Subject Matter Expert',
          role: 'Specialized knowledge and industry context',
          canInvite: true
        }
      ],
      agents: [
        {
          name: 'Problem Analyst AI',
          role: 'Root cause analysis and issue identification',
          model: 'claude-3-opus',
          voice: 'andrew',
          tools: ['problem-analyzer', 'root-cause-finder', 'issue-mapper']
        },
        {
          name: 'Solution Architect AI',
          role: 'Solution development and implementation planning',
          model: 'gpt-4o',
          voice: 'sarah',
          tools: ['solution-generator', 'implementation-planner', 'risk-mitigator']
        }
      ]
    },
    {
      id: 'client-consultation-nexus',
      name: 'Client Consultation Nexus',
      description: '2 Attorneys + Client + AI Advisor for comprehensive client meetings',
      icon: <FaComments />,
      type: 'collaborative',
      category: 'client-communication',
      humanRoles: [
        {
          name: 'Lead Attorney',
          role: 'Primary legal counsel and session facilitator',
          canInvite: true
        },
        {
          name: 'Client',
          role: 'Client seeking legal advice and guidance',
          canInvite: true,
          isClient: true
        }
      ],
      agents: [
        {
          name: 'Legal Advisor AI',
          role: 'Legal research, case precedents, and strategic recommendations',
          model: 'claude-3-opus',
          voice: 'andrew',
          tools: ['legal-research', 'precedent-finder', 'case-analysis']
        },
        {
          name: 'Client Communication AI',
          role: 'Translate legal concepts into plain language for client understanding',
          model: 'gpt-4o',
          voice: 'sarah',
          tools: ['plain-language-translator', 'client-education', 'document-explainer']
        }
      ]
    },
    {
      id: 'client-intake-nexus',
      name: 'Client Intake & Assessment Nexus',
      description: '1 Attorney + Client + 2 AI Specialists for comprehensive intake process',
      icon: <FaUsers />,
      type: 'collaborative',
      category: 'client-communication',
      humanRoles: [
        {
          name: 'Intake Attorney',
          role: 'Initial client assessment and case evaluation',
          canInvite: true
        },
        {
          name: 'Prospective Client',
          role: 'Individual seeking legal representation',
          canInvite: true,
          isClient: true
        }
      ],
      agents: [
        {
          name: 'Intake Specialist AI',
          role: 'Structured information gathering and case categorization',
          model: 'gpt-4o',
          voice: 'sarah',
          tools: ['intake-questionnaire', 'case-categorizer', 'conflict-checker']
        },
        {
          name: 'Case Evaluator AI',
          role: 'Initial case merit assessment and resource estimation',
          model: 'claude-3-opus',
          voice: 'andrew',
          tools: ['case-merit-analyzer', 'resource-estimator', 'timeline-predictor']
        }
      ]
    },
    {
      id: 'client-settlement-nexus',
      name: 'Settlement Discussion Nexus',
      description: '2 Attorneys + Client + Negotiation AI for settlement negotiations',
      icon: <FaGavel />,
      type: 'collaborative',
      category: 'client-communication',
      humanRoles: [
        {
          name: 'Lead Negotiator',
          role: 'Primary settlement negotiation attorney',
          canInvite: true
        },
        {
          name: 'Client',
          role: 'Decision maker for settlement terms',
          canInvite: true,
          isClient: true
        }
      ],
      agents: [
        {
          name: 'Settlement Analyzer AI',
          role: 'Settlement value analysis and comparison with similar cases',
          model: 'claude-3-opus',
          voice: 'andrew',
          tools: ['settlement-calculator', 'case-comparison', 'risk-assessment']
        },
        {
          name: 'Negotiation Strategist AI',
          role: 'Negotiation tactics and communication strategy',
          model: 'gpt-4o',
          voice: 'sarah',
          tools: ['negotiation-tactics', 'communication-optimizer', 'concession-planner']
        }
      ]
    },
    {
      id: 'client-crisis-response',
      name: 'Crisis Response Team',
      description: '2 Attorneys + Client + Crisis AI for urgent legal situations',
      icon: <FaShieldAlt />,
      type: 'collaborative',
      category: 'client-communication',
      humanRoles: [
        {
          name: 'Crisis Attorney',
          role: 'Emergency legal response and immediate action coordination',
          canInvite: true
        },
        {
          name: 'Client',
          role: 'Individual facing urgent legal crisis',
          canInvite: true,
          isClient: true
        }
      ],
      agents: [
        {
          name: 'Crisis Analyzer AI',
          role: 'Rapid situation assessment and priority identification',
          model: 'gpt-4o',
          voice: 'andrew',
          tools: ['crisis-assessor', 'priority-ranker', 'immediate-actions']
        },
        {
          name: 'Response Coordinator AI',
          role: 'Action plan development and resource mobilization',
          model: 'claude-3-opus',
          voice: 'sarah',
          tools: ['action-planner', 'resource-coordinator', 'timeline-manager']
        }
      ]
    },
    {
      id: 'family-mediation-nexus',
      name: 'Family Mediation Nexus',
      description: '1 Mediator + 2 Parties + Mediation AI for family law disputes',
      icon: <FaUsers />,
      type: 'collaborative',
      category: 'client-communication',
      humanRoles: [
        {
          name: 'Family Mediator',
          role: 'Neutral mediator facilitating family dispute resolution',
          canInvite: true
        },
        {
          name: 'Party A',
          role: 'First party in family dispute',
          canInvite: true,
          isClient: true
        },
        {
          name: 'Party B',
          role: 'Second party in family dispute',
          canInvite: true,
          isClient: true
        }
      ],
      agents: [
        {
          name: 'Mediation Facilitator AI',
          role: 'Neutral facilitation and communication guidance',
          model: 'claude-3-opus',
          voice: 'sarah',
          tools: ['mediation-techniques', 'communication-facilitator', 'emotion-detector']
        },
        {
          name: 'Solution Generator AI',
          role: 'Creative solution development and option generation',
          model: 'gpt-4o',
          voice: 'andrew',
          tools: ['solution-generator', 'option-evaluator', 'fairness-assessor']
        }
      ]
    },
    {
      id: 'client-education-nexus',
      name: 'Legal Education Session',
      description: '1 Attorney + Multiple Clients + Education AI for legal workshops',
      icon: <FaCog />,
      type: 'collaborative',
      category: 'client-communication',
      humanRoles: [
        {
          name: 'Education Attorney',
          role: 'Legal educator and workshop facilitator',
          canInvite: true
        },
        {
          name: 'Client Participants',
          role: 'Clients attending legal education workshop',
          canInvite: true,
          isClient: true,
          allowMultiple: true
        }
      ],
      agents: [
        {
          name: 'Education Specialist AI',
          role: 'Curriculum delivery and interactive learning facilitation',
          model: 'gpt-4o',
          voice: 'sarah',
          tools: ['curriculum-manager', 'interactive-quiz', 'progress-tracker']
        },
        {
          name: 'Q&A Assistant AI',
          role: 'Real-time question answering and clarification',
          model: 'claude-3-opus',
          voice: 'andrew',
          tools: ['question-answerer', 'concept-clarifier', 'example-generator']
        }
      ]
    }
  ];

  // Load existing sessions
  useEffect(() => {
    loadMySessions();
    loadActiveSessions();
    loadSessionHistory();
    loadSavedTemplates();
  }, []);

  const loadMySessions = async () => {
    try {
      setLoading(true);
      // Load my saved sessions and custom configurations
      setMySessions([]);
    } catch (error) {
      console.error('Error loading my sessions:', error);
      setError('Failed to load my sessions');
    } finally {
      setLoading(false);
    }
  };

  const loadActiveSessions = async () => {
    try {
      setLoading(true);
      // Load active sessions from Vapi MCP
      if (vapiMcpService.connected) {
        const sessionList = await vapiMcpService.listSquads(); // Backend still uses squads
        setActiveSessions(sessionList || []);
      }
    } catch (error) {
      console.error('Error loading active sessions:', error);
      setError('Failed to load active sessions');
    } finally {
      setLoading(false);
    }
  };

  const loadSessionHistory = async () => {
    try {
      // Load completed session history
      setSessionHistory([]);
    } catch (error) {
      console.error('Error loading session history:', error);
      setError('Failed to load session history');
    }
  };

  const loadSavedTemplates = async () => {
    try {
      // Load user's saved custom templates
      setSavedTemplates([]);
    } catch (error) {
      console.error('Error loading saved templates:', error);
      setError('Failed to load saved templates');
    }
  };

  const createSessionFromTemplate = async (template) => {
    try {
      setLoading(true);

      // Create assistants for each agent in the template
      const assistants = [];
      for (const agent of template.agents) {
        const assistantConfig = {
          name: `${attorney.firm_name} - ${agent.name}`,
          firstMessage: `Hello, I'm ${agent.name} from ${attorney.firm_name}. ${agent.role}`,
          model: {
            provider: agent.model.includes('claude') ? 'anthropic' : 'openai',
            model: agent.model,
            messages: [{
              role: 'system',
              content: `You are ${agent.name}, a specialized AI assistant for ${attorney.firm_name}. Your role is: ${agent.role}. You work as part of a team to provide comprehensive legal services.`
            }]
          },
          voice: {
            provider: '11labs',
            voiceId: agent.voice
          }
          // Note: Tools will be added separately after assistant creation
        };

        const assistant = await vapiMcpService.createAssistant(assistantConfig);
        assistants.push(assistant);
      }

      // Create session with the assistants (backend uses squads)
      const sessionConfig = {
        name: `${attorney.firm_name} - ${template.name}`,
        members: assistants.map(assistant => ({ assistantId: assistant.id }))
      };

      const session = await vapiMcpService.createSquad(sessionConfig); // Backend API

      // Refresh active sessions list
      await loadActiveSessions();

      setSelectedTemplate(null);
      setError(null);
    } catch (error) {
      console.error('Error creating session:', error);
      setError('Failed to create session from template');
    } finally {
      setLoading(false);
    }
  };

  const startSession = async (session) => {
    try {
      setLoading(true);

      // Create a call using the session (backend uses squad ID)
      const callConfig = {
        squadId: session.id, // Backend API parameter
        customer: {
          phoneNumber: '+1234567890' // This would be dynamic in real use
        }
      };

      const call = await vapiMcpService.createCall(callConfig);
      setActiveSession(call);
      setActiveSection('active'); // Switch to active tab

    } catch (error) {
      console.error('Error starting session:', error);
      setError('Failed to start session');
    } finally {
      setLoading(false);
    }
  };

  const startDemoSession = () => {
    // Create a demo session for demonstration
    setActiveSession({
      id: 'demo-session-' + Date.now(),
      type: 'demo',
      status: 'active',
      sessionName: 'Legal Consultation Session',
      startedAt: new Date().toISOString(),
      participants: [
        { name: 'You', type: 'human', role: 'Lead Attorney', status: 'active' },
        { name: 'Sarah Johnson', type: 'human', role: 'Legal Associate', status: 'invited' },
        { name: 'Legal Analyst AI', type: 'ai', role: 'Case Research', status: 'active' },
        { name: 'Client Advocate AI', type: 'ai', role: 'Risk Assessment', status: 'standby' }
      ]
    });
    setActiveSection('active');
  };

  const createCollaborativeSession = (template) => {
    if (template.type === 'collaborative') {
      setSelectedTemplateForInvite(template);
      setShowInviteModal(true);
    } else {
      createSessionFromTemplate(template);
    }
  };

  const addInviteEmail = () => {
    if (currentInviteEmail && !inviteEmails.includes(currentInviteEmail)) {
      setInviteEmails([...inviteEmails, currentInviteEmail]);
      setCurrentInviteEmail('');
    }
  };

  const removeInviteEmail = (email) => {
    setInviteEmails(inviteEmails.filter(e => e !== email));
  };

  const launchCollaborativeSession = async () => {
    try {
      setLoading(true);

      // Create the session first
      await createSessionFromTemplate(selectedTemplateForInvite);

      // TODO: Send email invitations to participants
      // This would integrate with your email service
      console.log('Sending invitations to:', inviteEmails);

      // Create a collaborative session record
      const sessionData = {
        templateId: selectedTemplateForInvite.id,
        templateName: selectedTemplateForInvite.name,
        invitedEmails: inviteEmails,
        createdBy: attorney.id,
        createdAt: new Date().toISOString(),
        status: 'pending'
      };

      // TODO: Save session to Supabase
      console.log('Creating collaborative session:', sessionData);

      setShowInviteModal(false);
      setInviteEmails([]);
      setSelectedTemplateForInvite(null);

    } catch (error) {
      console.error('Error launching collaborative session:', error);
      setError('Failed to launch collaborative session');
    } finally {
      setLoading(false);
    }
  };

  const addParticipantToSession = async () => {
    try {
      setLoading(true);

      if (participantType === 'email') {
        // Add email participant
        const newParticipant = {
          id: 'participant-' + Date.now(),
          name: participantName || currentInviteEmail.split('@')[0],
          email: currentInviteEmail,
          type: 'human',
          role: participantRole || 'Collaborator',
          status: 'invited',
          addedAt: new Date().toISOString()
        };

        // Update active session participants
        if (activeSession) {
          setActiveSession({
            ...activeSession,
            participants: [...(activeSession.participants || []), newParticipant]
          });
        }

        // TODO: Send email invitation
        console.log('Sending invitation to:', currentInviteEmail);

      } else if (participantType === 'phone') {
        // Add phone participant via call
        const callConfig = {
          customer: {
            phoneNumber: phoneNumber,
            name: participantName || 'Phone Participant'
          },
          assistantId: activeWorkflow?.squadId, // Use the squad's assistant
          metadata: {
            sessionId: activeWorkflow?.id,
            participantRole: participantRole || 'Phone Participant'
          }
        };

        const call = await vapiMcpService.createCall(callConfig);

        const newParticipant = {
          id: 'participant-' + Date.now(),
          name: participantName || 'Phone Participant',
          phone: phoneNumber,
          type: 'human',
          role: participantRole || 'Phone Participant',
          status: 'calling',
          callId: call.id,
          addedAt: new Date().toISOString()
        };

        // Update active workflow participants
        if (activeWorkflow) {
          setActiveWorkflow({
            ...activeWorkflow,
            participants: [...(activeWorkflow.participants || []), newParticipant]
          });
        }

      } else if (participantType === 'ai') {
        // Add new AI agent
        const assistantConfig = {
          name: `${attorney.firm_name} - ${participantName}`,
          firstMessage: `Hello, I'm ${participantName}. ${aiInstructions}`,
          model: {
            provider: selectedAiModel.includes('claude') ? 'anthropic' : 'openai',
            model: selectedAiModel,
            messages: [{
              role: 'system',
              content: aiInstructions || `You are ${participantName}, an AI assistant helping with this collaborative session.`
            }]
          },
          voice: {
            provider: '11labs',
            voiceId: 'sarah'
          }
        };

        const assistant = await vapiMcpService.createAssistant(assistantConfig);

        const newParticipant = {
          id: 'participant-' + Date.now(),
          name: participantName,
          type: 'ai',
          role: participantRole || 'AI Assistant',
          status: 'active',
          assistantId: assistant.id,
          model: selectedAiModel,
          addedAt: new Date().toISOString()
        };

        // Update active workflow participants
        if (activeWorkflow) {
          setActiveWorkflow({
            ...activeWorkflow,
            participants: [...(activeWorkflow.participants || []), newParticipant]
          });
        }
      }

      // Reset form
      setCurrentInviteEmail('');
      setPhoneNumber('');
      setParticipantName('');
      setParticipantRole('');
      setAiInstructions('');
      setShowAddParticipantModal(false);

    } catch (error) {
      console.error('Error adding participant:', error);
      setError('Failed to add participant to session');
    } finally {
      setLoading(false);
    }
  };

  const removeParticipantFromSession = async (participantId) => {
    try {
      if (activeWorkflow) {
        const participant = activeWorkflow.participants.find(p => p.id === participantId);

        // If it's a phone participant with an active call, end the call
        if (participant?.callId) {
          // TODO: End the call via Vapi API
          console.log('Ending call for participant:', participant.callId);
        }

        // Remove from participants list
        setActiveWorkflow({
          ...activeWorkflow,
          participants: activeWorkflow.participants.filter(p => p.id !== participantId)
        });
      }
    } catch (error) {
      console.error('Error removing participant:', error);
      setError('Failed to remove participant');
    }
  };

  const renderTemplates = () => {
    const filteredTemplates = sessionTemplates.filter(template => {
      if (templateFilter === 'all') return true;
      return template.category === templateFilter;
    });

    return (
      <div className="session-templates">
        <div className="section-header">
          <h3>Session Templates</h3>
          <p>Pre-configured multi-agent sessions for legal scenarios</p>

          <div className="template-filters">
            <button
              className={`filter-btn ${templateFilter === 'all' ? 'active' : ''}`}
              onClick={() => setTemplateFilter('all')}
            >
              All Templates ({sessionTemplates.length})
            </button>
            <button
              className={`filter-btn ${templateFilter === 'attorney-focused' ? 'active' : ''}`}
              onClick={() => setTemplateFilter('attorney-focused')}
            >
              Attorney Sessions ({sessionTemplates.filter(t => t.category === 'attorney-focused').length})
            </button>
            <button
              className={`filter-btn ${templateFilter === 'client-communication' ? 'active' : ''}`}
              onClick={() => setTemplateFilter('client-communication')}
            >
              Client Communication ({sessionTemplates.filter(t => t.category === 'client-communication').length})
            </button>
          </div>

          <button
            className="btn-secondary demo-btn"
            onClick={startDemoSession}
          >
            <FaPlay />
            Try Demo Session
          </button>
        </div>

        <div className="template-grid">
          {filteredTemplates.map(template => (
          <div key={template.id} className="template-card" data-category={template.category}>
            <div className="template-header">
              <div className="template-icon">{template.icon}</div>
              <h4>{template.name}</h4>
            </div>
            <p className="template-description">{template.description}</p>

            <div className="template-participants">
              <div className="participant-section">
                <h5>Human Participants ({template.humanRoles?.length || 0})</h5>
                <div className="participant-list">
                  {template.humanRoles?.map((human, index) => (
                    <div key={index} className={`participant-item human ${human.isClient ? 'client' : ''}`}>
                      <FaUsers />
                      <span>{human.name}</span>
                      {human.isClient && <small>👤 Client</small>}
                    </div>
                  )) || <div className="participant-item">No human roles defined</div>}
                </div>
              </div>

              <div className="participant-section">
                <h5>AI Agents ({template.agents.length})</h5>
                <div className="participant-list">
                  {template.agents.map((agent, index) => (
                    <div key={index} className="participant-item ai">
                      <FaRobot />
                      <span>{agent.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <button
              className="btn-primary"
              onClick={() => createCollaborativeSession(template)}
              disabled={loading}
            >
              {template.type === 'collaborative' ? <FaUsers /> : <FaPlus />}
              {template.type === 'collaborative' ? 'Start Session' : 'Create Session'}
            </button>
          </div>
          ))}
        </div>
      </div>
    );
  };

  const renderMySessions = () => (
    <div className="my-sessions">
      <div className="section-header">
        <h3>My Sessions</h3>
        <p>Your saved session templates and custom configurations</p>
        <button className="btn-primary">
          <FaPlus />
          Create Custom Session
        </button>
      </div>

      <div className="sessions-grid">
        {/* Saved Templates */}
        <div className="session-category">
          <h4>Saved Templates ({savedTemplates.length})</h4>
          {savedTemplates.length === 0 ? (
            <div className="empty-state-inline">
              <FaCog />
              <p>No saved templates yet. Customize and save templates from the Templates tab.</p>
            </div>
          ) : (
            <div className="session-list">
              {savedTemplates.map(template => (
                <div key={template.id} className="session-card saved-template">
                  <div className="session-header">
                    <h5>{template.name}</h5>
                    <div className="session-actions">
                      <button className="btn-secondary" onClick={() => startSession(template)}>
                        <FaPlay />
                        Start
                      </button>
                      <button className="btn-icon">
                        <FaEdit />
                      </button>
                      <button className="btn-icon danger">
                        <FaTrash />
                      </button>
                    </div>
                  </div>
                  <p>{template.description}</p>
                  <div className="session-meta">
                    <span>{template.agents?.length || 0} AI agents</span>
                    <span>{template.humanRoles?.length || 0} human roles</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Ready Sessions */}
        <div className="session-category">
          <h4>Ready Sessions ({mySessions.length})</h4>
          {mySessions.length === 0 ? (
            <div className="empty-state-inline">
              <FaUsers />
              <p>No ready sessions yet. Create sessions from templates to get started.</p>
            </div>
          ) : (
            <div className="session-list">
              {mySessions.map(session => (
                <div key={session.id} className="session-card ready-session">
                  <div className="session-header">
                    <h5>{session.name}</h5>
                    <div className="session-actions">
                      <button className="btn-secondary" onClick={() => startSession(session)}>
                        <FaPlay />
                        Start
                      </button>
                      <button className="btn-icon">
                        <FaEdit />
                      </button>
                      <button className="btn-icon danger">
                        <FaTrash />
                      </button>
                    </div>
                  </div>
                  <div className="session-members">
                    <span>{session.members?.length || 0} AI agents configured</span>
                    <FaRobot />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderSessionHistory = () => (
    <div className="session-history">
      <div className="section-header">
        <h3>Session History</h3>
        <p>Past multi-agent sessions and their outcomes</p>
      </div>

      {sessionHistory.length === 0 ? (
        <div className="empty-state">
          <FaUsers />
          <h4>No session history yet</h4>
          <p>Complete your first session to see history here</p>
        </div>
      ) : (
        <div className="history-list">
          {sessionHistory.map(session => (
            <div key={session.id} className="history-card">
              <div className="history-header">
                <h5>{session.name}</h5>
                <span className="session-date">{new Date(session.completedAt).toLocaleDateString()}</span>
              </div>
              <div className="history-details">
                <span className="duration">Duration: {session.duration}</span>
                <span className="participants">{session.participantCount} participants</span>
                <span className={`status ${session.status}`}>{session.status}</span>
              </div>
              <div className="history-actions">
                <button className="btn-secondary">
                  <FaPlay />
                  View Transcript
                </button>
                <button className="btn-secondary">
                  <FaCog />
                  Reuse Template
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderActiveWorkflows = () => (
    <div className="active-workflows">
      <div className="section-header">
        <h3>Active Workflows</h3>
        <p>Currently running multi-agent sessions</p>
      </div>

      {activeSession ? (
        <div className="workflow-monitor">
          <div className="workflow-status">
            <h4>Legal Consultation Workflow</h4>
            <span className="status-indicator active">Running</span>
          </div>

          <div className="workflow-progress">
            <div className="participants-header">
              <h5>Session Participants ({activeSession.participants?.length || 0})</h5>
              <button
                className="btn-secondary add-participant-btn"
                onClick={() => setShowAddParticipantModal(true)}
              >
                <FaPlus />
                Add Participant
              </button>
            </div>
            <div className="participant-grid">
              {activeSession.participants?.map((participant, index) => (
                <div key={index} className={`participant-card ${participant.type} ${participant.status}`}>
                  <div className="participant-avatar">
                    {participant.type === 'human' ? <FaUsers /> : <FaRobot />}
                  </div>
                  <div className="participant-info">
                    <h6>{participant.name}</h6>
                    <span className="participant-role">{participant.role}</span>
                    <span className={`participant-status ${participant.status}`}>
                      {participant.status === 'active' && '🟢 Active'}
                      {participant.status === 'invited' && '🟡 Invited'}
                      {participant.status === 'calling' && '📞 Calling'}
                      {participant.status === 'standby' && '⚪ Standby'}
                    </span>
                    {participant.phone && (
                      <span className="participant-contact">📞 {participant.phone}</span>
                    )}
                    {participant.email && (
                      <span className="participant-contact">✉️ {participant.email}</span>
                    )}
                  </div>
                  <div className="participant-actions">
                    {participant.status === 'active' && (
                      <div className="participant-indicator">
                        <div className="pulse-dot"></div>
                      </div>
                    )}
                    <button
                      className="btn-icon danger"
                      onClick={() => removeParticipantFromSession(participant.id)}
                      title="Remove participant"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </div>
              ))}

              {/* Add Participant Card */}
              <div
                className="participant-card add-participant-card"
                onClick={() => setShowAddParticipantModal(true)}
              >
                <div className="add-participant-content">
                  <FaPlus />
                  <span>Add Participant</span>
                  <small>Email, Phone, or AI</small>
                </div>
              </div>
            </div>
          </div>

          <div className="workflow-transcript">
            <h5>Live Transcript</h5>
            <div className="transcript-container">
              <div className="transcript-message user">
                <span className="speaker">Client:</span>
                <span className="message">I need help with a contract dispute...</span>
                <span className="timestamp">2:34 PM</span>
              </div>
              <div className="transcript-message assistant">
                <span className="speaker">Intake Specialist:</span>
                <span className="message">I understand you're dealing with a contract dispute. Can you tell me more about the nature of the contract?</span>
                <span className="timestamp">2:34 PM</span>
              </div>
            </div>
          </div>

          <div className="workflow-controls">
            <button className="btn-secondary">
              <FaPause />
              Pause
            </button>
            <button className="btn-secondary">
              <FaComments />
              Join Call
            </button>
            <button className="btn-danger">
              <FaStop />
              End Session
            </button>
          </div>
        </div>
      ) : (
        <div className="empty-state">
          <FaProjectDiagram />
          <h4>No active workflows</h4>
          <p>Start a squad to begin a multi-agent workflow</p>
          <button
            className="btn-primary"
            onClick={() => setActiveSection('templates')}
            style={{ marginTop: '1rem', width: 'auto' }}
          >
            <FaPlus />
            Create Your First Workflow
          </button>
        </div>
      )}
    </div>
  );

  return (
    <div className="workflow-tab">
      <div className="tab-header">
        <h2>Sessions</h2>
        <p>Manage multi-agent AI sessions for collaborative legal work</p>
      </div>

      {error && (
        <div className="error-message">
          <span>{error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="section-tabs">
        <button
          className={`section-tab ${activeSection === 'templates' ? 'active' : ''}`}
          onClick={() => setActiveSection('templates')}
        >
          <FaProjectDiagram />
          <span>Templates</span>
        </button>

        <button
          className={`section-tab ${activeSection === 'my-sessions' ? 'active' : ''}`}
          onClick={() => setActiveSection('my-sessions')}
        >
          <FaCog />
          <span>My Sessions</span>
        </button>

        <button
          className={`section-tab ${activeSection === 'active' ? 'active' : ''}`}
          onClick={() => setActiveSection('active')}
        >
          <FaPlay />
          <span>Active</span>
        </button>

        <button
          className={`section-tab ${activeSection === 'history' ? 'active' : ''}`}
          onClick={() => setActiveSection('history')}
        >
          <FaUsers />
          <span>History</span>
        </button>
      </div>

      <div className="section-content">
        {activeSection === 'templates' && renderTemplates()}
        {activeSection === 'my-sessions' && renderMySessions()}
        {activeSection === 'active' && renderActiveWorkflows()}
        {activeSection === 'history' && renderSessionHistory()}
      </div>

      {/* Invite Modal */}
      {showInviteModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Invite Collaborators</h3>
              <button
                className="modal-close"
                onClick={() => setShowInviteModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="template-info">
                <h4>{selectedTemplateForInvite?.name}</h4>
                <p>{selectedTemplateForInvite?.description}</p>
              </div>

              <div className="human-roles">
                <h5>Human Participants Needed:</h5>
                <div className="role-list">
                  {selectedTemplateForInvite?.humanRoles?.map((role, index) => (
                    <div key={index} className="role-item">
                      <FaUsers />
                      <div>
                        <strong>{role.name}</strong>
                        <span>{role.role}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="invite-section">
                <h5>Invite by Email:</h5>
                <div className="invite-input">
                  <input
                    type="email"
                    value={currentInviteEmail}
                    onChange={(e) => setCurrentInviteEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    onKeyPress={(e) => e.key === 'Enter' && addInviteEmail()}
                  />
                  <button
                    className="btn-secondary"
                    onClick={addInviteEmail}
                    disabled={!currentInviteEmail}
                  >
                    Add
                  </button>
                </div>

                {inviteEmails.length > 0 && (
                  <div className="invited-emails">
                    <h6>Invited Participants:</h6>
                    <div className="email-list">
                      {inviteEmails.map((email, index) => (
                        <div key={index} className="email-item">
                          <span>{email}</span>
                          <button
                            className="remove-email"
                            onClick={() => removeInviteEmail(email)}
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="modal-footer">
              <button
                className="btn-secondary"
                onClick={() => setShowInviteModal(false)}
              >
                Cancel
              </button>
              <button
                className="btn-primary"
                onClick={launchCollaborativeSession}
                disabled={loading || inviteEmails.length === 0}
              >
                {loading ? 'Creating...' : 'Launch Session'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Participant Modal */}
      {showAddParticipantModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Add Participant to Session</h3>
              <button
                className="modal-close"
                onClick={() => setShowAddParticipantModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="participant-type-selector">
                <h5>How would you like to add this participant?</h5>
                <div className="type-options">
                  <button
                    className={`type-option ${participantType === 'email' ? 'active' : ''}`}
                    onClick={() => setParticipantType('email')}
                  >
                    <FaEnvelope />
                    <span>Email Invitation</span>
                    <small>Send an email invite to join the session</small>
                  </button>

                  <button
                    className={`type-option ${participantType === 'phone' ? 'active' : ''}`}
                    onClick={() => setParticipantType('phone')}
                  >
                    <FaPhone />
                    <span>Phone Call</span>
                    <small>Call them directly into the session</small>
                  </button>

                  <button
                    className={`type-option ${participantType === 'ai' ? 'active' : ''}`}
                    onClick={() => setParticipantType('ai')}
                  >
                    <FaRobot />
                    <span>AI Agent</span>
                    <small>Add a specialized AI assistant</small>
                  </button>
                </div>
              </div>

              {/* Email Participant Form */}
              {participantType === 'email' && (
                <div className="participant-form">
                  <div className="form-group">
                    <label>Email Address *</label>
                    <input
                      type="email"
                      value={currentInviteEmail}
                      onChange={(e) => setCurrentInviteEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="form-group">
                    <label>Name (Optional)</label>
                    <input
                      type="text"
                      value={participantName}
                      onChange={(e) => setParticipantName(e.target.value)}
                      placeholder="John Doe"
                    />
                  </div>
                  <div className="form-group">
                    <label>Role in Session</label>
                    <input
                      type="text"
                      value={participantRole}
                      onChange={(e) => setParticipantRole(e.target.value)}
                      placeholder="Legal Advisor, Expert Witness, etc."
                    />
                  </div>
                </div>
              )}

              {/* Phone Participant Form */}
              {participantType === 'phone' && (
                <div className="participant-form">
                  <div className="form-group">
                    <label>Phone Number *</label>
                    <input
                      type="tel"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      placeholder="+****************"
                    />
                  </div>
                  <div className="form-group">
                    <label>Name *</label>
                    <input
                      type="text"
                      value={participantName}
                      onChange={(e) => setParticipantName(e.target.value)}
                      placeholder="John Doe"
                    />
                  </div>
                  <div className="form-group">
                    <label>Role in Session</label>
                    <input
                      type="text"
                      value={participantRole}
                      onChange={(e) => setParticipantRole(e.target.value)}
                      placeholder="Client, Expert, Witness, etc."
                    />
                  </div>
                  <div className="form-note">
                    <small>📞 We'll call this number immediately and connect them to the session.</small>
                  </div>
                </div>
              )}

              {/* AI Agent Form */}
              {participantType === 'ai' && (
                <div className="participant-form">
                  <div className="form-group">
                    <label>AI Agent Name *</label>
                    <input
                      type="text"
                      value={participantName}
                      onChange={(e) => setParticipantName(e.target.value)}
                      placeholder="Document Analyzer, Risk Assessor, etc."
                    />
                  </div>
                  <div className="form-group">
                    <label>AI Model</label>
                    <select
                      value={selectedAiModel}
                      onChange={(e) => setSelectedAiModel(e.target.value)}
                    >
                      <option value="gpt-4o">GPT-4 Omni (OpenAI)</option>
                      <option value="gpt-4">GPT-4 (OpenAI)</option>
                      <option value="claude-3-opus">Claude 3 Opus (Anthropic)</option>
                      <option value="claude-3-sonnet">Claude 3 Sonnet (Anthropic)</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Role & Instructions *</label>
                    <textarea
                      value={aiInstructions}
                      onChange={(e) => setAiInstructions(e.target.value)}
                      placeholder="You are a specialized legal AI assistant. Your role is to..."
                      rows="4"
                    />
                  </div>
                  <div className="form-note">
                    <small>🤖 This AI agent will be created and added to the session immediately.</small>
                  </div>
                </div>
              )}
            </div>

            <div className="modal-footer">
              <button
                className="btn-secondary"
                onClick={() => setShowAddParticipantModal(false)}
              >
                Cancel
              </button>
              <button
                className="btn-primary"
                onClick={addParticipantToSession}
                disabled={loading ||
                  (participantType === 'email' && !currentInviteEmail) ||
                  (participantType === 'phone' && (!phoneNumber || !participantName)) ||
                  (participantType === 'ai' && (!participantName || !aiInstructions))
                }
              >
                {loading ? 'Adding...' :
                 participantType === 'email' ? 'Send Invitation' :
                 participantType === 'phone' ? 'Call Now' :
                 'Create AI Agent'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SessionsTab;
