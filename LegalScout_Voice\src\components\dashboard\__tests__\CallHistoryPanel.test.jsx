import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CallHistoryPanel from '../CallHistoryPanel';
import { callHistoryService } from '../../../services/callHistoryService';

// Mock the callHistoryService
jest.mock('../../../services/callHistoryService', () => ({
  callHistoryService: {
    getCallHistory: jest.fn(),
    getCallStatistics: jest.fn(),
    formatStatus: jest.fn(),
    formatDuration: jest.fn()
  }
}));

describe('CallHistoryPanel', () => {
  const mockAttorneyId = 'test-attorney-id';
  const mockOnViewDetails = jest.fn();
  
  const mockCalls = [
    {
      call_id: 'call-1',
      start_time: '2023-01-01T12:00:00Z',
      customer_phone: '+1234567890',
      duration: 120,
      status: 'completed'
    },
    {
      call_id: 'call-2',
      start_time: '2023-01-02T14:30:00Z',
      customer_phone: '+0987654321',
      duration: 300,
      status: 'failed'
    },
    {
      call_id: 'call-3',
      start_time: '2023-01-03T09:15:00Z',
      customer_phone: '+1122334455',
      duration: 180,
      status: 'in_progress'
    }
  ];
  
  const mockStatistics = {
    totalCalls: 10,
    completedCalls: 7,
    failedCalls: 3,
    totalDuration: 1800,
    averageDuration: 180,
    completionRate: 70
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock the formatStatus function
    callHistoryService.formatStatus.mockImplementation((status) => {
      const statusMap = {
        completed: { label: 'Completed', color: 'green' },
        failed: { label: 'Failed', color: 'red' },
        in_progress: { label: 'In Progress', color: 'blue' }
      };
      return statusMap[status] || { label: status, color: 'gray' };
    });
    
    // Mock the formatDuration function
    callHistoryService.formatDuration.mockImplementation((seconds) => {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    });
    
    // Mock the getCallHistory function
    callHistoryService.getCallHistory.mockResolvedValue({
      calls: mockCalls,
      total: mockCalls.length,
      page: 1,
      limit: 10
    });
    
    // Mock the getCallStatistics function
    callHistoryService.getCallStatistics.mockResolvedValue(mockStatistics);
  });
  
  test('renders loading state initially', () => {
    render(
      <CallHistoryPanel
        attorneyId={mockAttorneyId}
        limit={10}
        onViewDetails={mockOnViewDetails}
      />
    );
    
    expect(screen.getByText('Loading call history...')).toBeInTheDocument();
  });
  
  test('renders call history after loading', async () => {
    render(
      <CallHistoryPanel
        attorneyId={mockAttorneyId}
        limit={10}
        onViewDetails={mockOnViewDetails}
      />
    );
    
    // Wait for the call history to load
    await waitFor(() => {
      expect(callHistoryService.getCallHistory).toHaveBeenCalledWith(
        mockAttorneyId,
        expect.objectContaining({
          limit: 10,
          page: 1
        })
      );
    });
    
    // Check if the call history table is rendered
    expect(screen.getByText('Date')).toBeInTheDocument();
    expect(screen.getByText('Time')).toBeInTheDocument();
    expect(screen.getByText('Phone')).toBeInTheDocument();
    expect(screen.getByText('Duration')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
    
    // Check if the call data is rendered
    expect(screen.getByText('+1234567890')).toBeInTheDocument();
    expect(screen.getByText('+0987654321')).toBeInTheDocument();
    expect(screen.getByText('+1122334455')).toBeInTheDocument();
    
    // Check if the formatted durations are rendered
    expect(screen.getByText('2:00')).toBeInTheDocument();
    expect(screen.getByText('5:00')).toBeInTheDocument();
    expect(screen.getByText('3:00')).toBeInTheDocument();
    
    // Check if the status badges are rendered
    expect(screen.getByText('Completed')).toBeInTheDocument();
    expect(screen.getByText('Failed')).toBeInTheDocument();
    expect(screen.getByText('In Progress')).toBeInTheDocument();
  });
  
  test('renders statistics after loading', async () => {
    render(
      <CallHistoryPanel
        attorneyId={mockAttorneyId}
        limit={10}
        onViewDetails={mockOnViewDetails}
      />
    );
    
    // Wait for the statistics to load
    await waitFor(() => {
      expect(callHistoryService.getCallStatistics).toHaveBeenCalledWith(
        mockAttorneyId,
        expect.any(Object)
      );
    });
    
    // Check if the statistics are rendered
    expect(screen.getByText('10')).toBeInTheDocument(); // totalCalls
    expect(screen.getByText('7')).toBeInTheDocument(); // completedCalls
    expect(screen.getByText('3')).toBeInTheDocument(); // failedCalls
    expect(screen.getByText('3:00')).toBeInTheDocument(); // averageDuration
    expect(screen.getByText('70.0%')).toBeInTheDocument(); // completionRate
  });
  
  test('calls onViewDetails when View Details button is clicked', async () => {
    render(
      <CallHistoryPanel
        attorneyId={mockAttorneyId}
        limit={10}
        onViewDetails={mockOnViewDetails}
      />
    );
    
    // Wait for the call history to load
    await waitFor(() => {
      expect(callHistoryService.getCallHistory).toHaveBeenCalled();
    });
    
    // Find all View Details buttons
    const viewDetailsButtons = screen.getAllByText('View Details');
    
    // Click the first View Details button
    fireEvent.click(viewDetailsButtons[0]);
    
    // Check if onViewDetails was called with the correct call ID
    expect(mockOnViewDetails).toHaveBeenCalledWith('call-1');
  });
  
  test('changes page when pagination buttons are clicked', async () => {
    // Mock multiple pages
    callHistoryService.getCallHistory.mockResolvedValueOnce({
      calls: mockCalls,
      total: 30, // 3 pages with limit 10
      page: 1,
      limit: 10
    });
    
    render(
      <CallHistoryPanel
        attorneyId={mockAttorneyId}
        limit={10}
        onViewDetails={mockOnViewDetails}
      />
    );
    
    // Wait for the call history to load
    await waitFor(() => {
      expect(callHistoryService.getCallHistory).toHaveBeenCalled();
    });
    
    // Check if pagination is rendered
    expect(screen.getByText('Page 1 of 3')).toBeInTheDocument();
    
    // Reset mock to return page 2
    callHistoryService.getCallHistory.mockResolvedValueOnce({
      calls: mockCalls,
      total: 30,
      page: 2,
      limit: 10
    });
    
    // Click the Next button
    fireEvent.click(screen.getByText('Next'));
    
    // Check if getCallHistory was called with page 2
    await waitFor(() => {
      expect(callHistoryService.getCallHistory).toHaveBeenCalledWith(
        mockAttorneyId,
        expect.objectContaining({
          page: 2
        })
      );
    });
    
    // Check if pagination is updated
    expect(screen.getByText('Page 2 of 3')).toBeInTheDocument();
    
    // Reset mock to return page 1
    callHistoryService.getCallHistory.mockResolvedValueOnce({
      calls: mockCalls,
      total: 30,
      page: 1,
      limit: 10
    });
    
    // Click the Previous button
    fireEvent.click(screen.getByText('Previous'));
    
    // Check if getCallHistory was called with page 1
    await waitFor(() => {
      expect(callHistoryService.getCallHistory).toHaveBeenCalledWith(
        mockAttorneyId,
        expect.objectContaining({
          page: 1
        })
      );
    });
    
    // Check if pagination is updated
    expect(screen.getByText('Page 1 of 3')).toBeInTheDocument();
  });
  
  test('applies filters when changed', async () => {
    render(
      <CallHistoryPanel
        attorneyId={mockAttorneyId}
        limit={10}
        onViewDetails={mockOnViewDetails}
      />
    );
    
    // Wait for the call history to load
    await waitFor(() => {
      expect(callHistoryService.getCallHistory).toHaveBeenCalled();
    });
    
    // Change status filter to 'completed'
    fireEvent.change(screen.getByLabelText('Status:'), { target: { value: 'completed' } });
    
    // Check if getCallHistory was called with the status filter
    await waitFor(() => {
      expect(callHistoryService.getCallHistory).toHaveBeenCalledWith(
        mockAttorneyId,
        expect.objectContaining({
          status: 'completed'
        })
      );
    });
    
    // Set a start date
    const startDate = '2023-01-01';
    fireEvent.change(screen.getByLabelText('From:'), { target: { value: startDate } });
    
    // Check if getCallHistory was called with the start date
    await waitFor(() => {
      expect(callHistoryService.getCallHistory).toHaveBeenCalledWith(
        mockAttorneyId,
        expect.objectContaining({
          startDate: expect.any(String)
        })
      );
    });
    
    // Set an end date
    const endDate = '2023-01-31';
    fireEvent.change(screen.getByLabelText('To:'), { target: { value: endDate } });
    
    // Check if getCallHistory was called with the end date
    await waitFor(() => {
      expect(callHistoryService.getCallHistory).toHaveBeenCalledWith(
        mockAttorneyId,
        expect.objectContaining({
          endDate: expect.any(String)
        })
      );
    });
  });
  
  test('renders empty state when no calls are found', async () => {
    // Mock empty call history
    callHistoryService.getCallHistory.mockResolvedValueOnce({
      calls: [],
      total: 0,
      page: 1,
      limit: 10
    });
    
    render(
      <CallHistoryPanel
        attorneyId={mockAttorneyId}
        limit={10}
        onViewDetails={mockOnViewDetails}
      />
    );
    
    // Wait for the call history to load
    await waitFor(() => {
      expect(callHistoryService.getCallHistory).toHaveBeenCalled();
    });
    
    // Check if empty state is rendered
    expect(screen.getByText('No calls found.')).toBeInTheDocument();
  });
  
  test('renders error message when loading fails', async () => {
    // Mock error
    const errorMessage = 'Failed to load call history';
    callHistoryService.getCallHistory.mockRejectedValueOnce(new Error(errorMessage));
    
    render(
      <CallHistoryPanel
        attorneyId={mockAttorneyId}
        limit={10}
        onViewDetails={mockOnViewDetails}
      />
    );
    
    // Wait for the error to be displayed
    await waitFor(() => {
      expect(screen.getByText('Failed to load call history. Please try again.')).toBeInTheDocument();
    });
  });
});
