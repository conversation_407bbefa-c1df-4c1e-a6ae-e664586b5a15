.config-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.config-modal {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  position: relative;
}

[data-theme="dark"] .config-modal {
  background-color: #1a1c20;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

[data-theme="dark"] .close-button {
  color: #aaa;
}

[data-theme="dark"] .close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.config-modal h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 24px;
  color: #D85722;
  text-align: center;
}

.modal-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.start-option {
  padding: 20px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .start-option {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.start-option h3 {
  margin-top: 0;
  color: #D85722;
  font-size: 18px;
}

.input-group {
  margin-top: 15px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #555;
}

[data-theme="dark"] .input-group label {
  color: #ccc;
}

.modern-input, .modern-select {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  font-size: 16px;
  transition: all 0.2s ease;
}

.modern-input:focus, .modern-select:focus {
  border-color: #D85722;
  outline: none;
  box-shadow: 0 0 0 2px rgba(216, 87, 34, 0.2);
}

[data-theme="dark"] .modern-input, [data-theme="dark"] .modern-select {
  background-color: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.modern-button {
  background-color: #D85722;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 15px;
  width: 100%;
  font-weight: 500;
}

.modern-button:hover {
  background-color: #c04d1e;
  transform: translateY(-1px);
}

.modern-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.or-divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
}

.divider-line {
  flex-grow: 1;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .divider-line {
  background-color: rgba(255, 255, 255, 0.1);
}

.divider-text {
  padding: 0 15px;
  color: #888;
  font-size: 14px;
  font-weight: 500;
}

[data-theme="dark"] .divider-text {
  color: #aaa;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.spinner {
  animation: spin 1s linear infinite;
  width: 20px;
  height: 20px;
}

.spinner-circle {
  fill: none;
  stroke: white;
  stroke-width: 2;
  stroke-dasharray: 60, 200;
  stroke-dashoffset: 0;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}
