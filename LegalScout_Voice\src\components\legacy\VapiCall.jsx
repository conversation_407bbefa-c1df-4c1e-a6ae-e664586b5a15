/**
 * LEGACY COMPONENT - KEPT FOR REFERENCE ONLY
 * 
 * This is the original VapiCall component that has been replaced by EnhancedVapiCall.
 * It is kept here for reference purposes only.
 * 
 * For new implementations, please use EnhancedVapiCall instead.
 */

import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react'
import { withDevTools } from '../../utils/debugConfig'
import useVapiCall from '../../hooks/useVapiCall'
import useVapiEmissions from '../../hooks/useVapiEmissions'
import { CALL_STATUS, DEFAULT_ASSISTANT_ID } from '../../constants/vapiConstants'
import '../VapiCall.css'
import TextShimmerWave from '../TextShimmerWave'
import SpeechParticles from '../SpeechParticles'
import FirecrawlResultsDisplay from '../search/FirecrawlResultsDisplay'
import { createDemoPreviewConfig, createAttorneyPreviewConfig } from '../../utils/previewConfigHandler'

// Create a global variable to track if the call is active
// This will prevent the component from being unmounted while the call is active
window.vapiCallActive = false;

/**
 * Component for handling voice calls with Vapi.ai assistant
 *
 * This component manages:
 * - Call initialization and connection
 * - Audio processing
 * - Message handling
 * - Dossier data collection
 *
 * @param {Object} props Component props
 * @param {Function} props.onEndCall Callback function when call ends
 * @param {string} props.subdomain Attorney subdomain for customization
 * @param {Object} props.customInstructions Custom instructions for the Vapi call
 * @param {Object} props.assistantOverrides Overrides for the Vapi assistant configuration
 * @param {boolean} props.isDemo Whether this is a demo preview (uses default assistant)
 * @param {Object} props.attorneyData Attorney data from Supabase (for attorney-specific preview)
 * @param {boolean} props.forceDefaultAssistant Whether to force using the default assistant
 * @param {number} props.initializationDelay Delay in milliseconds before starting the call (default: 500)
 */
const VapiCall = ({
  onEndCall,
  subdomain = 'default',
  customInstructions = null,
  assistantOverrides = null,
  assistantId = null,
  isDemo = false,
  attorneyData = null,
  forceDefaultAssistant = false,
  initializationDelay = 500
}) => {
  // Ensure customInstructions is always an object with default values
  const defaultCustomInstructions = {
    firmName: 'LegalScout',
    welcomeMessage: 'Hello, I\'m Scout from LegalScout. How can I help you today?',
    voiceId: 'sarah',
    voiceProvider: 'playht'
  };

  // Merge provided customInstructions with defaults - using useMemo to prevent recreation on every render
  const mergedCustomInstructions = useMemo(() => ({
    ...defaultCustomInstructions,
    ...(customInstructions || {})
  }), [customInstructions]);

  // Create default assistantOverrides if none provided - using useMemo to prevent recreation on every render
  const defaultAssistantOverrides = useMemo(() => ({
    firstMessage: mergedCustomInstructions.welcomeMessage,
    firstMessageMode: 'assistant-speaks-first',
    voice: {
      provider: mergedCustomInstructions.voiceProvider || '11labs',
      voiceId: mergedCustomInstructions.voiceId || 'sarah'
    }
  }), [mergedCustomInstructions.welcomeMessage, mergedCustomInstructions.voiceProvider, mergedCustomInstructions.voiceId]);

  // Use provided assistantOverrides or defaults - using useMemo to prevent recreation on every render
  const mergedAssistantOverrides = useMemo(() =>
    assistantOverrides || defaultAssistantOverrides
  , [assistantOverrides, defaultAssistantOverrides]);
  
  // Process configuration based on whether this is a demo or attorney-specific preview
  const [processedConfig, setProcessedConfig] = useState(null);

  // Process the configuration on component mount
  useEffect(() => {
    // Skip if we already have a processed config
    if (processedConfig) {
      return;
    }

    let config;

    // If forceDefaultAssistant is true, always use the default assistant ID without any overrides
    if (forceDefaultAssistant) {
      console.log('VapiCall: Using default assistant without overrides');
      config = {
        previewConfig: {},
        vapiConfig: {
          assistantId: DEFAULT_ASSISTANT_ID,
          assistantOverrides: null // No overrides at all for default assistant
        }
      };
    } else if (isDemo) {
      // For demo preview, use the default assistant with template overrides
      config = createDemoPreviewConfig(mergedCustomInstructions);
      console.log('VapiCall: Using demo preview configuration');
    } else if (attorneyData) {
      // For attorney dashboard preview, use attorney-specific configuration
      config = createAttorneyPreviewConfig(attorneyData);
      console.log('VapiCall: Using attorney preview configuration');
    } else {
      // Fallback to direct configuration
      config = {
        previewConfig: mergedCustomInstructions,
        vapiConfig: {
          assistantId: assistantId || mergedCustomInstructions.assistantId || DEFAULT_ASSISTANT_ID,
          assistantOverrides: mergedAssistantOverrides
        }
      };
      console.log('VapiCall: Using direct configuration');
    }

    console.log('VapiCall: Setting processed config with assistantId:',
      forceDefaultAssistant ? DEFAULT_ASSISTANT_ID : (config.vapiConfig.assistantId || 'Not set'));

    setProcessedConfig(config);
  }, [isDemo, attorneyData, mergedCustomInstructions, mergedAssistantOverrides, assistantId, forceDefaultAssistant, processedConfig]);

  // Use our custom hook to manage call state and functionality
  const {
    status,
    dossierData,
    volumeLevel,
    assistantIsSpeaking,
    errorMessage,
    subdomainConfig,
    startCall,
    stopCall,
    vapi,
    messageHistory,
    callId
  } = useVapiCall({
    subdomain,
    onEndCall,
    // Only pass customInstructions if we're not forcing the default assistant
    customInstructions: forceDefaultAssistant ? null : (processedConfig?.previewConfig || mergedCustomInstructions),
    // Only pass assistantOverrides if explicitly set in processedConfig and not null
    assistantOverrides: processedConfig?.vapiConfig?.assistantOverrides === null ? null :
                        (processedConfig?.vapiConfig?.assistantOverrides || mergedAssistantOverrides),
    // Always pass the assistantId
    assistantId: forceDefaultAssistant ? DEFAULT_ASSISTANT_ID : processedConfig?.vapiConfig?.assistantId
  });

  // State for messages
  const [messages, setMessages] = useState([]);
  // State for current transcript
  const [currentTranscript, setCurrentTranscript] = useState('');
  // Ref for messages end
  const messagesEndRef = useRef(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle transcript
  const handleTranscript = (data) => {
    if (!data || !data.transcript) return;

    // Update current transcript
    setCurrentTranscript(data.transcript);

    // If final, add to messages
    if (data.is_final) {
      setMessages(prev => [...prev, {
        type: 'user',
        text: data.transcript,
        timestamp: new Date().toISOString(),
        animated: false
      }]);
      setCurrentTranscript('');
    }
  };

  // Render the call interface based on status
  const renderCallInterface = () => {
    switch (status) {
      case CALL_STATUS.CONNECTING:
        return (
          <div className="call-status connecting">
            <h2>Connecting to voice assistant...</h2>
            <div className="connecting-animation">
              <div className="dot"></div>
              <div className="dot"></div>
              <div className="dot"></div>
            </div>
          </div>
        );
      case CALL_STATUS.CONNECTED:
        return (
          <div className="three-column-layout">
            {/* Left column - Dossier */}
            <div className="column left-column">
              {dossierData && Object.keys(dossierData).length > 0 && (
                <div className="dossier">
                  <h3>Consultation Information</h3>
                  <div className="dossier-content">
                    {Object.entries(dossierData).map(([key, value]) => (
                      <div key={key} className="dossier-item">
                        <span className="dossier-key">{key.replace(/_/g, ' ')}:</span>
                        <span className="dossier-value">{value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Middle column - Conversation */}
            <div className="column middle-column">
              <div className="conversation-area">
                {messages.map((message, index) => (
                  <div key={index} className={`message ${message.type}`}>
                    <div className="message-content">
                      {message.animated ? (
                        <TextShimmerWave text={message.text} />
                      ) : (
                        <p>{message.text}</p>
                      )}
                    </div>
                  </div>
                ))}
                {currentTranscript && (
                  <div className="message user transcribing">
                    <div className="message-content">
                      <p>{currentTranscript}</p>
                      <div className="typing-indicator">...</div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Right column - Voice indicators */}
            <div className="column right-column">
              <div className="voice-indicators">
                <div className="voice-status">
                  <span className="status-label">Status:</span>
                  <span className="status-value">Connected</span>
                </div>
                <div className="speaking-indicator">
                  {assistantIsSpeaking ? 'Assistant is speaking...' : 'Listening...'}
                </div>
                <div className="volume-indicator">
                  <div className="volume-label">Volume:</div>
                  <div className="volume-bars">
                    {[...Array(10)].map((_, i) => (
                      <div
                        key={i}
                        className={`volume-bar ${i < Math.floor(volumeLevel * 10) ? 'active' : ''}`}
                      ></div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case CALL_STATUS.ERROR:
        return (
          <div className="call-status error">
            <h2>Error connecting to voice assistant</h2>
            <p>{errorMessage || 'An unknown error occurred. Please try again later.'}</p>
            <button className="retry-button" onClick={startCall}>
              Retry
            </button>
          </div>
        );
      default:
        return (
          <div className="call-status idle">
            <h2>Ready to start voice call</h2>
            <button className="start-button" onClick={startCall}>
              Start Call
            </button>
          </div>
        );
    }
  };

  return (
    <div className="vapi-call-container">
      {/* Speech Particles Visualization (visible when connected) */}
      {status === CALL_STATUS.CONNECTED && (
        <SpeechParticles className="speech-particles" />
      )}

      {/* Globe Background (visible when connected) */}
      {status === CALL_STATUS.CONNECTED && (
        <div className="globe-background">
          <div className="globe-visualization">
            <div className="globe-sphere"></div>
            <div className="globe-highlight usa-highlight">
              <span className="location-label">USA begins highlighted all warm blue, as location updates zooms in</span>
            </div>
          </div>
        </div>
      )}

      {/* Call Interface */}
      <div className="call-interface">
        {renderCallInterface()}
      </div>

      {/* End Call Button (visible when connected) */}
      {status === CALL_STATUS.CONNECTED && (
        <button className="end-call-button" onClick={stopCall}>
          <img src="/icons/end-call.svg" alt="End call" />
          End Call
        </button>
      )}
    </div>
  );
};

export default VapiCall;
