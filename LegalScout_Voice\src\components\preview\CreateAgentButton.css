@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500&display=swap');

/* CreateAgentButton styles */
.create-agent-overlay {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  pointer-events: none;
  width: auto;
}

.create-agent-button {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid #D85722;
  border-radius: 16px;
  padding: 16px 32px;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transform: translateZ(0);
  overflow: hidden;
  pointer-events: auto;
  transform-origin: bottom center;
  font-family: 'Inter', 'Segoe UI', 'Roboto', -apple-system, sans-serif;
}

[data-theme="light"] .create-agent-button {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 20px rgba(216, 87, 34, 0.15);
}

[data-theme="dark"] .create-agent-button {
  background: rgba(18, 18, 20, 0.8);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.create-agent-button:hover {
  border-radius: 24px;
  padding: 32px;
  flex-direction: column;
  width: 800px;
  max-width: 90vw;
  height: auto;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 12px 40px rgba(0,0,0,0.2), 0 0 10px rgba(216,87,34,0.4);
  transform: translateY(-20px);
}

[data-theme="light"] .create-agent-button:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15), 0 0 15px rgba(216,87,34,0.3);
}

[data-theme="dark"] .create-agent-button:hover {
  background: rgba(18, 18, 20, 0.95);
  box-shadow: 0 12px 40px rgba(0,0,0,0.4), 0 0 15px rgba(216,87,34,0.3);
}

.create-agent-icon {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(216,87,34,0.3);
  border-radius: 50%;
  padding: 2px;
  box-shadow: 0 0 10px rgba(216,87,34,0.4);
  transition: all 0.3s ease;
}

.create-agent-button:hover .create-agent-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
}

.create-agent-text {
  font-size: 16px;
  font-weight: 500;
  color: #D85722;
  letter-spacing: 0.4px;
}

[data-theme="light"] .create-agent-text {
  color: #D85722;
  text-shadow: none;
}

[data-theme="dark"] .create-agent-text {
  color: #FF8C42;
  text-shadow: 0 0 10px rgba(255,140,66,0.3);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 24px 0;
  width: 100%;
}

.feature-card {
  background: rgba(255,255,255,0.05);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(216,87,34,0.2);
  display: flex;
  flex-direction: column;
  gap: 12px;
  transition: all 0.3s ease;
}

[data-theme="light"] .feature-card {
  background: rgba(255,255,255,0.8);
  border: 1px solid rgba(216,87,34,0.2);
}

[data-theme="dark"] .feature-card {
  background: rgba(18,18,20,0.6);
  border: 1px solid rgba(255,140,66,0.2);
}

.feature-card:hover {
  transform: translateY(-2px);
  border-color: rgba(216,87,34,0.4);
}

.feature-icon {
  font-size: 40px;
  margin-bottom: 8px;
  color: #D85722;
}

[data-theme="dark"] .feature-icon {
  color: #FF8C42;
}

.feature-title {
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  letter-spacing: 0.2px;
}

[data-theme="light"] .feature-title {
  color: #333;
  text-shadow: none;
}

[data-theme="dark"] .feature-title {
  color: #fff;
  text-shadow: 0 0 8px rgba(216,87,34,0.4);
}

.feature-description {
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
  letter-spacing: 0.1px;
}

[data-theme="light"] .feature-description {
  color: #555;
}

[data-theme="dark"] .feature-description {
  color: rgba(255,255,255,0.9);
}

.feature-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.feature-tag {
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 12px;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  font-weight: 400;
  letter-spacing: 0.1px;
  transition: all 0.2s ease;
}

[data-theme="light"] .feature-tag {
  background: rgba(216,87,34,0.1);
  color: #D85722;
}

[data-theme="dark"] .feature-tag {
  background: rgba(255,140,66,0.15);
  color: #FF8C42;
}

.get-started-button {
  background: #D85722;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px;
  font-size: 17px;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  letter-spacing: 0.3px;
  margin-top: 24px;
  transition: all 0.2s ease;
}

[data-theme="light"] .get-started-button {
  background: #D85722;
  box-shadow: 0 4px 15px rgba(216,87,34,0.3);
}

[data-theme="dark"] .get-started-button {
  background: #FF8C42;
  box-shadow: 0 4px 15px rgba(255,140,66,0.3);
}

.get-started-button:hover {
  transform: translateY(-2px);
}

.get-started-button:active {
  transform: translateY(0);
}

/* Scrollbar styling for the expanded button */
.create-agent-button:hover {
  scrollbar-width: thin;
  scrollbar-color: rgba(216,87,34,0.5) transparent;
}

.create-agent-button:hover::-webkit-scrollbar {
  width: 6px;
}

.create-agent-button:hover::-webkit-scrollbar-track {
  background: transparent;
}

.create-agent-button:hover::-webkit-scrollbar-thumb {
  background-color: rgba(216,87,34,0.5);
  border-radius: 3px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .create-agent-button:hover {
    width: 90vw;
    max-height: 70vh;
    padding: 24px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .create-agent-button:hover .create-agent-icon {
    width: 50px;
    height: 50px;
  }
}

@keyframes glow {
  0% { border-color: rgba(216,87,34,0.8); box-shadow: 0 0 10px rgba(216,87,34,0.4); }
  50% { border-color: #D85722; box-shadow: 0 0 15px rgba(216,87,34,0.6); }
  100% { border-color: rgba(216,87,34,0.8); box-shadow: 0 0 10px rgba(216,87,34,0.4); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(1); opacity: 0.8; }
}

/* Add visible class for JavaScript animation fallback */
.create-agent-overlay.visible {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}