import React, { useState, useEffect } from 'react';
import './CreateAgentButton.css';

// Fallback components to replace framer-motion
const Motion = ({ children, ...props }) => {
  return <div {...props}>{children}</div>;
};

const AnimatePresence = ({ children }) => {
  return <>{children}</>;
};

// This is a simplified version of the CreateAgentButton component
const CreateAgentButton = ({
  primaryColor = '#4B74AA',
  secondaryColor = '#2C3E50',
  isDark = true,
  isPreviewVisible = false,
  logoUrl = '/PRIMARY CLEAR.png',
  buttonOpacity = 0.15,
  onGetStarted = () => {}
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const features = [
    {
      title: 'Custom Landing Page + Domain',
      description: 'Your own branded web presence with custom domain integration',
      icon: '🌐',
      details: ['Personalized URL', 'Brand-matched design', 'SEO optimization']
    },
    {
      title: 'Embeddable Agent Widget',
      description: 'Seamlessly integrate your AI assistant anywhere',
      icon: '🔌',
      details: ['One-click installation', 'Customizable appearance', 'Mobile responsive']
    },
    {
      title: 'Lead Management & Analytics',
      description: 'Comprehensive client interaction tracking',
      icon: '📊',
      details: ['Real-time dashboard', 'Conversion tracking', 'Performance insights']
    },
    {
      title: 'Case Marketplace Access',
      description: 'Connect with potential clients in your area',
      icon: '🤝',
      details: ['Local client matching', 'Practice area filtering', 'Direct messaging']
    }
  ];

  const contentVariants = {
    initial: {
      opacity: 0,
      scale: 0.98
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 200,
        damping: 20,
        mass: 1,
        staggerChildren: 0.05
      }
    }
  };

  const featureVariants = {
    initial: {
      opacity: 0,
      y: 10,
      scale: 0.98
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 200,
        damping: 20,
        mass: 1
      }
    }
  };

  // Add a useEffect to handle the animation without framer-motion
  useEffect(() => {
    if (isPreviewVisible) {
      // Add a class to trigger CSS animations
      const overlay = document.querySelector('.create-agent-overlay');
      if (overlay) {
        overlay.classList.add('visible');
      }
    }
  }, [isPreviewVisible]);

  return (
    <AnimatePresence>
      {isPreviewVisible && (
        <div
          className="create-agent-overlay"
          style={{
            opacity: 0,
            transform: 'translateY(20px)',
            animation: 'fadeIn 0.3s forwards'
          }}
        >
          <div
            className="create-agent-button"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            style={{
              animation: 'glow 3s ease-in-out infinite'
            }}
          >
            {!isHovered ? (
              <>
                <div className="create-agent-icon">
                  <div
                    style={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      backgroundColor: '#D85722',
                      animation: 'pulse 2s infinite ease-in-out'
                    }}
                  />
                </div>
                <span className="create-agent-text">Create My Agent</span>
              </>
            ) : (
              <div
                className="content-container"
                style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '24px',
                  animation: 'fadeIn 0.3s forwards'
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '16px',
                    borderBottom: '1px solid rgba(216,87,34,0.4)',
                    paddingBottom: '16px'
                  }}
                >
                  <div className="create-agent-icon">
                    {logoUrl && logoUrl !== '/PRIMARY CLEAR.png' ? (
                      <img
                        src={logoUrl}
                        alt="Logo"
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain'
                        }}
                        onError={(e) => {
                          console.error('Error loading logo:', e);
                          e.target.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div
                        style={{
                          width: 12,
                          height: 12,
                          borderRadius: '50%',
                          backgroundColor: 'white',
                          animation: 'pulse 2s infinite ease-in-out'
                        }}
                      />
                    )}
                  </div>
                  <div>
                    <h2 className="feature-title" style={{ fontSize: '24px' }}>
                      Create My Agent
                    </h2>
                    <p className="feature-description" style={{ fontSize: '14px', marginTop: '4px' }}>
                      Get started with your AI legal assistant
                    </p>
                  </div>
                </div>

                <div className="feature-grid">
                  {features.map((feature, i) => (
                    <div
                      key={feature.title}
                      className="feature-card"
                      style={{
                        animation: `fadeInUp 0.3s forwards ${i * 0.05}s`
                      }}
                    >
                      <span className="feature-icon">{feature.icon}</span>
                      <h3 className="feature-title">{feature.title}</h3>
                      <p className="feature-description">{feature.description}</p>
                      <div className="feature-tags">
                        {feature.details.map((detail, index) => (
                          <span key={index} className="feature-tag">
                            {detail}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>

                <button
                  className="get-started-button"
                  onClick={() => {
                    console.log('Create agent clicked');
                    // Use the same action as the Sign In/Sign Up button - open the auth overlay
                    if (typeof window.handleGetStarted === 'function') {
                      window.handleGetStarted();
                    } else if (typeof onGetStarted === 'function') {
                      onGetStarted();
                    } else {
                      // Fallback to redirect if handleGetStarted is not available
                      window.location.href = '/login';
                    }
                  }}
                >
                  Get Started
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M5 12h14M12 5l7 7-7 7" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default CreateAgentButton;