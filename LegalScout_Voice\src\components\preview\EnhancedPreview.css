.enhanced-preview-container {
  height: 100vh;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: hidden;
  position: relative;
}

.preview-header {
  padding: 10px 0;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.preview-logo {
  height: 32px;
  margin-right: 10px;
  display: block;
}

.preview-firm-name {
  font-size: 18px;
  margin: 0;
}

.preview-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 70px;
}

.start-button-container {
  text-align: center;
  max-width: 600px;
}

.preview-heading {
  font-size: 28px;
  margin-bottom: 20px;
}

.practice-description {
  margin-bottom: 30px;
  padding: 15px;
  border-radius: 8px;
}

.start-consultation-button {
  color: white;
  border: none;
  border-radius: 50%;
  width: 200px;
  height: 200px;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.start-consultation-button:hover {
  transform: scale(1.05);
}

.button-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
  object-fit: contain;
  display: block;
}

.chat-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .enhanced-preview-container {
    padding: 10px;
  }

  .start-consultation-button {
    width: 150px;
    height: 150px;
    font-size: 16px;
  }

  .button-logo {
    width: 60px;
    height: 60px;
  }
}
