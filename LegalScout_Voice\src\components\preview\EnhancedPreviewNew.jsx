import React, { useState, useEffect, useRef } from 'react';
import { DEFAULT_ASSISTANT_ID } from '../../constants/vapiConstants';
import VapiCall from '../VapiCall';
import Button from '../Button';
import './EnhancedPreview.css';

// Utility to convert hex to RGB for opacity handling
const hexToRgb = (hex) => {
  if (!hex) return '0, 0, 0';

  // Remove the # if present
  hex = hex.replace('#', '');

  // Parse the hex values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Return the RGB values as a string
  return `${r}, ${g}, ${b}`;
};

/**
 * Enhanced Preview Component with Vapi Integration
 *
 * This component provides a preview of the attorney interface with full Vapi integration.
 * It accepts all configuration variables and passes them to the Vapi call.
 */
const EnhancedPreviewNew = ({
  // Firm details
  firmName = 'Your Law Firm',
  titleText = '',  // Add titleText property with default empty string
  attorneyName = 'Your Name',
  practiceAreas = [],
  state = '',
  practiceDescription = "Your AI legal assistant is ready to help",

  // Visual customization
  primaryColor = '#4B74AA',
  secondaryColor = '#2C3E50',
  backgroundColor = '#1a1a1a',
  backgroundOpacity = 0.9,
  buttonText = 'Start Consultation',
  buttonOpacity = 1,
  practiceAreaBackgroundOpacity = 0.1,
  textBackgroundColor = '#634C38',

  // Content
  welcomeMessage = "Hello! I'm Scout, your legal assistant. How can I help you today?",
  informationGathering = "Tell me about your situation, and I'll help find the right solution for you.",

  // Assets
  logoUrl = '/PRIMARY CLEAR.png',
  mascot = '/PRIMARY CLEAR.png',

  // Vapi configuration
  vapiInstructions = '',
  vapiContext = '',
  vapiAssistantId = null, // Add assistant ID prop
  voiceId = 'sarah',
  voiceProvider = 'playht',
  subdomain = 'default', // Add subdomain prop

  // Custom fields configuration
  customFields = [],
  summaryPrompt = '',
  structuredDataPrompt = '',
  structuredDataSchema = null,

  // Theme
  theme = 'dark'
}) => {
  // Add error boundary and logging
  console.log('[EnhancedPreviewNew] Component initializing with props:', {
    firmName,
    titleText,
    theme,
    vapiAssistantId,
    primaryColor,
    secondaryColor
  });
  // State for UI
  const [showStartButton, setShowStartButton] = useState(true);
  const [chatActive, setChatActive] = useState(false);

  // State for customizable properties
  const [firmNameState, setFirmName] = useState(firmName);
  const [titleTextState, setTitleText] = useState(titleText || firmName); // Add titleText state with fallback to firmName
  const [attorneyNameState, setAttorneyName] = useState(attorneyName);
  const [practiceAreasState, setPracticeAreas] = useState(practiceAreas);
  const [stateState, setState] = useState(state);
  const [practiceDescriptionState, setPracticeDescription] = useState(practiceDescription);
  const [primaryColorState, setPrimaryColor] = useState(primaryColor);
  const [secondaryColorState, setSecondaryColor] = useState(secondaryColor);
  const [backgroundColorState, setBackgroundColor] = useState(backgroundColor);
  const [backgroundOpacityState, setBackgroundOpacity] = useState(backgroundOpacity);
  const [buttonTextState, setButtonText] = useState(buttonText);
  const [buttonOpacityState, setButtonOpacity] = useState(buttonOpacity);
  const [practiceAreaBackgroundOpacityState, setPracticeAreaBackgroundOpacity] = useState(practiceAreaBackgroundOpacity);
  const [textBackgroundColorState, setTextBackgroundColor] = useState(textBackgroundColor);
  const [welcomeMessageState, setWelcomeMessage] = useState(welcomeMessage);
  const [informationGatheringState, setInformationGathering] = useState(informationGathering);
  const [logoUrlState, setLogoUrl] = useState(logoUrl);
  const [mascotState, setMascot] = useState(mascot);
  const [vapiInstructionsState, setVapiInstructions] = useState(vapiInstructions);
  const [vapiContextState, setVapiContext] = useState(vapiContext);
  const [vapiAssistantIdState, setVapiAssistantId] = useState(vapiAssistantId); // Add assistant ID state
  const [voiceIdState, setVoiceId] = useState(voiceId);
  const [voiceProviderState, setVoiceProvider] = useState(voiceProvider);
  const [customFieldsState, setCustomFields] = useState(customFields);
  const [summaryPromptState, setSummaryPrompt] = useState(summaryPrompt);
  const [structuredDataPromptState, setStructuredDataPrompt] = useState(structuredDataPrompt);
  const [structuredDataSchemaState, setStructuredDataSchema] = useState(structuredDataSchema);
  const [themeState, setTheme] = useState(theme);
  const [messages, setMessages] = useState([{ sender: 'bot', text: welcomeMessage }]);

  // Derived state
  const isDark = themeState !== 'light';

  // Refs
  const contentRef = useRef(null);

  // Update messages when welcome message changes
  useEffect(() => {
    setMessages([{ sender: 'bot', text: welcomeMessageState }]);
  }, [welcomeMessageState]);

  // State for error handling
  const [previewError, setPreviewError] = useState(null);
  const [isDebugMode, setIsDebugMode] = useState(false);

  // Toggle debug mode
  const toggleDebugMode = () => {
    setIsDebugMode(prev => !prev);
  };

  // Log when component mounts
  useEffect(() => {
    console.log('[EnhancedPreview] Component mounted and ready to receive messages');
    console.log('[EnhancedPreview] Initial assistant ID:', vapiAssistantId);
  }, []);

  // Handle messages from parent window (for iframe communication)
  useEffect(() => {
    const handleMessage = (event) => {
      console.log('[EnhancedPreview] Received ANY message:', event.data);
      console.log('[EnhancedPreview] Message source:', event.source === window.parent ? 'parent' : 'other');
      console.log('[EnhancedPreview] Message type:', event.data?.type);

      try {
        // Handle all message types: 'updateCustomizations' (from DemoPage), 'UPDATE_PREVIEW_CONFIG' (from Dashboard), and 'PREVIEW_CONFIG_UPDATE' (from iframe manager)
        if (event.data && (event.data.type === 'updateCustomizations' || event.data.type === 'UPDATE_PREVIEW_CONFIG' || event.data.type === 'PREVIEW_CONFIG_UPDATE')) {
          // Get customizations from the appropriate property based on message type
          const customizations = event.data.type === 'updateCustomizations'
            ? event.data.customizations
            : event.data.config;

          console.log('[EnhancedPreview] Received customization updates:', customizations);
          console.log('[EnhancedPreview] Assistant ID in customizations:', customizations.vapiAssistantId || customizations.vapi_assistant_id);
          console.log('[EnhancedPreview] All assistant ID related fields:', {
            vapiAssistantId: customizations.vapiAssistantId,
            vapi_assistant_id: customizations.vapi_assistant_id,
            assistantId: customizations.assistantId
          });

          // Track which properties were updated
          const updatedProps = [];

          // Update all state variables based on customizations
          if (customizations.firmName) {
            setFirmName(customizations.firmName);
            updatedProps.push('firmName');
          }

          // Handle titleText with proper fallback logic
          if (customizations.titleText !== undefined) {
            // If titleText is explicitly provided (even if empty), use it
            setTitleText(customizations.titleText);
            updatedProps.push('titleText');
          } else if (customizations.firmName) {
            // If no titleText is provided but firmName is updated,
            // check if current titleText should be updated to match firmName
            // This handles the case where titleText was previously set to firmName
            // and should be updated when firmName changes
            if (!titleTextState || titleTextState === firmNameState) {
              setTitleText(customizations.firmName);
              updatedProps.push('titleText (from firmName)');
            }
          }

          if (customizations.attorneyName) {
            setAttorneyName(customizations.attorneyName);
            updatedProps.push('attorneyName');
          }

          if (customizations.practiceAreas) {
            setPracticeAreas(customizations.practiceAreas);
            updatedProps.push('practiceAreas');
          }

          if (customizations.state) {
            setState(customizations.state);
            updatedProps.push('state');
          }

          if (customizations.practiceDescription) {
            setPracticeDescription(customizations.practiceDescription);
            updatedProps.push('practiceDescription');
          }

          if (customizations.primaryColor) {
            setPrimaryColor(customizations.primaryColor);
            updatedProps.push('primaryColor');
          }

          if (customizations.secondaryColor) {
            setSecondaryColor(customizations.secondaryColor);
            updatedProps.push('secondaryColor');
          }

          if (customizations.backgroundColor) {
            setBackgroundColor(customizations.backgroundColor);
            updatedProps.push('backgroundColor');
          }

          if (customizations.backgroundOpacity !== undefined) {
            setBackgroundOpacity(customizations.backgroundOpacity);
            updatedProps.push('backgroundOpacity');
          }

          if (customizations.buttonText) {
            setButtonText(customizations.buttonText);
            updatedProps.push('buttonText');
          }

          if (customizations.buttonOpacity !== undefined) {
            setButtonOpacity(customizations.buttonOpacity);
            updatedProps.push('buttonOpacity');
          }

          if (customizations.practiceAreaBackgroundOpacity !== undefined) {
            setPracticeAreaBackgroundOpacity(customizations.practiceAreaBackgroundOpacity);
            updatedProps.push('practiceAreaBackgroundOpacity');
          }

          if (customizations.textBackgroundColor) {
            setTextBackgroundColor(customizations.textBackgroundColor);
            updatedProps.push('textBackgroundColor');
          }

          if (customizations.welcomeMessage) {
            setWelcomeMessage(customizations.welcomeMessage);
            setMessages([{ sender: 'bot', text: customizations.welcomeMessage }]);
            updatedProps.push('welcomeMessage');
          }

          if (customizations.informationGathering) {
            setInformationGathering(customizations.informationGathering);
            updatedProps.push('informationGathering');
          }

          if (customizations.logoUrl) {
            setLogoUrl(customizations.logoUrl);
            updatedProps.push('logoUrl');
          }

          if (customizations.mascot) {
            setMascot(customizations.mascot);
            updatedProps.push('mascot');
          }

          if (customizations.vapiInstructions) {
            setVapiInstructions(customizations.vapiInstructions);
            updatedProps.push('vapiInstructions');
          }

          if (customizations.vapiContext) {
            setVapiContext(customizations.vapiContext);
            updatedProps.push('vapiContext');
          }

          // CRITICAL: Handle Vapi assistant ID
          if (customizations.vapiAssistantId || customizations.vapi_assistant_id) {
            const assistantId = customizations.vapiAssistantId || customizations.vapi_assistant_id;
            setVapiAssistantId(assistantId);
            updatedProps.push('vapiAssistantId');
            console.log('EnhancedPreview: Updated Vapi assistant ID to:', assistantId);
          }

          if (customizations.customFields) {
            setCustomFields(customizations.customFields);
            updatedProps.push('customFields');
          }

          if (customizations.summaryPrompt) {
            setSummaryPrompt(customizations.summaryPrompt);
            updatedProps.push('summaryPrompt');
          }

          if (customizations.structuredDataPrompt) {
            setStructuredDataPrompt(customizations.structuredDataPrompt);
            updatedProps.push('structuredDataPrompt');
          }

          if (customizations.structuredDataSchema) {
            setStructuredDataSchema(customizations.structuredDataSchema);
            updatedProps.push('structuredDataSchema');
          }

          if (customizations.theme) {
            setTheme(customizations.theme);
            updatedProps.push('theme');
          }

          if (customizations.voiceId) {
            setVoiceId(customizations.voiceId);
            updatedProps.push('voiceId');
          }

          if (customizations.voiceProvider) {
            setVoiceProvider(customizations.voiceProvider);
            updatedProps.push('voiceProvider');
          }

          if (customizations.buttonColor) {
            // This is a special case as it's not directly in the state
            updatedProps.push('buttonColor');
          }

          // Clear any previous errors
          setPreviewError(null);

          // Log which properties were updated
          if (updatedProps.length > 0) {
            console.log('Updated properties:', updatedProps.join(', '));

            // Show a temporary notification for debug mode
            if (isDebugMode) {
              const notification = document.createElement('div');
              notification.style.position = 'absolute';
              notification.style.bottom = '50px';
              notification.style.left = '10px';
              notification.style.backgroundColor = 'rgba(0, 128, 0, 0.8)';
              notification.style.color = 'white';
              notification.style.padding = '10px';
              notification.style.borderRadius = '4px';
              notification.style.zIndex = '1000';
              notification.style.maxWidth = '300px';
              notification.style.fontSize = '12px';
              notification.textContent = `Updated: ${updatedProps.join(', ')}`;

              document.body.appendChild(notification);

              // Remove after 3 seconds
              setTimeout(() => {
                document.body.removeChild(notification);
              }, 3000);
            }
          } else {
            console.log('No properties were updated');
          }

          // Send acknowledgment back to parent
          try {
            // Send different response types based on the message type
            if (event.data.type === 'PREVIEW_CONFIG_UPDATE') {
              window.parent.postMessage({
                type: 'PREVIEW_CONFIG_UPDATE_RESPONSE',
                success: true,
                updatedProps,
                timestamp: event.data.timestamp
              }, '*');
            } else {
              window.parent.postMessage({
                type: 'PREVIEW_UPDATE_RECEIVED',
                updatedProps,
                timestamp: Date.now()
              }, '*');
            }
          } catch (e) {
            console.error('Error sending acknowledgment to parent:', e);
          }
        }
      } catch (error) {
        console.error('Error processing message in EnhancedPreview:', error);
        setPreviewError(`Error processing message: ${error.message}`);
      }
    };

    window.addEventListener('message', handleMessage);

    // Send ready message to parent
    if (window !== window.parent) {
      try {
        window.parent.postMessage({
          type: 'PREVIEW_READY',
          timestamp: Date.now(),
          previewType: 'EnhancedPreviewNew'
        }, '*');
        console.log('EnhancedPreview: Sent ready message to parent');

        // Add a visible indicator that the preview has loaded
        const notification = document.createElement('div');
        notification.style.position = 'absolute';
        notification.style.top = '10px';
        notification.style.left = '10px';
        notification.style.backgroundColor = 'rgba(0, 128, 0, 0.8)';
        notification.style.color = 'white';
        notification.style.padding = '10px';
        notification.style.borderRadius = '4px';
        notification.style.zIndex = '1000';
        notification.style.fontSize = '14px';
        notification.textContent = 'Preview loaded successfully!';
        document.body.appendChild(notification);

        // Remove after 5 seconds
        setTimeout(() => {
          try {
            document.body.removeChild(notification);
          } catch (e) {
            console.error('Error removing notification:', e);
          }
        }, 5000);

        // Also send a ping every 2 seconds to ensure communication
        const pingInterval = setInterval(() => {
          try {
            window.parent.postMessage({
              type: 'PREVIEW_PING',
              timestamp: Date.now()
            }, '*');
          } catch (e) {
            console.error('Error sending ping to parent:', e);
          }
        }, 2000);

        // Clear interval on unmount
        return () => {
          clearInterval(pingInterval);
          window.removeEventListener('message', handleMessage);
        };
      } catch (e) {
        console.error('EnhancedPreview: Error sending message to parent:', e);
        setPreviewError(`Error communicating with parent window: ${e.message}`);
      }
    }

    // Log environment variables for debugging
    console.log('EnhancedPreview environment check:');
    console.log('VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL || window.VITE_SUPABASE_URL || 'not set');
    console.log('VITE_VAPI_PUBLIC_KEY:', import.meta.env.VITE_VAPI_PUBLIC_KEY ? 'set (hidden)' : 'not set');

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [titleTextState, isDebugMode]);

  // Debug logging for props
  useEffect(() => {
    console.log('[EnhancedPreviewNew] State updated:');
    console.log('firmName:', firmNameState);
    console.log('titleText:', titleTextState);
    console.log('logoUrl:', logoUrlState);
    console.log('primaryColor:', primaryColorState);
    console.log('secondaryColor:', secondaryColorState);
    console.log('vapiInstructions:', vapiInstructionsState);
    console.log('vapiAssistantId:', vapiAssistantIdState); // Add assistant ID logging
    console.log('voiceId:', voiceIdState);
    console.log('voiceProvider:', voiceProviderState);
    console.log('chatActive:', chatActive);
  }, [firmNameState, titleTextState, logoUrlState, primaryColorState, secondaryColorState, vapiInstructionsState, vapiAssistantIdState, voiceIdState, voiceProviderState, chatActive]);

  // Handle start consultation button click
  const handleStartConsultation = () => {
    console.log("🚀 [PREVIEW CALL START] Starting consultation...");
    console.log("🎯 [PREVIEW CALL START] Using assistant ID:", vapiAssistantIdState);
    console.log("💬 [PREVIEW CALL START] Welcome message:", welcomeMessageState);
    console.log("🔊 [PREVIEW CALL START] Voice settings:", { voiceId: voiceIdState, voiceProvider: voiceProviderState });
    console.log("✅ [PREVIEW CALL START] VapiCall will receive assistantId:", vapiAssistantIdState);
    setShowStartButton(false);
    setChatActive(true);
  };

  // Handle ending the call
  const handleEndCall = () => {
    console.log("Ending call...");
    setChatActive(false);
    setShowStartButton(true);
  };

  // Convert markdown to HTML for practice description
  const renderPracticeDescription = () => {
    if (!practiceDescriptionState) return '';

    // Simple markdown parser
    return practiceDescriptionState
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
      .replace(/### (.*?)$/gm, '<h3>$1</h3>') // H3
      .replace(/## (.*?)$/gm, '<h2>$1</h2>') // H2
      .replace(/# (.*?)$/gm, '<h1>$1</h1>') // H1
      .replace(/- (.*?)$/gm, '<li>$1</li>') // List items
      .replace(/<li>(.*?)<\/li>/g, '<ul><li>$1</li></ul>') // Wrap list items
      .replace(/<\/ul><ul>/g, '') // Fix multiple lists
      .replace(/\n/g, '<br>'); // Line breaks
  };

  // Get the appropriate logo to use
  const getLogoToUse = () => {
    // Prioritize user uploaded logo
    if (logoUrlState && logoUrlState !== '/PRIMARY CLEAR.png') {
      console.log('Using custom logoUrl:', logoUrlState);

      // Check if the logo URL is a relative path and convert to absolute if needed
      if (logoUrlState.startsWith('/')) {
        // Use relative path directly to avoid cross-origin issues
        console.log('Using relative logo path:', logoUrlState);
        return logoUrlState;
      }

      // If it's already an absolute URL, use it directly
      return logoUrlState;
    }

    // Fallback to mascot
    if (mascotState && mascotState !== '/PRIMARY CLEAR.png') {
      console.log('Using mascot:', mascotState);

      // Check if the mascot URL is a relative path
      if (mascotState.startsWith('/')) {
        // Use relative path directly
        console.log('Using relative mascot path:', mascotState);
        return mascotState;
      }

      // If it's already an absolute URL, use it directly
      return mascotState;
    }

    // Default fallback - use relative path
    const defaultLogoPath = '/PRIMARY CLEAR.png';
    console.log('Using default logo path:', defaultLogoPath);
    return defaultLogoPath;
  };

  const logoToUse = getLogoToUse();

  return (
    <div
      className="enhanced-preview-container"
      style={{
        backgroundColor: `rgba(${hexToRgb(backgroundColorState)}, ${backgroundOpacityState})`,
        color: isDark ? '#ffffff' : '#333333'
      }}
      ref={contentRef}
    >
      {/* Error display */}
      {previewError && (
        <div className="preview-error-container" style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          right: '10px',
          padding: '10px',
          backgroundColor: 'rgba(255, 0, 0, 0.1)',
          border: '1px solid #ff0000',
          borderRadius: '4px',
          color: '#ff0000',
          zIndex: 1000
        }}>
          <h3>Preview Error</h3>
          <p>{previewError}</p>
          <button onClick={() => setPreviewError(null)} style={{
            padding: '5px 10px',
            backgroundColor: '#ff0000',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}>
            Dismiss
          </button>
        </div>
      )}

      {/* Debug toggle button */}
      <button
        onClick={toggleDebugMode}
        style={{
          position: 'absolute',
          bottom: '10px',
          right: '10px',
          padding: '5px 10px',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          zIndex: 1000
        }}
      >
        {isDebugMode ? 'Hide Debug' : 'Debug'}
      </button>

      {/* Debug panel */}
      {isDebugMode && (
        <div className="debug-panel" style={{
          position: 'absolute',
          bottom: '50px',
          right: '10px',
          width: '300px',
          maxHeight: '400px',
          overflowY: 'auto',
          padding: '10px',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          borderRadius: '4px',
          zIndex: 1000,
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          <h3>Debug Information</h3>
          <div>
            <h4>Environment</h4>
            <p>VITE_SUPABASE_URL: {import.meta.env.VITE_SUPABASE_URL || window.VITE_SUPABASE_URL || 'not set'}</p>
            <p>VITE_VAPI_PUBLIC_KEY: {import.meta.env.VITE_VAPI_PUBLIC_KEY ? 'set (hidden)' : 'not set'}</p>
          </div>
          <div>
            <h4>Current State</h4>
            <p>Firm Name: {firmNameState}</p>
            <p>Title Text: {titleTextState}</p>
            <p>Logo URL: {logoUrlState}</p>
            <p>Primary Color: {primaryColorState}</p>
            <p>Theme: {themeState}</p>
          </div>
        </div>
      )}
      {/* Header */}
      <header
        className="preview-header"
        style={{
          borderBottom: `1px solid ${isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}`
        }}
      >
        <img
          src={logoToUse}
          alt={`${firmNameState} logo`}
          className="preview-logo"
          onError={(e) => {
            console.error('Error loading logo in header:', e);
            e.target.style.display = 'none';
          }}
        />
        <h1 className="preview-firm-name">{titleTextState || firmNameState}</h1>
      </header>

      {/* Main content area */}
      <div className="preview-content">
        {showStartButton ? (
          <div className="start-button-container">
            <h2
              className="preview-heading"
              style={{ color: primaryColorState }}
            >
              {titleTextState || firmNameState}
            </h2>
            <div
              className="practice-description"
              style={{
                backgroundColor: `rgba(${hexToRgb(textBackgroundColorState)}, ${practiceAreaBackgroundOpacityState})`
              }}
              dangerouslySetInnerHTML={{ __html: renderPracticeDescription() }}
            />
            <Button
              onClick={handleStartConsultation}
              label={buttonTextState || 'Start Consultation'}
              mascot={logoToUse}
              buttonColor={secondaryColorState}
              buttonOpacity={buttonOpacityState}
            />
          </div>
        ) : (
          <div className="chat-container">
            {chatActive && (
              <>
                {console.log("🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId:", vapiAssistantIdState)}
                {console.log("🎬 [PREVIEW CALL RENDER] Assistant ID type:", typeof vapiAssistantIdState)}
                {console.log("🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined:", vapiAssistantIdState == null)}
                <VapiCall
                  onEndCall={handleEndCall}
                  subdomain={subdomain} // Use the subdomain prop instead of hardcoded "default"
                  assistantId={vapiAssistantIdState} // CRITICAL: Pass the assistant ID
                  customInstructions={{
                    initialMessage: welcomeMessageState,
                    welcomeMessage: welcomeMessageState,
                    informationGathering: informationGatheringState,
                    firmName: titleTextState || firmNameState,
                    attorneyName: attorneyNameState,
                    practiceAreas: practiceAreasState,
                    state: stateState,
                    primaryColor: primaryColorState,
                    secondaryColor: secondaryColorState,
                    vapiInstructions: vapiInstructionsState,
                    vapiContext: vapiContextState,
                    customFields: customFieldsState,
                    summaryPrompt: summaryPromptState,
                    structuredDataPrompt: structuredDataPromptState,
                    structuredDataSchema: structuredDataSchemaState,
                    voiceId: voiceIdState,
                    voiceProvider: voiceProviderState || 'playht',
                    // Also include assistant ID in custom instructions for fallback
                    assistantId: vapiAssistantIdState
                  }}
                />
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedPreviewNew;
