.simplified-preview-container {
  height: 100vh;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  background-color: rgba(30, 30, 30, 0.9);
  color: #ffffff;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: hidden;
  position: relative;
  /* Ensure scroll containment within iframe */
  contain: layout style paint;
  overscroll-behavior: contain;
  isolation: isolate;
}

.simplified-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo {
  height: 50px;
  width: auto;
  max-width: 50px;
  object-fit: contain;
}

.firm-name {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.markdown-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
  line-height: 1.6;
}

.action-button {
  padding: 12px 24px;
  background-color: #4A90E2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  margin-top: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.action-button:hover {
  background-color: #357ABD;
}

.theme-toggle {
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  transition: background-color 0.3s ease;
}

.theme-toggle:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Dark mode styles */
.simplified-preview-container.dark {
  background-color: rgba(30, 30, 30, 0.9);
  color: #ffffff;
}

/* Light mode styles */
.simplified-preview-container.light {
  background-color: rgba(245, 245, 245, 0.9);
  color: #333333;
}

.simplified-preview-container.light .markdown-content {
  color: #333333;
}

.simplified-preview-container.light .action-button {
  background-color: #4A90E2;
}

.simplified-preview-container.light .theme-toggle {
  background-color: rgba(0, 0, 0, 0.1);
}

.simplified-preview-container.light .theme-toggle:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .simplified-preview-container {
    padding: 15px;
  }

  .simplified-preview-header {
    padding-bottom: 15px;
  }

  .logo {
    height: 40px;
    max-width: 40px;
  }

  .firm-name {
    font-size: 20px;
  }

  .markdown-content {
    padding: 15px 0;
  }

  .action-button {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .simplified-preview-container {
    padding: 10px;
  }

  .simplified-preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .logo-container {
    width: 100%;
    justify-content: space-between;
  }

  .logo {
    height: 35px;
    max-width: 35px;
  }

  .firm-name {
    font-size: 18px;
  }

  .action-button {
    width: 100%;
  }
}