/* Search Results Display Component Styles */

.search-results-container {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 70vh;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.search-results-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #e74c3c;
}

.search-results-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px 20px;
}

.search-results-footer {
  padding: 10px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 12px;
  color: #95a5a6;
  text-align: right;
}

.search-results-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  color: #7f8c8d;
  font-style: italic;
}

/* Card Layout */
.search-results-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.search-result-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.search-result-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.card-thumbnail {
  height: 150px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.card-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-content {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  margin: 0 0 10px 0;
  font-size: 16px;
  line-height: 1.4;
}

.card-title a {
  color: #2980b9;
  text-decoration: none;
}

.card-title a:hover {
  text-decoration: underline;
}

.card-description {
  margin: 0 0 15px 0;
  font-size: 14px;
  line-height: 1.5;
  color: #555;
  flex: 1;
}

.card-url {
  font-size: 12px;
  color: #7f8c8d;
}

/* Timeline Layout */
.search-results-timeline {
  position: relative;
  padding: 20px 0;
}

.timeline-track {
  position: relative;
  padding-left: 30px;
}

.timeline-track:before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #3498db;
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
}

.timeline-point {
  position: absolute;
  left: -30px;
  top: 5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #3498db;
  border: 3px solid #fff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.timeline-date {
  font-size: 14px;
  font-weight: 600;
  color: #3498db;
  margin-bottom: 5px;
}

.timeline-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 15px;
}

.timeline-title {
  margin: 0 0 10px 0;
  font-size: 16px;
}

.timeline-title a {
  color: #2980b9;
  text-decoration: none;
}

.timeline-title a:hover {
  text-decoration: underline;
}

.timeline-description {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}

/* Statute Layout */
.search-results-statute {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 20px;
}

.statute-title {
  margin: 0 0 20px 0;
  font-size: 20px;
  color: #2c3e50;
  padding-bottom: 10px;
  border-bottom: 1px solid #ecf0f1;
}

.statute-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ecf0f1;
}

.statute-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.section-title {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #34495e;
}

.section-text {
  margin: 0 0 15px 0;
  font-size: 14px;
  line-height: 1.6;
  color: #555;
}

.section-link {
  display: inline-block;
  font-size: 14px;
  color: #3498db;
  text-decoration: none;
}

.section-link:hover {
  text-decoration: underline;
}

/* Concept Map Layout */
.search-results-concept-map {
  padding: 20px 0;
}

.concept-map-container {
  position: relative;
  min-height: 300px;
}

.concept-map-center {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.concept-node {
  background-color: #3498db;
  color: white;
  padding: 10px 20px;
  border-radius: 30px;
  display: inline-block;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.concept-node a {
  color: white;
  text-decoration: none;
}

.concept-node.main-concept {
  background-color: #e74c3c;
  font-size: 18px;
  padding: 15px 30px;
}

.concept-map-branches {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
}

.concept-branch {
  width: calc(33.333% - 20px);
  text-align: center;
  position: relative;
}

.concept-branch:before {
  content: '';
  position: absolute;
  top: -30px;
  left: 50%;
  height: 30px;
  width: 2px;
  background-color: #bdc3c7;
}

.concept-description {
  margin-top: 10px;
  font-size: 14px;
  color: #555;
}

/* List Layout */
.search-results-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.search-result-item {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 15px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.search-result-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.search-result-item h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
}

.search-result-item h4 a {
  color: #2980b9;
  text-decoration: none;
}

.search-result-item h4 a:hover {
  text-decoration: underline;
}

.search-result-item p {
  margin: 0 0 10px 0;
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}

.item-url {
  font-size: 12px;
  color: #7f8c8d;
}

/* Dark Theme Support */
[data-theme="dark"] .search-results-container {
  background-color: rgba(30, 30, 30, 0.95);
  color: #f5f5f5;
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .search-results-header {
  background-color: rgba(40, 40, 40, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .search-results-header h3 {
  color: #ecf0f1;
}

[data-theme="dark"] .close-button {
  color: #bdc3c7;
}

[data-theme="dark"] .close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #e74c3c;
}

[data-theme="dark"] .search-results-footer {
  border-color: rgba(255, 255, 255, 0.1);
  color: #95a5a6;
}

[data-theme="dark"] .search-result-card,
[data-theme="dark"] .timeline-content,
[data-theme="dark"] .search-results-statute,
[data-theme="dark"] .search-result-item {
  background-color: #2c3e50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .card-title a,
[data-theme="dark"] .timeline-title a,
[data-theme="dark"] .section-link,
[data-theme="dark"] .search-result-item h4 a {
  color: #3498db;
}

[data-theme="dark"] .card-description,
[data-theme="dark"] .timeline-description,
[data-theme="dark"] .section-text,
[data-theme="dark"] .search-result-item p,
[data-theme="dark"] .concept-description {
  color: #bdc3c7;
}

[data-theme="dark"] .card-url,
[data-theme="dark"] .item-url {
  color: #95a5a6;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .search-results-cards {
    grid-template-columns: 1fr;
  }
  
  .concept-branch {
    width: 100%;
    margin-bottom: 30px;
  }
  
  .search-results-container {
    max-width: 95%;
    max-height: 80vh;
  }
}
