import { Auth0Provider } from '@auth0/auth0-react';

export const AUTH_CONFIG = {
  domain: import.meta.env.VITE_AUTH0_DOMAIN || '',
  clientId: import.meta.env.VITE_AUTH0_CLIENT_ID || '',
  audience: import.meta.env.VITE_AUTH0_AUDIENCE || '',
  redirectUri: typeof window !== 'undefined' ? window.location.origin : '',
};

export const AuthProvider = ({ children }) => {
  return (
    <Auth0Provider
      domain={AUTH_CONFIG.domain}
      clientId={AUTH_CONFIG.clientId}
      authorizationParams={{
        redirect_uri: AUTH_CONFIG.redirectUri,
        audience: AUTH_CONFIG.audience,
        connection: 'google-oauth2',
        prompt: 'login',
        screen_hint: 'signin'
      }}
    >
      {children}
    </Auth0Provider>
  );
}; 