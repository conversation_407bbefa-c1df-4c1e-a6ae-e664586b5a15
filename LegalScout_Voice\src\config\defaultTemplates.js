/**
 * Default templates for Vapi assistant configuration
 */

// Default structured data schema for legal CRM
export const DEFAULT_STRUCTURED_DATA_SCHEMA = {
  type: "object",
  properties: {
    // Client Information
    client: {
      type: "object",
      properties: {
        full_name: {
          type: "string",
          description: "<PERSON><PERSON>'s full legal name"
        },
        email: {
          type: "string",
          description: "<PERSON><PERSON>'s email address"
        },
        phone: {
          type: "string",
          description: "<PERSON><PERSON>'s phone number"
        },
        alternate_phone: {
          type: "string",
          description: "Alternative contact number"
        },
        address: {
          type: "object",
          properties: {
            street: { type: "string" },
            city: { type: "string" },
            state: { type: "string" },
            zip: { type: "string" },
            country: { type: "string", default: "United States" }
          }
        },
        date_of_birth: {
          type: "string",
          description: "<PERSON><PERSON>'s date of birth (YYYY-MM-DD format)"
        },
        preferred_contact_method: {
          type: "string",
          enum: ["email", "phone", "text", "mail"],
          description: "<PERSON><PERSON>'s preferred method of contact"
        },
        referral_source: {
          type: "string",
          description: "How the client found your firm"
        }
      },
      required: ["full_name", "phone"]
    },

    // Case Information
    case: {
      type: "object",
      properties: {
        practice_area: {
          type: "string",
          description: "Primary practice area for this case"
        },
        sub_practice_area: {
          type: "string",
          description: "More specific category within the practice area"
        },
        legal_issue_summary: {
          type: "string",
          description: "Brief summary of the client's legal issue (1-2 sentences)"
        },
        detailed_description: {
          type: "string",
          description: "Detailed description of the legal issue"
        },
        incident_date: {
          type: "string",
          description: "Date when the incident occurred (YYYY-MM-DD format)"
        },
        statute_of_limitations_date: {
          type: "string",
          description: "Calculated deadline for filing (YYYY-MM-DD format)"
        },
        jurisdiction: {
          type: "string",
          description: "Jurisdiction where the case would be filed"
        },
        opposing_parties: {
          type: "array",
          items: {
            type: "string"
          },
          description: "Names of opposing parties or entities"
        },
        documents_mentioned: {
          type: "array",
          items: {
            type: "string"
          },
          description: "Any legal documents mentioned during the conversation"
        },
        current_stage: {
          type: "string",
          enum: ["pre-filing", "initial_consultation", "investigation", "litigation", "settlement", "trial", "appeal", "post-judgment"],
          description: "Current stage of the legal matter"
        }
      },
      required: ["practice_area", "legal_issue_summary"]
    },

    // Case Evaluation Metrics
    case_evaluation: {
      type: "object",
      properties: {
        urgency: {
          type: "string",
          enum: ["low", "medium", "high", "critical"],
          description: "How urgently this matter needs attention"
        },
        time_sensitivity: {
          type: "object",
          properties: {
            has_deadline: { type: "boolean" },
            deadline_date: { type: "string" },
            reason: { type: "string" }
          },
          description: "Any time-sensitive aspects of the case"
        },
        potential_value: {
          type: "string",
          enum: ["low", "medium", "high", "very_high"],
          description: "Estimated potential value of the case"
        },
        damages_estimate: {
          type: "string",
          description: "Rough estimate of damages mentioned by client"
        },
        liability_assessment: {
          type: "string",
          enum: ["clear", "likely", "questionable", "unlikely", "unknown"],
          description: "Initial assessment of liability based on client's description"
        },
        evidence_strength: {
          type: "string",
          enum: ["strong", "moderate", "weak", "none", "unknown"],
          description: "Initial assessment of available evidence"
        },
        complexity: {
          type: "string",
          enum: ["simple", "moderate", "complex", "very_complex"],
          description: "Estimated complexity of the legal matter"
        },
        resource_requirements: {
          type: "string",
          enum: ["minimal", "moderate", "substantial", "extensive"],
          description: "Estimated firm resources required for this case"
        },
        success_probability: {
          type: "string",
          enum: ["high", "medium", "low", "uncertain"],
          description: "Initial assessment of probability of favorable outcome"
        },
        conflict_check_needed: {
          type: "boolean",
          description: "Whether a conflict check is needed based on parties mentioned"
        },
        special_expertise_required: {
          type: "array",
          items: {
            type: "string"
          },
          description: "Any special expertise or specialists needed for this case"
        }
      }
    },

    // Client Relationship Indicators
    client_relationship: {
      type: "object",
      properties: {
        communication_clarity: {
          type: "string",
          enum: ["excellent", "good", "fair", "poor"],
          description: "How clearly the client communicates"
        },
        expectations: {
          type: "string",
          description: "Client's expressed expectations about the case"
        },
        fee_sensitivity: {
          type: "string",
          enum: ["high", "medium", "low", "unknown"],
          description: "Client's sensitivity to legal fees"
        },
        preferred_fee_arrangement: {
          type: "string",
          enum: ["hourly", "contingency", "flat_fee", "hybrid", "not_discussed"],
          description: "Client's preferred fee arrangement if mentioned"
        },
        decision_making_style: {
          type: "string",
          enum: ["decisive", "analytical", "collaborative", "hesitant", "unknown"],
          description: "Client's apparent decision-making style"
        }
      }
    },

    // Follow-up Information
    follow_up: {
      type: "object",
      properties: {
        requested: {
          type: "boolean",
          description: "Whether follow-up was requested"
        },
        preferred_time: {
          type: "string",
          description: "Client's preferred time for follow-up"
        },
        action_items: {
          type: "array",
          items: {
            type: "string"
          },
          description: "Specific action items promised to the client"
        },
        documents_requested: {
          type: "array",
          items: {
            type: "string"
          },
          description: "Documents requested from the client"
        },
        next_steps: {
          type: "string",
          description: "Agreed next steps with the client"
        }
      },
      required: ["requested"]
    },

    // Marketing Insights
    marketing_insights: {
      type: "object",
      properties: {
        referral_source_details: {
          type: "string",
          description: "Detailed information about how client found the firm"
        },
        marketing_campaign: {
          type: "string",
          description: "Specific marketing campaign that led to this contact if mentioned"
        },
        keywords_mentioned: {
          type: "array",
          items: {
            type: "string"
          },
          description: "Key search terms or phrases client mentioned using to find the firm"
        }
      }
    },

    // Conflict Check Information
    conflict_check: {
      type: "object",
      properties: {
        related_parties: {
          type: "array",
          items: {
            type: "object",
            properties: {
              name: { type: "string" },
              relationship: { type: "string" }
            }
          },
          description: "All parties related to the case for conflict checking"
        },
        previous_representation: {
          type: "boolean",
          description: "Whether client mentioned previous representation by any attorney"
        },
        adverse_parties: {
          type: "array",
          items: {
            type: "string"
          },
          description: "Parties adverse to the client in this matter"
        }
      }
    }
  },
  required: ["client", "case", "follow_up"]
};

// Default success evaluation prompt
export const DEFAULT_SUCCESS_EVALUATION_PROMPT = `
Evaluate whether this call was successful as a legal intake by determining if the following critical information was obtained:

1. IDENTITY: Did we get the client's full name and at least one reliable contact method (phone or email)?
2. LEGAL ISSUE: Did we clearly identify the client's legal problem and which practice area it falls under?
3. TIMELINE: Did we establish when the incident occurred and identify any urgent deadlines or statute of limitations concerns?
4. NEXT STEPS: Was a clear next step established (e.g., scheduling a full consultation, requesting documents, etc.)?
5. CLIENT EXPECTATIONS: Did we understand what the client hopes to achieve through legal representation?

For a call to be considered "Successful", criteria 1-4 must be met.
For a call to be considered "Partially Successful", criteria 1-2 must be met, plus either 3, 4, or 5.
For a call to be considered "Unsuccessful", either criteria 1 or 2 was not met.

Provide your evaluation as one of: "Successful", "Partially Successful", or "Unsuccessful", followed by a brief explanation of which criteria were or were not met.
`;

// Default end of call report prompt
export const DEFAULT_SUMMARY_PROMPT = `
Create a concise yet comprehensive legal intake summary that would be valuable for an attorney reviewing this potential case. Include:

1. CLIENT PROFILE: Client's name, contact information, and any relevant personal details that might impact the case.

2. MATTER SUMMARY: A clear 1-2 sentence description of the legal issue, followed by a more detailed explanation of the facts as presented by the client.

3. KEY DATES: Highlight any critical dates mentioned, including when incidents occurred, deadlines, or statute of limitations concerns.

4. CASE EVALUATION: Provide an initial assessment of:
   - Practice area(s) this case falls under
   - Potential strengths and weaknesses based on the client's description
   - Any red flags or special considerations (e.g., jurisdictional issues, conflicts of interest)
   - Estimated complexity and resource requirements

5. CLIENT EXPECTATIONS: What the client hopes to achieve and their expressed priorities.

6. NEXT STEPS: What was promised to the client and what actions should be taken next.

7. FOLLOW-UP: Whether follow-up was requested, preferred timing, and any documents the client needs to provide.

Keep the summary focused on facts and objective observations that would help an attorney quickly understand the potential case and determine next steps. Avoid speculative legal conclusions.
`;

// Default structured data extraction prompt
export const DEFAULT_STRUCTURED_DATA_PROMPT = `
Extract the following key information from this legal intake conversation. Be precise and only include information that was explicitly stated or can be directly inferred with high confidence:

1. All client personal and contact information
2. Complete details about the legal issue, including dates, locations, and parties involved
3. Case evaluation factors such as urgency, potential value, and complexity
4. Any deadlines or time-sensitive elements
5. Client relationship indicators such as communication style and fee sensitivity
6. Follow-up arrangements and next steps
7. Marketing insights about how the client found the firm
8. Information needed for conflict checks

For fields where information wasn't provided, use null or "unknown" rather than making assumptions. For enumerated fields, select the most appropriate value based on the conversation.

Format the extracted data according to the provided JSON schema.
`;

// Practice area specific templates
export const PRACTICE_AREA_TEMPLATES = {
  "general_purpose": {
    name: "General Purpose Legal CRM",
    description: "General purpose template for all practice areas",
    success_prompt: `
Evaluate whether this call was successful as a legal intake by determining if the following critical information was obtained:

1. IDENTITY: Did we get the client's full name and at least one reliable contact method (phone or email)?
2. LEGAL ISSUE: Did we clearly identify the client's legal problem and which practice area it falls under?
3. TIMELINE: Did we establish when the incident occurred and identify any urgent deadlines or statute of limitations concerns?
4. NEXT STEPS: Was a clear next step established (e.g., scheduling a full consultation, requesting documents, etc.)?
5. CLIENT EXPECTATIONS: Did we understand what the client hopes to achieve through legal representation?

For a call to be considered "Successful", criteria 1-4 must be met.
For a call to be considered "Partially Successful", criteria 1-2 must be met, plus either 3, 4, or 5.
For a call to be considered "Unsuccessful", either criteria 1 or 2 was not met.

Provide your evaluation as one of: "Successful", "Partially Successful", or "Unsuccessful", followed by a brief explanation of which criteria were or were not met.
`,
    summary_prompt: `
Create a concise yet comprehensive legal intake summary that would be valuable for an attorney reviewing this potential case. Include:

1. CLIENT PROFILE: Client's name, contact information, and any relevant personal details that might impact the case.

2. MATTER SUMMARY: A clear 1-2 sentence description of the legal issue, followed by a more detailed explanation of the facts as presented by the client.

3. KEY DATES: Highlight any critical dates mentioned, including when incidents occurred, deadlines, or statute of limitations concerns.

4. CASE EVALUATION: Provide an initial assessment of:
   - Practice area(s) this case falls under
   - Potential strengths and weaknesses based on the client's description
   - Any red flags or special considerations (e.g., jurisdictional issues, conflicts of interest)
   - Estimated complexity and resource requirements

5. CLIENT EXPECTATIONS: What the client hopes to achieve and their expressed priorities.

6. NEXT STEPS: What was promised to the client and what actions should be taken next.

7. FOLLOW-UP: Whether follow-up was requested, preferred timing, and any documents the client needs to provide.

Keep the summary focused on facts and objective observations that would help an attorney quickly understand the potential case and determine next steps. Avoid speculative legal conclusions.
`,
    structured_data_prompt: `
Extract the following key information from this legal intake conversation. Be precise and only include information that was explicitly stated or can be directly inferred with high confidence:

1. All client personal and contact information
2. Complete details about the legal issue, including dates, locations, and parties involved
3. Case evaluation factors such as urgency, potential value, and complexity
4. Any deadlines or time-sensitive elements
5. Client relationship indicators such as communication style and fee sensitivity
6. Follow-up arrangements and next steps
7. Marketing insights about how the client found the firm
8. Information needed for conflict checks

For fields where information wasn't provided, use null or "unknown" rather than making assumptions. For enumerated fields, select the most appropriate value based on the conversation.

Format the extracted data according to the provided JSON schema.
`,
    structured_data_schema: DEFAULT_STRUCTURED_DATA_SCHEMA
  },

  "personal_injury": {
    name: "Personal Injury Template",
    description: "Template for personal injury cases",
    structured_data_schema: {
      ...DEFAULT_STRUCTURED_DATA_SCHEMA,
      properties: {
        ...DEFAULT_STRUCTURED_DATA_SCHEMA.properties,
        case: {
          ...DEFAULT_STRUCTURED_DATA_SCHEMA.properties.case,
          properties: {
            ...DEFAULT_STRUCTURED_DATA_SCHEMA.properties.case.properties,
            injury_details: {
              type: "object",
              properties: {
                injury_type: {
                  type: "string",
                  description: "Type of injury sustained"
                },
                injury_severity: {
                  type: "string",
                  enum: ["minor", "moderate", "severe", "catastrophic"],
                  description: "Severity of the injury"
                },
                medical_treatment: {
                  type: "string",
                  description: "Medical treatment received so far"
                },
                ongoing_treatment: {
                  type: "boolean",
                  description: "Whether client is still receiving treatment"
                },
                medical_expenses: {
                  type: "string",
                  description: "Estimated medical expenses to date"
                },
                lost_wages: {
                  type: "string",
                  description: "Estimated lost wages or income"
                },
                insurance_coverage: {
                  type: "object",
                  properties: {
                    has_insurance: { type: "boolean" },
                    insurance_type: { type: "string" },
                    policy_limits: { type: "string" }
                  }
                }
              }
            }
          }
        }
      }
    }
  },

  "family_law": {
    name: "Family Law Template",
    description: "Template for family law cases",
    structured_data_schema: {
      ...DEFAULT_STRUCTURED_DATA_SCHEMA,
      properties: {
        ...DEFAULT_STRUCTURED_DATA_SCHEMA.properties,
        case: {
          ...DEFAULT_STRUCTURED_DATA_SCHEMA.properties.case,
          properties: {
            ...DEFAULT_STRUCTURED_DATA_SCHEMA.properties.case.properties,
            family_details: {
              type: "object",
              properties: {
                marriage_status: {
                  type: "string",
                  enum: ["married", "separated", "divorced", "never_married"],
                  description: "Current marital status"
                },
                marriage_length: {
                  type: "string",
                  description: "Length of marriage if applicable"
                },
                children: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      age: { type: "number" },
                      custody_status: { type: "string" }
                    }
                  },
                  description: "Information about children involved"
                },
                assets: {
                  type: "object",
                  properties: {
                    real_estate: { type: "boolean" },
                    retirement_accounts: { type: "boolean" },
                    business_interests: { type: "boolean" },
                    significant_assets: { type: "string" }
                  },
                  description: "Major assets that may be subject to division"
                }
              }
            }
          }
        }
      }
    }
  },

  "estate_planning": {
    name: "Estate Planning Template",
    description: "Template for estate planning cases",
    structured_data_schema: {
      ...DEFAULT_STRUCTURED_DATA_SCHEMA,
      properties: {
        ...DEFAULT_STRUCTURED_DATA_SCHEMA.properties,
        case: {
          ...DEFAULT_STRUCTURED_DATA_SCHEMA.properties.case,
          properties: {
            ...DEFAULT_STRUCTURED_DATA_SCHEMA.properties.case.properties,
            estate_details: {
              type: "object",
              properties: {
                existing_documents: {
                  type: "object",
                  properties: {
                    has_will: { type: "boolean" },
                    has_trust: { type: "boolean" },
                    has_power_of_attorney: { type: "boolean" },
                    has_healthcare_directive: { type: "boolean" },
                    last_updated: { type: "string" }
                  },
                  description: "Existing estate planning documents"
                },
                family_structure: {
                  type: "string",
                  description: "Brief description of family structure"
                },
                assets: {
                  type: "object",
                  properties: {
                    real_estate: { type: "boolean" },
                    investment_accounts: { type: "boolean" },
                    business_interests: { type: "boolean" },
                    approximate_net_worth: { type: "string" }
                  },
                  description: "Major assets to be included in estate plan"
                },
                specific_concerns: {
                  type: "array",
                  items: { type: "string" },
                  description: "Specific concerns or goals for estate plan"
                }
              }
            }
          }
        }
      }
    }
  }
};

// Export a function to get a template by practice area
export const getTemplateByPracticeArea = (practiceArea) => {
  const normalizedArea = practiceArea?.toLowerCase().replace(/\s+/g, '_') || '';
  return PRACTICE_AREA_TEMPLATES[normalizedArea] || {
    name: "Default Template",
    description: "Standard legal intake template",
    structured_data_schema: DEFAULT_STRUCTURED_DATA_SCHEMA,
    success_prompt: DEFAULT_SUCCESS_EVALUATION_PROMPT,
    summary_prompt: DEFAULT_SUMMARY_PROMPT,
    structured_data_prompt: DEFAULT_STRUCTURED_DATA_PROMPT
  };
};
