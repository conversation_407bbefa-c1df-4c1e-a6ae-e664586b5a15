import { useEffect } from 'react';

// This file is deprecated - use vapiConfig.js instead
// Keeping for backward compatibility

export const VAPI_CONFIG = {
  publicKey: import.meta.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7',
  baseUrl: import.meta.env.VITE_VAPI_BASE_URL || 'https://api.vapi.ai'
};

// Debug function for development
export const debugVapiConfig = () => {
  if (import.meta.env.MODE === 'development') {
    console.log('VAPI Key:', import.meta.env.VITE_VAPI_PUBLIC_KEY ? 'Set' : 'Not set');
    console.log('VAPI Base URL:', VAPI_CONFIG.baseUrl);
  }
};