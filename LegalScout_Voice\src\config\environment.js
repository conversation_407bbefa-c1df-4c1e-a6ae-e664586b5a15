/**
 * Environment Configuration
 *
 * Centralized environment detection and API URL configuration
 */

// Robust development mode detection
const isDevelopment = () => {
  // Safely check import.meta.env first
  try {
    if (import.meta.env.DEV || import.meta.env.MODE === 'development') {
      return true;
    }
  } catch (e) {
    // import.meta.env may not be available in all contexts
  }

  // Check window location if available
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    const port = window.location.port;

    return hostname === 'localhost' ||
           hostname === '127.0.0.1' ||
           port === '5174' ||
           port === '5173';
  }

  return false;
};

// Environment configuration
export const ENV_CONFIG = {
  isDevelopment: isDevelopment(),
  isProduction: !isDevelopment(),

  // API URLs - Force localhost in development
  getApiUrl: () => {
    if (isDevelopment()) {
      return 'http://localhost:5174';
    }
    return 'https://dashboard.legalscout.net';
  },

  // Get base URL for current environment
  getBaseUrl: () => {
    if (typeof window !== 'undefined') {
      return window.location.origin;
    }
    return isDevelopment() ? 'http://localhost:5174' : 'https://dashboard.legalscout.net';
  },

  // Feature flags - safely check environment variables
  enableMockMode: (() => {
    try {
      return import.meta.env.VITE_FALLBACK_MODE === 'true';
    } catch (e) {
      return false;
    }
  })(),
  enableDebugMode: (() => {
    try {
      return isDevelopment() || import.meta.env.VITE_DEBUG === 'true';
    } catch (e) {
      return isDevelopment();
    }
  })()
};

// Debug logging
if (ENV_CONFIG.enableDebugMode) {
  console.log('[Environment] Configuration:', {
    isDevelopment: ENV_CONFIG.isDevelopment,
    apiUrl: ENV_CONFIG.getApiUrl(),
    baseUrl: ENV_CONFIG.getBaseUrl()
  });
}

export default ENV_CONFIG;
