/**
 * Enhanced MCP Configuration
 *
 * This file contains configuration for Model Context Protocol (MCP) services
 * like Vapi, Supabase, and other integrations.
 *
 * It supports different environments (development, production, test) and
 * provides fallback values for missing environment variables.
 */

// Helper function to safely get environment variables
const getEnvVar = (name) => {
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env[name] || '';
  }
  if (typeof process !== 'undefined' && process.env) {
    return process.env[name] || '';
  }
  return '';
};

// Import centralized Vapi configuration
import { getVapiApiKey, VAPI_PUBLIC_KEY, VAPI_SECRET_KEY } from './vapiConfig.js';
const SUPABASE_URL = getEnvVar('VITE_SUPABASE_URL');
const SUPABASE_ANON_KEY = getEnvVar('VITE_SUPABASE_ANON_KEY');
const DEFAULT_ASSISTANT_ID = getEnvVar('VITE_VAPI_DEFAULT_ASSISTANT_ID');

// Determine environment
const isDevelopment =
  (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') ||
  (typeof import.meta !== 'undefined' && import.meta.env?.MODE === 'development');

const isTest =
  (typeof process !== 'undefined' && process.env?.NODE_ENV === 'test') ||
  (typeof import.meta !== 'undefined' && import.meta.env?.MODE === 'test');

// MCP Configuration
export const mcpConfig = {
  // Vapi configuration
  voice: {
    vapi: {
      publicKey: VAPI_PUBLIC_KEY,
      secretKey: VAPI_SECRET_KEY,
      getApiKey: getVapiApiKey,
      mcpUrl: 'https://mcp.vapi.ai/sse',
      // Use different proxy path for development vs production
      mcpProxyPath: isDevelopment ? 'https://mcp.vapi.ai/sse' : 'https://mcp.vapi.ai/sse',
      // Fallback proxy paths to try if the main one fails
      fallbackProxyPaths: [
        'https://mcp.vapi.ai/sse',
        '/api/vapi-mcp-server/sse',
        '/api/vapi-mcp/sse',
        '/vapi-mcp-server/sse',
        '/vapi-mcp/sse'
      ],
      defaultVoice: 'echo', // OpenAI voice - reliable and commonly used
      defaultProvider: 'openai',
      defaultAssistantId: DEFAULT_ASSISTANT_ID,
      // API endpoints
      apiEndpoints: {
        production: 'https://api.vapi.ai',
        development: 'https://api.vapi.ai',
        test: 'https://api.vapi.ai'
      },
      // Fallback options
      fallbacks: {
        // Whether to use direct API if MCP fails
        useDirectApiOnFailure: true,
        // Whether to create a new assistant if one doesn't exist
        createAssistantIfNotExists: true,
        // Maximum retries for API calls
        maxRetries: 3,
        // Retry delay in milliseconds
        retryDelay: 1000
      }
    }
  },

  // Supabase configuration
  database: {
    supabase: {
      url: SUPABASE_URL,
      anonKey: SUPABASE_ANON_KEY
    }
  },

  // Default assistant configuration
  assistant: {
    defaultModel: 'gpt-4o',
    defaultProvider: 'openai',
    defaultTranscriber: {
      provider: 'deepgram',
      model: 'nova-3'
    }
  },

  // Environment information
  environment: {
    isDevelopment,
    isTest,
    isProduction: !isDevelopment && !isTest
  }
};

// Export default configuration
export default mcpConfig;
