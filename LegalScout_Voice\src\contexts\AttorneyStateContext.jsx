import React, { useContext, useState, useEffect } from 'react';
import attorneyStateManager from '../services/attorneyStateManager';

// Lazy initialization to avoid circular dependency issues in production builds
let AttorneyStateContext = null;

const getAttorneyStateContext = () => {
  if (!AttorneyStateContext) {
    // Ensure React is fully loaded before creating context
    if (typeof React !== 'undefined' && React.createContext) {
      AttorneyStateContext = React.createContext(null);
    } else {
      // Fallback for edge cases
      throw new Error('React is not properly loaded');
    }
  }
  return AttorneyStateContext;
};

/**
 * Attorney State Provider
 *
 * Provides attorney state to all child components.
 */
export function AttorneyStateProvider({ children }) {
  const [attorney, setAttorney] = useState(attorneyStateManager.attorney);
  const [isLoading, setIsLoading] = useState(attorneyStateManager.isLoading);
  const [isSaving, setIsSaving] = useState(attorneyStateManager.isSaving);
  const [isSyncing, setIsSyncing] = useState(attorneyStateManager.isSyncing);
  const [lastError, setLastError] = useState(attorneyStateManager.lastError);

  useEffect(() => {
    // Subscribe to attorney state changes
    const unsubscribe = attorneyStateManager.subscribe((newAttorney) => {
      setAttorney(newAttorney);
    });

    // Set up status listeners
    const loadingInterval = setInterval(() => {
      setIsLoading(attorneyStateManager.isLoading);
      setIsSaving(attorneyStateManager.isSaving);
      setIsSyncing(attorneyStateManager.isSyncing);
      setLastError(attorneyStateManager.lastError);
    }, 100);

    // Clean up on unmount
    return () => {
      unsubscribe();
      clearInterval(loadingInterval);
    };
  }, []);

  // Create value object
  const value = {
    attorney,
    isLoading,
    isSaving,
    isSyncing,
    lastError,
    loadAttorney: async (options) => {
      try {
        setIsLoading(true);
        const result = await attorneyStateManager.loadAttorney(options);
        return result;
      } finally {
        setIsLoading(false);
      }
    },
    saveAttorney: async (attorneyData, options) => {
      try {
        setIsSaving(true);
        const result = await attorneyStateManager.saveAttorney(attorneyData, options);
        return result;
      } finally {
        setIsSaving(false);
      }
    },
    updateAttorney: async (attorneyData) => {
      try {
        setIsSaving(true);
        const result = await attorneyStateManager.updateAttorney(attorneyData);
        return result;
      } finally {
        setIsSaving(false);
      }
    },
    createAttorney: async (attorneyData) => {
      try {
        setIsSaving(true);
        const result = await attorneyStateManager.createAttorney(attorneyData);
        return result;
      } finally {
        setIsSaving(false);
      }
    },
    syncWithVapi: async (options) => {
      try {
        setIsSyncing(true);
        const result = await attorneyStateManager.syncWithVapi(options);
        return result;
      } finally {
        setIsSyncing(false);
      }
    }
  };

  const Context = getAttorneyStateContext();
  return (
    <Context.Provider value={value}>
      {children}
    </Context.Provider>
  );
}

/**
 * Use Attorney State Hook
 *
 * Hook for accessing attorney state in components.
 */
export function useAttorneyState() {
  try {
    // Try to use the context with lazy initialization
    const Context = getAttorneyStateContext();
    const context = useContext(Context);

    if (context) {
      return context;
    }

    // If context is not available, fall back to the global attorney state manager
    if (window.attorneyStateManager) {
      console.log('[useAttorneyState] Using global attorney state manager fallback');

      // Create a minimal context-like object using the global attorney state manager
      return {
        attorney: window.attorneyStateManager.attorney,
        isLoading: window.attorneyStateManager.isLoading || false,
        isSaving: window.attorneyStateManager.isSaving || false,
        isSyncing: window.attorneyStateManager.isSyncing || false,
        lastError: window.attorneyStateManager.lastError,
        loadAttorney: () => Promise.resolve(window.attorneyStateManager.loadFromLocalStorage()),
        saveAttorney: () => Promise.resolve(window.attorneyStateManager.attorney),
        updateAttorney: () => Promise.resolve(window.attorneyStateManager.attorney),
        createAttorney: () => Promise.resolve(window.attorneyStateManager.attorney),
        syncWithVapi: () => Promise.resolve({ action: 'none' })
      };
    }

    // If all else fails, throw an error
    throw new Error('useAttorneyState must be used within an AttorneyStateProvider');
  } catch (error) {
    console.error('[useAttorneyState] Error:', error);

    // Return a minimal fallback object to prevent app crashes
    return {
      attorney: null,
      isLoading: false,
      isSaving: false,
      isSyncing: false,
      lastError: error,
      loadAttorney: () => Promise.resolve(null),
      saveAttorney: () => Promise.resolve(null),
      updateAttorney: () => Promise.resolve(null),
      createAttorney: () => Promise.resolve(null),
      syncWithVapi: () => Promise.resolve({ action: 'none' })
    };
  }
}

/**
 * Standalone hook for components outside the provider
 */
export function useStandaloneAttorneyState() {
  const [attorney, setAttorney] = useState(attorneyStateManager.attorney);
  const [isLoading, setIsLoading] = useState(attorneyStateManager.isLoading);
  const [isSaving, setIsSaving] = useState(attorneyStateManager.isSaving);
  const [isSyncing, setIsSyncing] = useState(attorneyStateManager.isSyncing);
  const [lastError, setLastError] = useState(attorneyStateManager.lastError);

  useEffect(() => {
    // Subscribe to attorney state changes
    const unsubscribe = attorneyStateManager.subscribe((newAttorney) => {
      setAttorney(newAttorney);
    });

    // Set up status listeners
    const statusInterval = setInterval(() => {
      setIsLoading(attorneyStateManager.isLoading);
      setIsSaving(attorneyStateManager.isSaving);
      setIsSyncing(attorneyStateManager.isSyncing);
      setLastError(attorneyStateManager.lastError);
    }, 100);

    // Clean up on unmount
    return () => {
      unsubscribe();
      clearInterval(statusInterval);
    };
  }, []);

  return {
    attorney,
    isLoading,
    isSaving,
    isSyncing,
    lastError,
    loadAttorney: attorneyStateManager.loadAttorney,
    saveAttorney: attorneyStateManager.saveAttorney,
    updateAttorney: attorneyStateManager.updateAttorney,
    createAttorney: attorneyStateManager.createAttorney,
    syncWithVapi: attorneyStateManager.syncWithVapi
  };
}
