// US state boundaries GeoJSON data
export const US_STATES_GEOJSON = {
  type: 'FeatureCollection',
  features: [
    // United States as a whole
    {
      type: 'Feature',
      properties: { name: 'USA', ISO_A2: 'US' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [-125.0, 24.0],
          [-125.0, 49.0],
          [-66.0, 49.0],
          [-66.0, 24.0],
          [-125.0, 24.0]
        ]]
      }
    },
    // Add a few major states for visibility
    {
      type: 'Feature',
      properties: { name: 'California', ISO_A2: 'US' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [-124.4, 32.5],
          [-124.4, 42.0],
          [-114.1, 42.0],
          [-114.1, 32.5],
          [-124.4, 32.5]
        ]]
      }
    },
    {
      type: 'Feature',
      properties: { name: 'Texas', ISO_A2: 'US' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [-106.6, 25.8],
          [-106.6, 36.5],
          [-93.5, 36.5],
          [-93.5, 25.8],
          [-106.6, 25.8]
        ]]
      }
    },
    {
      type: 'Feature',
      properties: { name: 'Florida', ISO_A2: 'US' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [-87.6, 24.5],
          [-87.6, 31.0],
          [-80.0, 31.0],
          [-80.0, 24.5],
          [-87.6, 24.5]
        ]]
      }
    },
    {
      type: 'Feature',
      properties: { name: 'New York', ISO_A2: 'US' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [-79.8, 40.5],
          [-79.8, 45.0],
          [-71.9, 45.0],
          [-71.9, 40.5],
          [-79.8, 40.5]
        ]]
      }
    }
  ]
};

// Full US boundaries for highlighting
export const US_BOUNDARIES = {
  type: 'Feature',
  properties: { name: 'United States', ISO_A2: 'US' },
  geometry: {
    type: 'Polygon',
    coordinates: [[
      [-125.0, 24.0],
      [-125.0, 49.0],
      [-66.0, 49.0],
      [-66.0, 24.0],
      [-125.0, 24.0]
    ]]
  }
}; 