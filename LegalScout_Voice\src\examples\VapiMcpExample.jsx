import { useState, useEffect } from 'react';
import { vapiMcpService } from '../services/vapiMcpService';

/**
 * Example component demonstrating Vapi MCP Server integration
 * 
 * This component shows how to:
 * 1. Connect to the Vapi MCP Server
 * 2. List assistants and phone numbers
 * 3. Create and schedule calls
 */
const VapiMcpExample = () => {
  // State
  const [connected, setConnected] = useState(false);
  const [assistants, setAssistants] = useState([]);
  const [phoneNumbers, setPhoneNumbers] = useState([]);
  const [selectedAssistant, setSelectedAssistant] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [scheduleDate, setScheduleDate] = useState('');
  const [scheduleTime, setScheduleTime] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Connect to Vapi MCP Server on component mount
  useEffect(() => {
    const connectToMcp = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Get API key from environment variables
        const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
        
        if (!apiKey) {
          throw new Error('Vapi API key not found in environment variables');
        }
        
        // Connect to MCP server
        const success = await vapiMcpService.connect(apiKey);
        setConnected(success);
        
        if (success) {
          // Load assistants and phone numbers
          await loadAssistantsAndPhoneNumbers();
        }
      } catch (err) {
        console.error('Error connecting to Vapi MCP Server:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    connectToMcp();
    
    // Disconnect when component unmounts
    return () => {
      vapiMcpService.disconnect();
    };
  }, []);

  // Load assistants and phone numbers
  const loadAssistantsAndPhoneNumbers = async () => {
    try {
      setLoading(true);
      
      // List assistants
      const assistantsList = await vapiMcpService.listAssistants();
      setAssistants(assistantsList || []);
      
      // List phone numbers
      const phoneNumbersList = await vapiMcpService.listPhoneNumbers();
      setPhoneNumbers(phoneNumbersList || []);
      
      // Set default selected assistant if available
      if (assistantsList && assistantsList.length > 0) {
        setSelectedAssistant(assistantsList[0].id);
      }
    } catch (err) {
      console.error('Error loading data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Create a call
  const handleCreateCall = async (e) => {
    e.preventDefault();
    
    if (!connected || !selectedAssistant || !customerPhone) {
      setError('Please fill in all required fields');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      // Prepare options
      const options = {};
      
      // If phone numbers are available, use the first one
      if (phoneNumbers.length > 0) {
        options.phoneNumberId = phoneNumbers[0].id;
      }
      
      // If schedule date and time are provided, add scheduledAt
      if (scheduleDate && scheduleTime) {
        const scheduledDateTime = new Date(`${scheduleDate}T${scheduleTime}`);
        options.scheduledAt = scheduledDateTime.toISOString();
      }
      
      // Create the call
      const call = await vapiMcpService.createCall(selectedAssistant, customerPhone, options);
      
      // Show success message
      setSuccess(`Call ${options.scheduledAt ? 'scheduled' : 'created'} successfully! Call ID: ${call.id}`);
      
      // Reset form
      setCustomerPhone('');
      setScheduleDate('');
      setScheduleTime('');
    } catch (err) {
      console.error('Error creating call:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Vapi MCP Server Example</h1>
      
      {/* Connection Status */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Connection Status</h2>
        <p className={`font-medium ${connected ? 'text-green-600' : 'text-red-600'}`}>
          {connected ? 'Connected to Vapi MCP Server' : 'Not Connected'}
        </p>
        
        {loading && <p className="text-blue-600 mt-2">Loading...</p>}
        
        {error && (
          <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            <p className="font-semibold">Error:</p>
            <p>{error}</p>
          </div>
        )}
        
        {success && (
          <div className="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            <p className="font-semibold">Success:</p>
            <p>{success}</p>
          </div>
        )}
      </div>
      
      {/* Assistants */}
      {connected && (
        <div className="mb-6 p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Available Assistants</h2>
          {assistants.length > 0 ? (
            <ul className="list-disc pl-5">
              {assistants.map((assistant) => (
                <li key={assistant.id}>
                  {assistant.name} ({assistant.id})
                </li>
              ))}
            </ul>
          ) : (
            <p>No assistants found</p>
          )}
        </div>
      )}
      
      {/* Phone Numbers */}
      {connected && (
        <div className="mb-6 p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Available Phone Numbers</h2>
          {phoneNumbers.length > 0 ? (
            <ul className="list-disc pl-5">
              {phoneNumbers.map((phoneNumber) => (
                <li key={phoneNumber.id}>
                  {phoneNumber.phoneNumber} ({phoneNumber.id})
                </li>
              ))}
            </ul>
          ) : (
            <p>No phone numbers found</p>
          )}
        </div>
      )}
      
      {/* Create Call Form */}
      {connected && (
        <div className="p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Create or Schedule a Call</h2>
          
          <form onSubmit={handleCreateCall}>
            {/* Assistant Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                Assistant
              </label>
              <select
                value={selectedAssistant}
                onChange={(e) => setSelectedAssistant(e.target.value)}
                className="w-full p-2 border rounded"
                required
              >
                <option value="">Select an assistant</option>
                {assistants.map((assistant) => (
                  <option key={assistant.id} value={assistant.id}>
                    {assistant.name}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Customer Phone */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                Customer Phone Number
              </label>
              <input
                type="tel"
                value={customerPhone}
                onChange={(e) => setCustomerPhone(e.target.value)}
                placeholder="+1234567890"
                className="w-full p-2 border rounded"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter phone number in international format (e.g., +1234567890)
              </p>
            </div>
            
            {/* Schedule Date and Time */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                Schedule Call (Optional)
              </label>
              <div className="flex space-x-2">
                <input
                  type="date"
                  value={scheduleDate}
                  onChange={(e) => setScheduleDate(e.target.value)}
                  className="w-1/2 p-2 border rounded"
                />
                <input
                  type="time"
                  value={scheduleTime}
                  onChange={(e) => setScheduleTime(e.target.value)}
                  className="w-1/2 p-2 border rounded"
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Leave blank for immediate call
              </p>
            </div>
            
            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading || !selectedAssistant || !customerPhone}
              className={`px-4 py-2 rounded ${
                loading || !selectedAssistant || !customerPhone
                  ? 'bg-gray-300 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              {loading ? 'Processing...' : scheduleDate && scheduleTime ? 'Schedule Call' : 'Create Call'}
            </button>
          </form>
        </div>
      )}
    </div>
  );
};

export default VapiMcpExample;
