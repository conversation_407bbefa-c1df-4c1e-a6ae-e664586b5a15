/**
 * Interactive Elements Fix
 *
 * This CSS file fixes issues with input fields, dropdowns, and other interactive elements
 * that prevent users from clicking or interacting with them.
 */

/* Ensure all interactive elements are clickable */
input,
textarea,
select,
button,
a,
[role="button"],
[role="combobox"],
[role="listbox"],
[role="option"],
.dropdown,
.dropdown-menu,
.dropdown-toggle,
.dropdown-item,
.select-wrapper,
.select-dropdown,
.select-options,
.modern-select,
.modern-dropdown {
  pointer-events: auto !important;
  z-index: 50 !important;
  position: relative !important;
}

/* Fix for the glass-effect and start-option classes */
.glass-effect,
.start-option {
  pointer-events: auto !important;
  position: relative !important;
  z-index: 40 !important;
}

/* Fix for the url-input-group */
.url-input-group {
  pointer-events: auto !important;
  position: relative !important;
  z-index: 45 !important;
}

/* Fix for any overlays that might be blocking inputs */
.start-options.vertical {
  pointer-events: auto !important;
  position: relative !important;
  z-index: 40 !important;
}

/* Fix for the input-group */
.input-group {
  pointer-events: auto !important;
  position: relative !important;
  z-index: 45 !important;
}

/* Fix for the modern-input class */
.modern-input {
  pointer-events: auto !important;
  position: relative !important;
  z-index: 50 !important;
  cursor: text !important;
}

/* Fix for dropdown containers */
.dropdown-container,
.select-container,
.modern-select-container,
.dropdown-wrapper,
.select-wrapper,
.dropdown,
.select {
  pointer-events: auto !important;
  position: relative !important;
  z-index: 60 !important;
}

/* Fix for dropdown menus */
.dropdown-menu,
.select-menu,
.dropdown-options,
.select-options,
.dropdown-list,
.select-list,
.options-list,
.dropdown-items,
.select-items {
  pointer-events: auto !important;
  position: absolute !important;
  z-index: 100 !important;
  display: block !important;
}

/* Fix for hover effects that might interfere with clicking */
.start-option:hover {
  pointer-events: auto !important;
}

/* Fix for any backdrop filters that might be causing issues */
.glass-effect,
.start-option {
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* Fix for any animations that might be interfering */
@keyframes pulse-border {
  0% { border-color: rgba(75, 116, 170, 0.3); }
  100% { border-color: rgba(75, 116, 170, 0.8); }
}

/* Fix for any transform effects that might be causing issues */
.start-option:hover {
  transform: translateY(-2px) !important;
}
