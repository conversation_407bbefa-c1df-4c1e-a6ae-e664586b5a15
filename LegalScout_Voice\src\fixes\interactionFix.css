/**
 * Interaction Fix
 *
 * This CSS file fixes issues with input fields and dropdowns
 * that prevent users from clicking or interacting with them.
 */

/* Make sure inputs and selects are clickable */
#firmUrl,
input[type="url"],
input[type="text"],
select,
.dropdown,
.dropdown-toggle,
.dropdown-menu {
  pointer-events: auto !important;
  position: relative !important;
  z-index: 1000 !important; /* Very high z-index to ensure it's above everything */
}

/* Ensure the submit button is visible and clickable */
.begin-config,
.modern-button,
button[type="submit"],
.url-submit-button {
  pointer-events: auto !important;
  position: relative !important;
  z-index: 1001 !important; /* Above inputs */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Fix for containers */
.start-option,
.glass-effect,
.url-input-group,
.input-group,
.dropdown-container,
.select-container {
  pointer-events: auto !important;
  position: relative !important;
  z-index: 999 !important; /* High z-index but below the actual inputs */
}

/* Ensure dropdown menus appear above other elements */
.dropdown-menu,
.select-dropdown,
.options-list {
  position: absolute !important;
  z-index: 1001 !important; /* Above inputs */
}

/* Fix for any potential overlays */
.overlay,
.modal-overlay,
.backdrop,
.background-layer,
.animated-background {
  pointer-events: none !important; /* Allow clicks to pass through */
}

/* Ensure the main content area allows interaction */
.main-content,
.app-wrapper,
main,
.container,
.content-area {
  position: relative !important;
  z-index: 500 !important; /* Above background elements */
}
