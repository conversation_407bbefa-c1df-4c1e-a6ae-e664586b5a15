import { useState, useEffect, useCallback, useRef } from 'react';
import { enhancedVapiMcpService } from '../services/EnhancedVapiMcpService';
import { mcpConfig } from '../config/mcp.config';

/**
 * Custom hook for interacting with Vapi
 * 
 * This hook provides a simplified interface for interacting with Vapi's voice AI services.
 * It handles the connection to Vapi, starting and stopping calls, and managing the conversation.
 * 
 * @param {Object} options - Options for the hook
 * @param {string} options.assistantId - The Vapi assistant ID to use
 * @param {Object} options.customInstructions - Custom instructions for the assistant
 * @param {Function} options.onCallEnd - Callback when call ends
 * @returns {Object} - The hook state and methods
 */
const useVapi = (options = {}) => {
  // Extract options
  const {
    assistantId = mcpConfig.voice.vapi.defaultAssistantId,
    customInstructions = {},
    onCallEnd
  } = options;

  // State for session active status
  const [isSessionActive, setIsSessionActive] = useState(false);
  // State for conversation
  const [conversation, setConversation] = useState([]);
  // State for current speaker
  const [currentSpeaker, setCurrentSpeaker] = useState(null);
  // State for volume level
  const [volumeLevel, setVolumeLevel] = useState(0);
  // State for error
  const [error, setError] = useState(null);
  // State for connection status
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  
  // Refs for iframe and call ID
  const iframeRef = useRef(null);
  const callIdRef = useRef(null);
  
  // Initialize Vapi
  const initialize = useCallback(async () => {
    try {
      setConnectionStatus('connecting');
      
      // Connect to Vapi MCP service
      const connected = await enhancedVapiMcpService.connect(
        mcpConfig.voice.vapi.publicKey
      );
      
      if (!connected) {
        throw new Error('Failed to connect to Vapi');
      }
      
      setConnectionStatus('connected');
      return true;
    } catch (error) {
      console.error('[use-vapi] Error initializing Vapi:', error);
      setError(error.message);
      setConnectionStatus('error');
      return false;
    }
  }, []);
  
  // Start a call
  const startCall = useCallback(async (overrideAssistantId) => {
    try {
      // Initialize if not already connected
      if (connectionStatus !== 'connected') {
        const initialized = await initialize();
        if (!initialized) {
          throw new Error('Failed to initialize Vapi');
        }
      }
      
      // Use override assistant ID if provided, otherwise use the one from options
      const assistantIdToUse = overrideAssistantId || assistantId;
      
      if (!assistantIdToUse) {
        throw new Error('No assistant ID provided');
      }
      
      // Create iframe for call
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = `https://embed.vapi.ai/${assistantIdToUse}`;
      document.body.appendChild(iframe);
      
      // Store iframe ref
      iframeRef.current = iframe;
      
      // Listen for messages from iframe
      const handleMessage = (event) => {
        if (event.data && event.data.what === 'iframe-call-message') {
          // Handle audio level updates
          if (event.data.action === 'remote-participants-audio-level') {
            const levels = event.data.participantsAudioLevel || {};
            const assistantLevel = levels.assistant || 0;
            const userLevel = levels.user || 0;
            
            // Set volume level based on who is speaking
            if (assistantLevel > userLevel) {
              setVolumeLevel(assistantLevel);
              setCurrentSpeaker('assistant');
            } else if (userLevel > 0) {
              setVolumeLevel(userLevel);
              setCurrentSpeaker('user');
            } else {
              setVolumeLevel(0);
              setCurrentSpeaker(null);
            }
          }
          
          // Handle transcript updates
          if (event.data.action === 'transcript') {
            const { role, text } = event.data;
            if (role && text) {
              setConversation(prev => [...prev, { role, text }]);
            }
          }
          
          // Handle call status updates
          if (event.data.action === 'call-status') {
            const { status, callId } = event.data;
            
            if (status === 'connected') {
              setIsSessionActive(true);
              if (callId) {
                callIdRef.current = callId;
              }
            } else if (status === 'disconnected') {
              setIsSessionActive(false);
              if (onCallEnd) {
                onCallEnd();
              }
            }
          }
        }
      };
      
      window.addEventListener('message', handleMessage);
      
      // Set cleanup function
      const cleanup = () => {
        window.removeEventListener('message', handleMessage);
        if (iframeRef.current) {
          document.body.removeChild(iframeRef.current);
          iframeRef.current = null;
        }
      };
      
      // Store cleanup function
      window.vapiCleanup = cleanup;
      
      setIsSessionActive(true);
      return true;
    } catch (error) {
      console.error('[use-vapi] Error starting call:', error);
      setError(error.message);
      return false;
    }
  }, [assistantId, connectionStatus, initialize, onCallEnd]);
  
  // Stop a call
  const stopCall = useCallback(async () => {
    try {
      // Send end call message to iframe
      if (iframeRef.current) {
        iframeRef.current.contentWindow.postMessage(
          { action: 'end-call' },
          '*'
        );
      }
      
      // Clean up
      if (window.vapiCleanup) {
        window.vapiCleanup();
      }
      
      setIsSessionActive(false);
      setCurrentSpeaker(null);
      setVolumeLevel(0);
      
      return true;
    } catch (error) {
      console.error('[use-vapi] Error stopping call:', error);
      setError(error.message);
      return false;
    }
  }, []);
  
  // Toggle call
  const toggleCall = useCallback(async () => {
    if (isSessionActive) {
      return stopCall();
    } else {
      return startCall();
    }
  }, [isSessionActive, startCall, stopCall]);
  
  // Clear conversation
  const clearConversation = useCallback(() => {
    setConversation([]);
  }, []);
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (isSessionActive) {
        stopCall();
      }
    };
  }, [isSessionActive, stopCall]);
  
  return {
    isSessionActive,
    conversation,
    currentSpeaker,
    volumeLevel,
    error,
    connectionStatus,
    toggleCall,
    startCall,
    stopCall,
    clearConversation
  };
};

export default useVapi;
