import { useEffect, useRef, useState, useCallback } from 'react';
import Vapi from '@vapi-ai/web';

/**
 * Custom hook for interacting with Vapi voice AI
 * Based on the Vapi Blocks implementation but customized for LegalScout
 */
const useVapi = () => {
  // State for volume level (0-1)
  const [volumeLevel, setVolumeLevel] = useState(0);
  
  // State for tracking if a call is active
  const [isSessionActive, setIsSessionActive] = useState(false);
  
  // State for tracking the conversation history
  const [conversation, setConversation] = useState<{ role: string, text: string }[]>([]);
  
  // State for tracking the current speaker (assistant or user)
  const [currentSpeaker, setCurrentSpeaker] = useState<string | null>(null);
  
  // Reference to the Vapi instance
  const vapiRef = useRef<any>(null);
  
  // Get API key and assistant ID from environment variables
  const publicKey = process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY;
  const assistantId = process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID;

  /**
   * Initialize the Vapi instance and set up event listeners
   */
  const initializeVapi = useCallback(() => {
    if (!vapiRef.current && publicKey) {
      console.log('Initializing Vapi with public key');
      const vapiInstance = new Vapi(publicKey);
      vapiRef.current = vapiInstance;

      // Event: Call started
      vapiInstance.on('call-start', () => {
        console.log('Vapi call started');
        setIsSessionActive(true);
      });

      // Event: Call ended
      vapiInstance.on('call-end', () => {
        console.log('Vapi call ended');
        setIsSessionActive(false);
        setCurrentSpeaker(null);
        // Keep conversation history for reference
      });

      // Event: Volume level changed
      vapiInstance.on('volume-level', (volume: number) => {
        setVolumeLevel(volume);
      });

      // Event: Message received (transcripts, etc.)
      vapiInstance.on('message', (message: any) => {
        console.log('Vapi message:', message);
        
        // Handle transcript messages
        if (message.type === 'transcript') {
          // Set current speaker based on who is talking
          setCurrentSpeaker(message.role);
          
          // Only add final transcripts to the conversation history
          if (message.transcriptType === 'final') {
            setConversation((prev) => [
              ...prev,
              { role: message.role, text: message.transcript },
            ]);
          }
        }
        
        // Handle audio level messages for assistant
        if (message.type === 'iframe-call-message' && 
            message.action === 'remote-participants-audio-level' && 
            message.participantsAudioLevel) {
          // Extract assistant audio level if available
          const assistantLevel = message.participantsAudioLevel.assistant;
          if (assistantLevel !== undefined) {
            // If assistant is speaking, update current speaker
            if (assistantLevel > 0.05) {
              setCurrentSpeaker('assistant');
            } else if (currentSpeaker === 'assistant' && assistantLevel <= 0.05) {
              setCurrentSpeaker(null);
            }
          }
        }
      });

      // Event: Error occurred
      vapiInstance.on('error', (e: Error) => {
        console.error('Vapi error:', e);
      });
    }
  }, [publicKey, currentSpeaker]);

  // Initialize Vapi on component mount
  useEffect(() => {
    initializeVapi();

    // Cleanup function to end call and dispose Vapi instance
    return () => {
      if (vapiRef.current) {
        vapiRef.current.stop();
        vapiRef.current = null;
      }
    };
  }, [initializeVapi]);

  /**
   * Toggle the call state (start or stop)
   */
  const toggleCall = async () => {
    try {
      if (!vapiRef.current) {
        initializeVapi();
      }
      
      if (isSessionActive) {
        console.log('Stopping Vapi call');
        await vapiRef.current.stop();
      } else {
        if (!assistantId) {
          console.error('No assistant ID provided');
          return;
        }
        
        console.log('Starting Vapi call with assistant:', assistantId);
        await vapiRef.current.start(assistantId);
      }
    } catch (err) {
      console.error('Error toggling Vapi session:', err);
    }
  };

  /**
   * Start a call with a specific assistant ID
   * @param {string} id - The assistant ID to use
   */
  const startCall = async (id: string) => {
    try {
      if (!vapiRef.current) {
        initializeVapi();
      }
      
      if (isSessionActive) {
        await vapiRef.current.stop();
      }
      
      console.log('Starting Vapi call with assistant:', id);
      await vapiRef.current.start(id);
    } catch (err) {
      console.error('Error starting Vapi session:', err);
    }
  };

  /**
   * Stop the current call
   */
  const stopCall = async () => {
    try {
      if (vapiRef.current && isSessionActive) {
        console.log('Stopping Vapi call');
        await vapiRef.current.stop();
      }
    } catch (err) {
      console.error('Error stopping Vapi session:', err);
    }
  };

  /**
   * Clear the conversation history
   */
  const clearConversation = () => {
    setConversation([]);
  };

  return { 
    volumeLevel, 
    isSessionActive, 
    conversation, 
    currentSpeaker,
    toggleCall,
    startCall,
    stopCall,
    clearConversation
  };
};

export default useVapi;
