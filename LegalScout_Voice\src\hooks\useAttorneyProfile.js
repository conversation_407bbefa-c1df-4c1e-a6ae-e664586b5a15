/**
 * useAttorneyProfile Hook
 * 
 * A React hook for managing attorney profiles across systems:
 * - Supabase (database)
 * - <PERSON><PERSON><PERSON> (voice assistant)
 * - localStorage (client-side cache)
 * 
 * This hook provides:
 * - Loading attorney profiles
 * - Updating attorney profiles
 * - Synchronizing with Vapi
 * - Error handling and recovery
 */

import { useState, useEffect, useCallback } from 'react';
import { attorneyProfileManager } from '../services/AttorneyProfileManager';
import { useAuth } from '../contexts/AuthContext';

/**
 * Hook for managing attorney profiles
 * @returns {Object} Attorney profile state and methods
 */
export const useAttorneyProfile = () => {
  // Get authentication state
  const { user, isAuthenticated } = useAuth();
  
  // State
  const [attorney, setAttorney] = useState(attorneyProfileManager.currentAttorney);
  const [loading, setLoading] = useState(!attorney);
  const [error, setError] = useState(null);
  const [syncStatus, setSyncStatus] = useState(attorneyProfileManager.syncStatus);
  const [lastSyncTime, setLastSyncTime] = useState(attorneyProfileManager.lastSyncTime);

  // Initialize on mount if user is authenticated
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      setLoading(true);
      setError(null);

      attorneyProfileManager.initialize(user.id, user.email)
        .then(result => {
          // The listener will handle setting the attorney
          setLoading(false);
        })
        .catch(err => {
          console.error('[useAttorneyProfile] Error initializing attorney profile:', err);
          setError(err.message);
          setLoading(false);
        });
    }
  }, [isAuthenticated, user?.id, user?.email]);

  // Listen for attorney updates
  useEffect(() => {
    const handleUpdate = (updatedAttorney) => {
      setAttorney(updatedAttorney);
      setSyncStatus(attorneyProfileManager.syncStatus);
      setLastSyncTime(attorneyProfileManager.lastSyncTime);
    };

    attorneyProfileManager.addListener(handleUpdate);

    return () => {
      attorneyProfileManager.removeListener(handleUpdate);
    };
  }, []);

  // Force synchronization
  const forceSync = useCallback(async () => {
    try {
      setLoading(true);
      await attorneyProfileManager.forceSynchronization();
      setSyncStatus(attorneyProfileManager.syncStatus);
      setLastSyncTime(attorneyProfileManager.lastSyncTime);
      return { success: true };
    } catch (error) {
      console.error('[useAttorneyProfile] Error forcing synchronization:', error);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, []);

  // Check synchronization status
  const checkSyncStatus = useCallback(async () => {
    try {
      if (!attorney) {
        throw new Error('No attorney profile loaded');
      }

      await attorneyProfileManager.checkVapiSynchronization(attorney);
      setSyncStatus(attorneyProfileManager.syncStatus);
      setLastSyncTime(attorneyProfileManager.lastSyncTime);
      return { success: true };
    } catch (error) {
      console.error('[useAttorneyProfile] Error checking sync status:', error);
      setError(error.message);
      return { success: false, error: error.message };
    }
  }, [attorney]);

  // Update attorney profile
  const updateAttorney = useCallback(async (data) => {
    try {
      if (!attorney) {
        throw new Error('No attorney profile loaded');
      }

      setLoading(true);
      
      // Update in Supabase
      const updatedAttorney = await attorneyProfileManager.updateAttorneyInSupabase({
        ...data,
        id: attorney.id
      });
      
      // The Realtime subscription will handle updating the local state
      
      return { success: true, attorney: updatedAttorney };
    } catch (error) {
      console.error('[useAttorneyProfile] Error updating attorney:', error);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [attorney]);

  return {
    attorney,
    loading,
    error,
    syncStatus,
    lastSyncTime,
    forceSync,
    checkSyncStatus,
    updateAttorney
  };
};

export default useAttorneyProfile;
