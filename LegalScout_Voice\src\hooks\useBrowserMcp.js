/**
 * Hook for using the Browser MCP Server in React components
 * 
 * This hook provides:
 * - Connection management
 * - Browser navigation and interaction
 * - Error handling
 */

import { useState, useEffect, useCallback } from 'react';
import { browserMcpService } from '../services/browserMcpService';
import { createDebugger } from '../utils/debugConfig';

// Create debugger for this hook
const debug = createDebugger('useBrowserMcp');

const useBrowserMcp = () => {
  // State
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [snapshot, setSnapshot] = useState(null);

  // Initialize the Browser MCP client on mount
  useEffect(() => {
    const initializeMcp = async () => {
      try {
        setIsLoading(true);
        setError(null);
        await browserMcpService.initialize();
        setIsConnected(browserMcpService.isClientConnected());
      } catch (err) {
        debug.error('Error initializing Browser MCP:', err);
        setError(err.message || 'Failed to initialize Browser MCP');
      } finally {
        setIsLoading(false);
      }
    };

    initializeMcp();

    // Clean up on unmount
    return () => {
      browserMcpService.disconnect();
    };
  }, []);

  /**
   * Navigate to a URL
   * @param {string} url - URL to navigate to
   */
  const navigate = useCallback(async (url) => {
    try {
      setIsLoading(true);
      setError(null);
      await browserMcpService.navigate(url);
    } catch (err) {
      debug.error('Error navigating to URL:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Take a snapshot of the current page
   */
  const takeSnapshot = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const snapshotData = await browserMcpService.takeSnapshot();
      setSnapshot(snapshotData);
      return snapshotData;
    } catch (err) {
      debug.error('Error taking snapshot:', err);
      setError(err.message);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Click on an element
   * @param {string} element - Human-readable element description
   * @param {string} ref - Element reference from snapshot
   */
  const clickElement = useCallback(async (element, ref) => {
    try {
      setIsLoading(true);
      setError(null);
      await browserMcpService.clickElement(element, ref);
    } catch (err) {
      debug.error('Error clicking element:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Type text into an element
   * @param {string} element - Human-readable element description
   * @param {string} ref - Element reference from snapshot
   * @param {string} text - Text to type
   * @param {boolean} submit - Whether to submit after typing
   */
  const typeText = useCallback(async (element, ref, text, submit = false) => {
    try {
      setIsLoading(true);
      setError(null);
      await browserMcpService.typeText(element, ref, text, submit);
    } catch (err) {
      debug.error('Error typing text:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Take a screenshot of the current page
   */
  const takeScreenshot = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const screenshotData = await browserMcpService.takeScreenshot();
      return screenshotData;
    } catch (err) {
      debug.error('Error taking screenshot:', err);
      setError(err.message);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Return hook state and methods
  return {
    // State
    isConnected,
    isLoading,
    error,
    snapshot,
    
    // Methods
    navigate,
    takeSnapshot,
    clickElement,
    typeText,
    takeScreenshot,
    
    // Reset error
    clearError: () => setError(null)
  };
};

export default useBrowserMcp;
