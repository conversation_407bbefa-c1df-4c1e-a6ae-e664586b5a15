/**
 * Call Control Hook
 *
 * This hook provides functionality for controlling and monitoring calls.
 * It integrates with the EnhancedVapiService to provide a consistent interface
 * for call control operations.
 */

import { useState, useEffect, useCallback } from 'react';
import { enhancedVapiCallService } from '../services/EnhancedVapiCallService';
import { verifyCallControlToken } from '../utils/tokenUtils';
import { supabase } from '../lib/supabase';

/**
 * Hook for call control functionality
 * @param {Object} options - Hook options
 * @param {string} options.token - Call control token
 * @param {Function} options.onError - Error callback
 * @returns {Object} - Hook state and methods
 */
const useCallControl = (options = {}) => {
  // Extract options
  const { token, onError } = options;

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tokenData, setTokenData] = useState(null);
  const [call, setCall] = useState(null);
  const [attorney, setAttorney] = useState(null);
  const [transcripts, setTranscripts] = useState([]);
  const [assistantIsSpeaking, setAssistantIsSpeaking] = useState(false);
  const [volumeLevel, setVolumeLevel] = useState(0);
  const [callStatus, setCallStatus] = useState('unknown');
  const [polling, setPolling] = useState(false);

  // Handle errors
  const handleError = useCallback((error) => {
    console.error('Call control error:', error);
    setError(error.message || 'An error occurred');

    if (onError) {
      onError(error);
    }
  }, [onError]);

  // Verify token and load initial data
  useEffect(() => {
    const initializeCallControl = async () => {
      try {
        setLoading(true);

        if (!token) {
          throw new Error('No token provided');
        }

        // Verify token
        const decoded = await verifyCallControlToken(token);

        if (!decoded) {
          throw new Error('Invalid or expired token');
        }

        setTokenData(decoded);

        // Initialize Vapi call service
        const initialized = await enhancedVapiCallService.initialize();

        if (!initialized) {
          throw new Error('Failed to initialize Vapi call service');
        }

        // Get call details
        const callData = await enhancedVapiCallService.getCall(decoded.callId);

        if (!callData) {
          throw new Error('Call not found');
        }

        setCall(callData);
        setCallStatus(callData.status);

        // Get attorney details
        const { data: attorneyData, error: attorneyError } = await supabase
          .from('attorneys')
          .select('*')
          .eq('id', decoded.attorneyId)
          .single();

        if (attorneyError) {
          throw new Error('Attorney not found');
        }

        setAttorney(attorneyData);

        // Set initial transcripts
        if (callData.transcripts && Array.isArray(callData.transcripts)) {
          setTranscripts(callData.transcripts);
        }

        setLoading(false);
        setPolling(true);
      } catch (error) {
        handleError(error);
        setLoading(false);
      }
    };

    if (token) {
      initializeCallControl();
    }
  }, [token, handleError]);

  // Poll for call updates
  useEffect(() => {
    if (!polling || !tokenData || !tokenData.callId) return;

    const pollInterval = setInterval(async () => {
      try {
        const callData = await enhancedVapiCallService.getCall(tokenData.callId);

        if (!callData) {
          clearInterval(pollInterval);
          setError('Call no longer available');
          return;
        }

        setCall(callData);
        setCallStatus(callData.status);

        // Update transcripts
        if (callData.transcripts && Array.isArray(callData.transcripts)) {
          setTranscripts(callData.transcripts);
        }

        // If call is completed, stop polling
        if (callData.status === 'completed' || callData.status === 'ended') {
          clearInterval(pollInterval);
          setPolling(false);
        }
      } catch (error) {
        console.error('Error polling call updates:', error);
      }
    }, 5000); // Poll every 5 seconds

    return () => clearInterval(pollInterval);
  }, [polling, tokenData]);

  // Send a message to the call
  const sendMessage = useCallback(async (message) => {
    if (!message.trim() || !tokenData || !tokenData.callId) return;

    try {
      // TODO: Implement message sending in EnhancedVapiCallService
      // For now, just log the message
      console.log(`[useCallControl] Sending message to call ${tokenData.callId}: ${message}`);

      // In development, return true to simulate success
      if (process.env.NODE_ENV === 'development') {
        return true;
      }

      // Send message to the call
      // await enhancedVapiCallService.sendMessage(tokenData.callId, message);
      return true;
    } catch (error) {
      handleError(error);
      return false;
    }
  }, [tokenData, handleError]);

  // Take over the call
  const takeOverCall = useCallback(async () => {
    if (!tokenData || !tokenData.callId) return;

    try {
      // TODO: Implement call takeover logic in EnhancedVapiCallService
      // For now, just log the takeover attempt
      console.log(`[useCallControl] Taking over call ${tokenData.callId} for attorney ${tokenData.attorneyId}`);

      // In development, return true to simulate success
      if (process.env.NODE_ENV === 'development') {
        return true;
      }

      // Implement call takeover logic
      // This would typically involve transferring the call to the attorney's phone
      // return await enhancedVapiCallService.takeOverCall(tokenData.callId, tokenData.attorneyId);
      return false;
    } catch (error) {
      handleError(error);
      return false;
    }
  }, [tokenData, handleError]);

  // End the call
  const endCall = useCallback(async () => {
    if (!tokenData || !tokenData.callId) return;

    try {
      // TODO: Implement call ending in EnhancedVapiCallService
      // For now, just log the end call attempt
      console.log(`[useCallControl] Ending call ${tokenData.callId}`);

      // In development, update status to simulate success
      if (process.env.NODE_ENV === 'development') {
        setCallStatus('ended');
        return true;
      }

      // End the call
      // await enhancedVapiCallService.endCall(tokenData.callId);

      // Update status
      setCallStatus('ended');
      return true;
    } catch (error) {
      handleError(error);
      return false;
    }
  }, [tokenData, handleError]);

  return {
    // State
    loading,
    error,
    tokenData,
    call,
    attorney,
    transcripts,
    assistantIsSpeaking,
    volumeLevel,
    callStatus,

    // Methods
    sendMessage,
    takeOverCall,
    endCall,

    // Reset error
    clearError: () => setError(null)
  };
};

export default useCallControl;
