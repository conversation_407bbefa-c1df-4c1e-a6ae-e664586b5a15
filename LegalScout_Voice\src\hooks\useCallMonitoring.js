/**
 * useCallMonitoring Hook
 * 
 * This hook provides real-time monitoring of Vapi calls, including status updates,
 * transcripts, and control functions.
 */

import { useState, useEffect, useCallback } from 'react';
import { enhancedVapiCallService } from '../services/EnhancedVapiCallService';

/**
 * Hook for monitoring a call
 * @param {string} callId - Call ID to monitor
 * @returns {Object} - Call monitoring state and functions
 */
export const useCallMonitoring = (callId) => {
  const [call, setCall] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [transcripts, setTranscripts] = useState([]);
  const [status, setStatus] = useState('unknown');
  const [assistantIsSpeaking, setAssistantIsSpeaking] = useState(false);
  const [volumeLevel, setVolumeLevel] = useState(0);
  
  // Load call data
  useEffect(() => {
    let mounted = true;
    let pollingInterval;
    
    const loadCall = async () => {
      try {
        if (!callId) return;
        
        setLoading(true);
        
        // Initialize the call service if needed
        await enhancedVapiCallService.initialize();
        
        // Get call data
        const callData = await enhancedVapiCallService.getCall(callId);
        
        if (mounted) {
          setCall(callData);
          setStatus(callData.status);
          setLoading(false);
        }
      } catch (err) {
        if (mounted) {
          console.error('[useCallMonitoring] Error loading call:', err);
          setError(err.message);
          setLoading(false);
        }
      }
    };
    
    loadCall();
    
    // Set up polling for updates
    pollingInterval = setInterval(loadCall, 5000);
    
    return () => {
      mounted = false;
      clearInterval(pollingInterval);
    };
  }, [callId]);
  
  // Set up event listeners
  useEffect(() => {
    if (!callId) return;
    
    // Listen for status changes
    const statusUnsubscribe = enhancedVapiCallService.addEventListener(
      callId,
      'status',
      (newStatus) => {
        setStatus(newStatus);
      }
    );
    
    // Listen for transcript updates
    const transcriptUnsubscribe = enhancedVapiCallService.addEventListener(
      callId,
      'transcript',
      (newTranscript) => {
        setTranscripts(prev => [...prev, newTranscript]);
      }
    );
    
    // Listen for speaking status
    const speakingUnsubscribe = enhancedVapiCallService.addEventListener(
      callId,
      'speaking',
      (isSpeaking) => {
        setAssistantIsSpeaking(isSpeaking);
      }
    );
    
    // Listen for volume level
    const volumeUnsubscribe = enhancedVapiCallService.addEventListener(
      callId,
      'volume',
      (level) => {
        setVolumeLevel(level);
      }
    );
    
    return () => {
      statusUnsubscribe();
      transcriptUnsubscribe();
      speakingUnsubscribe();
      volumeUnsubscribe();
    };
  }, [callId]);
  
  // End call function
  const endCall = useCallback(async () => {
    try {
      if (!callId) return;
      
      // TODO: Implement call ending in VapiMcpService
      // await enhancedVapiCallService.endCall(callId);
      
      setStatus('completed');
    } catch (err) {
      console.error('[useCallMonitoring] Error ending call:', err);
      setError(err.message);
    }
  }, [callId]);
  
  // Send message to call
  const sendMessage = useCallback(async (message) => {
    try {
      if (!callId || !message) return;
      
      // TODO: Implement message sending in VapiMcpService
      // await enhancedVapiCallService.sendMessage(callId, message);
      
      return true;
    } catch (err) {
      console.error('[useCallMonitoring] Error sending message:', err);
      setError(err.message);
      return false;
    }
  }, [callId]);
  
  return {
    call,
    loading,
    error,
    transcripts,
    status,
    assistantIsSpeaking,
    volumeLevel,
    endCall,
    sendMessage
  };
};

export default useCallMonitoring;
