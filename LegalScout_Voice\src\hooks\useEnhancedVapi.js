/**
 * Enhanced Vapi Hook
 *
 * This hook provides a consistent interface for interacting with Vapi voice AI.
 * It combines the functionality of the original useVapi hook with the enhanced
 * Vapi services for better reliability and consistency.
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { enhancedVapiMcpService } from '../services/EnhancedVapiMcpService';
import { initializeEnhancedVapi } from '../utils/enhancedIntegration';
import Vapi from '@vapi-ai/web';

/**
 * Custom hook for interacting with Vapi voice AI
 * @param {Object} options - Hook options
 * @param {string} options.assistantId - The ID of the assistant to use
 * @param {Object} options.customInstructions - Custom instructions for the assistant
 * @param {Function} options.onCallStart - Callback when call starts
 * @param {Function} options.onCallEnd - Callback when call ends
 * @param {Function} options.onError - Callback when an error occurs
 * @returns {Object} - Hook state and methods
 */
const useEnhancedVapi = ({
  assistantId,
  customInstructions,
  onCallStart,
  onCallEnd,
  onError
} = {}) => {
  // State for volume level (0-1)
  const [volumeLevel, setVolumeLevel] = useState(0);
  
  // State for tracking if a call is active
  const [isSessionActive, setIsSessionActive] = useState(false);
  
  // State for tracking the conversation history
  const [conversation, setConversation] = useState([]);
  
  // State for tracking the current speaker (assistant or user)
  const [currentSpeaker, setCurrentSpeaker] = useState(null);
  
  // State for tracking errors
  const [error, setError] = useState(null);
  
  // State for tracking loading state
  const [loading, setLoading] = useState(false);
  
  // Reference to the Vapi instance
  const vapiRef = useRef(null);
  
  // Reference to the call ID
  const callIdRef = useRef(null);
  
  /**
   * Initialize Vapi
   */
  const initializeVapi = useCallback(async () => {
    try {
      setLoading(true);
      
      // Initialize enhanced Vapi services
      const initResult = await initializeEnhancedVapi();
      
      if (!initResult) {
        throw new Error('Failed to initialize enhanced Vapi services');
      }
      
      // Get API key
      const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                    (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY);
      
      if (!apiKey) {
        throw new Error('No Vapi API key available');
      }
      
      // Create Vapi instance
      vapiRef.current = new Vapi(apiKey);
      
      // Set up event listeners
      setupEventListeners();
      
      return true;
    } catch (err) {
      console.error('[useEnhancedVapi] Error initializing Vapi:', err);
      setError(err.message);
      
      // Call onError callback if provided
      if (onError) {
        onError(err);
      }
      
      return false;
    } finally {
      setLoading(false);
    }
  }, [onError]);
  
  /**
   * Set up event listeners for the Vapi instance
   */
  const setupEventListeners = useCallback(() => {
    if (!vapiRef.current) return;
    
    // Set up event listeners
    vapiRef.current.on('call-start', () => {
      console.log('[useEnhancedVapi] Call started');
      setIsSessionActive(true);
      
      // Call onCallStart callback if provided
      if (onCallStart) {
        onCallStart();
      }
    });
    
    vapiRef.current.on('call-end', () => {
      console.log('[useEnhancedVapi] Call ended');
      setIsSessionActive(false);
      
      // Call onCallEnd callback if provided
      if (onCallEnd) {
        onCallEnd();
      }
    });
    
    vapiRef.current.on('error', (err) => {
      console.error('[useEnhancedVapi] Vapi error:', err);
      setError(err.message);
      
      // Call onError callback if provided
      if (onError) {
        onError(err);
      }
    });
    
    vapiRef.current.on('transcript', (transcript) => {
      console.log('[useEnhancedVapi] Transcript:', transcript);
      
      // Add transcript to conversation
      setConversation((prev) => [
        ...prev,
        {
          role: transcript.speaker === 'assistant' ? 'assistant' : 'user',
          text: transcript.text
        }
      ]);
      
      // Update current speaker
      setCurrentSpeaker(transcript.speaker);
    });
    
    vapiRef.current.on('audio-level', (level) => {
      // Update volume level (normalize to 0-1)
      setVolumeLevel(Math.min(1, Math.max(0, level / 100)));
      
      // Update audio source if available
      if (typeof window !== 'undefined' && window.updateAudioSource) {
        const speaker = currentSpeaker || 'ambient';
        const frequency = speaker === 'assistant' ? 200 : 300;
        window.updateAudioSource(level / 100, frequency, speaker);
      }
    });
  }, [currentSpeaker, onCallStart, onCallEnd, onError]);
  
  /**
   * Start a call
   */
  const startCall = useCallback(async () => {
    try {
      if (!vapiRef.current) {
        await initializeVapi();
      }
      
      if (!assistantId) {
        throw new Error('No assistant ID provided');
      }
      
      console.log('[useEnhancedVapi] Starting call with assistant:', assistantId);
      
      // Clear conversation
      setConversation([]);
      
      // Start call
      const callOptions = customInstructions ? { options: customInstructions } : undefined;
      await vapiRef.current.start(assistantId, callOptions);
      
      return true;
    } catch (err) {
      console.error('[useEnhancedVapi] Error starting call:', err);
      setError(err.message);
      
      // Call onError callback if provided
      if (onError) {
        onError(err);
      }
      
      return false;
    }
  }, [assistantId, customInstructions, initializeVapi, onError]);
  
  /**
   * Stop a call
   */
  const stopCall = useCallback(async () => {
    try {
      if (!vapiRef.current) {
        return false;
      }
      
      console.log('[useEnhancedVapi] Stopping call');
      
      // Stop call
      await vapiRef.current.stop();
      
      return true;
    } catch (err) {
      console.error('[useEnhancedVapi] Error stopping call:', err);
      setError(err.message);
      
      // Call onError callback if provided
      if (onError) {
        onError(err);
      }
      
      return false;
    }
  }, [onError]);
  
  /**
   * Toggle the call state (start or stop)
   */
  const toggleCall = useCallback(async () => {
    if (isSessionActive) {
      return stopCall();
    } else {
      return startCall();
    }
  }, [isSessionActive, startCall, stopCall]);
  
  /**
   * Clear the conversation history
   */
  const clearConversation = useCallback(() => {
    setConversation([]);
  }, []);
  
  // Initialize Vapi on component mount
  useEffect(() => {
    initializeVapi();
    
    // Cleanup function to end call and dispose Vapi instance
    return () => {
      if (vapiRef.current) {
        vapiRef.current.stop();
        vapiRef.current = null;
      }
    };
  }, [initializeVapi]);
  
  // Return hook state and methods
  return {
    // State
    volumeLevel,
    isSessionActive,
    conversation,
    currentSpeaker,
    error,
    loading,
    
    // Methods
    startCall,
    stopCall,
    toggleCall,
    clearConversation,
    
    // Clear error
    clearError: () => setError(null)
  };
};

export default useEnhancedVapi;
