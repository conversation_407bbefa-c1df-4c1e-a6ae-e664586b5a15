/**
 * Enhanced Vapi Hook with Debugging
 *
 * This hook provides a consistent interface for interacting with Vapi voice AI
 * with added debugging functionality.
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { enhancedVapiMcpService } from '../services/EnhancedVapiMcpService';
import { initializeEnhancedVapi } from '../utils/enhancedIntegration';
import { getCallDebugger } from '../utils/callDebugger';
import Vapi from '@vapi-ai/web';

/**
 * Custom hook for interacting with Vapi voice AI with debugging
 * @param {Object} options - Hook options
 * @param {string} options.assistantId - The ID of the assistant to use
 * @param {Object} options.customInstructions - Custom instructions for the assistant
 * @param {Function} options.onCallStart - Callback when call starts
 * @param {Function} options.onCallEnd - Callback when call ends
 * @param {Function} options.onError - Callback when an error occurs
 * @returns {Object} - Hook state and methods
 */
const useEnhancedVapiWithDebug = ({
  assistantId,
  customInstructions,
  onCallStart,
  onCallEnd,
  onError
} = {}) => {
  // Initialize debugger
  const callDebugger = getCallDebugger('useEnhancedVapi');
  callDebugger.log('Initializing useEnhancedVapi hook', { assistantId });

  // State for volume level (0-1)
  const [volumeLevel, setVolumeLevel] = useState(0);
  // State for session active status
  const [isSessionActive, setIsSessionActive] = useState(false);
  // State for conversation history
  const [conversation, setConversation] = useState([]);
  // State for current speaker
  const [currentSpeaker, setCurrentSpeaker] = useState(null);
  // State for error
  const [error, setError] = useState(null);
  // State for loading
  const [loading, setLoading] = useState(false);

  // Ref for Vapi instance
  const vapiRef = useRef(null);

  /**
   * Initialize Vapi
   */
  const initializeVapi = useCallback(async () => {
    try {
      callDebugger.log('Initializing Vapi');
      setLoading(true);

      // Initialize enhanced Vapi services
      const initResult = await initializeEnhancedVapi();

      if (!initResult) {
        throw new Error('Failed to initialize enhanced Vapi services');
      }

      // Get API key
      const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                    (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY);

      if (!apiKey) {
        throw new Error('No Vapi API key available');
      }

      callDebugger.log('Creating Vapi instance with API key', {
        keyPrefix: apiKey.substring(0, 5) + "..."
      });

      // Create Vapi instance
      vapiRef.current = new Vapi(apiKey);

      // Set up event listeners
      setupEventListeners();

      callDebugger.log('Vapi initialized successfully');
      return true;
    } catch (err) {
      callDebugger.error('Error initializing Vapi', err);
      console.error('[useEnhancedVapi] Error initializing Vapi:', err);
      setError(err.message);

      // Call onError callback if provided
      if (onError) {
        onError(err);
      }

      return false;
    } finally {
      setLoading(false);
    }
  }, [onError]);

  /**
   * Set up event listeners for the Vapi instance
   */
  const setupEventListeners = useCallback(() => {
    if (!vapiRef.current) return;

    callDebugger.log('Setting up Vapi event listeners');

    // Set up event listeners
    vapiRef.current.on('call-start', () => {
      callDebugger.log('Call started event received');
      console.log('[useEnhancedVapi] Call started');
      setIsSessionActive(true);
      callDebugger.updateStatus('active');

      // Call onCallStart callback if provided
      if (onCallStart) {
        onCallStart();
      }
    });

    vapiRef.current.on('call-end', () => {
      callDebugger.log('Call ended event received');
      console.log('[useEnhancedVapi] Call ended');
      setIsSessionActive(false);
      callDebugger.updateStatus('ended');

      // Call onCallEnd callback if provided
      if (onCallEnd) {
        onCallEnd();
      }
    });

    vapiRef.current.on('error', (err) => {
      callDebugger.error('Vapi error event received', err);
      console.error('[useEnhancedVapi] Vapi error:', err);
      setError(err.message);

      // Call onError callback if provided
      if (onError) {
        onError(err);
      }
    });

    vapiRef.current.on('transcript', (transcript) => {
      callDebugger.log('Transcript received', transcript);
      console.log('[useEnhancedVapi] Transcript:', transcript);

      // Add transcript to conversation
      setConversation((prev) => [
        ...prev,
        {
          role: transcript.speaker === 'assistant' ? 'assistant' : 'user',
          text: transcript.text
        }
      ]);

      // Update current speaker
      setCurrentSpeaker(transcript.speaker);
    });

    vapiRef.current.on('audio-level', (level) => {
      // Only log significant volume changes to reduce noise
      if (level > 20) {
        callDebugger.log(`Audio level: ${level}`);
      }

      // Update volume level (normalize to 0-1)
      setVolumeLevel(Math.min(1, Math.max(0, level / 100)));

      // Update audio source if available
      if (typeof window !== 'undefined' && window.updateAudioSource) {
        const speaker = currentSpeaker || 'ambient';
        const frequency = speaker === 'assistant' ? 200 : 300;
        window.updateAudioSource(level / 100, frequency, speaker);
      }
    });

    // Track the call in the debugger
    if (vapiRef.current.call) {
      callDebugger.trackCall(vapiRef.current.call);
    }
  }, [onCallStart, onCallEnd, onError, currentSpeaker]);

  /**
   * Start a call
   */
  const startCall = useCallback(async () => {
    try {
      callDebugger.log('Starting call', { assistantId });

      if (!vapiRef.current) {
        callDebugger.log('Vapi not initialized, initializing now');
        await initializeVapi();
      }

      if (!assistantId) {
        const errorMsg = 'No assistant ID provided';
        callDebugger.error(errorMsg, new Error(errorMsg));
        throw new Error(errorMsg);
      }

      console.log('[useEnhancedVapi] Starting call with assistant:', assistantId);

      // Clear conversation
      setConversation([]);

      // Start call
      const callOptions = customInstructions ? { options: customInstructions } : undefined;
      callDebugger.log('Call options', callOptions);

      await vapiRef.current.start(assistantId, callOptions);
      callDebugger.updateStatus('connecting');

      // Track the call in the debugger if it wasn't tracked during setup
      if (vapiRef.current.call && !callDebugger.callId) {
        callDebugger.trackCall(vapiRef.current.call);
      }

      return true;
    } catch (err) {
      callDebugger.error('Error starting call', err);
      console.error('[useEnhancedVapi] Error starting call:', err);
      setError(err.message);

      // Call onError callback if provided
      if (onError) {
        onError(err);
      }

      return false;
    }
  }, [assistantId, customInstructions, initializeVapi, onError]);

  /**
   * Stop a call
   */
  const stopCall = useCallback(() => {
    try {
      callDebugger.log('Stopping call');

      if (vapiRef.current) {
        vapiRef.current.stop();
      }

      return true;
    } catch (err) {
      callDebugger.error('Error stopping call', err);
      console.error('[useEnhancedVapi] Error stopping call:', err);
      setError(err.message);

      // Call onError callback if provided
      if (onError) {
        onError(err);
      }

      return false;
    }
  }, [onError]);

  /**
   * Toggle call (start if not active, stop if active)
   */
  const toggleCall = useCallback(() => {
    callDebugger.log(`Toggling call, current status: ${isSessionActive ? 'active' : 'inactive'}`);

    if (isSessionActive) {
      return stopCall();
    } else {
      return startCall();
    }
  }, [isSessionActive, startCall, stopCall]);

  /**
   * Clear conversation history
   */
  const clearConversation = useCallback(() => {
    callDebugger.log('Clearing conversation');
    setConversation([]);
  }, []);

  // Initialize Vapi on component mount
  useEffect(() => {
    callDebugger.log('Component mounted, initializing Vapi');
    initializeVapi();

    // Cleanup function to end call and dispose Vapi instance
    return () => {
      callDebugger.log('Component unmounting, cleaning up Vapi');
      if (vapiRef.current) {
        vapiRef.current.stop();
        vapiRef.current = null;
      }
    };
  }, [initializeVapi]);

  // Return hook state and methods
  return {
    // State
    volumeLevel,
    isSessionActive,
    conversation,
    currentSpeaker,
    error,
    loading,

    // Methods
    startCall,
    stopCall,
    toggleCall,
    clearConversation,

    // Clear error
    clearError: () => {
      callDebugger.log('Clearing error');
      setError(null);
    }
  };
};

export default useEnhancedVapiWithDebug;
