import { useState, useCallback } from 'react';

/**
 * Hook to manage preview functionality for the demo page
 * 
 * @param {Object} options - Configuration options
 * @param {Function} options.onError - Error handler function
 * @returns {Object} Preview methods and state
 */
export function usePreview({ onError = () => {} } = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [previewData, setPreviewData] = useState(null);
  
  /**
   * Initialize or reset the preview with custom settings
   */
  const openPreview = useCallback(async (customizations) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would likely call an API
      // to initialize a preview instance with the given customizations
      const data = {
        id: `preview-${Date.now()}`,
        customizations,
        status: 'active',
        timestamp: new Date().toISOString(),
      };
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setPreviewData(data);
      return data;
    } catch (err) {
      setError(err.message || 'Failed to open preview');
      onError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [onError]);
  
  /**
   * Close the current preview instance
   */
  const closePreview = useCallback(async () => {
    if (!previewData) return;
    
    setIsLoading(true);
    
    try {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      setPreviewData(null);
    } catch (err) {
      setError(err.message || 'Failed to close preview');
      onError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [previewData, onError]);
  
  /**
   * Update the customizations for the current preview
   */
  const updateCustomizations = useCallback(async (customizations) => {
    if (!previewData) {
      return openPreview(customizations);
    }
    
    setIsLoading(true);
    
    try {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const updatedData = {
        ...previewData,
        customizations,
        lastUpdated: new Date().toISOString(),
      };
      
      setPreviewData(updatedData);
      return updatedData;
    } catch (err) {
      setError(err.message || 'Failed to update customizations');
      onError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [previewData, openPreview, onError]);
  
  /**
   * Update the preview with website scraping data
   */
  const updateScraping = useCallback(async (websiteUrl, scrapedData) => {
    if (!previewData) {
      throw new Error('Preview not initialized');
    }
    
    setIsLoading(true);
    
    try {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedData = {
        ...previewData,
        websiteUrl,
        scrapedData,
        lastScraped: new Date().toISOString(),
      };
      
      setPreviewData(updatedData);
      return updatedData;
    } catch (err) {
      setError(err.message || 'Failed to update website data');
      onError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [previewData, onError]);
  
  return {
    isLoading,
    error,
    previewData,
    openPreview,
    closePreview,
    updateCustomizations,
    updateScraping,
  };
} 