import { useState, useEffect, useCallback } from 'react';

/**
 * Hook to access the standalone attorney manager
 *
 * This hook provides access to the standalone attorney manager
 * from any React component.
 *
 * @returns {Object} The attorney state and methods
 */
export function useStandaloneAttorney() {
  const [attorney, setAttorney] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Function to initialize with manager
    const initializeWithManager = () => {
      const manager = window.standaloneAttorneyManager;

      if (!manager) {
        console.log('[useStandaloneAttorney] Manager not ready, will retry...');
        return false;
      }

      console.log('[useStandaloneAttorney] Manager found, initializing hook...');
      return manager;
    };

    // Try to get manager immediately
    let manager = initializeWithManager();

    // If manager not ready, set up retry mechanism
    if (!manager) {
      console.log('[useStandaloneAttorney] Setting up retry mechanism for manager...');

      // In development, also try to load from localStorage as fallback
      if (process.env.NODE_ENV === 'development') {
        try {
          const storedAttorney = localStorage.getItem('attorney');
          if (storedAttorney) {
            const parsedAttorney = JSON.parse(storedAttorney);
            console.log('[useStandaloneAttorney] Using stored attorney as fallback:', parsedAttorney);
            setAttorney(parsedAttorney);
          }
        } catch (error) {
          console.error('[useStandaloneAttorney] Error loading fallback attorney:', error);
        }
      }

      // Set up interval to check for manager
      const checkInterval = setInterval(() => {
        manager = initializeWithManager();
        if (manager) {
          clearInterval(checkInterval);
          // Initialize with the manager now that it's ready
          initializeHookWithManager(manager);
        }
      }, 100);

      // Clear interval after 5 seconds
      setTimeout(() => {
        clearInterval(checkInterval);
        if (!manager && process.env.NODE_ENV === 'development') {
          console.warn('[useStandaloneAttorney] Manager still not ready after 5 seconds');
        }
      }, 5000);

      return () => clearInterval(checkInterval);
    }

    // Initialize immediately if manager is ready
    return initializeHookWithManager(manager);
  }, []);

  // Separate function to initialize the hook with the manager
  const initializeHookWithManager = (manager) => {
    console.log('[useStandaloneAttorney] Initializing hook with manager...');

    // Get initial attorney state
    let initialAttorney = manager.attorney;

    // If manager doesn't have attorney, try to load from localStorage
    if (!initialAttorney) {
      console.log('[useStandaloneAttorney] Manager has no attorney, checking localStorage...');
      try {
        const storedAttorney = localStorage.getItem('attorney');
        if (storedAttorney) {
          let parsedAttorney = JSON.parse(storedAttorney);

          // Fix: If attorney is an array, take the first element
          if (Array.isArray(parsedAttorney)) {
            console.log('[useStandaloneAttorney] Attorney is an array, taking first element');
            parsedAttorney = parsedAttorney[0];
          }

          initialAttorney = parsedAttorney;
          console.log('[useStandaloneAttorney] Found attorney in localStorage:', initialAttorney);
          // Update the manager with the stored attorney
          manager.attorney = initialAttorney;
          manager.notifySubscribers();
        }
      } catch (error) {
        console.error('[useStandaloneAttorney] Error loading from localStorage:', error);
      }
    }

    // Set initial attorney state
    if (initialAttorney) {
      // Fix: If attorney is an array, take the first element
      let effectiveAttorney = initialAttorney;
      if (Array.isArray(initialAttorney)) {
        console.log('[useStandaloneAttorney] Initial attorney is an array, taking first element');
        effectiveAttorney = initialAttorney[0];
      }

      console.log('[useStandaloneAttorney] Setting initial attorney:', effectiveAttorney);
      setAttorney(effectiveAttorney);
    } else {
      console.log('[useStandaloneAttorney] No attorney found anywhere');
    }

    // Subscribe to changes
    const unsubscribe = manager.subscribe(newAttorney => {
      console.log('[useStandaloneAttorney] Attorney updated via subscription:', newAttorney);

      // Fix: If attorney is an array, take the first element
      let effectiveAttorney = newAttorney;
      if (Array.isArray(newAttorney)) {
        console.log('[useStandaloneAttorney] Subscription attorney is an array, taking first element');
        effectiveAttorney = newAttorney[0];
      }

      setAttorney(effectiveAttorney);
    });

    // Return cleanup function
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  };

  const updateAttorney = useCallback(async (updates) => {
    try {
      // Get the standalone attorney manager
      const manager = window.standaloneAttorneyManager;

      if (!manager) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[useStandaloneAttorney] Standalone attorney manager not found');
          // In development, just update the local state
          setAttorney(prev => ({ ...prev, ...updates }));
          return { ...attorney, ...updates };
        }
        throw new Error('Standalone attorney manager not found');
      }

      const updatedAttorney = await manager.updateAttorney(updates);
      return updatedAttorney;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[useStandaloneAttorney] Error updating attorney:', error);
      }
      setError(error);
      throw error;
    }
  }, [attorney]);

  const refreshAttorney = useCallback(() => {
    try {
      // Get the standalone attorney manager
      const manager = window.standaloneAttorneyManager;

      if (!manager) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[useStandaloneAttorney] Standalone attorney manager not found');
        }
        return null;
      }

      return manager.loadFromLocalStorage();
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[useStandaloneAttorney] Error refreshing attorney:', error);
      }
      setError(error);
      return null;
    }
  }, []);

  const loadAttorneyForUser = useCallback(async (userId) => {
    try {
      // Get the standalone attorney manager
      const manager = window.standaloneAttorneyManager;

      if (!manager) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[useStandaloneAttorney] Standalone attorney manager not found');
        }
        throw new Error('Standalone attorney manager not found');
      }

      const loadedAttorney = await manager.loadAttorneyForUser(userId);
      // The hook's state will be updated via the manager's subscriber notification
      return loadedAttorney; // Return the result for potential chaining

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[useStandaloneAttorney] Error loading attorney for user:', error);
      }
      setError(error);
      // Re-throw the error so the caller can handle it
      throw error;
    }
  }, []);

  return {
    attorney,
    error,
    updateAttorney,
    refreshAttorney,
    loadAttorneyForUser
  };
}
