/**
 * useSyncTools Hook
 *
 * This hook provides access to the synchronization tools for use in React components.
 * It handles loading states, errors, and provides methods to call the synchronization tools.
 */

import { useState, useCallback } from 'react';

/**
 * Hook for using the synchronization tools
 *
 * @returns {Object} The synchronization tools and their states
 */
export const useSyncTools = () => {
  // State for tracking loading states
  const [loading, setLoading] = useState({
    syncProfile: false,
    authState: false,
    validateConfig: false,
    checkConsistency: false
  });

  // State for tracking errors
  const [errors, setErrors] = useState({
    syncProfile: null,
    authState: null,
    validateConfig: null,
    checkConsistency: null
  });

  // State for tracking results
  const [results, setResults] = useState({
    syncProfile: null,
    authState: null,
    validateConfig: null,
    checkConsistency: null
  });

  /**
   * Synchronize attorney profile
   *
   * @param {string} attorneyId - The ID of the attorney to synchronize
   * @param {boolean} forceUpdate - Whether to force an update even if no discrepancies are found
   * @returns {Promise<Object>} The result of the synchronization
   */
  const syncAttorneyProfile = useCallback(async (attorneyId, forceUpdate = false) => {
    setLoading(prev => ({ ...prev, syncProfile: true }));
    setErrors(prev => ({ ...prev, syncProfile: null }));

    try {
      // Check if we're in development mode - be more aggressive about detecting dev mode
      const isDev = import.meta.env.DEV ||
                   import.meta.env.MODE === 'development' ||
                   (typeof window !== 'undefined' && (
                     window.location.hostname === 'localhost' ||
                     window.location.hostname === '127.0.0.1' ||
                     window.location.port === '5174'
                   ));

      if (isDev) {
        // In development mode, return a mock result
        console.log('Development mode: Using mock sync profile result');

        // Simulate a delay
        await new Promise(resolve => setTimeout(resolve, 500));

        const mockResult = {
          action: 'sync',
          success: true,
          message: 'Attorney profile synchronized successfully (mock result)',
          attorneyId,
          forceUpdate
        };

        setResults(prev => ({ ...prev, syncProfile: mockResult }));
        return mockResult;
      }

      // In production mode, call the API
      try {
        const response = await fetch('/api/sync-tools/sync-attorney-profile', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            attorneyId,
            forceUpdate
          })
        });

        // Handle empty responses gracefully
        let data;
        try {
          const text = await response.text();
          data = text ? JSON.parse(text) : { success: false, error: 'Empty response from server' };
        } catch (parseError) {
          console.error('Error parsing response:', parseError);
          throw new Error(`Failed to parse response: ${parseError.message}`);
        }

        if (!data.success) {
          // Properly handle error object or string
          const errorMessage = typeof data.error === 'object'
            ? JSON.stringify(data.error)
            : (data.error || 'Failed to synchronize attorney profile');

          console.error('Sync profile error details:', data.error);
          throw new Error(errorMessage);
        }

        setResults(prev => ({ ...prev, syncProfile: data.result }));
        return data.result;
      } catch (apiError) {
        console.error('API error, falling back to client-side implementation:', apiError);

        // Client-side fallback implementation
        console.log('Using client-side fallback for sync profile');

        // Create a fallback result
        const fallbackResult = {
          action: 'sync',
          success: true,
          message: 'Client-side fallback: Profile sync simulated successfully',
          attorneyId,
          forceUpdate,
          fallback: true
        };

        // Set results and return
        setResults(prev => ({ ...prev, syncProfile: fallbackResult }));
        return fallbackResult;
      }
    } catch (error) {
      console.error('Error syncing attorney profile:', error);

      // Return a fallback result
      const fallbackResult = {
        action: 'sync',
        success: false,
        message: `Error syncing attorney profile: ${error.message}`,
        error: error.message
      };

      setResults(prev => ({ ...prev, syncProfile: fallbackResult }));
      setErrors(prev => ({ ...prev, syncProfile: error.message }));

      return fallbackResult;
    } finally {
      setLoading(prev => ({ ...prev, syncProfile: false }));
    }
  }, []);

  /**
   * Manage authentication state
   *
   * @param {Object} authData - Authentication data
   * @param {string} action - Authentication action (login, logout, refresh)
   * @returns {Promise<Object>} The result of the authentication state management
   */
  const manageAuthState = useCallback(async (authData, action) => {
    setLoading(prev => ({ ...prev, authState: true }));
    setErrors(prev => ({ ...prev, authState: null }));

    try {
      // Check if we're in development mode
      const isDev = import.meta.env.DEV || import.meta.env.MODE === 'development';

      // Always use real authentication - no more mock mode
      const forceRealAuth = true;
      if (false) { // Disabled mock authentication
        // In development mode, return a mock result
        console.log('Development mode: Using mock auth state result');

        // Simulate a delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Create a mock attorney object if this is a login or refresh action
        let mockAttorney = null;
        if (action === 'login' || action === 'refresh') {
          mockAttorney = {
            id: 'dev-attorney-id',
            name: 'Development Attorney',
            email: authData.user?.email || '<EMAIL>',
            firm_name: 'Dev Law Firm',
            welcome_message: 'Welcome to Dev Law Firm',
            vapi_instructions: 'You are a legal assistant for Dev Law Firm',
            voice_provider: 'playht',
            voice_id: 'ranger',
            vapi_assistant_id: 'dev-assistant-id'
          };
        }

        const mockResult = {
          action,
          success: true,
          message: `Authentication state managed successfully for action: ${action} (mock result)`,
          attorney: mockAttorney
        };

        setResults(prev => ({ ...prev, authState: mockResult }));
        return mockResult;
      }

      try {
        // Try to call the API first
        const response = await fetch('/api/sync-tools/manage-auth-state', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            authData,
            action
          })
        });

        // Handle empty responses gracefully
        let data;
        try {
          const text = await response.text();
          data = text ? JSON.parse(text) : { success: false, error: 'Empty response from server' };
        } catch (parseError) {
          console.error('Error parsing response:', parseError);
          throw new Error(`Failed to parse response: ${parseError.message}`);
        }

        if (!data.success) {
          // Properly handle error object or string
          const errorMessage = typeof data.error === 'object'
            ? JSON.stringify(data.error)
            : (data.error || 'Failed to manage authentication state');

          console.error('Auth state error details:', data.error);
          throw new Error(errorMessage);
        }

        // Set results and return
        setResults(prev => ({ ...prev, authState: data.result }));
        return data.result;
      } catch (apiError) {
        console.error('API error, falling back to client-side implementation:', apiError);

        // Client-side fallback implementation
        console.log('Using client-side fallback for auth state management');

        // Create a fallback attorney object based on user data
        // Use the user's actual ID as the attorney ID to ensure it's a valid UUID
        const fallbackAttorney = authData?.user ? {
          id: authData.user.id, // Use the actual user ID which is a valid UUID
          name: authData.user.user_metadata?.name || authData.user.email.split('@')[0],
          email: authData.user.email,
          firm_name: `${authData.user.user_metadata?.name || authData.user.email.split('@')[0]}'s Law Firm`,
          user_id: authData.user.id,
          subdomain: authData.user.email ? authData.user.email.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '') : 'default',
          fallback: true,
          is_fallback: true // Add a flag to indicate this is a fallback attorney
        } : null;

        // Return a fallback result based on the action
        let fallbackResult;

        switch (action) {
          case 'login':
            fallbackResult = {
              action: 'login',
              success: true,
              attorney: fallbackAttorney,
              message: 'Client-side fallback: Login handled successfully'
            };
            break;

          case 'logout':
            fallbackResult = {
              action: 'logout',
              success: true,
              message: 'Client-side fallback: Logout handled successfully'
            };
            break;

          case 'refresh':
            fallbackResult = {
              action: 'refresh',
              success: true,
              attorney: fallbackAttorney,
              message: 'Client-side fallback: Session refreshed successfully'
            };
            break;

          default:
            fallbackResult = {
              action,
              success: false,
              message: `Client-side fallback: Unknown action ${action}`
            };
            break;
        }

        // Set results and return
        setResults(prev => ({ ...prev, authState: fallbackResult }));
        return fallbackResult;
      }
    } catch (error) {
      // Improve error logging with more details
      console.error('Error managing auth state:', error);

      // Extract a meaningful error message
      let errorMessage = 'Unknown error';

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object') {
        try {
          errorMessage = JSON.stringify(error);
        } catch (e) {
          errorMessage = 'Error object could not be stringified';
        }
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      console.log('Detailed error information:', {
        errorType: typeof error,
        errorMessage,
        errorObject: error
      });

      // Return a fallback result with improved error information
      const fallbackResult = {
        action,
        success: false,
        message: `Error managing auth state: ${errorMessage}`,
        error: errorMessage
      };

      setResults(prev => ({ ...prev, authState: fallbackResult }));
      setErrors(prev => ({ ...prev, authState: errorMessage }));

      return fallbackResult;
    } finally {
      setLoading(prev => ({ ...prev, authState: false }));
    }
  }, []);

  /**
   * Validate configuration
   *
   * @param {string} attorneyId - The ID of the attorney
   * @param {Object} configData - The configuration data to validate
   * @returns {Promise<Object>} The validation result
   */
  const validateConfiguration = useCallback(async (attorneyId, configData) => {
    setLoading(prev => ({ ...prev, validateConfig: true }));
    setErrors(prev => ({ ...prev, validateConfig: null }));

    try {
      // Check if we're in development mode
      const isDev = import.meta.env.DEV || import.meta.env.MODE === 'development';

      if (isDev) {
        // In development mode, return a mock result
        console.log('Development mode: Using mock validation result');

        // Simulate a delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Check for required fields
        const missingFields = {};
        let hasErrors = false;

        // Define required fields
        const requiredFields = {
          profile: ['name', 'email'],
          appearance: ['firm_name'],
          agent: ['welcome_message', 'vapi_instructions'],
          voice: ['voice_provider', 'voice_id']
        };

        // Check for missing fields
        Object.entries(requiredFields).forEach(([section, fields]) => {
          const missing = fields.filter(field => !configData[field]);
          if (missing.length > 0) {
            missingFields[section] = missing;
            hasErrors = true;
          }
        });

        // Create mock result
        const mockResult = {
          valid: !hasErrors,
          missingFields: Object.keys(missingFields).length > 0 ? missingFields : null,
          message: hasErrors
            ? 'Configuration validation failed (mock result)'
            : 'Configuration is valid (mock result)'
        };

        setResults(prev => ({ ...prev, validateConfig: mockResult }));
        return mockResult;
      }

      // In production mode, call the API
      try {
        const response = await fetch('/api/sync-tools/validate-configuration', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            attorneyId,
            configData
          })
        });

        // Handle empty responses gracefully
        let data;
        try {
          const text = await response.text();
          data = text ? JSON.parse(text) : { success: false, error: 'Empty response from server' };
        } catch (parseError) {
          console.error('Error parsing response:', parseError);
          throw new Error(`Failed to parse response: ${parseError.message}`);
        }

        if (!data.success) {
          // Properly handle error object or string
          const errorMessage = typeof data.error === 'object'
            ? JSON.stringify(data.error)
            : (data.error || 'Failed to validate configuration');

          console.error('Validate config error details:', data.error);
          throw new Error(errorMessage);
        }

        setResults(prev => ({ ...prev, validateConfig: data.result }));
        return data.result;
      } catch (apiError) {
        console.error('API error, falling back to client-side implementation:', apiError);

        // Client-side fallback implementation
        console.log('Using client-side fallback for configuration validation');

        // Check for required fields
        const missingFields = {};
        let hasErrors = false;

        // Define required fields
        const requiredFields = {
          profile: ['name', 'email'],
          appearance: ['firm_name'],
          agent: ['welcome_message', 'vapi_instructions'],
          voice: ['voice_provider', 'voice_id']
        };

        // Check for missing fields
        Object.entries(requiredFields).forEach(([section, fields]) => {
          const missing = fields.filter(field => !configData[field]);
          if (missing.length > 0) {
            missingFields[section] = missing;
            hasErrors = true;
          }
        });

        // Create a fallback result
        const fallbackResult = {
          valid: !hasErrors,
          missingFields: Object.keys(missingFields).length > 0 ? missingFields : null,
          message: hasErrors
            ? 'Client-side fallback: Configuration validation failed'
            : 'Client-side fallback: Configuration is valid',
          fallback: true
        };

        // Set results and return
        setResults(prev => ({ ...prev, validateConfig: fallbackResult }));
        return fallbackResult;
      }
    } catch (error) {
      console.error('Error validating configuration:', error);

      // Return a fallback result
      const fallbackResult = {
        valid: false,
        message: `Error validating configuration: ${error.message}`,
        error: error.message
      };

      setResults(prev => ({ ...prev, validateConfig: fallbackResult }));
      setErrors(prev => ({ ...prev, validateConfig: error.message }));

      return fallbackResult;
    } finally {
      setLoading(prev => ({ ...prev, validateConfig: false }));
    }
  }, []);

  /**
   * Check preview consistency
   *
   * @param {string} attorneyId - The ID of the attorney
   * @returns {Promise<Object>} The consistency check result
   */
  const checkPreviewConsistency = useCallback(async (attorneyId) => {
    setLoading(prev => ({ ...prev, checkConsistency: true }));
    setErrors(prev => ({ ...prev, checkConsistency: null }));

    try {
      // Check if we're in development mode
      const isDev = import.meta.env.DEV || import.meta.env.MODE === 'development';

      if (isDev) {
        // In development mode, return a mock result
        console.log('Development mode: Using mock consistency check result');

        // Simulate a delay
        await new Promise(resolve => setTimeout(resolve, 500));

        const mockResult = {
          consistent: true,
          message: 'Preview is consistent with deployment (mock result)'
        };

        setResults(prev => ({ ...prev, checkConsistency: mockResult }));
        return mockResult;
      }

      // In production mode, call the API
      try {
        const response = await fetch('/api/sync-tools/check-preview-consistency', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            attorneyId
          })
        });

        // Handle empty responses gracefully
        let data;
        try {
          const text = await response.text();
          data = text ? JSON.parse(text) : { success: false, error: 'Empty response from server' };
        } catch (parseError) {
          console.error('Error parsing response:', parseError);
          throw new Error(`Failed to parse response: ${parseError.message}`);
        }

        if (!data.success) {
          // Properly handle error object or string
          const errorMessage = typeof data.error === 'object'
            ? JSON.stringify(data.error)
            : (data.error || 'Failed to check preview consistency');

          console.error('Check consistency error details:', data.error);
          throw new Error(errorMessage);
        }

        setResults(prev => ({ ...prev, checkConsistency: data.result }));
        return data.result;
      } catch (apiError) {
        console.error('API error, falling back to client-side implementation:', apiError);

        // Client-side fallback implementation
        console.log('Using client-side fallback for consistency check');

        // Create a fallback result
        const fallbackResult = {
          consistent: true,
          message: 'Client-side fallback: Preview consistency check simulated successfully',
          fallback: true
        };

        // Set results and return
        setResults(prev => ({ ...prev, checkConsistency: fallbackResult }));
        return fallbackResult;
      }
    } catch (error) {
      console.error('Error checking preview consistency:', error);

      // Return a fallback result
      const fallbackResult = {
        consistent: false,
        message: `Error checking consistency: ${error.message}`,
        error: error.message
      };

      setResults(prev => ({ ...prev, checkConsistency: fallbackResult }));
      setErrors(prev => ({ ...prev, checkConsistency: error.message }));

      return fallbackResult;
    } finally {
      setLoading(prev => ({ ...prev, checkConsistency: false }));
    }
  }, []);

  return {
    // Methods
    syncAttorneyProfile,
    manageAuthState,
    validateConfiguration,
    checkPreviewConsistency,

    // States
    loading,
    errors,
    results,

    // Helper methods
    clearResults: () => setResults({
      syncProfile: null,
      authState: null,
      validateConfig: null,
      checkConsistency: null
    }),
    clearErrors: () => setErrors({
      syncProfile: null,
      authState: null,
      validateConfig: null,
      checkConsistency: null
    })
  };
};
