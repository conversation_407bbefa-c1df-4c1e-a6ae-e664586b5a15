import { useState, useEffect, useCallback, useRef } from 'react';
import Vapi from '@vapi-ai/web';
import { getVapiApiKey } from '../config/vapiConfig.js';
import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';

/**
 * Simplified Vapi Call Hook - Following Official Vapi Web SDK Pattern
 * 
 * This hook uses the official Vapi Web SDK pattern as documented at:
 * https://docs.vapi.ai/quickstart/web
 * 
 * @param {Object} options - Options for the call
 * @param {string} options.assistantId - The assistant ID to use
 * @param {Function} options.onEndCall - Function to call when the call ends
 * @param {Object} options.assistantOverrides - Overrides for the assistant
 * @returns {Object} Call state and functions
 */
const useVapiCallSimplified = ({
  assistantId = DEFAULT_ASSISTANT_ID,
  onEndCall,
  assistantOverrides = {}
}) => {
  // State management
  const [status, setStatus] = useState('idle'); // idle, connecting, connected, ended, error
  const [isCallActive, setIsCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [volumeLevel, setVolumeLevel] = useState(0);
  const [messages, setMessages] = useState([]);
  const [error, setError] = useState(null);
  
  // Refs
  const vapiRef = useRef(null);
  const isInitializedRef = useRef(false);

  // Initialize Vapi instance
  const initializeVapi = useCallback(async () => {
    if (isInitializedRef.current || vapiRef.current) {
      return vapiRef.current;
    }

    try {
      console.log('[useVapiCallSimplified] Initializing Vapi...');
      
      const apiKey = getVapiApiKey();
      if (!apiKey) {
        throw new Error('Vapi API key not found');
      }

      // Create Vapi instance using official pattern
      const vapi = new Vapi(apiKey);
      
      // Set up event listeners using official pattern
      vapi.on('call-start', () => {
        console.log('[useVapiCallSimplified] Call started');
        setStatus('connected');
        setIsCallActive(true);
        setError(null);
      });

      vapi.on('call-end', () => {
        console.log('[useVapiCallSimplified] Call ended');
        setStatus('ended');
        setIsCallActive(false);
        
        // Call the onEndCall callback if provided
        if (typeof onEndCall === 'function') {
          onEndCall({
            messages,
            duration: Date.now() // Simple duration tracking
          });
        }
      });

      vapi.on('speech-start', () => {
        console.log('[useVapiCallSimplified] Assistant started speaking');
      });

      vapi.on('speech-end', () => {
        console.log('[useVapiCallSimplified] Assistant stopped speaking');
      });

      vapi.on('volume-level', (volume) => {
        setVolumeLevel(volume);
      });

      vapi.on('message', (message) => {
        console.log('[useVapiCallSimplified] Received message:', message);
        
        // Handle different message types according to official docs
        if (message.type === 'transcript') {
          // Add user transcript to messages
          if (message.transcriptType === 'final') {
            setMessages(prev => [...prev, {
              id: `transcript-${Date.now()}`,
              role: 'user',
              content: message.transcript,
              timestamp: new Date().toISOString(),
              type: 'transcript'
            }]);
          }
        } else if (message.type === 'model-output') {
          // Add assistant response to messages
          if (message.output && message.output.content) {
            setMessages(prev => [...prev, {
              id: `assistant-${Date.now()}`,
              role: 'assistant', 
              content: message.output.content,
              timestamp: new Date().toISOString(),
              type: 'response'
            }]);
          }
        }
        // Handle other message types as needed
      });

      vapi.on('error', (error) => {
        console.error('[useVapiCallSimplified] Vapi error:', error);
        setError(error.message || 'An error occurred');
        setStatus('error');
        setIsCallActive(false);
      });

      vapiRef.current = vapi;
      isInitializedRef.current = true;
      
      console.log('[useVapiCallSimplified] Vapi initialized successfully');
      return vapi;
      
    } catch (error) {
      console.error('[useVapiCallSimplified] Failed to initialize Vapi:', error);
      setError(error.message);
      setStatus('error');
      throw error;
    }
  }, [onEndCall, messages]);

  // Start call function
  const startCall = useCallback(async () => {
    try {
      console.log('[useVapiCallSimplified] Starting call...');
      setStatus('connecting');
      setError(null);
      setMessages([]);

      // Initialize Vapi if not already done
      const vapi = await initializeVapi();
      
      if (!vapi) {
        throw new Error('Failed to initialize Vapi');
      }

      // Prepare assistant overrides according to official pattern
      const finalAssistantOverrides = {
        recordingEnabled: true,
        ...assistantOverrides
      };

      console.log('[useVapiCallSimplified] Starting call with assistant:', assistantId);
      console.log('[useVapiCallSimplified] Assistant overrides:', finalAssistantOverrides);

      // Start call using official pattern
      await vapi.start(assistantId, finalAssistantOverrides);
      
    } catch (error) {
      console.error('[useVapiCallSimplified] Failed to start call:', error);
      setError(error.message);
      setStatus('error');
      setIsCallActive(false);
    }
  }, [assistantId, assistantOverrides, initializeVapi]);

  // Stop call function
  const stopCall = useCallback(async () => {
    try {
      console.log('[useVapiCallSimplified] Stopping call...');
      
      if (vapiRef.current && isCallActive) {
        await vapiRef.current.stop();
      }
      
      setStatus('ended');
      setIsCallActive(false);
      
    } catch (error) {
      console.error('[useVapiCallSimplified] Failed to stop call:', error);
      setError(error.message);
    }
  }, [isCallActive]);

  // Send message function
  const sendMessage = useCallback(async (message) => {
    try {
      if (!vapiRef.current || !isCallActive) {
        throw new Error('Call not active');
      }

      console.log('[useVapiCallSimplified] Sending message:', message);
      
      // Send message using official pattern
      await vapiRef.current.send({
        type: 'add-message',
        message: {
          role: 'system',
          content: message
        }
      });
      
    } catch (error) {
      console.error('[useVapiCallSimplified] Failed to send message:', error);
      setError(error.message);
    }
  }, [isCallActive]);

  // Mute/unmute functions
  const toggleMute = useCallback(() => {
    if (vapiRef.current) {
      const newMutedState = !isMuted;
      vapiRef.current.setMuted(newMutedState);
      setIsMuted(newMutedState);
      console.log('[useVapiCallSimplified] Mute toggled:', newMutedState);
    }
  }, [isMuted]);

  const setMute = useCallback((muted) => {
    if (vapiRef.current) {
      vapiRef.current.setMuted(muted);
      setIsMuted(muted);
      console.log('[useVapiCallSimplified] Mute set to:', muted);
    }
  }, []);

  // Say function for programmatic speech
  const say = useCallback(async (message, endCallAfter = false) => {
    try {
      if (!vapiRef.current || !isCallActive) {
        throw new Error('Call not active');
      }

      console.log('[useVapiCallSimplified] Making assistant say:', message);
      await vapiRef.current.say(message, endCallAfter);
      
    } catch (error) {
      console.error('[useVapiCallSimplified] Failed to make assistant speak:', error);
      setError(error.message);
    }
  }, [isCallActive]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (vapiRef.current && isCallActive) {
        console.log('[useVapiCallSimplified] Cleaning up call on unmount');
        vapiRef.current.stop().catch(console.error);
      }
    };
  }, [isCallActive]);

  // Return hook interface
  return {
    // State
    status,
    isCallActive,
    isMuted,
    volumeLevel,
    messages,
    error,
    
    // Actions
    startCall,
    stopCall,
    sendMessage,
    toggleMute,
    setMute,
    say,
    
    // Utilities
    isConnected: status === 'connected',
    isConnecting: status === 'connecting',
    hasError: !!error
  };
};

export default useVapiCallSimplified;
