import { useState, useEffect, useCallback, useRef } from 'react';
import { CALL_STATUS } from '../constants/vapiConstants';
import { getSubdomainConfig } from '../utils/subdomainUtils';
import { getCallDebugger } from '../utils/callDebugger';
import { vapiService } from '../services/vapiService.jsx';

/**
 * Custom hook to manage Vapi call state and functionality with enhanced debugging
 *
 * @param {Object} options - Options for the call
 * @param {string} options.subdomain - The attorney subdomain
 * @param {Function} options.onEndCall - Function to call when the call ends
 * @param {Object} options.customInstructions - Custom instructions for the Vapi call
 * @param {Object} options.assistantOverrides - Overrides for the Vapi assistant configuration
 * @param {string} options.assistantId - Explicit assistant ID to use (overrides any ID in customInstructions)
 * @returns {Object} Call state and functions
 */
const useVapiCallWithDebug = ({
  subdomain = 'default',
  onEndCall,
  customInstructions = null,
  assistantOverrides = null,
  assistantId = null
}) => {
  // Initialize debugger
  const callDebugger = getCallDebugger('useVapiCall');
  callDebugger.log('Initializing useVapiCall hook', { subdomain, assistantId });

  // State for call status
  const [status, setStatus] = useState(CALL_STATUS.IDLE);
  // State for error message
  const [errorMessage, setErrorMessage] = useState(null);
  // State for Vapi instance
  const [vapi, setVapi] = useState(null);
  // State for volume level
  const [volumeLevel, setVolumeLevel] = useState(0);
  // State for assistant speaking state
  const [assistantIsSpeaking, setAssistantIsSpeaking] = useState(false);
  // State for dossier data
  const [dossierData, setDossierData] = useState({});
  // State for message history
  const [messageHistory, setMessageHistory] = useState([]);
  // State for subdomain configuration
  const [subdomainConfig, setSubdomainConfig] = useState(null);
  // State for force mock mode
  const [forceMockMode, setForceMockMode] = useState(false);
  // Ref to track if onEndCall has been called
  const onEndCallCalled = useRef(false);

  // Prepare call parameters
  const callParams = {
    assistantId: assistantId || (customInstructions?.assistantId),
    assistantOverrides: assistantOverrides || null
  };

  // Load subdomain configuration
  useEffect(() => {
    const loadSubdomainConfig = async () => {
      try {
        callDebugger.log(`Loading subdomain config for: ${subdomain}`);
        const config = await getSubdomainConfig(subdomain);
        if (config) {
          callDebugger.log('Subdomain config loaded successfully', {
            firmName: config.firmName,
            practiceArea: config.practiceArea
          });
          setSubdomainConfig(config);
        } else {
          callDebugger.log('No subdomain config found, using default');
        }
      } catch (error) {
        callDebugger.error('Error loading subdomain config', error);
        console.error("Error loading subdomain config:", error);
      }
    };

    loadSubdomainConfig();
  }, [subdomain]);

  // Set up callbacks for Vapi events
  const callbacks = {
    onMessage: (message) => {
      callDebugger.log('Received message from Vapi', message);
      // Process message and update state
      setMessageHistory(prev => [...prev, message]);
    },
    onDossierUpdate: (data) => {
      callDebugger.log('Received dossier update from Vapi', data);
      // Update dossier data
      setDossierData(prev => ({ ...prev, ...data }));
    },
    onVolumeChange: (level) => {
      // Only log significant volume changes to reduce noise
      if (level > 0.1) {
        callDebugger.log(`Volume level changed: ${level}`);
      }
      setVolumeLevel(level);
    },
    onAssistantSpeakingChange: (speaking) => {
      callDebugger.log(`Assistant speaking state changed: ${speaking}`);
      setAssistantIsSpeaking(speaking);
    },
    onStatusChange: (newStatus) => {
      callDebugger.log(`Call status changed: ${newStatus}`);
      setStatus(newStatus);
      callDebugger.updateStatus(newStatus);
    },
    onCallStart: () => {
      callDebugger.log('Call started');
      setStatus(CALL_STATUS.CONNECTED);
      callDebugger.updateStatus('connected');
    },
    onCallEnd: () => {
      callDebugger.log('Call ended');
      setStatus(CALL_STATUS.IDLE);
      callDebugger.updateStatus('ended');

      if (typeof onEndCall === 'function' && !onEndCallCalled.current) {
        // Check if the assistant is still speaking when the call ends
        if (assistantIsSpeaking) {
          callDebugger.log('Call ended while assistant is still speaking');
          onEndCall({
            ...dossierData,
            forcedWhileSpeaking: true
          });
        } else {
          onEndCall(dossierData);
        }
        onEndCallCalled.current = true;
      }
    },
    onError: (error) => {
      callDebugger.error('Vapi error', error);
      console.error("Vapi error:", error);
      setErrorMessage(`Voice assistant error: ${error.message || 'Unknown error'}`);
    }
  };

  // Function to start the call
  const startCall = useCallback(() => {
    callDebugger.log('Starting call', { assistantId: callParams.assistantId });
    setStatus(CALL_STATUS.CONNECTING);
    setErrorMessage(null);
    onEndCallCalled.current = false;

    // Ensure we have valid call parameters
    if (!callParams.assistantId) {
      const errorMsg = "Missing assistantId in call parameters";
      callDebugger.error(errorMsg, new Error(errorMsg));
      setStatus(CALL_STATUS.ERROR);
      setErrorMessage(errorMsg);
      return;
    }

    // First check if Vapi is initialized
    if (!vapi) {
      callDebugger.log('Vapi not initialized, initializing now');

      try {
        const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
        if (!apiKey) {
          const errorMsg = "Missing Vapi API key";
          callDebugger.error(errorMsg, new Error(errorMsg));
          setStatus(CALL_STATUS.ERROR);
          setErrorMessage(errorMsg);
          return;
        }

        callDebugger.log('Initializing Vapi with API key', {
          keyPrefix: apiKey.substring(0, 5) + "..."
        });

        // Direct Vapi initialization to bypass complex vapiService.initialize
        callDebugger.log('Attempting direct Vapi initialization');

        try {
          // Import Vapi directly and create instance
          import('@vapi-ai/web').then(({ default: Vapi }) => {
            callDebugger.log('Vapi module imported successfully');

            // Create Vapi instance directly
            const vapiInstance = new Vapi(apiKey);

            if (!vapiInstance) {
              throw new Error("Failed to create Vapi instance");
            }

            callDebugger.log('Vapi instance created successfully');

            // Set up event listeners on the new instance
            vapiService.setupEventListeners(vapiInstance, callbacks);

            // Store the instance
            setVapi(vapiInstance);

            // Track the call
            callDebugger.trackCall(vapiInstance);

            // Wait a moment for the state to update before proceeding
            setTimeout(() => {
              callDebugger.log('Starting call after initialization');
              startCallWithInstance(vapiInstance);
            }, 1000);
          }).catch(error => {
            callDebugger.error('Error importing or creating Vapi instance', error);
            setStatus(CALL_STATUS.ERROR);
            setErrorMessage("Failed to initialize Vapi: " + error.message);
          });
        } catch (error) {
          callDebugger.error('Error in direct Vapi initialization', error);
          setStatus(CALL_STATUS.ERROR);
          setErrorMessage("Failed to initialize Vapi: " + error.message);
        }
      } catch (error) {
        callDebugger.error('Error in Vapi initialization', error);
        setStatus(CALL_STATUS.ERROR);
        setErrorMessage("Error initializing Vapi: " + error.message);
      }
    } else {
      // Vapi is already initialized, start the call
      callDebugger.log('Vapi already initialized, starting call directly');
      startCallWithInstance(vapi);
    }
  }, [vapi, callParams.assistantId, subdomain]);

  // Auto-start the call when ready
  useEffect(() => {
    // Only auto-start if we have an assistant ID and haven't started yet
    if (callParams.assistantId && status === CALL_STATUS.IDLE) {
      callDebugger.log('Auto-starting call with assistant ID:', callParams.assistantId);
      startCall();
    }
  }, [callParams.assistantId, status, startCall]);

  // Helper function to start call with a Vapi instance
  const startCallWithInstance = async (vapiInstance) => {
    try {
      callDebugger.log('Starting call with Vapi instance', {
        assistantId: callParams.assistantId,
        hasOverrides: !!callParams.assistantOverrides
      });

      // Start the call with the assistant ID and any overrides
      await vapiService.startCall(vapiInstance, {
        assistantId: callParams.assistantId,
        assistantOverrides: callParams.assistantOverrides
      });

      callDebugger.log('Call started successfully');
      setStatus(CALL_STATUS.CONNECTED);
    } catch (error) {
      callDebugger.error('Error starting call', error);
      console.error("Error starting call:", error);
      setStatus(CALL_STATUS.ERROR);
      setErrorMessage("Error starting call: " + error.message);
    }
  };

  // Function to stop the call
  const stopCall = useCallback(() => {
    callDebugger.log('Stopping call');

    try {
      if (vapi) {
        callDebugger.log('Calling stop on Vapi instance');
        vapiService.stopCall(vapi);
      } else {
        callDebugger.log('No Vapi instance to stop');
      }

      // Reset state
      setStatus(CALL_STATUS.IDLE);
      setVolumeLevel(0);
      setAssistantIsSpeaking(false);

      // Call onEndCall if it hasn't been called yet
      if (typeof onEndCall === 'function' && !onEndCallCalled.current) {
        callDebugger.log('Calling onEndCall from stopCall');
        onEndCall(dossierData);
        onEndCallCalled.current = true;
      }
    } catch (error) {
      callDebugger.error('Error stopping call', error);
      console.error("Error stopping call:", error);
    }
  }, [vapi, dossierData, onEndCall]);

  // Return the state and functions
  return {
    vapi,
    status,
    dossierData,
    messageHistory,
    volumeLevel,
    assistantIsSpeaking,
    errorMessage,
    subdomainConfig,
    forceMockMode,
    startCall,
    stopCall,
    callId: vapi?.callId || null // Expose the callId for emissions monitoring
  };
};

export default useVapiCallWithDebug;
