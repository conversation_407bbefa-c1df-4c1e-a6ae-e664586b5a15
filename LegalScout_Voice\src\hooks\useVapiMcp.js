import { useState, useEffect, useCallback } from 'react';
import { vapiMcpService } from '../services/vapiMcpService';

/**
 * Hook for using the Vapi MCP Server in React components
 *
 * This hook provides:
 * - Connection management
 * - Assistant and phone number listing
 * - Call creation and scheduling
 * - Error handling
 *
 * @param {Object} options - Hook options
 * @param {string} options.apiKey - Vapi API key (defaults to environment variable)
 * @param {boolean} options.autoConnect - Whether to connect automatically on mount
 * @returns {Object} Hook state and methods
 */
const useVapiMcp = ({ apiKey, autoConnect = true } = {}) => {
  // State
  const [connected, setConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [assistants, setAssistants] = useState([]);
  const [phoneNumbers, setPhoneNumbers] = useState([]);
  const [calls, setCalls] = useState([]);

  /**
   * Connect to the Vapi MCP Server
   * @param {string} key - Vapi API key (optional, defaults to options.apiKey or environment variable)
   */
  const connect = useCallback(async (key) => {
    try {
      setLoading(true);
      setError(null);

      // Use provided key, options.apiKey, or environment variable (prefer SECRET key for server operations)
      const apiKeyToUse = key || apiKey || import.meta.env.VITE_VAPI_SECRET_KEY || import.meta.env.VITE_VAPI_PUBLIC_KEY;

      if (!apiKeyToUse) {
        throw new Error('Vapi API key not found');
      }

      // Connect to MCP server
      const success = await vapiMcpService.connect(apiKeyToUse);
      setConnected(success);

      return success;
    } catch (err) {
      console.error('Error connecting to Vapi MCP Server:', err);
      setError(err.message);
      return false;
    } finally {
      setLoading(false);
    }
  }, [apiKey]);

  /**
   * Disconnect from the Vapi MCP Server
   */
  const disconnect = useCallback(async () => {
    try {
      await vapiMcpService.disconnect();
      setConnected(false);
    } catch (err) {
      console.error('Error disconnecting from Vapi MCP Server:', err);
      setError(err.message);
    }
  }, []);

  /**
   * Load assistants from the Vapi MCP Server
   */
  const loadAssistants = useCallback(async () => {
    if (!connected) {
      setError('Not connected to Vapi MCP Server');
      return [];
    }

    try {
      setLoading(true);
      setError(null);

      const assistantsList = await vapiMcpService.listAssistants();
      setAssistants(assistantsList || []);

      return assistantsList || [];
    } catch (err) {
      console.error('Error loading assistants:', err);
      setError(err.message);
      return [];
    } finally {
      setLoading(false);
    }
  }, [connected]);

  /**
   * Load phone numbers from the Vapi MCP Server
   */
  const loadPhoneNumbers = useCallback(async () => {
    if (!connected) {
      setError('Not connected to Vapi MCP Server');
      return [];
    }

    try {
      setLoading(true);
      setError(null);

      const phoneNumbersList = await vapiMcpService.listPhoneNumbers();
      setPhoneNumbers(phoneNumbersList || []);

      return phoneNumbersList || [];
    } catch (err) {
      console.error('Error loading phone numbers:', err);
      setError(err.message);
      return [];
    } finally {
      setLoading(false);
    }
  }, [connected]);

  /**
   * Load calls from the Vapi MCP Server
   */
  const loadCalls = useCallback(async () => {
    if (!connected) {
      setError('Not connected to Vapi MCP Server');
      return [];
    }

    try {
      setLoading(true);
      setError(null);

      const callsList = await vapiMcpService.listCalls();
      setCalls(callsList || []);

      return callsList || [];
    } catch (err) {
      console.error('Error loading calls:', err);
      setError(err.message);
      return [];
    } finally {
      setLoading(false);
    }
  }, [connected]);

  /**
   * Create a call using the Vapi MCP Server
   * @param {string} assistantId - Assistant ID
   * @param {string} phoneNumber - Customer phone number
   * @param {Object} options - Additional call options
   * @returns {Promise<Object>} Created call
   */
  const createCall = useCallback(async (assistantId, phoneNumber, options = {}) => {
    if (!connected) {
      setError('Not connected to Vapi MCP Server');
      return null;
    }

    if (!assistantId) {
      setError('Assistant ID is required');
      return null;
    }

    if (!phoneNumber) {
      setError('Phone number is required');
      return null;
    }

    try {
      setLoading(true);
      setError(null);

      const call = await vapiMcpService.createCall(assistantId, phoneNumber, options);

      // Refresh calls list
      await loadCalls();

      return call;
    } catch (err) {
      console.error('Error creating call:', err);
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [connected, loadCalls]);

  /**
   * Get a specific assistant by ID
   * @param {string} assistantId - Assistant ID
   * @returns {Promise<Object>} Assistant details
   */
  const getAssistant = useCallback(async (assistantId) => {
    if (!connected) {
      setError('Not connected to Vapi MCP Server');
      return null;
    }

    if (!assistantId) {
      setError('Assistant ID is required');
      return null;
    }

    try {
      setLoading(true);
      setError(null);

      return await vapiMcpService.getAssistant(assistantId);
    } catch (err) {
      console.error(`Error getting assistant ${assistantId}:`, err);
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [connected]);

  /**
   * Get a specific call by ID
   * @param {string} callId - Call ID
   * @returns {Promise<Object>} Call details
   */
  const getCall = useCallback(async (callId) => {
    if (!connected) {
      setError('Not connected to Vapi MCP Server');
      return null;
    }

    if (!callId) {
      setError('Call ID is required');
      return null;
    }

    try {
      setLoading(true);
      setError(null);

      return await vapiMcpService.getCall(callId);
    } catch (err) {
      console.error(`Error getting call ${callId}:`, err);
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [connected]);

  /**
   * Create a new assistant
   * @param {Object} assistantConfig - Assistant configuration
   * @returns {Promise<Object>} Created assistant
   */
  const createAssistant = useCallback(async (assistantConfig) => {
    if (!connected) {
      setError('Not connected to Vapi MCP Server');
      return null;
    }

    if (!assistantConfig) {
      setError('Assistant configuration is required');
      return null;
    }

    try {
      setLoading(true);
      setError(null);

      const assistant = await vapiMcpService.createAssistant(assistantConfig);

      // Refresh assistants list
      await loadAssistants();

      return assistant;
    } catch (err) {
      console.error('Error creating assistant:', err);
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [connected, loadAssistants]);

  // Connect on mount if autoConnect is true
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Disconnect on unmount
    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // Return hook state and methods
  return {
    // State
    connected,
    loading,
    error,
    assistants,
    phoneNumbers,
    calls,

    // Methods
    connect,
    disconnect,
    loadAssistants,
    loadPhoneNumbers,
    loadCalls,
    createCall,
    getAssistant,
    getCall,
    createAssistant,

    // Reset error
    clearError: () => setError(null)
  };
};

export default useVapiMcp;
