import { createClient } from '@supabase/supabase-js';

// Get Supabase credentials from environment variables
// Try different environment variable formats (Vite, React, etc.)
let supabaseUrl = import.meta.env.VITE_SUPABASE_URL ||
                import.meta.env.REACT_APP_SUPABASE_URL ||
                (typeof window !== 'undefined' && window.VITE_SUPABASE_URL) ||
                'https://utopqxsvudgrtiwenlzl.supabase.co';

let supabaseKey = import.meta.env.VITE_SUPABASE_KEY ||
                import.meta.env.VITE_SUPABASE_ANON_KEY ||
                import.meta.env.REACT_APP_SUPABASE_KEY ||
                import.meta.env.REACT_APP_SUPABASE_ANON_KEY ||
                (typeof window !== 'undefined' && window.VITE_SUPABASE_KEY) ||
                'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';

// Check for placeholder values and replace them
if (supabaseUrl === 'your-supabase-url') {
  console.warn('Detected placeholder Supabase URL, will use fallback value');
  supabaseUrl = null; // Will trigger fallback logic below
}

if (supabaseKey === 'your-anon-key') {
  console.warn('Detected placeholder Supabase key, will use fallback value');
  supabaseKey = null; // Will trigger fallback logic below
}

// Log all environment variables for debugging
console.log('Environment variables:', {
  VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
  REACT_APP_SUPABASE_URL: import.meta.env.REACT_APP_SUPABASE_URL,
  VITE_SUPABASE_KEY: import.meta.env.VITE_SUPABASE_KEY ? 'exists' : 'missing',
  VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY ? 'exists' : 'missing',
  REACT_APP_SUPABASE_KEY: import.meta.env.REACT_APP_SUPABASE_KEY ? 'exists' : 'missing',
  REACT_APP_SUPABASE_ANON_KEY: import.meta.env.REACT_APP_SUPABASE_ANON_KEY ? 'exists' : 'missing',
});

// Log Supabase configuration status (without revealing the full key)
if (supabaseUrl) {
  console.log('Supabase URL configured:', supabaseUrl);
} else {
  console.warn('Supabase URL not found in environment variables');
}

if (supabaseKey) {
  console.log('Supabase Key configured:', `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}`);
} else {
  console.warn('Supabase Key not found in environment variables');
}

// Fallback values for development
// These values should work for API calls as they're the actual project credentials
const FALLBACK_SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const FALLBACK_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';

// Helper to check if Supabase is properly configured
export const isSupabaseConfigured = () => {
  // For development, always return true if we're in development mode
  if (import.meta.env.DEV || import.meta.env.MODE === 'development') {
    console.log('Development mode detected, using fallback Supabase configuration');
    return true;
  }
  return Boolean(supabaseUrl && supabaseKey);
};

// Create a Supabase client if credentials are available, otherwise use a stub
let supabaseClient;

if (isSupabaseConfigured()) {
  try {
    // Use actual credentials if available, otherwise use fallback values
    let url = supabaseUrl || FALLBACK_SUPABASE_URL;
    const key = supabaseKey || FALLBACK_SUPABASE_KEY;

    // Validate URL format
    if (url === 'your-supabase-url' || !url.startsWith('http')) {
      console.warn('Invalid Supabase URL detected, using fallback URL');
      url = FALLBACK_SUPABASE_URL;
    }

    // Log the URL being used (without revealing full key)
    console.log('Using Supabase URL:', url);

    /**
     * Create a clean fetch function that bypasses interceptors
     * This fixes consultation syncing issues caused by fetch interceptors
     */
    function createCleanFetch() {
      try {
        // Method 1: Try to get original fetch from iframe
        if (typeof document !== 'undefined') {
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          document.body.appendChild(iframe);

          const cleanFetch = iframe.contentWindow.fetch.bind(window);
          document.body.removeChild(iframe);

          console.log('[Supabase] Created clean fetch from iframe to bypass interceptors');
          return cleanFetch;
        }
      } catch (error) {
        console.warn('[Supabase] Could not create clean fetch from iframe:', error.message);
      }

      // Method 2: Fallback to standard fetch
      console.log('[Supabase] Using standard fetch (no clean fetch available)');
      return fetch;
    }

    // Create clean fetch to avoid interceptor issues
    const cleanFetch = createCleanFetch();

    // Create the Supabase client with robust configuration and clean fetch
    supabaseClient = createClient(url, key, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      },
      global: {
        headers: {
          'apikey': key,
          'Authorization': `Bearer ${key}`
        },
        fetch: cleanFetch // Use clean fetch to bypass interceptors
      }
    });
    console.log('Supabase client initialized successfully with proper headers');

    // Test the connection to verify the API key works
    console.log('Testing Supabase connection...');
    supabaseClient
      .from('attorneys')
      .select('*')
      .eq('subdomain', 'default')
      .then(({ data, error }) => {
        if (error) {
          if (error.message === 'Invalid API key') {
            console.error('💀 Invalid Supabase API key detected. NO MOCKS ALLOWED!');
            throw new Error('Invalid Supabase API key - please check your configuration');
          } else {
            console.warn('Supabase query error:', error.message);
          }
        } else {
          console.log('Supabase connection test successful!');
        }
      })
      .catch(error => {
        console.error('💀 Unexpected error testing Supabase:', error);
        // NO MORE MOCKS! Let the error propagate
        throw error;
      });
  } catch (error) {
    console.error('💀 Error initializing Supabase client:', error);
    // NO MORE MOCKS! Let the error propagate
    throw error;
  }
} else {
  console.error('💀 Supabase credentials not found in environment variables. NO MOCKS ALLOWED!');
  throw new Error('Supabase credentials required - please check your configuration');
}

// 💀 NO MORE MOCKS! Real Supabase only!

// Simple, clean Supabase client - no complex middleware needed
console.log('Supabase client ready for use');

// Export the client
export const supabase = supabaseClient;

// Also attach the client to the window object for global access
// This is needed for components that access window.supabase directly
if (typeof window !== 'undefined') {
  console.log('Attaching Supabase client to window.supabase');
  window.supabase = supabaseClient;
}

// Helper function for OAuth sign-in
export const signInWithGoogle = async () => {
  // Always use real OAuth authentication, even in development mode
  console.log('Using real Google OAuth sign-in in all environments');

  if (!isSupabaseConfigured()) {
    console.error('Cannot sign in with Google: Supabase not configured');
    throw new Error('Supabase not configured');
  }

  try {
    // Always use current origin for redirect to fix localhost issue
    const redirectUrl = `${window.location.origin}/auth/callback`;

    console.log('[Auth] Using redirect URL:', redirectUrl);

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: redirectUrl,
      },
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Google sign-in error:', error);
    throw error;
  }
};

// Expose methods to get Supabase configuration
export const getSupabaseUrl = () => supabaseUrl || 'https://placeholder-url.supabase.co';
export const getSupabaseKey = () => supabaseKey || 'placeholder-key';