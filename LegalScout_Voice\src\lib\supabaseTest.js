import { supabase, isSupabaseConfigured } from './supabase';

// Simple test function to check if Supabase is properly configured
export function testSupabaseConnection() {
  console.log('Testing Supabase connection...');
  
  // Check if Supabase is configured
  const configured = isSupabaseConfigured();
  console.log('Supabase configured:', configured);
  
  if (!configured) {
    console.warn('Supabase is not configured. Using stub implementation.');
    return false;
  }
  
  // Try to make a simple query to test the connection
  return supabase.auth.getSession()
    .then(({ data, error }) => {
      if (error) {
        console.error('Supabase connection test failed:', error);
        return false;
      }
      
      console.log('Supabase connection test successful!');
      return true;
    })
    .catch(error => {
      console.error('Supabase connection test error:', error);
      return false;
    });
}

// Export a function to log Supabase environment variables (without revealing sensitive info)
export function logSupabaseConfig() {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseKeyExists = Boolean(import.meta.env.VITE_SUPABASE_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY);
  
  console.log('Supabase URL configured:', Boolean(supabaseUrl));
  console.log('Supabase Key configured:', supabaseKeyExists);
  
  if (supabaseUrl) {
    console.log('Supabase URL:', supabaseUrl);
  }
  
  return {
    urlConfigured: Boolean(supabaseUrl),
    keyConfigured: supabaseKeyExists
  };
}
