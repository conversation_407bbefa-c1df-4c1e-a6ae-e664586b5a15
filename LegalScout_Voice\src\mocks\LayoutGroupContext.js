/**
 * Mock implementation of LayoutGroupContext
 *
 * This file provides a direct import for LayoutGroupContext to prevent errors
 * when Framer Motion tries to use it.
 */

// Create a completely standalone mock context
export const LayoutGroupContext = {
  Provider: function(props) {
    return typeof props.children !== 'undefined' ? props.children : null;
  },
  Consumer: function(props) {
    return props.children && typeof props.children === 'function'
      ? props.children({})
      : null;
  },
  displayName: 'LayoutGroupContext',
  _currentValue: {},
  _currentValue2: {},
  _threadCount: 0,
  _defaultValue: {}
};

// Make it available globally
if (typeof window !== 'undefined') {
  window.LayoutGroupContext = LayoutGroupContext;
}

export default LayoutGroupContext;