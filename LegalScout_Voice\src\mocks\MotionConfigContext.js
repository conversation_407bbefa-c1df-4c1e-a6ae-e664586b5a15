/**
 * Mock implementation of MotionConfigContext
 * 
 * This file provides a direct import for MotionConfigContext to prevent errors
 * when Framer Motion tries to use it.
 */

// Create a completely standalone mock context
export const MotionConfigContext = {
  Provider: function(props) { 
    return typeof props.children !== 'undefined' ? props.children : null; 
  },
  Consumer: function(props) { 
    return props.children && typeof props.children === 'function' 
      ? props.children({}) 
      : null; 
  },
  displayName: 'MotionConfigContext',
  _currentValue: {},
  _currentValue2: {},
  _threadCount: 0,
  _defaultValue: {}
};

// Make it available globally
if (typeof window !== 'undefined') {
  window.MotionConfigContext = MotionConfigContext;
}

export default MotionConfigContext;
