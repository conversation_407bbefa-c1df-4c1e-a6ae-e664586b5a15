import React, { useState, useEffect } from 'react';
import { supabaseService } from '../services/supabaseService';
import { isSupabaseConfigured } from '../lib/supabase';
import { MigrationButton } from '../scripts/migrateToSupabase';
import AssistantMigrationButton from '../components/AssistantMigrationButton';

const AdminPage = () => {
  const [attorneys, setAttorneys] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentAttorney, setCurrentAttorney] = useState(null);
  const [formData, setFormData] = useState({
    subdomain: '',
    firm_name: '',
    logo_url: '',
    profile_image: '',
    vapi_instructions: '',
    vapi_context: '',
    practice_areas: [],
    interaction_deposit_url: '',
  });

  // Load attorneys on component mount
  useEffect(() => {
    loadAttorneys();
  }, []);

  // Load all attorneys from Supabase
  const loadAttorneys = async () => {
    if (!isSupabaseConfigured()) {
      setError('Supabase not configured. Please update your .env file with Supabase credentials.');
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabaseService.getAllAttorneys();

      if (error) throw error;
      setAttorneys(data || []);
    } catch (err) {
      console.error('Error loading attorneys:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === 'practice_areas') {
      // Convert comma-separated string to array
      setFormData({
        ...formData,
        [name]: value.split(',').map(item => item.trim())
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Edit an existing attorney
  const handleEdit = (attorney) => {
    setCurrentAttorney(attorney);

    // Convert practice_areas array to string for the input field
    const practiceAreasString = Array.isArray(attorney.practice_areas)
      ? attorney.practice_areas.join(', ')
      : '';

    setFormData({
      ...attorney,
      practice_areas: practiceAreasString,
    });
  };

  // Save attorney data (create or update)
  const handleSave = async (e) => {
    e.preventDefault();

    if (!formData.subdomain || !formData.firm_name) {
      setError('Subdomain and Firm Name are required');
      return;
    }

    setLoading(true);
    try {
      // Prepare data for saving
      const attorneyData = {
        ...formData,
        // Convert practice_areas to array if it's a string
        practice_areas: Array.isArray(formData.practice_areas)
          ? formData.practice_areas
          : formData.practice_areas.split(',').map(area => area.trim())
      };

      // If editing, include the ID
      if (currentAttorney) {
        attorneyData.id = currentAttorney.id;
      }

      const { data, error } = await supabaseService.saveAttorney(attorneyData);

      if (error) throw error;

      // Reset form and reload attorneys
      setFormData({
        subdomain: '',
        firm_name: '',
        logo_url: '',
        profile_image: '',
        vapi_instructions: '',
        vapi_context: '',
        practice_areas: [],
        interaction_deposit_url: '',
      });
      setCurrentAttorney(null);

      await loadAttorneys();
    } catch (err) {
      console.error('Error saving attorney:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Cancel editing
  const handleCancel = () => {
    setCurrentAttorney(null);
    setFormData({
      subdomain: '',
      firm_name: '',
      logo_url: '',
      profile_image: '',
      vapi_instructions: '',
      vapi_context: '',
      practice_areas: [],
      interaction_deposit_url: '',
    });
  };

  return (
    <div className="admin-page">
      <h1>Attorney Configuration Admin</h1>

      {!isSupabaseConfigured() && (
        <div className="error-banner">
          <strong>Supabase not configured.</strong> Please update your .env file with Supabase credentials.
        </div>
      )}

      <div className="admin-tools">
        <MigrationButton />
        <AssistantMigrationButton />
      </div>

      <h2>{currentAttorney ? 'Edit Attorney' : 'Add New Attorney'}</h2>

      {error && <div className="error">{error}</div>}

      <form onSubmit={handleSave} className="attorney-form">
        <div className="form-group">
          <label htmlFor="subdomain">Subdomain</label>
          <input
            type="text"
            id="subdomain"
            name="subdomain"
            value={formData.subdomain}
            onChange={handleChange}
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="firm_name">Firm Name</label>
          <input
            type="text"
            id="firm_name"
            name="firm_name"
            value={formData.firm_name}
            onChange={handleChange}
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="logo_url">Logo URL</label>
          <input
            type="url"
            id="logo_url"
            name="logo_url"
            value={formData.logo_url}
            onChange={handleChange}
          />
        </div>

        <div className="form-group">
          <label htmlFor="profile_image">Profile Image URL</label>
          <input
            type="url"
            id="profile_image"
            name="profile_image"
            value={formData.profile_image}
            onChange={handleChange}
          />
        </div>

        <div className="form-group">
          <label htmlFor="vapi_instructions">Vapi Instructions</label>
          <textarea
            id="vapi_instructions"
            name="vapi_instructions"
            value={formData.vapi_instructions}
            onChange={handleChange}
            rows={4}
          />
        </div>

        <div className="form-group">
          <label htmlFor="vapi_context">Vapi Context</label>
          <textarea
            id="vapi_context"
            name="vapi_context"
            value={formData.vapi_context}
            onChange={handleChange}
            rows={4}
          />
        </div>

        <div className="form-group">
          <label htmlFor="practice_areas">Practice Areas (comma-separated)</label>
          <input
            type="text"
            id="practice_areas"
            name="practice_areas"
            value={Array.isArray(formData.practice_areas)
              ? formData.practice_areas.join(', ')
              : formData.practice_areas}
            onChange={handleChange}
          />
        </div>

        <div className="form-group">
          <label htmlFor="interaction_deposit_url">Interaction Deposit URL</label>
          <input
            type="url"
            id="interaction_deposit_url"
            name="interaction_deposit_url"
            value={formData.interaction_deposit_url}
            onChange={handleChange}
          />
        </div>

        <div className="form-actions">
          <button type="submit" disabled={loading}>
            {loading ? 'Saving...' : 'Save Attorney'}
          </button>
          {currentAttorney && (
            <button type="button" onClick={handleCancel}>
              Cancel
            </button>
          )}
        </div>
      </form>

      <h2>Attorney List</h2>

      {loading ? (
        <div>Loading...</div>
      ) : (
        <table className="attorney-table">
          <thead>
            <tr>
              <th>Subdomain</th>
              <th>Firm Name</th>
              <th>Practice Areas</th>
              <th>Vapi Assistant</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {attorneys.length === 0 ? (
              <tr>
                <td colSpan="5">No attorneys found</td>
              </tr>
            ) : (
              attorneys.map(attorney => (
                <tr key={attorney.id}>
                  <td>{attorney.subdomain}</td>
                  <td>{attorney.firm_name}</td>
                  <td>
                    {Array.isArray(attorney.practice_areas)
                      ? attorney.practice_areas.join(', ')
                      : attorney.practice_areas}
                  </td>
                  <td>
                    {attorney.vapi_assistant_id ? (
                      <span className="assistant-id" title={attorney.vapi_assistant_id}>
                        {attorney.vapi_assistant_id.substring(0, 8)}...
                      </span>
                    ) : (
                      <span className="no-assistant">Not created</span>
                    )}
                  </td>
                  <td>
                    <button onClick={() => handleEdit(attorney)}>
                      Edit
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default AdminPage;