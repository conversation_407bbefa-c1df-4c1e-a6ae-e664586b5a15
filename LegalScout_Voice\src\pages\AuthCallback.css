.auth-callback-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #4B74AA;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

.success-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.success-message h2 {
  color: #4B74AA;
  margin-bottom: 1rem;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  background-color: #fff5f5;
  border-radius: 8px;
  border: 1px solid #ffcccc;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.error-message h2 {
  color: #e53e3e;
  margin-bottom: 1rem;
}

.error-message button {
  margin-top: 1.5rem;
  padding: 0.75rem 1.5rem;
  background-color: #4B74AA;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.error-message button:hover {
  background-color: #3a5d8a;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .success-message {
    background-color: #2a2a2a;
    color: #f5f5f5;
  }
  
  .success-message h2 {
    color: #7fa3d7;
  }
  
  .error-message {
    background-color: #3a2a2a;
    border-color: #5a3a3a;
    color: #f5f5f5;
  }
  
  .error-message h2 {
    color: #ff6b6b;
  }
  
  .loading-spinner {
    border-color: rgba(255, 255, 255, 0.1);
    border-top-color: #7fa3d7;
  }
}
