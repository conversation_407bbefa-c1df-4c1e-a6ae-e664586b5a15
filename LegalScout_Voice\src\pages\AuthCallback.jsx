import React, { useEffect, useState } from 'react';
import { supabase, isSupabaseConfigured } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';
import { fixAuthProfile } from '../utils/authProfileFixer';
import './AuthCallback.css';

const AuthCallback = () => {
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const handleCallback = async () => {
      console.log('Auth callback - environment mode:', import.meta.env.MODE);
      console.log('Using real authentication in all environments');

      // Check if Supabase is configured
      if (!isSupabaseConfigured()) {
        setError('Supabase is not properly configured. Please check your environment variables.');
        setLoading(false);
        return;
      }

      try {
        // Get the current session
        const { data, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) throw sessionError;

        if (!data.session) {
          throw new Error('No session found. Authentication may have failed.');
        }

        // Get user details
        const { data: userData, error: userError } = await supabase.auth.getUser();

        if (userError) throw userError;

        console.log('🔐 [AuthCallback] Processing authentication for:', userData.user.email);

        // Use the enhanced auth profile fixer
        const attorneyProfile = await fixAuthProfile(userData.user);

        if (attorneyProfile) {
          // User has an attorney profile, store and redirect to dashboard
          console.log('✅ [AuthCallback] Attorney profile found/created:', attorneyProfile.firm_name);
          localStorage.setItem('attorney', JSON.stringify(attorneyProfile));

          // Force navigation to dashboard with full URL
          const dashboardUrl = `${window.location.origin}/dashboard`;
          console.log('🚀 [AuthCallback] Redirecting to dashboard:', dashboardUrl);
          window.location.href = dashboardUrl;
        } else {
          // User needs to complete profile setup
          console.log('📝 [AuthCallback] No attorney profile found, redirecting to complete profile');
          navigate('/complete-profile');
        }
      } catch (err) {
        console.error('Auth callback error:', err);
        setError(err.message || 'Authentication failed. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [navigate]);

  if (loading) {
    return (
      <div className="auth-callback-container">
        <div className="loading-spinner"></div>
        <p>Completing authentication...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="auth-callback-container">
        <div className="error-message">
          <h2>Authentication Error</h2>
          <p>{error}</p>
          <button onClick={() => navigate('/')}>Return to Home</button>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-callback-container">
      <div className="success-message">
        <h2>Authentication Successful</h2>
        <p>Redirecting to your dashboard...</p>
      </div>
    </div>
  );
};

export default AuthCallback;
