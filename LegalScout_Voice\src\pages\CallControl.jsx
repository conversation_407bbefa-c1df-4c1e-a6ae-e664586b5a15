import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { getTokenFromUrl, verifyCallControlToken } from '../utils/tokenUtils';
import useCallControl from '../hooks/useCallControl';
import EnhancedSpeechParticles from '../components/EnhancedSpeechParticles';
import './CallControl.css';

/**
 * Call Control Page
 *
 * This page allows attorneys to monitor and control ongoing calls.
 * It's accessed via a secure link sent in SMS notifications.
 */
const CallControl = () => {
  const navigate = useNavigate();

  // Get token from URL
  const token = getTokenFromUrl();

  // Local state
  const [message, setMessage] = useState('');

  // Use our call control hook
  const {
    loading,
    error,
    call,
    attorney,
    transcripts,
    assistantIsSpeaking,
    volumeLevel,
    callStatus,
    sendMessage,
    takeOverCall,
    endCall
  } = useCallControl({
    token,
    onError: (error) => console.error('Call control error:', error)
  });

  // Handle sending a message to the call
  const handleSendMessage = async () => {
    if (!message.trim()) return;

    const success = await sendMessage(message);

    if (success) {
      // Clear input
      setMessage('');
    }
  };

  // Handle taking over the call
  const handleTakeOverCall = async () => {
    const success = await takeOverCall();

    if (!success) {
      alert('Call takeover not implemented yet');
    }
  };

  // Handle ending the call
  const handleEndCall = async () => {
    await endCall();
  };

  // Render loading state
  if (loading) {
    return (
      <div className="call-control-container loading">
        <div className="loading-spinner"></div>
        <p>Loading call control...</p>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="call-control-container error">
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => navigate('/')}>Return to Home</button>
      </div>
    );
  }

  return (
    <div className="call-control-container">
      <header className="call-control-header">
        <h1>Call Control</h1>
        <div className="call-status">
          <span className={`status-indicator ${callStatus}`}></span>
          <span className="status-text">{callStatus}</span>
        </div>
      </header>

      <div className="call-info">
        <div className="attorney-info">
          <h2>{attorney?.firm_name || 'Your Law Firm'}</h2>
          <p>{attorney?.name || 'Attorney'}</p>
        </div>

        <div className="call-details">
          <p>Call ID: {call?.id}</p>
          <p>Customer: {call?.customer?.phoneNumber || 'Unknown'}</p>
          <p>Duration: {Math.round((call?.duration || 0) / 60)} minutes</p>
        </div>
      </div>

      <div className="call-visualization">
        <EnhancedSpeechParticles
          isSpeaking={assistantIsSpeaking}
          volumeLevel={volumeLevel}
          speaker="assistant"
        />
      </div>

      <div className="transcript-container">
        <h3>Live Transcript</h3>
        <div className="transcript-messages">
          {transcripts.map((transcript, index) => (
            <div
              key={index}
              className={`transcript-message ${transcript.role}`}
            >
              <div className="message-header">
                <span className="message-role">
                  {transcript.role === 'assistant' ? 'Assistant' : 'Client'}
                </span>
                <span className="message-time">
                  {new Date(transcript.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <div className="message-content">{transcript.text}</div>
            </div>
          ))}
        </div>
      </div>

      <div className="control-panel">
        <div className="message-input">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type a message to send to the assistant..."
            disabled={callStatus !== 'in-progress'}
          />
          <button
            onClick={handleSendMessage}
            disabled={!message.trim() || callStatus !== 'in-progress'}
          >
            Send
          </button>
        </div>

        <div className="control-buttons">
          <button
            className="take-over-button"
            onClick={handleTakeOverCall}
            disabled={callStatus !== 'in-progress'}
          >
            Take Over Call
          </button>

          <button
            className="end-call-button"
            onClick={handleEndCall}
            disabled={callStatus !== 'in-progress'}
          >
            End Call
          </button>
        </div>
      </div>
    </div>
  );
};

export default CallControl;
