.complete-profile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f9f9f9;
}

.complete-profile-card {
  background-color: white;
  border-radius: 12px;
  padding: 32px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.complete-profile-card h1 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  font-size: 24px;
}

.complete-profile-card p {
  color: #666;
  margin-bottom: 24px;
  line-height: 1.5;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
  color: #555;
}

.form-group input {
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #ddd;
  font-size: 16px;
  transition: border-color 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: #4B74AA;
  box-shadow: 0 0 0 2px rgba(75, 116, 170, 0.1);
}

.subdomain-input {
  display: flex;
  align-items: center;
}

.subdomain-input input {
  flex: 1;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.subdomain-suffix {
  background-color: #f5f5f5;
  padding: 12px;
  border: 1px solid #ddd;
  border-left: none;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  color: #666;
  font-size: 16px;
}

.form-group small {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.error-message {
  background-color: #fff2f2;
  color: #e53e3e;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #e53e3e;
  margin-bottom: 16px;
}

/* Verification method selector */
.verification-method-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 8px;
}

.radio-option {
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.radio-option:hover {
  border-color: #4B74AA;
  background-color: #f8fafc;
}

.radio-option input[type="radio"] {
  margin-right: 12px;
  margin-bottom: 4px;
}

.radio-option input[type="radio"]:checked + span {
  color: #4B74AA;
  font-weight: 600;
}

.radio-option span {
  font-weight: 500;
  color: #374151;
}

.radio-option small {
  color: #6b7280;
  margin-top: 4px;
  margin-left: 24px;
}

/* Verification info section */
.verification-info {
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.verification-info p {
  margin: 0;
  color: #0369a1;
  font-size: 14px;
  line-height: 1.5;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin-top: 8px;
}

.back-button {
  padding: 12px 24px;
  border-radius: 6px;
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s;
}

.back-button:hover {
  background-color: #e5e7eb;
}

.back-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.submit-button {
  padding: 12px 24px;
  border-radius: 6px;
  background-color: #4B74AA;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s;
  flex: 1;
}

.submit-button:hover {
  background-color: #3a5d8a;
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #4B74AA;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .complete-profile-container {
    background-color: #1a1a1a;
  }
  
  .complete-profile-card {
    background-color: #2a2a2a;
    color: #f5f5f5;
  }
  
  .complete-profile-card h1 {
    color: #f5f5f5;
  }
  
  .complete-profile-card p {
    color: #ccc;
  }
  
  .form-group label {
    color: #ccc;
  }
  
  .form-group input {
    background-color: #333;
    border-color: #444;
    color: #f5f5f5;
  }
  
  .form-group input:focus {
    border-color: #7fa3d7;
    box-shadow: 0 0 0 2px rgba(127, 163, 215, 0.2);
  }
  
  .subdomain-suffix {
    background-color: #333;
    border-color: #444;
    color: #ccc;
  }
  
  .form-group small {
    color: #999;
  }

  .radio-option {
    border-color: #444;
    background-color: #2a2a2a;
  }

  .radio-option:hover {
    border-color: #7fa3d7;
    background-color: #333;
  }

  .radio-option span {
    color: #f5f5f5;
  }

  .radio-option input[type="radio"]:checked + span {
    color: #7fa3d7;
  }

  .radio-option small {
    color: #999;
  }

  .back-button {
    background-color: #333;
    color: #f5f5f5;
    border-color: #444;
  }

  .back-button:hover {
    background-color: #444;
  }

  .verification-info {
    background-color: #1e3a8a;
    border-color: #3b82f6;
  }

  .verification-info p {
    color: #93c5fd;
  }
}
