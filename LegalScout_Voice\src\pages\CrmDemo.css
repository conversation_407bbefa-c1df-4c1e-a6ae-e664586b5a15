.crm-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background-color: var(--background-color, #f5f7fa);
}

.crm-demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.crm-demo-header h1 {
  margin: 0;
  color: var(--primary-color, #4B74AA);
}

.theme-toggle-btn {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color, #4B74AA);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.theme-toggle-btn:hover {
  background-color: var(--primary-hover, #3A5D88);
}

.view-selector {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.crm-demo-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 600px;
}

/* Dark theme */
[data-theme="dark"] {
  --background-color: #121212;
  --text-color: rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .crm-demo-header h1 {
  color: var(--dark-accent, #64B5F6);
}

[data-theme="dark"] .crm-demo-content {
  background-color: #1e1e1e;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}
