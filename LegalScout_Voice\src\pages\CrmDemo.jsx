import React, { useState } from 'react';
import ConsultationTableView from '../components/crm/ConsultationTableView';
import ConsultationCardView from '../components/crm/ConsultationCardView';
import ConsultationMapView from '../components/crm/ConsultationMapView';
import '../components/crm/CrmViews.css';
import './Dashboard.css';

const CrmDemo = () => {
  const [viewMode, setViewMode] = useState('table');
  const [isDarkTheme, setIsDarkTheme] = useState(false);

  return (
    <div className="crm-demo-container" data-theme={isDarkTheme ? 'dark' : 'light'}>
      <div className="crm-demo-header">
        <h1>CRM View Demo</h1>
        <div className="theme-toggle">
          <button 
            className="theme-toggle-btn"
            onClick={() => setIsDarkTheme(!isDarkTheme)}
          >
            {isDarkTheme ? 'Light Mode' : 'Dark Mode'}
          </button>
        </div>
      </div>
      
      <div className="view-selector">
        <div className="view-toggle">
          <button 
            className={`view-toggle-btn ${viewMode === 'table' ? 'active' : ''}`}
            onClick={() => setViewMode('table')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="8" y1="6" x2="21" y2="6"></line>
              <line x1="8" y1="12" x2="21" y2="12"></line>
              <line x1="8" y1="18" x2="21" y2="18"></line>
              <line x1="3" y1="6" x2="3.01" y2="6"></line>
              <line x1="3" y1="12" x2="3.01" y2="12"></line>
              <line x1="3" y1="18" x2="3.01" y2="18"></line>
            </svg> Table View
          </button>
          <button 
            className={`view-toggle-btn ${viewMode === 'card' ? 'active' : ''}`}
            onClick={() => setViewMode('card')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="7" height="7"></rect>
              <rect x="14" y="3" width="7" height="7"></rect>
              <rect x="14" y="14" width="7" height="7"></rect>
              <rect x="3" y="14" width="7" height="7"></rect>
            </svg> Card View
          </button>
          <button 
            className={`view-toggle-btn ${viewMode === 'map' ? 'active' : ''}`}
            onClick={() => setViewMode('map')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polygon points="1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6"></polygon>
              <line x1="8" y1="2" x2="8" y2="18"></line>
              <line x1="16" y1="6" x2="16" y2="22"></line>
            </svg> Map View
          </button>
        </div>
      </div>
      
      <div className="crm-demo-content">
        {viewMode === 'table' && <ConsultationTableView />}
        {viewMode === 'card' && <ConsultationCardView />}
        {viewMode === 'map' && <ConsultationMapView />}
      </div>
    </div>
  );
};

export default CrmDemo;
