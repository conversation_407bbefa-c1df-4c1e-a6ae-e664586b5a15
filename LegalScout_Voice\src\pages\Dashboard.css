/* Root variables for the dashboard */
:root {
  --primary-color: #4B74AA;
  --primary-color-rgb: 75, 116, 170;
  --secondary-color: #607D8B;
  --accent-color: #D85722;
  --background-color: #ffffff;
  --background-color-rgb: 255, 255, 255;
  --border-color: #E0E7EF;
  --text-primary: #37474F;
  --text-secondary: #607D8B;
  --shadow-soft: 0 4px 12px rgba(0, 20, 50, 0.04);
  --shadow-medium: 0 8px 24px rgba(0, 20, 50, 0.06);
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --transition-default: all 0.3s ease;

  /* Scrollbar variables */
  --scrollbar-track-color: transparent;
  --scrollbar-thumb-color: rgba(75, 116, 170, 0.1);
  --scrollbar-thumb-hover-color: rgba(75, 116, 170, 0.3);
  --scrollbar-width: 6px;
  --scrollbar-glow-color: rgba(75, 116, 170, 0.5);

  /* Derived colors */
  --primary-hover: color-mix(in srgb, var(--primary-color) 85%, black);
  --secondary-hover: color-mix(in srgb, var(--secondary-color) 85%, black);
  --accent-hover: color-mix(in srgb, var(--accent-color) 85%, black);
}

/* Modern dark theme variables */
:root[data-theme="dark"] {
  --dark-bg: #121212;
  --dark-card-bg: rgba(18, 18, 20, 0.5);
  --dark-accent: #64B5F6;
  --dark-accent-hover: #90CAF9;
  --dark-text-primary: rgba(255, 255, 255, 0.95);
  --dark-text-secondary: rgba(255, 255, 255, 0.7);
  --dark-border: rgba(100, 181, 246, 0.2);
  --dark-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  --dark-input-bg: rgba(24, 24, 28, 0.4);
  --dark-glass-border: rgba(100, 181, 246, 0.25);

  /* Dark theme scrollbar variables */
  --scrollbar-track-color: transparent;
  --scrollbar-thumb-color: rgba(100, 181, 246, 0.1);
  --scrollbar-thumb-hover-color: rgba(100, 181, 246, 0.3);
  --scrollbar-glow-color: rgba(100, 181, 246, 0.5);
}

/* Main dashboard layout */
.dashboard-container {
  display: grid;
  grid-template-columns: 2fr 3fr; /* Changed from 1fr 1fr to 2fr 3fr (40% / 60% split) */
  min-height: 100vh;
  width: 100%;
  background-color: var(--background-color);
}

/* When no preview panel is shown, make config panel full width */
.dashboard-container.no-preview {
  grid-template-columns: 1fr;
}

/* Dashboard header */
.dashboard-header {
  grid-column: 1 / -1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 2rem;
  background-color: rgba(var(--background-color-rgb), 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.1);
  color: var(--text-primary);
  z-index: 10;
  position: sticky;
  top: 0;
}

.header-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-logo svg {
  color: var(--primary-color);
}

.dashboard-header h1 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
  letter-spacing: -0.5px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: transparent;
  color: var(--text-secondary);
  border: none;
  cursor: pointer;
  transition: var(--transition-default);
}

.theme-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary-color);
}

.sign-out-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-default);
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
}

.sign-out-button:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(var(--primary-color-rgb), 0.1);
}

/* Config panel */
.config-panel {
  grid-column: 1;
  overflow-y: auto;
  padding: 1.5rem;
  background-color: var(--background-color);
  border-right: 1px solid var(--border-color);
  height: calc(100vh - 64px);
  max-width: 100%;
}

/* Preview panel */
.preview-panel {
  grid-column: 2;
  background-color: #f5f7fa;
  height: calc(100vh - 64px);
  overflow: hidden;
  position: relative;
  max-width: 100%;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid var(--border-color);
}

.preview-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.preview-actions {
  display: flex;
  gap: 0.5rem;
}

.preview-button {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-default);
  font-size: 0.875rem;
}

.preview-button.secondary {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.preview-button:hover {
  background-color: var(--primary-hover);
}

.preview-button.secondary:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.preview-content {
  height: calc(100% - 60px);
  overflow: hidden;
}

/* System Status */
.preview-content.system-status {
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.status-card {
  background-color: white;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-soft);
  padding: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.status-card h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.status-value {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.status-value.active {
  color: #4CAF50;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* Config tabs */
.config-tabs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.75rem;
}

.config-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 0.925rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-default);
  border-radius: var(--radius-small);
  position: relative;
}

.config-tab:hover {
  background-color: rgba(0, 0, 0, 0.03);
  color: var(--text-primary);
}

.config-tab.active {
  color: var(--accent-color);
  font-weight: var(--font-weight-semibold);
  background-color: rgba(216, 87, 34, 0.1);
}

.config-tab.active::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--accent-color);
  border-radius: 4px 4px 0 0;
}

/* Tab content */
.tab-content {
  padding: 1rem 0;
}

/* Form elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-text {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background-color: white;
  color: var(--text-primary);
  transition: var(--transition-default);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.1);
}

.form-control-range {
  width: 100%;
  height: 8px;
  padding: 0;
  background-color: var(--border-color);
  border-radius: 4px;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
}

.form-control-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.form-control-range::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.input-group {
  display: flex;
  align-items: center;
}

.input-group-text {
  padding: 0.75rem;
  background-color: rgba(0, 0, 0, 0.03);
  border: 1px solid var(--border-color);
  border-left: none;
  border-radius: 0 var(--radius-small) var(--radius-small) 0;
  color: var(--text-secondary);
}

.input-group .form-control {
  border-radius: var(--radius-small) 0 0 var(--radius-small);
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.form-col {
  flex: 1;
}

/* Logo upload */
.logo-upload-container {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.logo-upload {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-medium);
  padding: 2rem;
  background-color: rgba(0, 0, 0, 0.02);
  cursor: pointer;
  transition: var(--transition-default);
}

.logo-upload:hover {
  background-color: rgba(0, 0, 0, 0.04);
  border-color: var(--primary-color);
}

.file-input {
  display: none;
}

.file-input-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-default);
}

.file-input-label svg {
  color: var(--primary-color);
}

.logo-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: var(--radius-medium);
  background-color: rgba(0, 0, 0, 0.02);
}

.logo-image {
  max-width: 100px;
  max-height: 100px;
  border-radius: var(--radius-small);
  object-fit: contain;
}

.remove-logo-button {
  padding: 0.5rem 1rem;
  background-color: rgba(255, 0, 0, 0.1);
  color: #e74c3c;
  border: 1px solid rgba(255, 0, 0, 0.2);
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-default);
  font-size: 0.875rem;
}

.remove-logo-button:hover {
  background-color: rgba(255, 0, 0, 0.15);
}

/* Color pickers */
.color-pickers {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.color-picker {
  margin-bottom: 0;
}

.color-input {
  height: 40px;
  padding: 0.25rem;
  cursor: pointer;
}

/* Opacity sliders */
.opacity-sliders {
  margin-top: 1.5rem;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.dashboard-button.secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.dashboard-button.secondary:hover {
  background-color: rgba(75, 116, 170, 0.1);
}

/* Card styles */
.dashboard-card {
  background-color: white;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-soft);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.dashboard-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.dashboard-card h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
}

/* Button styles */
.dashboard-button {
  padding: 0.75rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-default);
  font-weight: var(--font-weight-medium);
}

.dashboard-button:hover {
  background-color: var(--primary-hover);
}

/* Share section styles */
.share-section {
  margin-bottom: 2rem;
}

.share-section h3 {
  font-size: 1.1rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.section-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
  margin-bottom: 1rem;
}

.copy-field {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.copy-field input,
.copy-field textarea {
  flex: 1;
  font-family: monospace;
  font-size: 0.9rem;
  background-color: rgba(0, 0, 0, 0.02);
}

.code-textarea {
  min-height: 80px;
  resize: none;
  white-space: pre;
  overflow-x: auto;
  font-family: monospace;
  font-size: 0.9rem;
  line-height: 1.4;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-default);
  font-size: 0.875rem;
  white-space: nowrap;
}

.copy-button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.copy-success {
  color: #27ae60;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Consultation list */
.consultation-list {
  margin-top: 1rem;
}

.consultation-item {
  padding: 1rem;
  border-radius: var(--radius-small);
  background-color: rgba(0, 0, 0, 0.02);
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: var(--transition-default);
}

.consultation-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.consultation-info {
  flex: 1;
}

.consultation-date {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.consultation-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.consultation-actions {
  display: flex;
  gap: 0.5rem;
}

.consultation-button {
  padding: 0.4rem 0.75rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-default);
  font-size: 0.75rem;
}

.consultation-button.secondary {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.consultation-button:hover {
  background-color: var(--primary-hover);
}

.consultation-button.secondary:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

.empty-state.small {
  padding: 1rem;
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: rgba(0, 0, 0, 0.1);
}

.empty-state-icon.small {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* Modal styles */
.column-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-medium);
  padding: 2rem;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.forwarding-modal .form-group {
  margin-bottom: 1.5rem;
}

.forwarding-conditions {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.condition-group {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.01);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  cursor: pointer;
}

.condition-details {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed var(--border-color);
}

.time-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.time-range span {
  color: var(--text-secondary);
}

.form-control.small {
  padding: 0.5rem;
  font-size: 0.875rem;
}

/* Custom Columns and Rules */
.card-description {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

/* Coming Soon Styles */
.coming-soon-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.coming-soon-header h2 {
  margin: 0;
}

.coming-soon-badge {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
}

.coming-soon-badge.small {
  font-size: 0.7rem;
  padding: 0.15rem 0.4rem;
}

.coming-soon-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--radius-medium);
  text-align: center;
}

.placeholder-icon {
  margin-bottom: 1rem;
  color: rgba(var(--primary-color-rgb), 0.3);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.section-header h3 {
  margin: 0;
}

/* Integrations Styles */
.integrations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.integration-card {
  background-color: white;
  border-radius: var(--radius-small);
  border: 1px solid var(--border-color);
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  transition: var(--transition-default);
}

.integration-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-soft);
  border-color: var(--primary-color);
}

.integration-card.coming-soon {
  opacity: 0.7;
}

.integration-logo {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-radius: var(--radius-small);
  flex-shrink: 0;
}

.integration-info {
  flex: 1;
}

.integration-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.integration-info p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.integration-status {
  margin-left: auto;
  align-self: flex-start;
}

.integration-cta {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.feature-list {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
  color: var(--text-primary);
}

.feature-list li {
  margin-bottom: 0.5rem;
}

.custom-columns-list, .rules-list {
  margin-top: 1.5rem;
  border: 1px dashed var(--border-color);
  border-radius: var(--radius-small);
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.01);
}

.add-column-button, .add-rule-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: transparent;
  color: var(--primary-color);
  border: 1px dashed var(--primary-color);
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-default);
  margin: 1rem auto;
  font-weight: var(--font-weight-medium);
}

.add-column-button:hover, .add-rule-button:hover {
  background-color: rgba(75, 116, 170, 0.05);
}

/* Rules Section */
.rules-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.rules-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.section-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.section-description {
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.coming-soon-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: rgba(var(--accent-color-rgb, 216, 87, 34), 0.1);
  color: var(--accent-color, #D85722);
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
}

.coming-soon-badge.small {
  font-size: 0.65rem;
  padding: 0.15rem 0.35rem;
  margin-left: 0.5rem;
  vertical-align: middle;
}

.coming-soon-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--radius-small);
  color: var(--text-secondary);
  text-align: center;
}

.placeholder-icon {
  margin-bottom: 1rem;
  opacity: 0.3;
}

/* Integrations styles */
.coming-soon-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.integrations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.integration-card {
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  border-radius: var(--radius-medium);
  background-color: white;
  box-shadow: var(--shadow-soft);
  transition: var(--transition-default);
  border: 1px solid var(--border-color);
}

.integration-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.integration-card.coming-soon {
  opacity: 0.7;
}

.integration-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 12px;
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.integration-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.integration-info p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.integration-status {
  margin-top: auto;
  padding-top: 1rem;
}

.integration-cta {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.integration-cta p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

/* Modal */
.column-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-medium);
  padding: 2rem;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.forwarding-modal {
  width: 600px;
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

/* Form layout */
.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-col {
  flex: 1;
}

.form-control.small {
  padding: 0.5rem;
  font-size: 0.875rem;
}

/* Forwarding conditions */
.forwarding-conditions {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 0.5rem;
}

.condition-group {
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--radius-small);
  border: 1px solid var(--border-color);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.condition-details {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed var(--border-color);
}

.time-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.time-range span {
  color: var(--text-secondary);
}

/* Loading spinner */
.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary-color);
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 2rem auto;
}

.loading-spinner-small {
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 2px solid var(--primary-color);
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Vapi Integration Styles */
.vapi-field-actions {
  display: flex;
  margin-top: 0.5rem;
  gap: 0.5rem;
}

.vapi-save-button,
.vapi-abandon-button {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
  cursor: pointer;
  border: 1px solid transparent;
}

.vapi-save-button {
  background-color: #28a745;
  color: white;
}

.vapi-save-button:hover {
  background-color: #218838;
}

.vapi-save-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.vapi-abandon-button {
  background-color: #6c757d;
  color: white;
}

.vapi-abandon-button:hover {
  background-color: #5a6268;
}

.vapi-abandon-button:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
}

.vapi-assistant-status {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--radius-small);
  border: 1px solid var(--border-color);
}

.vapi-assistant-status h3 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.vapi-assistant-status .status-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.9rem;
}

.vapi-assistant-status .status-item:last-child {
  border-bottom: none;
}

.vapi-assistant-status .status-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.vapi-assistant-status .status-value {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  max-width: 60%;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

[data-theme="dark"] .vapi-assistant-status {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  /* Maintain the same ratio but adjust for medium screens */
  .dashboard-container {
    grid-template-columns: 2fr 3fr;
  }
}

@media (max-width: 1024px) {
  /* Switch to single column on smaller screens */
  .dashboard-container {
    grid-template-columns: 1fr;
  }

  .preview-panel {
    display: none;
  }

  .preview-panel.active {
    display: block;
    grid-column: 1;
    position: fixed;
    top: 64px;
    left: 0;
    width: 100%;
    z-index: 100;
  }

  .config-panel.preview-active {
    display: none;
  }

  .crm-panel {
    grid-column: 1;
  }
}

/* Dark theme styles */
[data-theme="dark"] {
  --background-color: #121212;
  --background-color-rgb: 18, 18, 18;
  --text-primary: var(--dark-text-primary);
  --text-secondary: var(--dark-text-secondary);
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-soft: 0 4px 12px rgba(0, 0, 0, 0.2);
  --shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.3);
  --card-background: #1e1e1e;
  --input-background: #2c2c2c;
  --table-header-background: #2c2c2c;
  --table-hover-background: #2c2c2c;
  --dark-primary-color: #0d6efd;
  --dark-primary-color-dark: #0b5ed7;
  --dark-secondary-color: #495057;
  --dark-secondary-color-dark: #343a40;
  --dark-disabled-color: #343a40;
  --dark-text-primary: #f8f9fa;
  --dark-text-secondary: #adb5bd;
  --dark-border-color: #444;
  --stats-background: rgba(13, 110, 253, 0.05);
}

[data-theme="dark"] .dashboard-header {
  background-color: rgba(18, 18, 18, 0.8);
  border-bottom-color: rgba(100, 181, 246, 0.1);
}

[data-theme="dark"] .header-logo svg,
[data-theme="dark"] .dashboard-header h1 {
  color: var(--dark-accent, #64B5F6);
}

[data-theme="dark"] .theme-toggle {
  color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .theme-toggle:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--dark-accent, #64B5F6);
}

[data-theme="dark"] .sign-out-button {
  color: var(--dark-accent, #64B5F6);
  border-color: var(--dark-accent, #64B5F6);
}

[data-theme="dark"] .sign-out-button:hover {
  background-color: rgba(100, 181, 246, 0.1);
  box-shadow: 0 2px 5px rgba(100, 181, 246, 0.1);
}

[data-theme="dark"] .config-panel {
  background-color: #121212;
}

[data-theme="dark"] .preview-panel {
  background-color: #0a0a0a;
}

[data-theme="dark"] .preview-header {
  background-color: rgba(255, 255, 255, 0.03);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .preview-header h2 {
  color: var(--dark-text-primary);
}

[data-theme="dark"] .preview-button.secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .preview-button.secondary:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .dashboard-card {
  background-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .dashboard-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .form-control {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .form-control:focus {
  border-color: var(--dark-accent);
  box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.2);
}

[data-theme="dark"] .input-group-text {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text-secondary);
}

[data-theme="dark"] .consultation-item {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .consultation-item:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .consultation-button.secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .consultation-button.secondary:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .empty-state-icon {
  color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .loading-spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: var(--dark-accent);
}

/* Dark mode styles for share card */
[data-theme="dark"] .copy-field input,
[data-theme="dark"] .copy-field textarea {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--dark-text-primary);
  border-color: var(--dark-border);
}

[data-theme="dark"] .copy-success {
  color: #4cd137;
}

[data-theme="dark"] .share-section h3 {
  color: var(--dark-text-primary);
}

[data-theme="dark"] .section-description {
  color: var(--dark-text-secondary);
}

/* Custom Scrollbar Styles */
/* For Webkit browsers (Chrome, Safari, Edge) */
.dashboard-container *::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
  background-color: var(--scrollbar-track-color);
}

.dashboard-container *::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb-color);
  border-radius: 10px;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-container *::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover-color);
  box-shadow: 0 0 8px var(--scrollbar-glow-color);
}

/* For Firefox */
.dashboard-container * {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);
}

/* Add glow effect on hover for elements with scrollbars */
.config-panel:hover,
.preview-content:hover,
.modal-content:hover,
.dashboard-card:hover,
.form-control:hover,
.code-textarea:hover {
  scrollbar-color: var(--scrollbar-thumb-hover-color) var(--scrollbar-track-color);
}

/* Add glow effect when scrolling */
.config-panel:active,
.preview-content:active,
.modal-content:active,
.dashboard-card:active,
.form-control:active,
.code-textarea:active {
  scrollbar-color: var(--scrollbar-thumb-hover-color) var(--scrollbar-track-color);
}
