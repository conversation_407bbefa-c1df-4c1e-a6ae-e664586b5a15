// This file contains a direct fix for the scrolling issue in the demo page
// Import this file in SimpleDemoPage.jsx and call the fixScrolling function

export const fixScrolling = () => {
  // Force enable scrolling on the document
  document.documentElement.style.overflow = 'auto';
  document.body.style.overflow = 'auto';
  document.body.style.height = 'auto';
  
  // Force the demo-page-container to be scrollable
  const demoContainer = document.querySelector('.demo-page-container');
  if (demoContainer) {
    demoContainer.style.overflow = 'auto';
    demoContainer.style.height = 'auto';
    demoContainer.style.scrollSnapType = 'none';
  }
  
  // Force the hero section to be smaller
  const heroSection = document.querySelector('.hero-section');
  if (heroSection) {
    heroSection.style.minHeight = '70vh';
    heroSection.style.height = 'auto';
  }
  
  // Force the config container to be visible
  const configContainer = document.querySelector('.config-container');
  if (configContainer) {
    configContainer.style.display = 'block';
    configContainer.style.visibility = 'visible';
    configContainer.style.opacity = '1';
    configContainer.style.marginTop = '20px';
  }
  
  // Scroll to the config section
  setTimeout(() => {
    const configSection = document.querySelector('.config-container');
    if (configSection) {
      window.scrollTo({
        top: configSection.offsetTop - 80,
        behavior: 'smooth'
      });
    }
  }, 500);
};

// Function to directly scroll to the config section
export const scrollToConfigDirect = () => {
  // Force the config container to be visible
  const configContainer = document.querySelector('.config-container');
  if (configContainer) {
    configContainer.style.display = 'block';
    configContainer.style.visibility = 'visible';
    configContainer.style.opacity = '1';
  }
  
  // Scroll to the config section
  window.scrollTo({
    top: 1000, // Scroll down a fixed amount
    behavior: 'smooth'
  });
};
