.login-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: var(--background-color, #f5f7fa);
}

.login-container {
  width: 100%;
  max-width: 400px;
  padding: 30px;
  background-color: var(--card-background, #ffffff);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.login-logo {
  width: 150px;
  margin-bottom: 20px;
}

.login-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary, #333333);
  margin: 0;
}

.login-error {
  padding: 12px;
  margin-bottom: 20px;
  background-color: var(--error-background, #f8d7da);
  color: var(--error-text, #721c24);
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.login-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
}

.google-sign-in-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 12px 16px;
  background-color: #4285F4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.google-sign-in-button:hover {
  background-color: #3367D6;
}

.google-sign-in-button:disabled {
  background-color: #A4C2F4;
  cursor: not-allowed;
}

.login-footer {
  text-align: center;
  font-size: 14px;
  color: var(--text-secondary, #666666);
}

.text-button {
  background: none;
  border: none;
  color: var(--primary-color, #4B74AA);
  font-weight: 500;
  cursor: pointer;
  padding: 0;
  font-size: inherit;
  text-decoration: underline;
}

.text-button:hover {
  color: var(--primary-color-dark, #3A5A88);
}

/* Dark theme styles */
[data-theme="dark"] .login-container,
.dark-theme .login-container {
  background-color: var(--card-background, #2a2a2a);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .login-header h1,
.dark-theme .login-header h1 {
  color: var(--text-primary, #e0e0e0);
}

[data-theme="dark"] .login-footer,
.dark-theme .login-footer {
  color: var(--text-secondary, #b0b0b0);
}

[data-theme="dark"] .text-button,
.dark-theme .text-button {
  color: var(--primary-color, #6B94CA);
}

[data-theme="dark"] .text-button:hover,
.dark-theme .text-button:hover {
  color: var(--primary-color-light, #8BAAD8);
}
