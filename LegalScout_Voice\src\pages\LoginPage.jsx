import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { FaGoogle } from 'react-icons/fa';
import './LoginPage.css';

/**
 * Login Page Component
 * 
 * This component provides a login interface for attorneys.
 */
const LoginPage = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Handle Google sign-in
  const handleGoogleSignIn = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await login('google');
      
      if (result.success) {
        // The OAuth flow will redirect to the callback URL
        console.log('Google sign-in initiated successfully');
      } else {
        setError('Failed to initiate Google sign-in');
      }
    } catch (error) {
      console.error('Error signing in with Google:', error);
      setError(error.message || 'An error occurred during sign-in');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-page">
      <div className="login-container">
        <div className="login-header">
          <img src="/nav_logo.webp" alt="LegalScout Logo" className="login-logo" />
          <h1>Attorney Login</h1>
        </div>
        
        {error && (
          <div className="login-error">
            {error}
          </div>
        )}
        
        <div className="login-buttons">
          <button 
            className="google-sign-in-button"
            onClick={handleGoogleSignIn}
            disabled={loading}
          >
            <FaGoogle />
            <span>{loading ? 'Signing in...' : 'Sign in with Google'}</span>
          </button>
        </div>
        
        <div className="login-footer">
          <p>
            Don't have an account yet?{' '}
            <button 
              className="text-button"
              onClick={() => navigate('/demo')}
            >
              Get started
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
