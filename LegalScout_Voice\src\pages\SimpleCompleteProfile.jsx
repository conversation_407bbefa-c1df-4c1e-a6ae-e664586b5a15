import React, { useState, useEffect } from 'react';
import { supabase, isSupabaseConfigured } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';
import { fixAuthProfile } from '../utils/authProfileFixer';
import './CompleteProfile.css';

const SimpleCompleteProfile = () => {
  const [firmName, setFirmName] = useState('');
  const [subdomain, setSubdomain] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [user, setUser] = useState(null);
  const [step, setStep] = useState('profile'); // 'profile', 'verify-email'
  const [verificationCode, setVerificationCode] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated and has profile
    const checkAuth = async () => {
      console.log('📝 [SimpleCompleteProfile] Checking authentication and profile status');

      // Check if Supabase is configured
      if (!isSupabaseConfigured()) {
        setError('Authentication is not configured. Please contact the administrator.');
        setLoading(true);
        return;
      }

      try {
        const { data, error } = await supabase.auth.getUser();

        if (error || !data.user) {
          // Not authenticated, redirect to home
          console.log('❌ [SimpleCompleteProfile] User not authenticated, redirecting to home');
          navigate('/');
          return;
        }

        setUser(data.user);
        console.log('✅ [SimpleCompleteProfile] User authenticated:', data.user.email);

        // Check if user already has an attorney profile using the fixer
        const attorneyProfile = await fixAuthProfile(data.user);

        if (attorneyProfile) {
          // User already has a profile, redirect to dashboard
          console.log('🚀 [SimpleCompleteProfile] Attorney profile found, redirecting to dashboard');
          localStorage.setItem('attorney', JSON.stringify(attorneyProfile));
          navigate('/dashboard');
        } else {
          console.log('📝 [SimpleCompleteProfile] No profile found, user can complete profile');
        }
      } catch (err) {
        console.error('❌ [SimpleCompleteProfile] Error checking authentication:', err);
        setError('Failed to verify authentication. Please try again.');
      }
    };

    checkAuth();
  }, [navigate]);

  // Handle profile form submission
  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    console.log('📝 [SimpleCompleteProfile] Submitting profile form');

    try {
      // Validate inputs
      if (!firmName || !subdomain) {
        throw new Error('Firm name and subdomain are required');
      }

      // Validate subdomain format (letters, numbers, hyphens only)
      if (!/^[a-z0-9-]+$/.test(subdomain)) {
        throw new Error('Subdomain can only contain lowercase letters, numbers, and hyphens');
      }

      // Check if Supabase is configured
      if (!isSupabaseConfigured()) {
        setError('Authentication is not configured. Please contact the administrator.');
        setLoading(false);
        return;
      }

      // Check if subdomain is available
      const { data: existingAttorney, error: lookupError } = await supabase
        .from('attorneys')
        .select('id')
        .eq('subdomain', subdomain)
        .maybeSingle();

      if (lookupError && lookupError.code !== 'PGRST116') {
        console.error('❌ [SimpleCompleteProfile] Error checking subdomain availability:', lookupError);
        throw new Error('Failed to check subdomain availability. Please try again.');
      }

      if (existingAttorney) {
        throw new Error('This subdomain is already taken. Please choose another one.');
      }

      console.log('✅ [SimpleCompleteProfile] Subdomain available, sending email verification');

      // Send email verification
      await sendEmailVerification();
      setStep('verify-email');
    } catch (err) {
      console.error('❌ [SimpleCompleteProfile] Profile form error:', err);
      setError(err.message || 'Failed to process profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Send email verification
  const sendEmailVerification = async () => {
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email: user.email,
        options: {
          shouldCreateUser: false // Don't create a new user, just verify
        }
      });

      if (error) throw error;
      console.log('✅ [SimpleCompleteProfile] Email verification sent');
    } catch (error) {
      console.error('❌ [SimpleCompleteProfile] Email verification error:', error);
      throw new Error('Failed to send email verification. Please try again.');
    }
  };

  // Handle verification code submission
  const handleVerificationSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    console.log('📝 [SimpleCompleteProfile] Verifying code');

    try {
      if (!verificationCode) {
        throw new Error('Verification code is required');
      }

      // Verify the OTP
      const { error: verifyError } = await supabase.auth.verifyOtp({
        email: user.email,
        token: verificationCode,
        type: 'email'
      });

      if (verifyError) {
        throw new Error('Invalid verification code. Please try again.');
      }

      console.log('✅ [SimpleCompleteProfile] Verification successful, creating attorney profile');

      // Create attorney record in the database
      const attorneyData = {
        subdomain,
        firm_name: firmName,
        email: user.email,
        name: user.user_metadata?.full_name || firmName,
        is_active: true,
        user_id: user.id,
        practice_areas: ['General Practice'],
        // Required fields to prevent validation errors
        vapi_instructions: `You are a professional legal assistant for ${firmName}. Help potential clients understand their legal needs and schedule consultations. Always remind clients that you cannot provide legal advice and that they should consult with an attorney for specific legal matters.`,
        welcome_message: `Hello! I'm the AI assistant for ${firmName}. How can I help you today?`,
        voice_provider: 'openai',
        voice_id: 'alloy',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: newAttorney, error: attorneyError } = await supabase
        .from('attorneys')
        .insert([attorneyData])
        .select()
        .single();

      if (attorneyError) {
        console.error('❌ [SimpleCompleteProfile] Error creating attorney profile:', attorneyError);
        throw attorneyError;
      }

      console.log('✅ [SimpleCompleteProfile] Attorney profile created successfully:', newAttorney.id);

      // Store attorney data in localStorage
      localStorage.setItem('attorney', JSON.stringify(newAttorney));

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (err) {
      console.error('❌ [SimpleCompleteProfile] Verification error:', err);
      setError(err.message || 'Failed to verify code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="complete-profile-container">
        <div className="loading-spinner"></div>
        <p>Loading...</p>
      </div>
    );
  }

  // Render profile form
  const renderProfileForm = () => (
    <div className="complete-profile-card">
      <h1>Complete Your Profile</h1>
      <p>Just two quick details to set up your AI legal assistant.</p>

      <form onSubmit={handleProfileSubmit} className="profile-form">
        <div className="form-group">
          <label htmlFor="firmName">Law Firm Name</label>
          <input
            type="text"
            id="firmName"
            value={firmName}
            onChange={(e) => setFirmName(e.target.value)}
            placeholder="Your Law Firm, LLC"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="subdomain">Subdomain</label>
          <div className="subdomain-input">
            <input
              type="text"
              id="subdomain"
              value={subdomain}
              onChange={(e) => setSubdomain(e.target.value.toLowerCase())}
              placeholder="yourfirm"
              required
            />
            <span className="subdomain-suffix">.legalscout.ai</span>
          </div>
          <small>This will be your unique URL: https://{subdomain || 'yourfirm'}.legalscout.ai</small>
        </div>

        <div className="verification-info">
          <p><strong>Security Verification:</strong> We'll send a verification code to <strong>{user?.email}</strong> to confirm your identity.</p>
        </div>

        {error && <div className="error-message">{error}</div>}

        <div className="form-actions">
          <button
            type="submit"
            className="submit-button"
            disabled={loading}
          >
            {loading ? 'Sending Verification...' : 'Send Verification Code'}
          </button>
        </div>
      </form>
    </div>
  );

  // Render verification form
  const renderVerificationForm = () => (
    <div className="complete-profile-card">
      <h1>Verify Your Email</h1>
      <p>
        We've sent a 6-digit verification code to <strong>{user?.email}</strong>.
        Please check your email and enter the code below.
      </p>

      <form onSubmit={handleVerificationSubmit} className="profile-form">
        <div className="form-group">
          <label htmlFor="verificationCode">Verification Code</label>
          <input
            type="text"
            id="verificationCode"
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
            placeholder="Enter 6-digit code"
            maxLength="6"
            required
          />
        </div>

        {error && <div className="error-message">{error}</div>}

        <div className="form-actions">
          <button
            type="button"
            className="back-button"
            onClick={() => setStep('profile')}
            disabled={loading}
          >
            Back
          </button>
          <button
            type="submit"
            className="submit-button"
            disabled={loading}
          >
            {loading ? 'Verifying...' : 'Verify & Complete Profile'}
          </button>
        </div>
      </form>
    </div>
  );

  return (
    <div className="complete-profile-container">
      {step === 'profile' && renderProfileForm()}
      {step === 'verify-email' && renderVerificationForm()}
    </div>
  );
};

export default SimpleCompleteProfile;
