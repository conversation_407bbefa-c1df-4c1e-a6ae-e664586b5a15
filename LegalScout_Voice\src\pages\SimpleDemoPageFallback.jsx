import React from 'react';

/**
 * Fallback component for SimpleDemoPage
 * This is a simplified version that will be shown when the original component fails to load
 */
const SimpleDemoPageFallback = () => {
  return (
    <div className="fallback-container" style={{
      padding: '40px',
      maxWidth: '800px',
      margin: '0 auto',
      textAlign: 'center'
    }}>
      <h1>Demo Page</h1>
      <p>The demo page is currently unavailable. Please try again later or contact support.</p>
      
      <div style={{
        marginTop: '40px',
        padding: '20px',
        borderRadius: '8px',
        backgroundColor: 'rgba(0,0,0,0.05)'
      }}>
        <h2>Why am I seeing this?</h2>
        <p>The demo page component could not be loaded. This could be due to:</p>
        <ul style={{ textAlign: 'left', maxWidth: '500px', margin: '0 auto' }}>
          <li>A temporary network issue</li>
          <li>A module loading error</li>
          <li>A configuration problem</li>
        </ul>
        <p style={{ marginTop: '20px' }}>Please refresh the page or try again later.</p>
      </div>
      
      <div style={{ marginTop: '40px' }}>
        <a href="/" style={{
          display: 'inline-block',
          padding: '10px 20px',
          backgroundColor: '#4B74AA',
          color: 'white',
          borderRadius: '4px',
          textDecoration: 'none'
        }}>
          Return to Home
        </a>
      </div>
    </div>
  );
};

export default SimpleDemoPageFallback;
