import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import SimplifiedPreview from '../components/preview/SimplifiedPreview';
import EnhancedPreviewNew from '../components/preview/EnhancedPreviewNew';
import { supabase } from '../lib/supabase';
import { mapDatabaseToPreview } from '../utils/configMapping';

/**
 * SimplePreviewPage Component
 *
 * This component renders a preview of the attorney's agent with the specified configuration.
 * It accepts URL parameters for all configuration options and passes them to the preview component.
 *
 * @returns {JSX.Element} The SimplePreviewPage component
 */
const SimplePreviewPage = () => {
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [config, setConfig] = useState({
    // Firm details
    firmName: 'Your Law Firm',
    titleText: '',  // Add titleText field
    attorneyName: 'Your Name',
    practiceAreas: [],
    state: '',
    practiceDescription: "Your AI legal assistant is ready to help",

    // Visual customization
    primaryColor: '#4B74AA',
    secondaryColor: '#2C3E50',
    buttonColor: '#D85722',
    backgroundColor: '#1a1a1a',
    backgroundOpacity: 0.9,
    buttonText: 'Start Consultation',
    buttonOpacity: 1,
    practiceAreaBackgroundOpacity: 0.1,
    textBackgroundColor: '#634C38',

    // Content
    welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?",
    informationGathering: "Tell me about your situation, and I'll help find the right solution for you.",

    // Assets
    logoUrl: '/PRIMARY CLEAR.png',
    mascot: '/PRIMARY CLEAR.png',

    // Vapi configuration
    vapiInstructions: '',
    vapiContext: '',

    // Theme
    theme: 'dark',

    // Preview type
    useEnhancedPreview: false
  });

  // Load attorney data from Supabase based on subdomain
  const loadAttorneyFromSupabase = async (subdomain) => {
    try {
      console.log('SimplePreviewPage: Loading attorney data from Supabase for subdomain:', subdomain);

      const { data, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('subdomain', subdomain)
        .single();

      if (error) {
        console.error('SimplePreviewPage: Error loading attorney data from Supabase:', error);
        console.error('SimplePreviewPage: Supabase error details:', error);
        return null;
      }

      if (!data) {
        console.error('SimplePreviewPage: No attorney found with subdomain:', subdomain);
        return null;
      }

      console.log('SimplePreviewPage: Successfully loaded attorney data from Supabase:', {
        subdomain: data.subdomain,
        firm_name: data.firm_name,
        title_text: data.title_text,
        welcome_message: data.welcome_message,
        practice_description: data.practice_description
      });

      // Log the complete data object for debugging
      console.log('SimplePreviewPage: Complete attorney data from database:', data);

      return data;
    } catch (error) {
      console.error('SimplePreviewPage: Error in loadAttorneyFromSupabase:', error);
      return null;
    }
  };

  // Parse URL parameters on component mount
  useEffect(() => {
    const loadConfig = async () => {
      console.log('SimplePreviewPage: Starting config load...');
      console.log('SimplePreviewPage: URL search params:', Object.fromEntries(searchParams.entries()));

      const newConfig = { ...config };

      // Extract parameters from URL
      for (const [key, value] of searchParams.entries()) {
        if (key === 'practiceAreas') {
          try {
            newConfig[key] = JSON.parse(value);
          } catch (e) {
            newConfig[key] = value.split(',');
          }
        } else if (key === 'backgroundOpacity' || key === 'buttonOpacity' || key === 'practiceAreaBackgroundOpacity') {
          newConfig[key] = parseFloat(value);
        } else if (key === 'useEnhancedPreview' || key === 'loadFromSupabase') {
          newConfig[key] = value === 'true';
        } else {
          newConfig[key] = value;
        }
      }

      // Check if we should load data from Supabase
      if (newConfig.loadFromSupabase && newConfig.subdomain) {
        console.log('SimplePreviewPage: Loading from Supabase for subdomain:', newConfig.subdomain);

        // Try to use GlobalAttorneyCoordinator first
        let attorneyData = null;
        if (window.GlobalAttorneyCoordinator) {
          try {
            console.log('SimplePreviewPage: Using GlobalAttorneyCoordinator');
            const coordinator = window.GlobalAttorneyCoordinator;

            // Register as preview context
            await coordinator.registerContext('simple-preview', 'preview', {
              canUpdate: false,
              canSync: false,
              realTimeUpdates: false
            });

            // Get attorney data from coordinator
            attorneyData = coordinator.getAttorney();

            if (!attorneyData) {
              // Initialize coordinator if needed
              await coordinator.initialize('simple-preview');
              attorneyData = coordinator.getAttorney();
            }

            console.log('SimplePreviewPage: Attorney data from GlobalAttorneyCoordinator:', attorneyData?.id);
          } catch (error) {
            console.warn('SimplePreviewPage: Error using GlobalAttorneyCoordinator:', error);
          }
        }

        // Fallback to direct Supabase loading
        if (!attorneyData) {
          console.log('SimplePreviewPage: Falling back to direct Supabase loading');
          attorneyData = await loadAttorneyFromSupabase(newConfig.subdomain);
        }

        if (attorneyData) {
          console.log('SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...');

          // Map database fields to preview properties
          const previewConfig = mapDatabaseToPreview(attorneyData);

          console.log('SimplePreviewPage: Mapped preview config:', {
            firmName: previewConfig.firmName,
            titleText: previewConfig.titleText,
            welcomeMessage: previewConfig.welcomeMessage,
            practiceDescription: previewConfig.practiceDescription
          });

          // Store original config for comparison
          const originalConfig = { ...newConfig };

          // Merge the preview config with the URL parameters
          // Database values should take precedence for consistency
          Object.assign(newConfig, previewConfig);

          // Add Vapi-specific fields
          newConfig.vapiInstructions = attorneyData.vapi_instructions || '';
          newConfig.vapiContext = attorneyData.vapi_context || '';
          newConfig.vapi_assistant_id = attorneyData.vapi_assistant_id;

          console.log('SimplePreviewPage: Config before database merge:', originalConfig);
          console.log('SimplePreviewPage: Config after database merge:', newConfig);
          console.log('SimplePreviewPage: Key fields from database:', {
            firm_name: attorneyData.firm_name,
            title_text: attorneyData.title_text,
            welcome_message: attorneyData.welcome_message
          });
        } else {
          console.warn('SimplePreviewPage: No attorney data found for subdomain:', newConfig.subdomain);
        }
      } else {
        console.log('SimplePreviewPage: Not loading from Supabase. loadFromSupabase:', newConfig.loadFromSupabase, 'subdomain:', newConfig.subdomain);
      }

      console.log('SimplePreviewPage: Final config:', newConfig);
      console.log('SimplePreviewPage: Setting config with firm name:', newConfig.firmName);
      console.log('SimplePreviewPage: Setting config with welcome message:', newConfig.welcomeMessage);

      setConfig(newConfig);
      setIsLoading(false);

      // Force a re-render by updating the key
      if (newConfig.loadFromSupabase && newConfig.subdomain) {
        console.log('SimplePreviewPage: Config updated from database, forcing re-render');
      }
    };

    loadConfig();
  }, [searchParams]);

  // Listen for messages from parent window (for iframe communication)
  useEffect(() => {
    const handleMessage = (event) => {
      console.log('🎯 [SimplePreviewPage] Received message from parent:', event.data);

      // Handle both message types for compatibility
      if (event.data && (event.data.type === 'UPDATE_PREVIEW_CONFIG' || event.data.type === 'PREVIEW_CONFIG_UPDATE')) {
        console.log('🎯 [SimplePreviewPage] Processing config update from parent:', event.data.config);
        setConfig(prevConfig => ({ ...prevConfig, ...event.data.config }));
        console.log('🎯 [SimplePreviewPage] Config updated successfully');

        // Send response back to parent for PREVIEW_CONFIG_UPDATE messages
        if (event.data.type === 'PREVIEW_CONFIG_UPDATE' && window !== window.parent) {
          try {
            window.parent.postMessage({
              type: 'PREVIEW_CONFIG_UPDATE_RESPONSE',
              success: true,
              timestamp: event.data.timestamp
            }, '*');
            console.log('🎯 [SimplePreviewPage] Sent response back to parent');
          } catch (e) {
            console.error('🚨 [SimplePreviewPage] Error sending response to parent:', e);
          }
        }
      }
    };

    window.addEventListener('message', handleMessage);

    // Send ready message to parent with retry mechanism
    if (window !== window.parent) {
      const sendReadyMessage = () => {
        try {
          window.parent.postMessage({ type: 'PREVIEW_READY' }, '*');
          console.log('🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent');
        } catch (e) {
          console.error('🚨 [SimplePreviewPage] Error sending PREVIEW_READY message to parent:', e);
        }
      };

      // Send immediately
      sendReadyMessage();

      // Also send after a short delay to ensure parent is ready
      setTimeout(sendReadyMessage, 100);
      setTimeout(sendReadyMessage, 500);
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  if (isLoading) {
    return (
      <div className="simple-preview-page" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <div>Loading attorney information...</div>
      </div>
    );
  }

  return (
    <div className="simple-preview-page">
      {config.useEnhancedPreview ? (
        <EnhancedPreviewNew
          firmName={config.firmName}
          titleText={config.titleText}
          attorneyName={config.attorneyName}
          practiceAreas={config.practiceAreas}
          state={config.state}
          practiceDescription={config.practiceDescription}
          primaryColor={config.primaryColor}
          secondaryColor={config.secondaryColor}
          backgroundColor={config.backgroundColor}
          backgroundOpacity={config.backgroundOpacity}
          buttonText={config.buttonText}
          buttonOpacity={config.buttonOpacity}
          practiceAreaBackgroundOpacity={config.practiceAreaBackgroundOpacity}
          textBackgroundColor={config.textBackgroundColor}
          welcomeMessage={config.welcomeMessage}
          informationGathering={config.informationGathering}
          logoUrl={config.logoUrl}
          mascot={config.mascot}
          vapiInstructions={config.vapiInstructions}
          vapiContext={config.vapiContext}
          theme={config.theme}
          subdomain={config.subdomain}
          vapiAssistantId={config.vapi_assistant_id}
        />
      ) : (
        <SimplifiedPreview
          firmName={config.firmName}
          titleText={config.titleText}
          primaryColor={config.primaryColor}
          secondaryColor={config.secondaryColor}
          buttonColor={config.buttonColor}
          backgroundColor={config.backgroundColor}
          backgroundOpacity={config.backgroundOpacity}
          practiceDescription={config.practiceDescription}
          welcomeMessage={config.welcomeMessage}
          informationGathering={config.informationGathering}
          theme={config.theme}
          logoUrl={config.logoUrl}
          buttonText={config.buttonText}
          mascot={config.mascot || config.buttonImageUrl || config.logoUrl}
          vapiInstructions={config.vapiInstructions}
          vapiContext={config.vapiContext}
          buttonOpacity={config.buttonOpacity}
          practiceAreaBackgroundOpacity={config.practiceAreaBackgroundOpacity}
          textBackgroundColor={config.textBackgroundColor}
          vapi_assistant_id={config.vapi_assistant_id}
          subdomain={config.subdomain}
        />
      )}
    </div>
  );
};

export default SimplePreviewPage;
