import React, { useState, useEffect } from 'react';
import SubdomainEditor from '../components/dashboard/SubdomainEditor';
import { supabase } from '../lib/supabase';
import './SubdomainEditorDemo.css';

const SubdomainEditorDemo = () => {
  const [mockAttorney, setMockAttorney] = useState({
    id: 'demo-attorney-123',
    subdomain: 'attorney-71550',
    firm_name: 'Camp Lakebottom'
  });

  const [updateLog, setUpdateLog] = useState([]);

  const handleSubdomainUpdate = async (data) => {
    console.log('Subdomain update requested:', data);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Update mock attorney
    setMockAttorney(prev => ({
      ...prev,
      ...data
    }));

    // Add to update log
    setUpdateLog(prev => [
      ...prev,
      {
        timestamp: new Date().toLocaleTimeString(),
        action: 'Subdomain Updated',
        data: data
      }
    ]);
  };

  return (
    <div className="subdomain-demo">
      <div className="demo-container">
        <h1>Subdomain Editor Demo</h1>
        <p>This demonstrates the new subdomain availability checker and editor.</p>

        <div className="demo-section">
          <h2>Current Attorney Data</h2>
          <div className="attorney-info">
            <div><strong>ID:</strong> {mockAttorney.id}</div>
            <div><strong>Firm Name:</strong> {mockAttorney.firm_name}</div>
            <div><strong>Current Subdomain:</strong> {mockAttorney.subdomain}</div>
            <div><strong>Current URL:</strong> https://{mockAttorney.subdomain}.legalscout.net</div>
          </div>
        </div>

        <div className="demo-section">
          <h2>Subdomain Editor</h2>
          <div className="editor-container">
            <SubdomainEditor
              currentSubdomain={mockAttorney.subdomain}
              firmName={mockAttorney.firm_name}
              onUpdate={handleSubdomainUpdate}
              disabled={false}
            />
          </div>
        </div>

        <div className="demo-section">
          <h2>Update Log</h2>
          <div className="update-log">
            {updateLog.length === 0 ? (
              <p>No updates yet. Try changing the subdomain above.</p>
            ) : (
              updateLog.map((entry, index) => (
                <div key={index} className="log-entry">
                  <span className="timestamp">{entry.timestamp}</span>
                  <span className="action">{entry.action}</span>
                  <span className="data">{JSON.stringify(entry.data)}</span>
                </div>
              ))
            )}
          </div>
        </div>

        <div className="demo-section">
          <h2>Features Demonstrated</h2>
          <ul>
            <li>✅ Real-time subdomain availability checking</li>
            <li>✅ Automatic suggestion based on firm name</li>
            <li>✅ Input validation (length, format, special characters)</li>
            <li>✅ Visual feedback for availability status</li>
            <li>✅ Preview of the final URL</li>
            <li>✅ Warning about URL changes breaking existing links</li>
            <li>✅ Edit button that opens a dedicated modal interface</li>
            <li>✅ Clean, professional UI that matches the dashboard</li>
          </ul>
        </div>

        <div className="demo-section">
          <h2>Business Benefits</h2>
          <ul>
            <li>🎯 <strong>Scarcity Creation:</strong> Unique URLs become valuable assets</li>
            <li>🔒 <strong>Brand Protection:</strong> Prevents subdomain squatting</li>
            <li>⚡ <strong>Instant Feedback:</strong> Users know immediately if their desired subdomain is available</li>
            <li>🎨 <strong>Professional Appearance:</strong> Clean, branded URLs for attorneys</li>
            <li>📈 <strong>SEO Benefits:</strong> Memorable, keyword-rich subdomains</li>
            <li>🔗 <strong>Link Stability:</strong> Clear warning about URL changes</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SubdomainEditorDemo;
