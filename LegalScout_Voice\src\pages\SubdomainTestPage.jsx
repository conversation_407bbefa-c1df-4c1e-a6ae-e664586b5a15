import React, { useEffect, useState } from 'react';
import { getCurrentSubdomain } from '../utils/subdomainTester';
import { getAttorneyConfigAsync } from '../config/attorneys';
import './SubdomainTestPage.css';

/**
 * Test page to verify subdomain functionality
 */
const SubdomainTestPage = () => {
  const [subdomain, setSubdomain] = useState('');
  const [attorneyProfile, setAttorneyProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Get current subdomain
        const currentSubdomain = getCurrentSubdomain();
        setSubdomain(currentSubdomain);

        // Load attorney profile
        const profile = await getAttorneyConfigAsync(currentSubdomain);
        setAttorneyProfile(profile);

        setLoading(false);
      } catch (err) {
        console.error('Error loading subdomain data:', err);
        setError(err.message);
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return <div className="loading">Loading subdomain data...</div>;
  }

  if (error) {
    return <div className="error">Error: {error}</div>;
  }

  return (
    <div className="subdomain-test-page">
      <h1>Subdomain Test Page</h1>

      <div className="info-section">
        <h2>Current Subdomain</h2>
        <p><strong>Subdomain:</strong> {subdomain || 'default'}</p>
        <p><strong>Is Attorney Subdomain:</strong> {subdomain !== 'default' && subdomain !== 'www' ? 'Yes' : 'No'}</p>
      </div>

      {attorneyProfile && (
        <div className="profile-section">
          <h2>Attorney Profile</h2>
          <div className="profile-details">
            <p><strong>Firm Name:</strong> {attorneyProfile.firmName}</p>
            <p><strong>Vapi Assistant ID:</strong> {attorneyProfile.vapi_assistant_id || 'Not set'}</p>
            <p><strong>Logo URL:</strong> {attorneyProfile.logo || 'Not set'}</p>
            <p><strong>Mascot URL:</strong> {attorneyProfile.mascot || 'Not set'}</p>
            <p><strong>Practice Areas:</strong> {attorneyProfile.practiceAreas && attorneyProfile.practiceAreas.length > 0 ? attorneyProfile.practiceAreas.join(', ') : 'None specified'}</p>

            <h3>Vapi Configuration</h3>
            <p><strong>Instructions:</strong> {attorneyProfile.vapiInstructions || 'Not set'}</p>
            <p><strong>Context:</strong> {attorneyProfile.vapiContext || 'Not set'}</p>
          </div>

          <h3>Raw Profile Data</h3>
          <pre>{JSON.stringify(attorneyProfile, null, 2)}</pre>
        </div>
      )}

      <div className="navigation-section">
        <h2>Navigation Test</h2>
        <p>
          The navigation bar above should {subdomain !== 'default' && subdomain !== 'www' ? 'hide' : 'show'} the Home and Agent buttons
          if you're on {subdomain !== 'default' && subdomain !== 'www' ? 'an attorney subdomain' : 'the main site'}.
        </p>
      </div>
    </div>
  );
};

export default SubdomainTestPage;
