import React, { useState } from 'react';
import SimpleVapiCallButton from '../components/SimpleVapiCallButton';
import VapiCall from '../components/VapiCall';
import VapiCallSimplified from '../components/VapiCallSimplified';
import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';

/**
 * Vapi Implementation Comparison Test
 *
 * This page allows you to test three implementations side by side:
 * 1. Simple implementation (same pattern as working Official Vapi SDK Test)
 * 2. New Simplified implementation (official Vapi Web SDK pattern)
 * 3. Complex implementation (current LegalScout pattern that's failing)
 */
const VapiComparisonTest = () => {
  const [complexCallActive, setComplexCallActive] = useState(false);
  const [simplifiedCallActive, setSimplifiedCallActive] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
    console.log(`[VapiComparison] ${message}`);
  };

  const startComplexCall = () => {
    addLog('Starting complex call (current LegalScout pattern)...', 'info');
    setComplexCallActive(true);
  };

  const endComplexCall = (data) => {
    addLog('Complex call ended', 'info');
    setComplexCallActive(false);
  };

  const startSimplifiedCall = () => {
    addLog('Starting simplified call (official Vapi Web SDK pattern)...', 'info');
    setSimplifiedCallActive(true);
  };

  const endSimplifiedCall = (data) => {
    addLog('Simplified call ended', 'info');
    setSimplifiedCallActive(false);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      maxWidth: '1200px',
      margin: '0 auto'
    }}>
      <h1>🔧 Vapi Implementation Comparison Test</h1>
      <p style={{ marginBottom: '30px', color: '#666' }}>
        This page compares the working simple implementation with the failing complex implementation.
      </p>

      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr 1fr',
        gap: '20px',
        marginBottom: '40px'
      }}>
        {/* Simple Implementation */}
        <div style={{
          padding: '30px',
          border: '2px solid #27ae60',
          borderRadius: '10px',
          backgroundColor: '#f8fff8'
        }}>
          <h2 style={{ color: '#27ae60', marginBottom: '20px' }}>
            ✅ Simple Implementation (Working)
          </h2>
          <p style={{ marginBottom: '20px', fontSize: '14px', color: '#666' }}>
            Uses the same pattern as the working "Official Vapi SDK Test":
          </p>
          <ul style={{ fontSize: '12px', color: '#666', marginBottom: '20px' }}>
            <li>Direct HTML script tag loading</li>
            <li>window.vapiSDK.run() pattern</li>
            <li>No complex React state management</li>
            <li>Minimal abstraction layers</li>
          </ul>
          
          <SimpleVapiCallButton
            assistantId={DEFAULT_ASSISTANT_ID}
            buttonText="Test Simple Call"
            onCallStart={() => addLog('✅ Simple call started successfully!', 'success')}
            onCallEnd={() => addLog('📞 Simple call ended', 'info')}
            onError={(error) => addLog(`❌ Simple call error: ${error.message || error}`, 'error')}
          />
        </div>

        {/* New Simplified Implementation */}
        <div style={{
          padding: '30px',
          border: '2px solid #3498db',
          borderRadius: '10px',
          backgroundColor: '#f0f8ff'
        }}>
          <h2 style={{ color: '#3498db', marginBottom: '20px' }}>
            🎯 New Simplified Implementation
          </h2>
          <p style={{ marginBottom: '20px', fontSize: '14px', color: '#666' }}>
            Official Vapi Web SDK pattern (~200 lines):
          </p>
          <ul style={{ fontSize: '12px', color: '#666', marginBottom: '20px' }}>
            <li>Direct Vapi Web SDK import</li>
            <li>new Vapi(apiKey) pattern</li>
            <li>Simple event listeners</li>
            <li>Built-in error handling</li>
          </ul>

          <button
            onClick={startSimplifiedCall}
            disabled={simplifiedCallActive}
            style={{
              padding: '15px 30px',
              fontSize: '18px',
              fontWeight: 'bold',
              color: 'white',
              background: simplifiedCallActive
                ? 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)'
                : 'linear-gradient(135deg, #3498db 0%, #2980b9 100%)',
              border: 'none',
              borderRadius: '50px',
              cursor: simplifiedCallActive ? 'not-allowed' : 'pointer',
              opacity: simplifiedCallActive ? 0.6 : 1,
              transition: 'all 0.3s ease',
              boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
              minWidth: '200px',
              marginBottom: '20px'
            }}
          >
            {simplifiedCallActive ? '🔄 Call Active...' : '🎯 Test Simplified Call'}
          </button>

          {simplifiedCallActive && (
            <div style={{ marginTop: '20px' }}>
              <VapiCallSimplified
                onEndCall={endSimplifiedCall}
                subdomain="default"
                assistantId={DEFAULT_ASSISTANT_ID}
                showControls={true}
                showMessages={true}
              />
            </div>
          )}
        </div>

        {/* Complex Implementation */}
        <div style={{
          padding: '30px',
          border: '2px solid #e74c3c',
          borderRadius: '10px',
          backgroundColor: '#fff8f8'
        }}>
          <h2 style={{ color: '#e74c3c', marginBottom: '20px' }}>
            ❌ Complex Implementation (Failing)
          </h2>
          <p style={{ marginBottom: '20px', fontSize: '14px', color: '#666' }}>
            Current LegalScout "Get Started" pattern:
          </p>
          <ul style={{ fontSize: '12px', color: '#666', marginBottom: '20px' }}>
            <li>React SDK with npm package import</li>
            <li>useVapiCall hook with complex state</li>
            <li>Multiple abstraction layers</li>
            <li>Async loading and timing dependencies</li>
          </ul>

          <button
            onClick={startComplexCall}
            disabled={complexCallActive}
            style={{
              padding: '15px 30px',
              fontSize: '18px',
              fontWeight: 'bold',
              color: 'white',
              background: complexCallActive 
                ? 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)'
                : 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
              border: 'none',
              borderRadius: '50px',
              cursor: complexCallActive ? 'not-allowed' : 'pointer',
              opacity: complexCallActive ? 0.6 : 1,
              transition: 'all 0.3s ease',
              boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
              minWidth: '200px',
              marginBottom: '20px'
            }}
          >
            {complexCallActive ? '🔄 Call Active...' : '🎤 Test Complex Call'}
          </button>

          {complexCallActive && (
            <div style={{ marginTop: '20px' }}>
              <VapiCall
                onEndCall={endComplexCall}
                subdomain="default"
                assistantId={DEFAULT_ASSISTANT_ID}
                forceDefaultAssistant={true}
                showDebugPanel={false}
              />
            </div>
          )}
        </div>
      </div>

      {/* Logs Section */}
      <div style={{
        padding: '20px',
        border: '1px solid #ddd',
        borderRadius: '10px',
        backgroundColor: '#f9f9f9'
      }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '15px'
        }}>
          <h3>📋 Test Logs</h3>
          <button 
            onClick={clearLogs}
            style={{
              padding: '5px 15px',
              background: '#95a5a6',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Clear Logs
          </button>
        </div>
        
        <div style={{
          height: '200px',
          overflow: 'auto',
          backgroundColor: '#000',
          color: '#00ff00',
          padding: '10px',
          borderRadius: '5px',
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          {logs.length === 0 ? (
            <div style={{ color: '#666' }}>No logs yet. Try testing both implementations above.</div>
          ) : (
            logs.map((log, index) => (
              <div 
                key={index} 
                style={{ 
                  color: log.type === 'error' ? '#ff6b6b' : 
                         log.type === 'success' ? '#51cf66' : '#00ff00',
                  marginBottom: '2px'
                }}
              >
                [{log.timestamp}] {log.message}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Analysis Section */}
      <div style={{
        marginTop: '40px',
        padding: '20px',
        border: '1px solid #3498db',
        borderRadius: '10px',
        backgroundColor: '#f0f8ff'
      }}>
        <h3 style={{ color: '#3498db', marginBottom: '15px' }}>🔍 Analysis</h3>
        
        <h4>Expected Results:</h4>
        <ul style={{ marginBottom: '20px' }}>
          <li><strong>Simple Implementation:</strong> Should work reliably, same as "Official Vapi SDK Test"</li>
          <li><strong>Complex Implementation:</strong> May fail due to React state management issues</li>
        </ul>

        <h4>Key Differences:</h4>
        <table style={{ width: '100%', borderCollapse: 'collapse', fontSize: '14px' }}>
          <thead>
            <tr style={{ backgroundColor: '#ecf0f1' }}>
              <th style={{ padding: '10px', border: '1px solid #bdc3c7', textAlign: 'left' }}>Aspect</th>
              <th style={{ padding: '10px', border: '1px solid #bdc3c7', textAlign: 'left' }}>Simple (Working)</th>
              <th style={{ padding: '10px', border: '1px solid #bdc3c7', textAlign: 'left' }}>Complex (Failing)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>SDK Loading</td>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>HTML script tag</td>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>npm package + fallback CDN</td>
            </tr>
            <tr>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>API Pattern</td>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>window.vapiSDK.run()</td>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>new Vapi() + hooks</td>
            </tr>
            <tr>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>State Management</td>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>Simple local state</td>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>Complex React state + hooks</td>
            </tr>
            <tr>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>Abstraction Layers</td>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>Direct API calls</td>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>Multiple service layers</td>
            </tr>
            <tr>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>Timing Dependencies</td>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>Minimal</td>
              <td style={{ padding: '10px', border: '1px solid #bdc3c7' }}>Multiple async operations</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default VapiComparisonTest;
