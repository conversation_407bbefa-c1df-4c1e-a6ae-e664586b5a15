import React from 'react';
import VapiCallDiagnostics from '../tests/VapiCallDiagnostics';
import VapiDebugTest from '../components/VapiDebugTest';

/**
 * Test page for Vapi diagnostics
 * 
 * This page provides access to the Vapi diagnostic tools
 * to help identify and resolve call issues.
 */
const VapiTestPage = () => {
  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f8f9fa',
      padding: '20px'
    }}>
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        padding: '20px'
      }}>
        <h1>🔧 Vapi Integration Test Suite</h1>
        <p style={{ 
          fontSize: '1.1em', 
          color: '#666',
          marginBottom: '30px'
        }}>
          This page provides comprehensive testing tools to diagnose Vapi call issues.
          Use this when calls are not working to identify the root cause.
        </p>
        
        <VapiCallDiagnostics />

        <div style={{ marginTop: '40px' }}>
          <VapiDebugTest />
        </div>
        
        <div style={{ 
          marginTop: '40px',
          padding: '20px',
          backgroundColor: '#f8f9fa',
          borderRadius: '4px',
          border: '1px solid #dee2e6'
        }}>
          <h3>📋 Troubleshooting Guide</h3>
          <ul style={{ lineHeight: '1.6' }}>
            <li><strong>Environment Variables:</strong> Ensure all required Vapi environment variables are set correctly</li>
            <li><strong>SDK Loading:</strong> Verify the Vapi SDK loads properly in the browser</li>
            <li><strong>Instance Creation:</strong> Check if Vapi instances can be created with the API key</li>
            <li><strong>Event Listeners:</strong> Confirm event listeners are set up correctly</li>
            <li><strong>Assistant ID:</strong> Validate the assistant ID format and availability</li>
            <li><strong>Call Start:</strong> Test the actual call initiation process</li>
          </ul>
          
          <h4>Common Issues:</h4>
          <ul style={{ lineHeight: '1.6' }}>
            <li><strong>401 Unauthorized:</strong> Check API key is correct (not assistant ID)</li>
            <li><strong>Assistant not found:</strong> Verify assistant ID exists in Vapi</li>
            <li><strong>SDK not loading:</strong> Check network connectivity and CDN access</li>
            <li><strong>Call connects but no audio:</strong> Check voice configuration and browser permissions</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default VapiTestPage;
