import React, { useState } from 'react';
import { migrateJsonConfigToSupabase } from '../config/attorneys';
import { isSupabaseConfigured } from '../lib/supabase';

/**
 * Utility script to migrate configuration data from JSON to Supabase
 * Run this function to perform the migration
 */
export const migrateToSupabase = async () => {
  try {
    console.log('Starting migration from JSON to Supabase...');
    
    if (!isSupabaseConfigured()) {
      console.error('Supabase not configured. Please update your .env file with Supabase credentials.');
      return { success: false, message: 'Supabase not configured' };
    }
    
    // Perform the migration
    const result = await migrateJsonConfigToSupabase();
    
    if (result.success) {
      console.log('Migration successful!');
      console.log('Summary:', result.results.reduce((acc, r) => {
        acc[r.status] = (acc[r.status] || 0) + 1;
        return acc;
      }, {}));
      
      // Log detailed results
      console.log('Detailed results:');
      result.results.forEach(r => {
        console.log(`${r.subdomain}: ${r.status} ${r.success ? '✓' : '✗'} ${r.error || ''}`);
      });
    } else {
      console.error('Migration failed:', result.error || 'Unknown error');
    }
    
    return result;
  } catch (error) {
    console.error('Error during migration:', error);
    return { success: false, error: error.message };
  }
};

// Export a UI component to trigger the migration
export const MigrationButton = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);
  
  const handleMigration = async () => {
    setIsLoading(true);
    try {
      const migrationResult = await migrateToSupabase();
      setResult(migrationResult);
    } catch (error) {
      setResult({ success: false, error: error.message });
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="migration-tool">
      <h2>JSON to Supabase Migration Tool</h2>
      <button 
        onClick={handleMigration} 
        disabled={isLoading || !isSupabaseConfigured()}
      >
        {isLoading ? 'Migrating...' : 'Migrate JSON Config to Supabase'}
      </button>
      
      {!isSupabaseConfigured() && (
        <div className="error">
          Supabase not configured. Please update your .env file with Supabase credentials.
        </div>
      )}
      
      {result && (
        <div className={`result ${result.success ? 'success' : 'error'}`}>
          <h3>{result.success ? 'Migration Successful!' : 'Migration Failed'}</h3>
          {result.success ? (
            <div>
              <p>Subdomain configurations have been migrated to Supabase.</p>
              <ul>
                {result.results.map((r, i) => (
                  <li key={i}>
                    {r.subdomain}: {r.status} {r.success ? '✓' : '✗'} {r.error || ''}
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <p>Error: {result.error || 'Unknown error'}</p>
          )}
        </div>
      )}
    </div>
  );
};

export default migrateToSupabase; 