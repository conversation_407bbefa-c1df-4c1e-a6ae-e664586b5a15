-- Schema for LegalScout's Supabase Database
-- Run this in the Supabase SQL Editor to set up the required tables

-- Attorneys table
CREATE TABLE IF NOT EXISTS public.attorneys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Basic Information
  subdomain TEXT UNIQUE NOT NULL,
  firm_name TEXT NOT NULL,
  name TEXT,
  email TEXT,
  phone TEXT,
  address TEXT,
  user_id UUID REFERENCES auth.users(id),

  -- Media URLs
  logo_url TEXT,
  profile_image TEXT,

  -- Vapi Configuration
  vapi_instructions TEXT,
  vapi_context TEXT,

  -- Other Configuration
  practice_areas TEXT[] DEFAULT '{}',
  interaction_deposit_url TEXT,

  -- Configuration
  is_active BOOLEAN DEFAULT true
);

-- Add index for faster subdomain lookups
CREATE INDEX IF NOT EXISTS attorneys_subdomain_idx ON public.attorneys (subdomain);

-- Add RLS policies for attorneys
ALTER TABLE public.attorneys ENABLE ROW LEVEL SECURITY;

-- Briefs table for storing conversation briefs
CREATE TABLE IF NOT EXISTS public.briefs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Relationships
  attorney_id UUID REFERENCES public.attorneys(id),

  -- Client Information
  client_name TEXT,
  email TEXT,
  phone TEXT,

  -- Case Information
  practice_area TEXT,
  location TEXT,
  issue TEXT,
  urgency TEXT,
  noteworthy TEXT,

  -- Extended Data
  location_data JSONB,
  metadata JSONB,

  -- Status tracking
  status TEXT DEFAULT 'new',
  follow_up_date TIMESTAMP WITH TIME ZONE
);

-- Add index for attorney_id for faster lookups
CREATE INDEX IF NOT EXISTS briefs_attorney_id_idx ON public.briefs (attorney_id);

-- Add RLS policies for briefs
ALTER TABLE public.briefs ENABLE ROW LEVEL SECURITY;

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to update attorneys updated_at
CREATE TRIGGER set_attorneys_updated_at
BEFORE UPDATE ON public.attorneys
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Add trigger to update briefs updated_at
CREATE TRIGGER set_briefs_updated_at
BEFORE UPDATE ON public.briefs
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Example row-level security policies - customize based on your auth setup
CREATE POLICY "Public attorneys are viewable by everyone"
  ON public.attorneys FOR SELECT
  USING (true);

CREATE POLICY "Only admins can insert attorneys"
  ON public.attorneys FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Only admins can update attorneys"
  ON public.attorneys FOR UPDATE
  USING (auth.role() = 'authenticated');

CREATE POLICY "Briefs are viewable by associated attorney"
  ON public.briefs FOR SELECT
  USING (auth.uid()::TEXT = attorney_id::TEXT OR auth.role() = 'authenticated');

CREATE POLICY "Only authenticated users can insert briefs"
  ON public.briefs FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

COMMENT ON TABLE public.attorneys IS 'Stores attorney profiles and configuration for LegalScout';
COMMENT ON TABLE public.briefs IS 'Stores client conversation briefs associated with attorneys';