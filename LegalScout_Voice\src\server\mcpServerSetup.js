/**
 * MCP Server Setup
 *
 * This file contains the setup for the Model Context Protocol (MCP) server.
 * It configures the MCP server for Vapi integration.
 */

import { createMcpServer } from '@vapi-ai/mcp-server';
import { mcpConfig } from '../config/mcp.config';

/**
 * Create an MCP server for Vapi
 * @returns {Object} The MCP server
 */
export function createVapiMcpServer() {
  // Get Vapi API key from config - use private key for server-side operations
  const apiKey = mcpConfig.voice.vapi.secretKey ||
                process.env.VAPI_SECRET_KEY ||
                process.env.VAPI_TOKEN ||
                '6734febc-fc65-4669-93b0-929b31ff6564';

  console.log(`Using Vapi API key for MCP server: ${apiKey.substring(0, 5)}...`);

  if (!apiKey) {
    console.error('No Vapi API key found in configuration');
    throw new Error('No Vapi API key found in configuration');
  }

  // Create MCP server
  const server = createMcpServer({
    name: 'vapi-mcp-server',
    description: 'MCP server for Vapi voice AI',
    tools: [
      // Assistant tools
      {
        name: 'list_assistants_vapi-mcp-server',
        description: 'Lists all Vapi assistants',
        handler: async () => {
          const response = await fetch('https://api.vapi.ai/assistant', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to list assistants: ${response.status} ${response.statusText}`);
          }

          return await response.json();
        }
      },
      {
        name: 'get_assistant_vapi-mcp-server',
        description: 'Gets a Vapi assistant by ID',
        parameters: {
          type: 'object',
          required: ['assistantId'],
          properties: {
            assistantId: {
              type: 'string',
              description: 'ID of the assistant to get'
            }
          }
        },
        handler: async ({ assistantId }) => {
          const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            if (response.status === 404) {
              return null;
            }
            throw new Error(`Failed to get assistant: ${response.status} ${response.statusText}`);
          }

          return await response.json();
        }
      },

      // Call tools
      {
        name: 'list_calls_vapi-mcp-server',
        description: 'Lists all Vapi calls',
        handler: async () => {
          const response = await fetch('https://api.vapi.ai/call', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to list calls: ${response.status} ${response.statusText}`);
          }

          return await response.json();
        }
      },
      {
        name: 'create_call_vapi-mcp-server',
        description: 'Creates a outbound call',
        parameters: {
          type: 'object',
          properties: {
            assistantId: {
              type: 'string',
              description: 'ID of the assistant to use for the call'
            },
            customer: {
              type: 'object',
              required: ['phoneNumber'],
              properties: {
                phoneNumber: {
                  type: 'string',
                  description: 'Customer phone number'
                }
              }
            },
            phoneNumberId: {
              type: 'string',
              description: 'ID of the phone number to use for the call'
            },
            scheduledAt: {
              type: 'string',
              description: 'ISO datetime string for when the call should be scheduled (e.g. "2025-03-25T22:39:27.771Z")'
            }
          }
        },
        handler: async (params) => {
          const response = await fetch('https://api.vapi.ai/call', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(params)
          });

          if (!response.ok) {
            throw new Error(`Failed to create call: ${response.status} ${response.statusText}`);
          }

          return await response.json();
        }
      },
      {
        name: 'get_call_vapi-mcp-server',
        description: 'Gets details of a specific call',
        parameters: {
          type: 'object',
          required: ['callId'],
          properties: {
            callId: {
              type: 'string',
              description: 'ID of the call to get'
            }
          }
        },
        handler: async ({ callId }) => {
          const response = await fetch(`https://api.vapi.ai/call/${callId}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            if (response.status === 404) {
              return null;
            }
            throw new Error(`Failed to get call: ${response.status} ${response.statusText}`);
          }

          return await response.json();
        }
      },

      // Phone number tools
      {
        name: 'list_phone_numbers_vapi-mcp-server',
        description: 'Lists all Vapi phone numbers',
        handler: async () => {
          const response = await fetch('https://api.vapi.ai/phone-number', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to list phone numbers: ${response.status} ${response.statusText}`);
          }

          return await response.json();
        }
      },
      {
        name: 'get_phone_number_vapi-mcp-server',
        description: 'Gets details of a specific phone number',
        parameters: {
          type: 'object',
          required: ['phoneNumberId'],
          properties: {
            phoneNumberId: {
              type: 'string',
              description: 'ID of the phone number to get'
            }
          }
        },
        handler: async ({ phoneNumberId }) => {
          const response = await fetch(`https://api.vapi.ai/phone-number/${phoneNumberId}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            if (response.status === 404) {
              return null;
            }
            throw new Error(`Failed to get phone number: ${response.status} ${response.statusText}`);
          }

          return await response.json();
        }
      },

      // Tool tools
      {
        name: 'list_tools_vapi-mcp-server',
        description: 'Lists all Vapi tools',
        handler: async () => {
          const response = await fetch('https://api.vapi.ai/tool', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to list tools: ${response.status} ${response.statusText}`);
          }

          return await response.json();
        }
      },
      {
        name: 'get_tool_vapi-mcp-server',
        description: 'Gets details of a specific tool',
        parameters: {
          type: 'object',
          required: ['toolId'],
          properties: {
            toolId: {
              type: 'string',
              description: 'ID of the tool to get'
            }
          }
        },
        handler: async ({ toolId }) => {
          const response = await fetch(`https://api.vapi.ai/tool/${toolId}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            if (response.status === 404) {
              return null;
            }
            throw new Error(`Failed to get tool: ${response.status} ${response.statusText}`);
          }

          return await response.json();
        }
      }
    ]
  });

  return server;
}

// Export the server
export default createVapiMcpServer;
