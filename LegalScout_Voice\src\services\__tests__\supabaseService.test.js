import { supabase } from '../../lib/supabase';
import { supabaseService } from '../supabaseService';

// Mock the supabase client
jest.mock('../../lib/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    single: jest.fn()
  }
}));

describe('SupabaseService', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
  });
  
  describe('storeBrief', () => {
    test('should store brief data in Supabase', async () => {
      // Mock the successful insertion
      supabase.single.mockResolvedValue({
        data: { id: '123', attorney_id: 'attorney1', practice_area: 'Family Law' },
        error: null
      });
      
      // Test data
      const briefData = {
        name: '<PERSON>',
        practiceArea: 'Family Law',
        location: { address: 'New York, NY', lat: 40.7128, lng: -74.0060 },
        issue: 'Divorce',
        urgency: 'High',
        noteworthy: 'Important details'
      };
      
      const attorneyId = 'attorney1';
      
      // Call the service
      const result = await supabaseService.storeBrief(briefData, attorneyId);
      
      // Verify Supabase was called correctly
      expect(supabase.from).toHaveBeenCalledWith('briefs');
      expect(supabase.insert).toHaveBeenCalledWith({
        attorney_id: 'attorney1',
        client_name: 'John Doe',
        practice_area: 'Family Law',
        location: 'New York, NY',
        issue: 'Divorce',
        urgency: 'High',
        noteworthy: 'Important details',
        location_data: { address: 'New York, NY', lat: 40.7128, lng: -74.0060 },
        created_at: expect.any(String),
        status: 'new'
      });
      
      // Verify the result
      expect(result).toEqual({ id: '123', attorney_id: 'attorney1', practice_area: 'Family Law' });
    });
    
    test('should handle errors when storing brief data', async () => {
      // Mock the error
      supabase.single.mockResolvedValue({
        data: null,
        error: new Error('Database error')
      });
      
      // Test data
      const briefData = {
        name: 'John Doe',
        practiceArea: 'Family Law'
      };
      
      const attorneyId = 'attorney1';
      
      // Expect the function to throw
      await expect(supabaseService.storeBrief(briefData, attorneyId))
        .rejects.toThrow('Database error');
      
      // Verify Supabase was called
      expect(supabase.from).toHaveBeenCalledWith('briefs');
    });
    
    test('should handle missing data with defaults', async () => {
      // Mock the successful insertion
      supabase.single.mockResolvedValue({
        data: { id: '123', attorney_id: 'attorney1' },
        error: null
      });
      
      // Test with minimal data
      const briefData = {};
      const attorneyId = 'attorney1';
      
      // Call the service
      await supabaseService.storeBrief(briefData, attorneyId);
      
      // Verify defaults were applied
      expect(supabase.insert).toHaveBeenCalledWith(expect.objectContaining({
        client_name: 'Anonymous',
        practice_area: 'General',
        location: 'Unknown',
        issue: 'Not specified',
        urgency: 'Medium',
        noteworthy: ''
      }));
    });
  });
  
  describe('getAttorneyBySubdomain', () => {
    test('should fetch attorney by subdomain', async () => {
      // Mock the response
      supabase.single.mockResolvedValue({
        data: { id: 'attorney1', subdomain: 'testfirm', name: 'Test Firm' },
        error: null
      });
      
      // Call the service
      const result = await supabaseService.getAttorneyBySubdomain('testfirm');
      
      // Verify Supabase was called correctly
      expect(supabase.from).toHaveBeenCalledWith('attorneys');
      expect(supabase.eq).toHaveBeenCalledWith('subdomain', 'testfirm');
      
      // Verify the result
      expect(result).toEqual({ id: 'attorney1', subdomain: 'testfirm', name: 'Test Firm' });
    });
    
    test('should handle errors when fetching attorney', async () => {
      // Mock the error
      supabase.single.mockResolvedValue({
        data: null,
        error: new Error('Attorney not found')
      });
      
      // Expect the function to throw
      await expect(supabaseService.getAttorneyBySubdomain('nonexistent'))
        .rejects.toThrow('Attorney not found');
      
      // Verify Supabase was called
      expect(supabase.from).toHaveBeenCalledWith('attorneys');
    });
  });
  
  describe('getAttorneyBriefs', () => {
    test('should fetch briefs for an attorney', async () => {
      // Mock the response
      supabase.order.mockResolvedValue({
        data: [
          { id: 'brief1', attorney_id: 'attorney1', practice_area: 'Family Law' },
          { id: 'brief2', attorney_id: 'attorney1', practice_area: 'Estate Planning' }
        ],
        error: null
      });
      
      // Call the service
      const result = await supabaseService.getAttorneyBriefs('attorney1');
      
      // Verify Supabase was called correctly
      expect(supabase.from).toHaveBeenCalledWith('briefs');
      expect(supabase.eq).toHaveBeenCalledWith('attorney_id', 'attorney1');
      expect(supabase.order).toHaveBeenCalledWith('created_at', { ascending: false });
      
      // Verify the result
      expect(result).toEqual([
        { id: 'brief1', attorney_id: 'attorney1', practice_area: 'Family Law' },
        { id: 'brief2', attorney_id: 'attorney1', practice_area: 'Estate Planning' }
      ]);
    });
    
    test('should handle errors when fetching briefs', async () => {
      // Mock the error
      supabase.order.mockResolvedValue({
        data: null,
        error: new Error('Failed to fetch briefs')
      });
      
      // Expect the function to throw
      await expect(supabaseService.getAttorneyBriefs('attorney1'))
        .rejects.toThrow('Failed to fetch briefs');
      
      // Verify Supabase was called
      expect(supabase.from).toHaveBeenCalledWith('briefs');
    });
  });
  
  describe('configureVapiRecording', () => {
    test('should configure Vapi recording settings', () => {
      // Create a mock Vapi instance
      const mockVapi = {
        configure: jest.fn().mockReturnThis()
      };
      
      // Call the service
      const result = supabaseService.configureVapiRecording(mockVapi, 'attorney1');
      
      // Verify Vapi was configured correctly
      expect(mockVapi.configure).toHaveBeenCalledWith({
        recordingEnabled: true,
        recordingProvider: 'supabase',
        recordingMetadata: {
          attorneyId: 'attorney1',
          source: 'legalscout-web'
        }
      });
      
      // Verify the result is the Vapi instance
      expect(result).toBe(mockVapi);
    });
    
    test('should handle errors in configuration', () => {
      // Create a mock Vapi instance that throws an error
      const mockVapi = {
        configure: jest.fn().mockImplementation(() => {
          throw new Error('Configuration error');
        })
      };
      
      // Call the service - should not throw
      const result = supabaseService.configureVapiRecording(mockVapi, 'attorney1');
      
      // Verify the result is still the Vapi instance
      expect(result).toBe(mockVapi);
    });
    
    test('should handle null Vapi instance', () => {
      // Call with null
      const result = supabaseService.configureVapiRecording(null, 'attorney1');
      
      // Should return null
      expect(result).toBeNull();
    });
  });
}); 