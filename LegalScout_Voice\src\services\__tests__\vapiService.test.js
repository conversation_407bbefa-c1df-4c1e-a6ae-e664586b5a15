import { vapiService } from '../vapiService.jsx';
import { supabaseService } from '../supabaseService';
import Vapi from '@vapi-ai/web';
import { PRACTICE_AREA_KEYWORDS } from '../../constants/vapiConstants';

// Mock the @vapi-ai/web package
jest.mock('@vapi-ai/web');

// Mock the supabaseService
jest.mock('../supabaseService', () => ({
  supabaseService: {
    configureVapiRecording: jest.fn().mockImplementation((vapiInstance) => vapiInstance),
    storeBrief: jest.fn().mockResolvedValue({ id: 'brief1' }),
    getAttorneyBySubdomain: jest.fn().mockResolvedValue({ id: 'attorney1' })
  }
}));

describe('VapiService', () => {
  let mockVapiInstance;
  
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Create a mock Vapi instance
    mockVapiInstance = {
      on: jest.fn(),
      off: jest.fn(),
      removeAllListeners: jest.fn(),
      start: jest.fn().mockResolvedValue(true),
      stop: jest.fn(),
      send: jest.fn().mockResolvedValue(true),
      configure: jest.fn()
    };
    
    // Mock the Vapi constructor to return our mock instance
    Vapi.mockImplementation(() => mockVapiInstance);
  });
  
  describe('initialize', () => {
    test('should initialize Vapi with API key', () => {
      const apiKey = 'test-api-key';
      const subdomain = 'testfirm';
      
      const result = vapiService.initialize(apiKey, subdomain);
      
      // Verify Vapi constructor was called
      expect(Vapi).toHaveBeenCalledWith(apiKey);
      
      // Verify supabaseService was called to configure recording
      expect(supabaseService.configureVapiRecording).toHaveBeenCalledWith(mockVapiInstance, subdomain);
      
      // Verify the result is the Vapi instance
      expect(result).toBe(mockVapiInstance);
    });
    
    test('should throw error if no API key is provided', () => {
      expect(() => vapiService.initialize(null)).toThrow('Missing Vapi API key');
    });
    
    test('should not configure recording for default subdomain', () => {
      vapiService.initialize('test-api-key', 'default');
      expect(supabaseService.configureVapiRecording).not.toHaveBeenCalled();
    });
  });
  
  describe('setupEventListeners', () => {
    test('should set up all event listeners with callbacks', () => {
      const callbacks = {
        onCallStarted: jest.fn(),
        onCallEnd: jest.fn(),
        onError: jest.fn(),
        onVolumeLevel: jest.fn(),
        onSpeechStart: jest.fn(),
        onSpeechEnd: jest.fn(),
        onMessage: jest.fn(),
        onToolExecution: jest.fn()
      };
      
      vapiService.setupEventListeners(mockVapiInstance, callbacks);
      
      // Verify all event listeners were set up
      expect(mockVapiInstance.on).toHaveBeenCalledWith('call-started', callbacks.onCallStarted);
      expect(mockVapiInstance.on).toHaveBeenCalledWith('call-end', callbacks.onCallEnd);
      expect(mockVapiInstance.on).toHaveBeenCalledWith('volume-level', callbacks.onVolumeLevel);
      expect(mockVapiInstance.on).toHaveBeenCalledWith('speech-start', callbacks.onSpeechStart);
      expect(mockVapiInstance.on).toHaveBeenCalledWith('speech-end', callbacks.onSpeechEnd);
      expect(mockVapiInstance.on).toHaveBeenCalledWith('message', callbacks.onMessage);
      expect(mockVapiInstance.on).toHaveBeenCalledWith('tool-execution', callbacks.onToolExecution);
      
      // The error event has special handling, so verify it was called
      expect(mockVapiInstance.on.mock.calls.some(call => call[0] === 'error')).toBe(true);
    });
    
    test('should handle null Vapi instance', () => {
      expect(() => vapiService.setupEventListeners(null, {})).not.toThrow();
    });
    
    test('should handle missing callbacks', () => {
      expect(() => vapiService.setupEventListeners(mockVapiInstance, {})).not.toThrow();
    });
  });
  
  describe('removeEventListeners', () => {
    test('should remove all event listeners', () => {
      vapiService.removeEventListeners(mockVapiInstance);
      
      // Verify removeAllListeners was called for each event type
      const eventTypes = [
        'call-started', 
        'call-end', 
        'error', 
        'volume-level', 
        'speech-start', 
        'speech-end', 
        'message',
        'tool-execution'
      ];
      
      eventTypes.forEach(eventType => {
        expect(mockVapiInstance.removeAllListeners).toHaveBeenCalledWith(eventType);
      });
    });
    
    test('should handle null Vapi instance', () => {
      expect(() => vapiService.removeEventListeners(null)).not.toThrow();
    });
    
    test('should use off method if removeAllListeners is not available', () => {
      // Create mock instance without removeAllListeners
      const mockVapiInstanceAlt = {
        off: jest.fn()
      };
      
      vapiService.removeEventListeners(mockVapiInstanceAlt);
      
      // Verify off was called for each event type
      const eventTypes = [
        'call-started', 
        'call-end', 
        'error', 
        'volume-level', 
        'speech-start', 
        'speech-end', 
        'message',
        'tool-execution'
      ];
      
      eventTypes.forEach(eventType => {
        expect(mockVapiInstanceAlt.off).toHaveBeenCalledWith(eventType);
      });
    });
  });
  
  describe('startCall', () => {
    test('should start call with assistantId and overrides', async () => {
      const params = {
        assistantId: 'test-assistant-id',
        assistantOverrides: {
          recordingEnabled: true,
          instructions: 'Test instructions'
        },
        initialMessage: 'Hello, how can I help you?'
      };
      
      await vapiService.startCall(mockVapiInstance, params);
      
      // Verify start was called with correct parameters
      expect(mockVapiInstance.start).toHaveBeenCalledWith('test-assistant-id', {
        recordingEnabled: true,
        instructions: 'Test instructions'
      });
      
      // Verify initial message was sent
      expect(mockVapiInstance.send).toHaveBeenCalledWith({
        type: 'add-message',
        message: {
          role: 'system',
          content: 'Hello, how can I help you?'
        }
      });
    });
    
    test('should throw error if Vapi instance is null', async () => {
      await expect(vapiService.startCall(null)).rejects.toThrow('Vapi instance not initialized');
    });
    
    test('should use default assistant ID if not provided', async () => {
      await vapiService.startCall(mockVapiInstance, {});
      
      // Verify start was called with default assistant ID
      expect(mockVapiInstance.start.mock.calls[0][0]).toBeDefined();
    });
  });
  
  describe('stopCall', () => {
    test('should stop the call', () => {
      vapiService.stopCall(mockVapiInstance);
      expect(mockVapiInstance.stop).toHaveBeenCalled();
    });
    
    test('should handle null Vapi instance', () => {
      expect(() => vapiService.stopCall(null)).not.toThrow();
    });
    
    test('should handle errors', () => {
      mockVapiInstance.stop.mockImplementation(() => {
        throw new Error('Stop error');
      });
      
      expect(() => vapiService.stopCall(mockVapiInstance)).not.toThrow();
    });
  });
  
  describe('sendSystemMessage', () => {
    test('should send a system message', async () => {
      await vapiService.sendSystemMessage(mockVapiInstance, 'Test message');
      
      expect(mockVapiInstance.send).toHaveBeenCalledWith({
        type: 'add-message',
        message: {
          role: 'system',
          content: 'Test message'
        }
      });
    });
    
    test('should handle null Vapi instance', async () => {
      await expect(vapiService.sendSystemMessage(null, 'Test')).resolves.toBeUndefined();
    });
    
    test('should handle empty message', async () => {
      await expect(vapiService.sendSystemMessage(mockVapiInstance, '')).resolves.toBeUndefined();
    });
  });
  
  describe('extractLocationData', () => {
    test('should extract NY borough location', () => {
      const content = 'I live in Brooklyn and need help with a divorce.';
      const result = vapiService.extractLocationData(content);
      
      expect(result).toEqual({
        location: {
          address: 'Brooklyn',
          lat: 40.6782,
          lng: -73.9442
        }
      });
    });
    
    test('should extract general NY location', () => {
      const content = 'I need help with a case in New York.';
      const result = vapiService.extractLocationData(content);
      
      expect(result).toEqual({
        location: {
          address: 'New York, NY',
          lat: 40.7128,
          lng: -74.0060
        }
      });
    });
    
    test('should extract address from text', () => {
      const content = 'My address is 123 Main St, Anytown, USA.';
      const result = vapiService.extractLocationData(content);
      
      expect(result.location).toBeDefined();
      expect(result.location.address).toBe('123 Main St, Anytown, USA');
    });
    
    test('should extract coordinates from text', () => {
      const content = 'My coordinates are 40.7128, -74.0060.';
      const result = vapiService.extractLocationData(content);
      
      expect(result.location).toBeDefined();
      expect(result.location.lat).toBe(40.7128);
      expect(result.location.lng).toBe(-74.0060);
    });
    
    test('should handle empty content', () => {
      const result = vapiService.extractLocationData('');
      expect(result).toEqual({});
    });
  });
  
  describe('extractPracticeAreaFromIssue', () => {
    test('should extract practice area from issue', () => {
      // Test various issues
      expect(vapiService.extractPracticeAreaFromIssue('I need help with my divorce')).toBe('Family Law');
      expect(vapiService.extractPracticeAreaFromIssue('Writing a will')).toBe('Estate Planning');
      expect(vapiService.extractPracticeAreaFromIssue('Car accident injury')).toBe('Personal Injury');
      expect(vapiService.extractPracticeAreaFromIssue('Workplace discrimination')).toBe('Employment Law');
    });
    
    test('should return default for unrecognized issue', () => {
      expect(vapiService.extractPracticeAreaFromIssue('Something unrelated')).toBe('General Practice');
    });
    
    test('should handle empty input', () => {
      expect(vapiService.extractPracticeAreaFromIssue('')).toBe('General Practice');
      expect(vapiService.extractPracticeAreaFromIssue(null)).toBe('General Practice');
    });
  });
  
  describe('syncWithMakeWebhook', () => {
    beforeEach(() => {
      // Mock fetch API
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({ success: true })
      });
    });
    
    test('should send data to webhook URL', async () => {
      const webhookUrl = 'https://example.com/webhook';
      const data = { test: 'data' };
      
      await vapiService.syncWithMakeWebhook(webhookUrl, data);
      
      expect(global.fetch).toHaveBeenCalledWith(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
    });
    
    test('should handle empty webhook URL', async () => {
      await vapiService.syncWithMakeWebhook('', { test: 'data' });
      expect(global.fetch).not.toHaveBeenCalled();
    });
    
    test('should handle fetch errors', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Fetch error'));
      
      const result = await vapiService.syncWithMakeWebhook('https://example.com/webhook', { test: 'data' });
      expect(result).toBe(false);
    });
    
    test('should handle non-OK response', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 500
      });
      
      const result = await vapiService.syncWithMakeWebhook('https://example.com/webhook', { test: 'data' });
      expect(result).toBe(false);
    });
  });
  
  describe('storeCallData', () => {
    test('should store call data in Supabase', async () => {
      const briefData = { name: 'John Doe', practiceArea: 'Family Law' };
      const subdomain = 'testfirm';
      
      await vapiService.storeCallData(briefData, subdomain);
      
      // Verify attorney was looked up by subdomain
      expect(supabaseService.getAttorneyBySubdomain).toHaveBeenCalledWith('testfirm');
      
      // Verify brief was stored
      expect(supabaseService.storeBrief).toHaveBeenCalledWith(briefData, 'attorney1');
    });
    
    test('should handle empty brief data', async () => {
      await vapiService.storeCallData(null, 'testfirm');
      expect(supabaseService.storeBrief).not.toHaveBeenCalled();
    });
    
    test('should handle default subdomain', async () => {
      await vapiService.storeCallData({ name: 'John Doe' }, 'default');
      expect(supabaseService.storeBrief).not.toHaveBeenCalled();
    });
    
    test('should handle missing attorney', async () => {
      supabaseService.getAttorneyBySubdomain.mockResolvedValue(null);
      
      await vapiService.storeCallData({ name: 'John Doe' }, 'nonexistent');
      expect(supabaseService.storeBrief).not.toHaveBeenCalled();
    });
  });
}); 