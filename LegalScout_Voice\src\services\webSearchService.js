/**
 * Web Search Service
 * 
 * This service provides web search functionality using Google's Custom Search API.
 * It can be used as a backend for the Vapi MCP tool.
 */

// Google Custom Search API configuration
const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY || 'YOUR_GOOGLE_API_KEY';
const GOOGLE_CSE_ID = process.env.GOOGLE_CSE_ID || 'YOUR_GOOGLE_CSE_ID';

/**
 * Search the web using Google's Custom Search API
 * @param {string} query - The search query
 * @param {number} numResults - Number of results to return (default: 5)
 * @returns {Promise<Object>} - Search results
 */
export const searchWeb = async (query, numResults = 5) => {
  try {
    // Validate inputs
    if (!query) {
      throw new Error('Search query is required');
    }
    
    // Build the API URL
    const url = new URL('https://www.googleapis.com/customsearch/v1');
    url.searchParams.append('key', GOOGLE_API_KEY);
    url.searchParams.append('cx', GOOGLE_CSE_ID);
    url.searchParams.append('q', query);
    url.searchParams.append('num', Math.min(numResults, 10)); // Max 10 results per API call
    
    // Make the API request
    const response = await fetch(url.toString());
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Google API error: ${errorData.error?.message || response.statusText}`);
    }
    
    const data = await response.json();
    
    // Format the results
    const formattedResults = {
      query,
      totalResults: data.searchInformation?.totalResults || 0,
      searchTime: data.searchInformation?.searchTime || 0,
      results: data.items?.map(item => ({
        title: item.title,
        link: item.link,
        snippet: item.snippet,
        displayLink: item.displayLink,
        pagemap: {
          thumbnail: item.pagemap?.cse_thumbnail?.[0]?.src,
          metatags: {
            description: item.pagemap?.metatags?.[0]?.['og:description'] || item.snippet
          }
        }
      })) || []
    };
    
    return formattedResults;
  } catch (error) {
    console.error('Web search error:', error);
    throw error;
  }
};

/**
 * Search for legal information using specialized legal APIs
 * @param {string} query - The search query
 * @param {string} type - Type of legal search (caselaw, statute, etc.)
 * @returns {Promise<Object>} - Search results
 */
export const searchLegalInfo = async (query, type = 'caselaw') => {
  // This is a placeholder for integration with legal APIs
  // You would implement connections to services like:
  // - Caselaw Access Project API
  // - Fastcase API
  // - LexisNexis API
  
  // For now, we'll return a mock response
  return {
    query,
    type,
    results: [
      {
        title: "Mock Legal Case: Smith v. Jones",
        citation: "123 F.3d 456 (9th Cir. 2020)",
        summary: "This is a mock case summary related to your query.",
        relevance: 0.95
      }
    ]
  };
};

/**
 * Format search results for display in the UI
 * @param {Object} searchResults - Raw search results
 * @param {string} format - Format type (cards, timeline, etc.)
 * @returns {Object} - Formatted results for display
 */
export const formatSearchResultsForDisplay = (searchResults, format = 'cards') => {
  switch (format) {
    case 'cards':
      return {
        type: 'cards',
        items: searchResults.results.map(result => ({
          title: result.title,
          url: result.link,
          description: result.snippet,
          thumbnail: result.pagemap?.thumbnail
        }))
      };
      
    case 'timeline':
      // Format results as a timeline (for case law evolution)
      return {
        type: 'timeline',
        items: searchResults.results.map((result, index) => ({
          id: index,
          title: result.title,
          url: result.link,
          description: result.snippet,
          date: '2023' // This would be extracted from the result in a real implementation
        }))
      };
      
    case 'statute':
      // Format results for statute display
      return {
        type: 'statute',
        title: searchResults.results[0]?.title || 'Statute',
        sections: searchResults.results.map(result => ({
          title: result.title,
          text: result.snippet,
          url: result.link
        }))
      };
      
    default:
      return {
        type: 'default',
        items: searchResults.results
      };
  }
};

export default {
  searchWeb,
  searchLegalInfo,
  formatSearchResultsForDisplay
};
