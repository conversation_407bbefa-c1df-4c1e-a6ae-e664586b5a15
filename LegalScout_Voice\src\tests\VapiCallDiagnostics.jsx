import React, { useState, useEffect, useRef } from 'react';
import { loadVapiSDK, createVapiInstance } from '../utils/vapiLoader';
import { getVapiApiKey } from '../config/vapiConfig';
import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';

/**
 * Comprehensive Vapi Call Diagnostics Component
 * 
 * This component tests all aspects of the Vapi integration to identify
 * where the call failure is occurring. It follows the patterns from
 * MAKE_VAPI_WORK.md and tests each step individually.
 */
const VapiCallDiagnostics = () => {
  const [testResults, setTestResults] = useState({});
  const [currentTest, setCurrentTest] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [vapiInstance, setVapiInstance] = useState(null);
  const [callStatus, setCallStatus] = useState('idle');
  const [logs, setLogs] = useState([]);
  const vapiRef = useRef(null);

  // Add log entry
  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
    console.log(`[VapiDiagnostics] ${message}`);
  };

  // Update test result
  const updateTestResult = (testName, result, details = '') => {
    setTestResults(prev => ({
      ...prev,
      [testName]: { result, details, timestamp: new Date().toLocaleTimeString() }
    }));
  };

  // Test 1: Environment Variables
  const testEnvironmentVariables = async () => {
    setCurrentTest('Environment Variables');
    addLog('Testing environment variables...');

    const tests = {
      'VITE_VAPI_PUBLIC_KEY': import.meta.env.VITE_VAPI_PUBLIC_KEY,
      'VITE_VAPI_BASE_URL': import.meta.env.VITE_VAPI_BASE_URL,
      'VITE_VAPI_DEFAULT_ASSISTANT_ID': import.meta.env.VITE_VAPI_DEFAULT_ASSISTANT_ID
    };

    let allPassed = true;
    const details = [];

    for (const [key, value] of Object.entries(tests)) {
      if (!value) {
        allPassed = false;
        details.push(`❌ ${key}: Missing`);
        addLog(`Missing environment variable: ${key}`, 'error');
      } else {
        details.push(`✅ ${key}: ${value.substring(0, 8)}...`);
        addLog(`Found ${key}: ${value.substring(0, 8)}...`);
      }
    }

    // Validate API key format
    const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
    if (apiKey && !apiKey.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)) {
      allPassed = false;
      details.push('❌ API Key format invalid');
      addLog('API key format is invalid', 'error');
    }

    // Validate assistant ID format
    const assistantId = import.meta.env.VITE_VAPI_DEFAULT_ASSISTANT_ID;
    if (assistantId && !assistantId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)) {
      allPassed = false;
      details.push('❌ Assistant ID format invalid');
      addLog('Assistant ID format is invalid', 'error');
    }

    // Test config file fallbacks
    try {
      const { getVapiApiKey } = await import('../config/vapiConfig');
      const configApiKey = getVapiApiKey('client');
      details.push(`📋 Config API Key: ${configApiKey ? configApiKey.substring(0, 8) + '...' : 'Missing'}`);
      addLog(`Config provides API key: ${configApiKey ? configApiKey.substring(0, 8) + '...' : 'Missing'}`);

      if (!configApiKey) {
        allPassed = false;
        details.push('❌ Config API key missing');
        addLog('Config API key is missing', 'error');
      }
    } catch (configError) {
      allPassed = false;
      details.push('❌ Config import failed');
      addLog(`Config import failed: ${configError.message}`, 'error');
    }

    updateTestResult('environmentVariables', allPassed ? 'PASS' : 'FAIL', details.join('\n'));
    return allPassed;
  };

  // Test 2: Vapi SDK Loading
  const testVapiSDKLoading = async () => {
    setCurrentTest('Vapi SDK Loading');
    addLog('Testing Vapi SDK loading...');

    try {
      // Test if Vapi is already available globally
      if (window.Vapi) {
        addLog('Vapi SDK already loaded globally');
        updateTestResult('vapiSDKLoading', 'PASS', 'Vapi SDK available globally');
        return true;
      }

      // Test our SDK loader
      addLog('Loading Vapi SDK using vapiLoader...');
      await loadVapiSDK();

      if (window.Vapi) {
        addLog('Vapi SDK loaded successfully');
        updateTestResult('vapiSDKLoading', 'PASS', 'Vapi SDK loaded via vapiLoader');
        return true;
      } else {
        addLog('Vapi SDK failed to load', 'error');
        updateTestResult('vapiSDKLoading', 'FAIL', 'Vapi SDK not available after loading');
        return false;
      }
    } catch (error) {
      addLog(`Vapi SDK loading failed: ${error.message}`, 'error');
      updateTestResult('vapiSDKLoading', 'FAIL', `Error: ${error.message}`);
      return false;
    }
  };

  // Test 3: Vapi Instance Creation
  const testVapiInstanceCreation = async () => {
    setCurrentTest('Vapi Instance Creation');
    addLog('Testing Vapi instance creation...');

    try {
      // Import the config function
      const { getVapiApiKey } = await import('../config/vapiConfig');

      const apiKey = getVapiApiKey('client');
      if (!apiKey) {
        addLog('No API key available', 'error');
        updateTestResult('vapiInstanceCreation', 'FAIL', 'No API key available');
        return false;
      }

      addLog(`Creating Vapi instance with API key: ${apiKey.substring(0, 8)}...`);

      // Add detailed logging for debugging
      addLog(`API key length: ${apiKey.length}`);
      addLog(`API key format check: ${/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(apiKey) ? 'Valid UUID' : 'Invalid format'}`);

      const instance = await createVapiInstance(apiKey);

      if (instance) {
        addLog('Vapi instance created successfully');
        addLog(`Instance type: ${typeof instance}`);
        addLog(`Instance has start method: ${typeof instance.start === 'function'}`);
        setVapiInstance(instance);
        vapiRef.current = instance;
        updateTestResult('vapiInstanceCreation', 'PASS', 'Vapi instance created successfully');
        return true;
      } else {
        addLog('Vapi instance creation returned null', 'error');
        updateTestResult('vapiInstanceCreation', 'FAIL', 'Instance creation returned null');
        return false;
      }
    } catch (error) {
      addLog(`Vapi instance creation failed: ${error.message}`, 'error');
      addLog(`Error stack: ${error.stack}`, 'error');
      updateTestResult('vapiInstanceCreation', 'FAIL', `Error: ${error.message}`);
      return false;
    }
  };

  // Test 4: Event Listeners Setup
  const testEventListeners = async () => {
    setCurrentTest('Event Listeners');
    addLog('Testing event listeners setup...');

    if (!vapiInstance) {
      addLog('No Vapi instance available for event listener test', 'error');
      updateTestResult('eventListeners', 'FAIL', 'No Vapi instance available');
      return false;
    }

    try {
      let eventsReceived = [];

      // Set up event listeners
      const events = [
        'call-start',
        'call-end',
        'speech-start',
        'speech-end',
        'volume-level',
        'message',
        'error'
      ];

      events.forEach(eventName => {
        vapiInstance.on(eventName, (data) => {
          eventsReceived.push(eventName);
          addLog(`Event received: ${eventName}`);
        });
      });

      addLog('Event listeners set up successfully');
      updateTestResult('eventListeners', 'PASS', `Set up listeners for: ${events.join(', ')}`);
      return true;
    } catch (error) {
      addLog(`Event listener setup failed: ${error.message}`, 'error');
      updateTestResult('eventListeners', 'FAIL', `Error: ${error.message}`);
      return false;
    }
  };

  // Test 5: Assistant ID Validation
  const testAssistantIdValidation = async () => {
    setCurrentTest('Assistant ID Validation');
    addLog('Testing assistant ID validation...');

    const assistantId = DEFAULT_ASSISTANT_ID;
    
    if (!assistantId) {
      addLog('No default assistant ID configured', 'error');
      updateTestResult('assistantIdValidation', 'FAIL', 'No default assistant ID');
      return false;
    }

    // Validate format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;
    if (!uuidRegex.test(assistantId)) {
      addLog('Assistant ID format is invalid', 'error');
      updateTestResult('assistantIdValidation', 'FAIL', 'Invalid UUID format');
      return false;
    }

    addLog(`Assistant ID is valid: ${assistantId}`);
    updateTestResult('assistantIdValidation', 'PASS', `Valid assistant ID: ${assistantId}`);
    return true;
  };

  // Test 6: Call Start Attempt
  const testCallStart = async () => {
    setCurrentTest('Call Start');
    addLog('Testing call start...');

    if (!vapiInstance) {
      addLog('No Vapi instance available for call test', 'error');
      updateTestResult('callStart', 'FAIL', 'No Vapi instance available');
      return false;
    }

    try {
      const assistantId = DEFAULT_ASSISTANT_ID;
      addLog(`Attempting to start call with assistant: ${assistantId}`);
      
      setCallStatus('connecting');
      
      // Use the simple Vapi Web SDK pattern as documented
      const call = await vapiInstance.start(assistantId);
      
      addLog('Call started successfully');
      addLog(`Call object: ${JSON.stringify(call, null, 2)}`);
      setCallStatus('connected');
      updateTestResult('callStart', 'PASS', 'Call started successfully');
      return true;
    } catch (error) {
      addLog(`Call start failed: ${error.message}`, 'error');
      setCallStatus('error');
      updateTestResult('callStart', 'FAIL', `Error: ${error.message}`);
      return false;
    }
  };

  // Test 7: Call Stop
  const testCallStop = async () => {
    setCurrentTest('Call Stop');
    addLog('Testing call stop...');

    if (!vapiInstance) {
      addLog('No Vapi instance available for stop test', 'error');
      updateTestResult('callStop', 'FAIL', 'No Vapi instance available');
      return false;
    }

    try {
      addLog('Attempting to stop call...');
      await vapiInstance.stop();
      
      addLog('Call stopped successfully');
      setCallStatus('idle');
      updateTestResult('callStop', 'PASS', 'Call stopped successfully');
      return true;
    } catch (error) {
      addLog(`Call stop failed: ${error.message}`, 'error');
      updateTestResult('callStop', 'FAIL', `Error: ${error.message}`);
      return false;
    }
  };

  // Run all tests
  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});
    setLogs([]);
    
    addLog('Starting comprehensive Vapi diagnostics...');
    
    const tests = [
      testEnvironmentVariables,
      testVapiSDKLoading,
      testVapiInstanceCreation,
      testEventListeners,
      testAssistantIdValidation,
      testCallStart
    ];

    for (const test of tests) {
      try {
        const result = await test();
        if (!result) {
          addLog(`Test failed, stopping diagnostics`, 'error');
          break;
        }
        // Wait a bit between tests
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        addLog(`Test threw error: ${error.message}`, 'error');
        break;
      }
    }
    
    setCurrentTest('');
    setIsRunning(false);
    addLog('Diagnostics complete');
  };

  // Manual call stop
  const stopCall = async () => {
    await testCallStop();
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>🔧 Vapi Call Diagnostics</h2>
      <p>This tool tests all aspects of the Vapi integration to identify call issues.</p>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={runAllTests} 
          disabled={isRunning}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: isRunning ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            marginRight: '10px'
          }}
        >
          {isRunning ? 'Running Tests...' : 'Run All Tests'}
        </button>
        
        {callStatus === 'connected' && (
          <button 
            onClick={stopCall}
            style={{ 
              padding: '10px 20px', 
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px'
            }}
          >
            Stop Call
          </button>
        )}
      </div>

      {currentTest && (
        <div style={{ 
          padding: '10px', 
          backgroundColor: '#fff3cd', 
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          Currently running: <strong>{currentTest}</strong>
        </div>
      )}

      <div style={{ 
        padding: '10px', 
        backgroundColor: '#f8f9fa', 
        border: '1px solid #dee2e6',
        borderRadius: '4px',
        marginBottom: '20px'
      }}>
        <strong>Call Status:</strong> {callStatus}
      </div>

      <h3>Test Results</h3>
      <div style={{ marginBottom: '20px' }}>
        {Object.entries(testResults).map(([testName, result]) => (
          <div 
            key={testName}
            style={{ 
              padding: '10px',
              margin: '5px 0',
              backgroundColor: result.result === 'PASS' ? '#d4edda' : '#f8d7da',
              border: `1px solid ${result.result === 'PASS' ? '#c3e6cb' : '#f5c6cb'}`,
              borderRadius: '4px'
            }}
          >
            <strong>{testName}:</strong> {result.result} 
            <span style={{ fontSize: '0.8em', color: '#666' }}> ({result.timestamp})</span>
            {result.details && (
              <pre style={{ 
                margin: '5px 0 0 0', 
                fontSize: '0.9em',
                whiteSpace: 'pre-wrap'
              }}>
                {result.details}
              </pre>
            )}
          </div>
        ))}
      </div>

      <h3>Logs</h3>
      <div style={{ 
        height: '300px', 
        overflow: 'auto', 
        backgroundColor: '#000', 
        color: '#00ff00', 
        padding: '10px',
        borderRadius: '4px',
        fontSize: '0.9em'
      }}>
        {logs.map((log, index) => (
          <div key={index} style={{ 
            color: log.type === 'error' ? '#ff6b6b' : '#00ff00' 
          }}>
            [{log.timestamp}] {log.message}
          </div>
        ))}
      </div>
    </div>
  );
};

export default VapiCallDiagnostics;
