/**
 * Vapi SDK Loader for React Application
 * 
 * This module properly imports and exposes the Vapi SDK for use in the React app.
 * It handles the installed package and provides fallbacks.
 */

let VapiClass = null;
let loadingPromise = null;

/**
 * Load the Vapi SDK
 * @returns {Promise<Function>} The Vapi constructor
 */
export async function loadVapiSDK() {
  // Return cached result if already loaded
  if (VapiClass) {
    console.log('[VapiLoader] Vapi SDK already loaded');
    return VapiClass;
  }

  // Return existing loading promise if already loading
  if (loadingPromise) {
    console.log('[VapiLoader] Vapi SDK loading in progress, waiting...');
    return loadingPromise;
  }

  // Start loading process
  loadingPromise = (async () => {
    console.log('[VapiLoader] Starting Vapi SDK loading process');

    try {
      // Try to import the installed package
      console.log('[VapiLoader] Attempting to import @vapi-ai/web package');
      const VapiModule = await import('@vapi-ai/web');

      console.log('[VapiLoader] Module imported, checking exports:', {
        hasDefault: !!VapiModule.default,
        hasVapi: !!VapiModule.Vapi,
        moduleKeys: Object.keys(VapiModule),
        defaultType: typeof VapiModule.default
      });

      // Extract the Vapi class from the module - try multiple patterns
      VapiClass = VapiModule.default || VapiModule.Vapi || VapiModule;

      // If we got an object with a default property, try that
      if (VapiClass && typeof VapiClass === 'object' && VapiClass.default) {
        VapiClass = VapiClass.default;
      }

      if (typeof VapiClass === 'function') {
        console.log('[VapiLoader] ✅ Successfully loaded Vapi SDK from installed package');

        // Expose globally for compatibility
        window.Vapi = VapiClass;
        window.__VAPI_BUNDLED__ = VapiClass;

        // Test instantiation
        try {
          const testInstance = new VapiClass('test-key');
          if (testInstance && typeof testInstance.start === 'function') {
            console.log('[VapiLoader] ✅ Vapi SDK validation successful');
          } else {
            console.log('[VapiLoader] ⚠️ Vapi instance created but missing start method');
          }
        } catch (testError) {
          console.log('[VapiLoader] ⚠️ Vapi SDK loaded but validation failed:', testError.message);
        }

        return VapiClass;
      } else {
        throw new Error(`Imported module does not contain a valid Vapi constructor. Got: ${typeof VapiClass}`);
      }
    } catch (importError) {
      console.error('[VapiLoader] ❌ Failed to import @vapi-ai/web package:', importError);
      
      // Fallback: try to use CDN-loaded version if available
      if (window.Vapi && typeof window.Vapi === 'function') {
        console.log('[VapiLoader] 🔄 Using CDN-loaded Vapi SDK as fallback');
        VapiClass = window.Vapi;
        return VapiClass;
      }
      
      // Final fallback: load from CDN
      console.log('[VapiLoader] 🔄 Attempting to load from CDN as final fallback');
      try {
        await loadFromCDN();
        if (window.Vapi && typeof window.Vapi === 'function') {
          VapiClass = window.Vapi;
          return VapiClass;
        }
      } catch (cdnError) {
        console.error('[VapiLoader] ❌ CDN fallback also failed:', cdnError);
      }
      
      throw new Error('Failed to load Vapi SDK from all sources');
    }
  })();

  return loadingPromise;
}

/**
 * Load Vapi SDK from CDN as fallback
 */
async function loadFromCDN() {
  return new Promise(async (resolve, reject) => {
    // Don't load if already available
    if (window.Vapi) {
      resolve();
      return;
    }

    // Try multiple CDN sources (official first)
    const cdnUrls = [
      'https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js', // Official Vapi CDN
      'https://cdn.jsdelivr.net/npm/@vapi-ai/web@2.3.1/dist/vapi.js',
      'https://unpkg.com/@vapi-ai/web@2.3.1/dist/vapi.js',
      'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/vapi.js'
    ];

    for (const url of cdnUrls) {
      try {
        console.log(`[VapiLoader] Trying CDN: ${url}`);

        await new Promise((resolveScript, rejectScript) => {
          const script = document.createElement('script');
          script.src = url;
          script.async = true;
          script.crossOrigin = 'anonymous';

          const timeout = setTimeout(() => {
            script.remove();
            rejectScript(new Error(`CDN loading timeout for ${url}`));
          }, 5000);

          script.onload = () => {
            clearTimeout(timeout);
            console.log(`[VapiLoader] ✅ Successfully loaded from ${url}`);
            resolveScript();
          };

          script.onerror = (event) => {
            clearTimeout(timeout);
            script.remove();
            rejectScript(new Error(`Failed to load from ${url}: ${event.message || 'Network error'}`));
          };

          document.head.appendChild(script);
        });

        // Check if Vapi is now available (multiple patterns)
        if (window.Vapi && typeof window.Vapi === 'function') {
          console.log('[VapiLoader] ✅ Vapi SDK successfully loaded and available (window.Vapi)');
          resolve();
          return;
        } else if (window.vapiSDK && typeof window.vapiSDK === 'object') {
          console.log('[VapiLoader] ✅ Vapi SDK successfully loaded and available (window.vapiSDK)');
          // For the official CDN (HTML script tag pattern), create a compatible Vapi class
          // This bridges the gap between HTML script tag pattern and React SDK pattern
          window.Vapi = function(apiKey) {
            const instance = {
              apiKey: apiKey,
              start: function(assistantId, overrides = {}) {
                // Use the HTML script tag SDK to create a call
                return window.vapiSDK.run({
                  apiKey: this.apiKey,
                  assistant: assistantId,
                  config: overrides.config || {},
                  ...overrides
                });
              },
              stop: function() {
                // Stop functionality would need to be implemented
                console.log('[VapiLoader] Stop called on bridged instance');
              },
              on: function(event, callback) {
                // Event handling would need to be implemented
                console.log(`[VapiLoader] Event listener registered: ${event}`);
              }
            };
            return instance;
          };
          resolve();
          return;
        } else {
          console.log(`[VapiLoader] ⚠️ Script loaded from ${url} but Vapi not available`);
          console.log(`[VapiLoader] Available globals:`, Object.keys(window).filter(k => k.toLowerCase().includes('vapi')));
        }
      } catch (error) {
        console.log(`[VapiLoader] ❌ Failed to load from ${url}: ${error.message}`);
        // Continue to next URL
      }
    }

    reject(new Error('Failed to load Vapi SDK from all CDN sources'));
  });
}

/**
 * Get the Vapi class (must be loaded first)
 * @returns {Function|null} The Vapi constructor or null if not loaded
 */
export function getVapiClass() {
  return VapiClass;
}

/**
 * Check if Vapi SDK is loaded
 * @returns {boolean} True if loaded, false otherwise
 */
export function isVapiLoaded() {
  return VapiClass !== null;
}

/**
 * Create a Vapi instance
 * @param {string} apiKey - The Vapi API key
 * @param {Object} options - Additional options
 * @returns {Object} Vapi instance
 */
export async function createVapiInstance(apiKey, options = {}) {
  if (!VapiClass) {
    console.log('[VapiLoader] Vapi not loaded, loading now...');
    await loadVapiSDK();
  }

  if (!VapiClass) {
    throw new Error('Failed to load Vapi SDK');
  }

  if (!apiKey || typeof apiKey !== 'string') {
    throw new Error('API key must be a non-empty string');
  }

  try {
    // Constructor: new Vapi(apiToken, apiBaseUrl, dailyCallConfig, dailyCallObject)
    const vapi = new VapiClass(apiKey, 'https://api.vapi.ai');
    console.log(`[VapiLoader] ✅ Vapi instance created with key: ${apiKey.substring(0, 8)}...`);
    return vapi;
  } catch (error) {
    console.error(`[VapiLoader] ❌ Failed to create Vapi instance:`, error);
    throw error;
  }
}

// Auto-load on module import
loadVapiSDK().catch(error => {
  console.error('[VapiLoader] ❌ Auto-load failed:', error);
});
