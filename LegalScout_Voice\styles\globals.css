/* ===== Base Styles ===== */
body {
    font-family: 'Inter', sans-serif;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    background-color: #f4f4f4;
}

p {
  margin: 0;
  padding: 0;
}

/* ===== Header & Navigation ===== */
header {
  background-color: #171717 !important; /* Force dark color */
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 1000;
  padding: 15px 20px;
  display: flex; /* Add this */
  align-items: center; /* Add this */
}

nav {
  display: flex;
  align-items: center;
  max-width: 1200px; /* Constrain width for larger screens */
  margin: 0 auto;     /* Center the nav container */
}

.logo-container {
  margin-right: auto; /* Push logo to left */
}

.logo {
  max-width: 150px;
  height: auto;
}

.nav-links {
  display: flex;
  gap: 2rem; /* Modern spacing (replaces margin) */
}

.nav-links a {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.nav-links a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* ===== Hero Section ===== */
.hero {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  background-color: #000000;
  padding-top: 80px; /* Better than margin-top for spacing */
}

.statement {
  text-align: center;
  color: #b0bac3;
  margin-bottom: 0; /* Reduce space below hero text */
}

.statement p span {
  display: block;
  margin: 0.5rem 0;
}

#root {
  margin-top: 10px; /* Space above content in #root */
}

/* ===== Utilities ===== */
.no-margin {
  margin: 0 !important;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes shimmer {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.button-container p {
  -moz-background-clip: text;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Add these at the end */
.mascot-button {
  width: 200px !important;
  height: 200px !important;
  display: block !important;
  position: relative !important;
}

.button-container {
  position: relative;
  z-index: 10000 !important; /* Higher than header */
}

.mascot-image {
  pointer-events: none !important; /* Allow click through image to button */
}

button {
  min-width: 200px !important; /* Prevent collapse */
  min-height: 200px !important;
} 