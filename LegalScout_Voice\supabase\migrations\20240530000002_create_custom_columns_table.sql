-- Create custom_columns table to store attorney-specific column configurations
CREATE TABLE IF NOT EXISTS public.custom_columns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Relationships
  attorney_id UUID REFERENCES public.attorneys(id) NOT NULL,
  
  -- Column Configuration
  name TEXT NOT NULL,
  display_name TEXT NOT NULL,
  field_type TEXT NOT NULL, -- 'text', 'number', 'date', 'boolean', 'select'
  source TEXT NOT NULL, -- 'metadata', 'transcript', 'custom'
  source_path TEXT, -- JSON path for metadata fields
  default_value TEXT,
  options JSONB, -- For select fields
  is_visible BOOLEAN DEFAULT true,
  display_order INTEGER DEFAULT 0,
  
  -- Constraints
  UNIQUE (attorney_id, name)
);

-- Add index for attorney_id for faster lookups
CREATE INDEX IF NOT EXISTS custom_columns_attorney_id_idx ON public.custom_columns (attorney_id);

-- Add RLS policies for custom_columns
ALTER TABLE public.custom_columns ENABLE ROW LEVEL SECURITY;

-- Create policy to allow authenticated users to view their own custom columns
CREATE POLICY "Users can view their own custom columns" ON public.custom_columns
  FOR SELECT
  USING (auth.uid() = attorney_id);

-- Create policy to allow authenticated users to insert their own custom columns
CREATE POLICY "Users can insert their own custom columns" ON public.custom_columns
  FOR INSERT
  WITH CHECK (auth.uid() = attorney_id);

-- Create policy to allow authenticated users to update their own custom columns
CREATE POLICY "Users can update their own custom columns" ON public.custom_columns
  FOR UPDATE
  USING (auth.uid() = attorney_id);

-- Create policy to allow authenticated users to delete their own custom columns
CREATE POLICY "Users can delete their own custom columns" ON public.custom_columns
  FOR DELETE
  USING (auth.uid() = attorney_id);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_custom_columns_updated_at
BEFORE UPDATE ON public.custom_columns
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Add comment to the table for documentation
COMMENT ON TABLE public.custom_columns IS 'Stores custom column configurations for attorney consultation tables';
