-- Add attorney_id column to call_records table for direct association
ALTER TABLE public.call_records
ADD COLUMN attorney_id UUID REFERENCES public.attorneys(id);

-- Add index on attorney_id for faster lookups
CREATE INDEX IF NOT EXISTS call_records_attorney_id_idx ON call_records (attorney_id);

-- Create function to update attorney_id based on assistant_id
CREATE OR REPLACE FUNCTION update_call_records_attorney_id()
RETURNS VOID AS $$
BEGIN
  UPDATE call_records cr
  SET attorney_id = a.id
  FROM attorneys a
  WHERE cr.assistant_id = a.vapi_assistant_id
  AND cr.attorney_id IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Run the function to update existing records
SELECT update_call_records_attorney_id();

-- Create trigger to automatically set attorney_id on insert
CREATE OR REPLACE FUNCTION set_attorney_id_on_insert()
RETURNS TRIGGER AS $$
BEGIN
  SELECT id INTO NEW.attorney_id
  FROM attorneys
  WHERE vapi_assistant_id = NEW.assistant_id
  LIMIT 1;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to run the function on insert
CREATE TRIGGER set_call_records_attorney_id
BEFORE INSERT ON call_records
FOR EACH ROW
EXECUTE FUNCTION set_attorney_id_on_insert();

-- Update RLS policy to use attorney_id for more efficient lookups
DROP POLICY IF EXISTS "Users can view their own call records" ON call_records;

CREATE POLICY "Users can view their own call records" ON call_records
  FOR SELECT
  USING (
    attorney_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM attorneys
      WHERE attorneys.id = auth.uid()
      AND attorneys.vapi_assistant_id = call_records.assistant_id
    )
  );
