-- Create call_records table to store Vapi call data
CREATE TABLE IF NOT EXISTS public.call_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Call identification
  call_id TEXT NOT NULL UNIQUE,
  assistant_id TEXT NOT NULL,
  attorney_id UUID REFERENCES public.attorneys(id) NOT NULL,
  
  -- Call details
  customer_phone TEXT,
  status TEXT NOT NULL,
  duration INTEGER,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  
  -- Call content
  transcripts JSONB DEFAULT '[]'::JSON<PERSON>,
  messages JSONB DEFAULT '[]'::JSONB,
  tool_executions JSONB DEFAULT '[]'::JSON<PERSON>,
  metadata JSONB DEFAULT '{}'::JSONB
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS call_records_call_id_idx ON public.call_records (call_id);
CREATE INDEX IF NOT EXISTS call_records_assistant_id_idx ON public.call_records (assistant_id);
CREATE INDEX IF NOT EXISTS call_records_attorney_id_idx ON public.call_records (attorney_id);
CREATE INDEX IF NOT EXISTS call_records_status_idx ON public.call_records (status);
CREATE INDEX IF NOT EXISTS call_records_start_time_idx ON public.call_records (start_time);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_call_records_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_call_records_updated_at
BEFORE UPDATE ON public.call_records
FOR EACH ROW
EXECUTE FUNCTION update_call_records_updated_at();

-- Enable Row Level Security
ALTER TABLE public.call_records ENABLE ROW LEVEL SECURITY;

-- Create policy to allow authenticated users to view their own call records
CREATE POLICY "Users can view their own call records" ON public.call_records
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM attorneys WHERE id = call_records.attorney_id
    )
  );

-- Create policy to allow service role to insert call records
CREATE POLICY "Service role can insert call records" ON public.call_records
  FOR INSERT
  WITH CHECK (auth.role() = 'service_role');

-- Create policy to allow service role to update call records
CREATE POLICY "Service role can update call records" ON public.call_records
  FOR UPDATE
  USING (auth.role() = 'service_role');

-- Create policy to allow service role to delete call records
CREATE POLICY "Service role can delete call records" ON public.call_records
  FOR DELETE
  USING (auth.role() = 'service_role');
