/**
 * Super Simple Server Test
 */

import express from 'express';

console.log('Creating Express app...');
const app = express();

console.log('Setting up middleware...');
app.use(express.json());

console.log('Setting up routes...');
app.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

console.log('Starting server...');
app.listen(3001, () => {
  console.log('🚀 Super simple server running on http://localhost:3001');
});
