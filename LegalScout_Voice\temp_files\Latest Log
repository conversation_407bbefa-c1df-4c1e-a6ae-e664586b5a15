Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:465 🚀 [PREVIEW CALL START] Starting consultation...
EnhancedPreviewNew.jsx:466 🎯 [PREVIEW CALL START] Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:467 💬 [PREVIEW CALL START] Welcome message: Mr. Hammond, The phones are working.
EnhancedPreviewNew.jsx:468 🔊 [PREVIEW CALL START] Voice settings: {voiceId: 'sarah', voiceProvider: 'playht'}
EnhancedPreviewNew.jsx:469 ✅ [PREVIEW CALL START] VapiCall will receive assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:676 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:677 🎬 [PREVIEW CALL RENDER] Assistant ID type: string
EnhancedPreviewNew.jsx:678 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: false
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:676 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:677 🎬 [PREVIEW CALL RENDER] Assistant ID type: string
EnhancedPreviewNew.jsx:678 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: false
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
callDebugger.js:75 [CallDebugger:VapiCall] Processing call configuration
callDebugger.js:73 [CallDebugger:VapiCall] Using direct configuration {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
VapiCall.jsx:134 VapiCall: Using direct configuration
callDebugger.js:75 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:139 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:524 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:790 [useVapiCall] Checking for Vapi public key...
useVapiCall.js?t=1748874308796:798 [useVapiCall] VITE_VAPI_PUBLIC_KEY: Set
useVapiCall.js?t=1748874308796:799 [useVapiCall] window.VITE_VAPI_PUBLIC_KEY: Not set
useVapiCall.js?t=1748874308796:800 [useVapiCall] Using API key: 310f0d43...
useVapiCall.js?t=1748874308796:810 [useVapiCall] Initializing Vapi instance with API key: 310f0d43...
useVapiCall.js?t=1748874308796:819 [useVapiCall] Vapi options: {baseURL: 'https://api.vapi.ai', serverURL: 'https://api.vapi.ai', debug: false}
useVapiCall.js?t=1748874308796:821 Using baseURL: https://api.vapi.ai
useVapiCall.js?t=1748874308796:822 Using serverURL: https://api.vapi.ai
useVapiCall.js?t=1748874308796:831 [useVapiCall] Creating Vapi instance directly using official pattern
VapiCall.jsx:1489 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1490 📊 Current status: idle
VapiCall.jsx:1491 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1492 🔧 Vapi instance available: false
VapiCall.jsx:1493 ⚙️ Processed config: null
VapiCall.jsx:1508 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1509 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1510 ⏸️ Vapi instance: false
VapiCall.jsx:1933 Status changed to: idle
VapiCall.jsx:1934 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:2027 🔄 VapiCall component received dossier update: {}
EnhancedPreviewNew.jsx:450 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:451 firmName: LegalScout
EnhancedPreviewNew.jsx:452 titleText: LegalScout
EnhancedPreviewNew.jsx:453 logoUrl: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:454 primaryColor: #aa4b4b
EnhancedPreviewNew.jsx:455 secondaryColor: #46ce93
EnhancedPreviewNew.jsx:456 vapiInstructions: You will guide the user through jurrasic park
EnhancedPreviewNew.jsx:457 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:458 voiceId: sarah
EnhancedPreviewNew.jsx:459 voiceProvider: playht
EnhancedPreviewNew.jsx:460 chatActive: true
VapiCall.jsx:1512 🧹 Cleanup function for not ready state
callDebugger.js:75 [CallDebugger:VapiCall] Processing call configuration
callDebugger.js:73 [CallDebugger:VapiCall] Using direct configuration {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
VapiCall.jsx:134 VapiCall: Using direct configuration
callDebugger.js:75 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:139 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:524 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1489 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1490 📊 Current status: idle
VapiCall.jsx:1491 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1492 🔧 Vapi instance available: false
VapiCall.jsx:1493 ⚙️ Processed config: null
VapiCall.jsx:1508 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1509 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1510 ⏸️ Vapi instance: false
VapiCall.jsx:1933 Status changed to: idle
VapiCall.jsx:1934 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:2027 🔄 VapiCall component received dossier update: {}
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:537 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:537 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:537 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:537 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1512 🧹 Cleanup function for not ready state
VapiCall.jsx:1489 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1490 📊 Current status: idle
VapiCall.jsx:1491 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1492 🔧 Vapi instance available: false
VapiCall.jsx:1493 ⚙️ Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1508 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1509 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1510 ⏸️ Vapi instance: false
useVapiCall.js?t=1748874308796:837 [useVapiCall] Vapi instance created successfully
VapiService.js:18 [VapiService] Setting up event listener for: call-start
VapiService.js:18 [VapiService] Setting up event listener for: call-end
VapiService.js:18 [VapiService] Setting up event listener for: speech-start
VapiService.js:18 [VapiService] Setting up event listener for: speech-end
VapiService.js:18 [VapiService] Setting up event listener for: message
VapiService.js:18 [VapiService] Setting up event listener for: error
VapiService.js:18 [VapiService] Setting up event listener for: volume-level
VapiService.js:18 [VapiService] Event listeners set up for Vapi instance
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1512 🧹 Cleanup function for not ready state
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:524 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:1108 Requesting microphone permission...
useVapiCall.js?t=1748874308796:885 Setting up Vapi event listeners
VapiService.js:18 [VapiService] Setting up event listener for: call-start
VapiService.js:18 [VapiService] Setting up event listener for: call-end
VapiService.js:18 [VapiService] Setting up event listener for: speech-start
VapiService.js:18 [VapiService] Setting up event listener for: speech-end
VapiService.js:18 [VapiService] Setting up event listener for: message
VapiService.js:18 [VapiService] Setting up event listener for: error
VapiService.js:18 [VapiService] Setting up event listener for: volume-level
VapiService.js:18 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:573 Setting up direct event handlers for Vapi
VapiCall.jsx:1489 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1490 📊 Current status: idle
VapiCall.jsx:1491 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1492 🔧 Vapi instance available: true
VapiCall.jsx:1493 ⚙️ Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1518 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1525 ⏱️ Using initialization delay of 500ms before starting call
VapiCall.jsx:1933 Status changed to: idle
VapiCall.jsx:1934 CALL_STATUS.CONNECTED value: connected
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:537 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:537 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:1111 Microphone permission granted
useVapiCall.js?t=1748874308796:1159 Starting call with existing Vapi instance using direct pattern
vapiMcpDebugger.js:175 [Vapi MCP] POST https://api.vapi.ai/call/web
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/call/web with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
VapiCall.jsx:1534 🎯 Auto-starting call from VapiCall component after delay
VapiCall.jsx:1535 📋 Current call parameters: {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantOverrides: 'Set', vapi: true, processedConfig: true}
VapiCall.jsx:1572 🎯 [VAPI CALL INIT] Final assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1573 ⚙️ [VAPI CALL INIT] Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1578 🔄 Starting call attempt 1
VapiCall.jsx:190 [VapiCall] Starting call with: {finalAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', attorneyDataAssistantId: undefined, processedConfigAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', propAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', forceDefaultAssistant: false, …}
callDebugger.js:73 [CallDebugger:VapiCall] Starting call {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', subdomain: 'damon'}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> connecting
useVapiCall.js?t=1748874308796:1199 [useVapiCall] Starting call with assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748874308796:1210 [useVapiCall] Starting call using official Vapi Web SDK pattern
useVapiCall.js?t=1748874308796:1212 [useVapiCall] Call started successfully: null
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2430 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2430 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1748874308796:890 Component unmounting - performing Vapi cleanup
VapiService.js:18 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Event listeners removed from Vapi instance
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:35 SpeechParticles: Script already loaded, configuring...
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #aa4b4b Assistant: #46ce93
speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#aa4b4b', assistantColor: '#46ce93'}
speech-particles.js:525 🎨 Converting user color: #aa4b4b
speech-particles.js:527 🎨 User HSL: {h: 0, s: 39, l: 48}
speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:537 🎨 Converting assistant color: #46ce93
speech-particles.js:539 🎨 Assistant HSL: {h: 154, s: 58, l: 54}
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
SpeechParticles.jsx:126 SpeechParticles: Setting up microphone...
SpeechParticles.jsx:138 SpeechParticles: New audio context created
SpeechParticles.jsx:155 SpeechParticles: Analyser created and configured
SpeechParticles.jsx:158 SpeechParticles: Requesting microphone access...
useVapiCall.js?t=1748874308796:885 Setting up Vapi event listeners
VapiService.js:18 [VapiService] Setting up event listener for: call-start
VapiService.js:18 [VapiService] Setting up event listener for: call-end
VapiService.js:18 [VapiService] Setting up event listener for: speech-start
VapiService.js:18 [VapiService] Setting up event listener for: speech-end
VapiService.js:18 [VapiService] Setting up event listener for: message
VapiService.js:18 [VapiService] Setting up event listener for: error
VapiService.js:18 [VapiService] Setting up event listener for: volume-level
VapiService.js:18 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:1933 Status changed to: connected
VapiCall.jsx:1934 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1938 Call connected - checking for custom welcome message
VapiCall.jsx:1959 mergedCustomInstructions: {firmName: 'LegalScout', welcomeMessage: 'Mr. Hammond, The phones are working.', voiceId: 'sarah', voiceProvider: 'playht', initialMessage: 'Mr. Hammond, The phones are working.', …}
VapiCall.jsx:1960 mergedAssistantOverrides: {firstMessage: 'Mr. Hammond, The phones are working.', model: 'Not present', artifactPlan: 'Not present'}
VapiCall.jsx:1989 Found welcome message from mergedCustomInstructions.welcomeMessage: Mr. Hammond, The phones are working.
VapiCall.jsx:2007 Adding welcome message to UI: Mr. Hammond, The phones are working.
VapiCall.jsx:2012 Welcome message should be spoken by the assistant automatically
VapiCall.jsx:2015 Voice settings: {voiceId: 'sarah', voiceProvider: 'playht'}
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:35 SpeechParticles: Script already loaded, configuring...
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #aa4b4b Assistant: #46ce93
speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#aa4b4b', assistantColor: '#46ce93'}
speech-particles.js:525 🎨 Converting user color: #aa4b4b
speech-particles.js:527 🎨 User HSL: {h: 0, s: 39, l: 48}
speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:537 🎨 Converting assistant color: #46ce93
speech-particles.js:539 🎨 Assistant HSL: {h: 154, s: 58, l: 54}
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
SpeechParticles.jsx:126 SpeechParticles: Setting up microphone...
SpeechParticles.jsx:138 SpeechParticles: New audio context created
SpeechParticles.jsx:158 SpeechParticles: Requesting microphone access...
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2430 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2430 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
SpeechParticles.jsx:126 SpeechParticles: Setting up microphone...
SpeechParticles.jsx:130 SpeechParticles: Audio context already exists, reusing...
SpeechParticles.jsx:158 SpeechParticles: Requesting microphone access...
VapiCall.jsx:1835 VapiCall: Scrolled conversation area to bottom
VapiCall.jsx:1945 Added force-visible class to call interface
vapiMcpDebugger.js:180 [Vapi MCP] Response: 201 https://api.vapi.ai/call/web
headers-fix.js:33 [HeadersFix] Fetch request to https://c.daily.co/call-machine/versioned/0.72.2/static/call-machine-object-bundle.js with headers: {Accept: 'application/json'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:66 Fetch finished loading: POST "https://api.vapi.ai/call/web".
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:66
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:46
(anonymous) @ @vapi-ai_web.js?v=0856315e:6951
(anonymous) @ @vapi-ai_web.js?v=0856315e:6996
await in (anonymous)
callControllerCreateWebCall @ @vapi-ai_web.js?v=0856315e:7179
start @ @vapi-ai_web.js?v=0856315e:8554
(anonymous) @ useVapiCall.js?t=1748874308796:1162
Promise.then
(anonymous) @ useVapiCall.js?t=1748874308796:1110
loadSubdomainConfig @ useVapiCall.js?t=1748874308796:758
(anonymous) @ useVapiCall.js?t=1748874308796:765
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:66 Fetch finished loading: GET "https://c.daily.co/call-machine/versioned/0.72.2/static/call-machine-object-bundle.js".
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:66
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
(anonymous) @ @vapi-ai_web.js?v=0856315e:4800
h @ @vapi-ai_web.js?v=0856315e:192
a2 @ @vapi-ai_web.js?v=0856315e:204
(anonymous) @ @vapi-ai_web.js?v=0856315e:209
(anonymous) @ @vapi-ai_web.js?v=0856315e:201
o.value @ @vapi-ai_web.js?v=0856315e:4813
(anonymous) @ @vapi-ai_web.js?v=0856315e:4778
h @ @vapi-ai_web.js?v=0856315e:192
a2 @ @vapi-ai_web.js?v=0856315e:204
Promise.then
h @ @vapi-ai_web.js?v=0856315e:196
a2 @ @vapi-ai_web.js?v=0856315e:204
(anonymous) @ @vapi-ai_web.js?v=0856315e:209
(anonymous) @ @vapi-ai_web.js?v=0856315e:201
o.value @ @vapi-ai_web.js?v=0856315e:4780
(anonymous) @ @vapi-ai_web.js?v=0856315e:4760
h @ @vapi-ai_web.js?v=0856315e:192
a2 @ @vapi-ai_web.js?v=0856315e:204
(anonymous) @ @vapi-ai_web.js?v=0856315e:209
(anonymous) @ @vapi-ai_web.js?v=0856315e:201
o.value @ @vapi-ai_web.js?v=0856315e:4762
value @ @vapi-ai_web.js?v=0856315e:4735
value @ @vapi-ai_web.js?v=0856315e:4716
(anonymous) @ @vapi-ai_web.js?v=0856315e:5530
(anonymous) @ @vapi-ai_web.js?v=0856315e:5527
h @ @vapi-ai_web.js?v=0856315e:192
a2 @ @vapi-ai_web.js?v=0856315e:204
(anonymous) @ @vapi-ai_web.js?v=0856315e:209
(anonymous) @ @vapi-ai_web.js?v=0856315e:201
o.value @ @vapi-ai_web.js?v=0856315e:5546
(anonymous) @ @vapi-ai_web.js?v=0856315e:5554
h @ @vapi-ai_web.js?v=0856315e:192
a2 @ @vapi-ai_web.js?v=0856315e:204
(anonymous) @ @vapi-ai_web.js?v=0856315e:209
(anonymous) @ @vapi-ai_web.js?v=0856315e:201
o.value @ @vapi-ai_web.js?v=0856315e:5586
start @ @vapi-ai_web.js?v=0856315e:8637
await in start
(anonymous) @ useVapiCall.js?t=1748874308796:1162
Promise.then
(anonymous) @ useVapiCall.js?t=1748874308796:1110
loadSubdomainConfig @ useVapiCall.js?t=1748874308796:758
(anonymous) @ useVapiCall.js?t=1748874308796:765
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'loaded', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'loaded', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'loaded', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'loaded', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'loaded', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:172 SpeechParticles: Microphone access granted, connecting to analyser...
SpeechParticles.jsx:203 SpeechParticles: Error accessing microphone: InvalidAccessError: Failed to execute 'connect' on 'AudioNode': cannot connect to an AudioNode belonging to a different audio context.
    at setupMicrophone (SpeechParticles.jsx:183:20)
setupMicrophone @ SpeechParticles.jsx:203
await in setupMicrophone
(anonymous) @ SpeechParticles.jsx:216
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
VapiCall.jsx:1586 ✅ Marking call as initialized
VapiCall.jsx:1591 ✅ Set window.vapiCallActive to true after initialization
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2430 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2430 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
SpeechParticles.jsx:172 SpeechParticles: Microphone access granted, connecting to analyser...
SpeechParticles.jsx:203 SpeechParticles: Error accessing microphone: InvalidAccessError: Failed to execute 'connect' on 'AudioNode': cannot connect to an AudioNode belonging to a different audio context.
    at setupMicrophone (SpeechParticles.jsx:183:20)
setupMicrophone @ SpeechParticles.jsx:203
await in setupMicrophone
(anonymous) @ SpeechParticles.jsx:216
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=0856315e:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=0856315e:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=0856315e:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
SpeechParticles.jsx:172 SpeechParticles: Microphone access granted, connecting to analyser...
SpeechParticles.jsx:203 SpeechParticles: Error accessing microphone: InvalidAccessError: Failed to execute 'connect' on 'AudioNode': cannot connect to an AudioNode belonging to a different audio context.
    at setupMicrophone (SpeechParticles.jsx:183:20)
setupMicrophone @ SpeechParticles.jsx:203
await in setupMicrophone
(anonymous) @ SpeechParticles.jsx:216
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
VM2147:4 XHR finished loading: POST "https://gs.daily.co/rooms/check/vapi/7LEU4XNjzTvtWBxSDNbG".
l._end @ VM2147:4
l.end @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
I @ VM2147:4
e.roomsCheck @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
i @ VM2147:4
postMessage
value @ @vapi-ai_web.js?v=0856315e:4586
value @ @vapi-ai_web.js?v=0856315e:6200
(anonymous) @ @vapi-ai_web.js?v=0856315e:5569
h @ @vapi-ai_web.js?v=0856315e:192
a2 @ @vapi-ai_web.js?v=0856315e:204
Promise.then
h @ @vapi-ai_web.js?v=0856315e:196
a2 @ @vapi-ai_web.js?v=0856315e:204
(anonymous) @ @vapi-ai_web.js?v=0856315e:209
(anonymous) @ @vapi-ai_web.js?v=0856315e:201
o.value @ @vapi-ai_web.js?v=0856315e:5586
start @ @vapi-ai_web.js?v=0856315e:8637
await in start
(anonymous) @ useVapiCall.js?t=1748874308796:1162
Promise.then
(anonymous) @ useVapiCall.js?t=1748874308796:1110
loadSubdomainConfig @ useVapiCall.js?t=1748874308796:758
(anonymous) @ useVapiCall.js?t=1748874308796:765
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VM2147:4 Preferred mic not found; skipping: true
value @ VM2147:4
value @ VM2147:4
Q @ VM2147:4
eval @ VM2147:4
e.getCamGumConstraints @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
y @ VM2147:4
e.getCamStream @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
iA @ VM2147:4
G.camOperation @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
dispatch @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
dispatch @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
i @ VM2147:4
postMessage
value @ @vapi-ai_web.js?v=0856315e:4586
value @ @vapi-ai_web.js?v=0856315e:6200
(anonymous) @ @vapi-ai_web.js?v=0856315e:5569
h @ @vapi-ai_web.js?v=0856315e:192
a2 @ @vapi-ai_web.js?v=0856315e:204
Promise.then
h @ @vapi-ai_web.js?v=0856315e:196
a2 @ @vapi-ai_web.js?v=0856315e:204
(anonymous) @ @vapi-ai_web.js?v=0856315e:209
(anonymous) @ @vapi-ai_web.js?v=0856315e:201
o.value @ @vapi-ai_web.js?v=0856315e:5586
start @ @vapi-ai_web.js?v=0856315e:8637
await in start
(anonymous) @ useVapiCall.js?t=1748874308796:1162
Promise.then
(anonymous) @ useVapiCall.js?t=1748874308796:1110
loadSubdomainConfig @ useVapiCall.js?t=1748874308796:758
(anonymous) @ useVapiCall.js?t=1748874308796:765
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: signaling
VapiCall.jsx:294 Global iframe message received: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
VapiCall.jsx:317 Window message received for processing: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
VM2147:4 daily-js version 0.72.2 is nearing end of support. Please upgrade to a newer version.
value @ VM2147:4
value @ VM2147:4
value @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
VapiCall.jsx:317 Window message received for processing: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
useVapiCall.js?t=1748874308796:1163 Direct Vapi call started successfully: {id: '7f145bb5-4eb5-46a1-beba-bb590adf2d20', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', createdAt: '2025-06-02T14:28:56.521Z', updatedAt: '2025-06-02T14:28:56.521Z', type: 'webCall', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
VapiCall.jsx:317 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17488745364500.7050568045012876', …}
VapiCall.jsx:317 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17488745364500.7050568045012876', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17488745364500.7050568045012876', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17488745364500.7050568045012876', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17488745364500.7050568045012876', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17488745364500.7050568045012876', …}
VM2147:4 KrispSDK - The KrispSDK is duplicated. Please ensure that the SDK is only imported once.
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2430 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1748874308796:29 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2430 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17488745375970.7910293870844259', error: null, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17488745375970.7910293870844259', error: null, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17488745375970.7910293870844259', error: null, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
VapiCall.jsx:317 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17488745375970.7910293870844259', error: null, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17488745375970.7910293870844259', error: null, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
Refused to load the script 'blob:http://localhost:5174/d79443df-abe9-4759-8e6d-4e2791a235aa' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

simple-preview:1 Refused to load the script 'blob:http://localhost:5174/d79443df-abe9-4759-8e6d-4e2791a235aa' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

VM2147:4 KrispSDK - KrispSDK:createNoiseFilter AbortError: Unable to load a worklet's module.
error @ VM2147:4
createNoiseFilter @ VM2147:4
await in createNoiseFilter
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
W @ VM2147:4
V @ VM2147:4
A.micPreferences @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
dispatch @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
J @ VM2147:4
O @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
i @ VM2147:4
postMessage
value @ @vapi-ai_web.js?v=0856315e:4586
value @ @vapi-ai_web.js?v=0856315e:6200
(anonymous) @ @vapi-ai_web.js?v=0856315e:5286
(anonymous) @ @vapi-ai_web.js?v=0856315e:5285
h @ @vapi-ai_web.js?v=0856315e:192
a2 @ @vapi-ai_web.js?v=0856315e:204
(anonymous) @ @vapi-ai_web.js?v=0856315e:209
(anonymous) @ @vapi-ai_web.js?v=0856315e:201
o.value @ @vapi-ai_web.js?v=0856315e:5291
start @ @vapi-ai_web.js?v=0856315e:8681
await in start
(anonymous) @ useVapiCall.js?t=1748874308796:1162
Promise.then
(anonymous) @ useVapiCall.js?t=1748874308796:1110
loadSubdomainConfig @ useVapiCall.js?t=1748874308796:758
(anonymous) @ useVapiCall.js?t=1748874308796:765
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VM2147:4 Uncaught (in promise) Error: WORKLET_NOT_SUPPORTED
    at E.createNoiseFilter (eval at <anonymous> (@vapi-ai_web.js?v=0856315e:4806:11), <anonymous>:4:2782345)
createNoiseFilter @ VM2147:4
await in createNoiseFilter
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
W @ VM2147:4
V @ VM2147:4
A.micPreferences @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
dispatch @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
J @ VM2147:4
O @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
i @ VM2147:4
postMessage
value @ @vapi-ai_web.js?v=0856315e:4586
value @ @vapi-ai_web.js?v=0856315e:6200
(anonymous) @ @vapi-ai_web.js?v=0856315e:5286
(anonymous) @ @vapi-ai_web.js?v=0856315e:5285
h @ @vapi-ai_web.js?v=0856315e:192
a2 @ @vapi-ai_web.js?v=0856315e:204
(anonymous) @ @vapi-ai_web.js?v=0856315e:209
(anonymous) @ @vapi-ai_web.js?v=0856315e:201
o.value @ @vapi-ai_web.js?v=0856315e:5291
start @ @vapi-ai_web.js?v=0856315e:8681
await in start
(anonymous) @ useVapiCall.js?t=1748874308796:1162
Promise.then
(anonymous) @ useVapiCall.js?t=1748874308796:1110
loadSubdomainConfig @ useVapiCall.js?t=1748874308796:758
(anonymous) @ useVapiCall.js?t=1748874308796:765
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-5bc5896ddc-t8kr9', what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-5bc5896ddc-t8kr9', what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: sfu
VapiCall.jsx:294 Global iframe message received: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-5bc5896ddc-t8kr9', what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-5bc5896ddc-t8kr9', what: 'iframe-call-message', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-5bc5896ddc-t8kr9', what: 'iframe-call-message', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-participant', id: 'd14da657-62db-42e7-9573-a38d9cd4a6ae', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-participant', id: 'd14da657-62db-42e7-9573-a38d9cd4a6ae', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'update-participant', id: 'd14da657-62db-42e7-9573-a38d9cd4a6ae', properties: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-participant', id: 'd14da657-62db-42e7-9573-a38d9cd4a6ae', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'update-participant', id: 'd14da657-62db-42e7-9573-a38d9cd4a6ae', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-participant', id: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-participant', id: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'update-participant', id: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', properties: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-participant', id: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'update-participant', id: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:536 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:383 SpeechParticles: Processing audio level event: {d14da657-62db-42e7-9573-a38d9cd4a6ae: 0, d45aeb7f-b793-413c-9af1-3fb63ee4e6bf: 0}
SpeechParticles.jsx:402 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'volume-level' received: 0
VapiCall.jsx:1079 Direct volume-level event received from Vapi: 0
VapiCall.jsx:1082 Setting volume level to: 0
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:1096 VapiCall: Dispatched volume change event from direct handler: 0
VapiCall.jsx:1106 Directly updating 0 volume bars in the DOM
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:536 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:383 SpeechParticles: Processing audio level event: {d14da657-62db-42e7-9573-a38d9cd4a6ae: 0, d45aeb7f-b793-413c-9af1-3fb63ee4e6bf: 0}
SpeechParticles.jsx:402 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'volume-level' received: 0
VapiCall.jsx:1079 Direct volume-level event received from Vapi: 0
VapiCall.jsx:1082 Setting volume level to: 0
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:1096 VapiCall: Dispatched volume change event from direct handler: 0
VapiCall.jsx:1106 Directly updating 0 volume bars in the DOM
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:536 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:383 SpeechParticles: Processing audio level event: {d14da657-62db-42e7-9573-a38d9cd4a6ae: 0, d45aeb7f-b793-413c-9af1-3fb63ee4e6bf: 0}
SpeechParticles.jsx:402 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'volume-level' received: 0
VapiCall.jsx:1079 Direct volume-level event received from Vapi: 0
VapiCall.jsx:1082 Setting volume level to: 0
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:1096 VapiCall: Dispatched volume change event from direct handler: 0
VapiCall.jsx:1106 Directly updating 0 volume bars in the DOM
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: 'listening', fromId: 'd14da657-62db-42e7-9573-a38d9cd4a6ae', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: 'listening', fromId: 'd14da657-62db-42e7-9573-a38d9cd4a6ae', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: 'listening', fromId: 'd14da657-62db-42e7-9573-a38d9cd4a6ae', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: 'listening', fromId: 'd14da657-62db-42e7-9573-a38d9cd4a6ae', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'app-message', data: 'listening', fromId: 'd14da657-62db-42e7-9573-a38d9cd4a6ae', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
useVapiCall.js?t=1748874308796:55 Call started - setting status to CONNECTED
useVapiCall.js?t=1748874308796:55 Call started - setting status to CONNECTED
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'call-start' received: undefined
useVapiCall.js?t=1748874308796:55 Call started - setting status to CONNECTED
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
useVapiCall.js?t=1748874308796:212 Received message: {type: 'status-update', status: 'in-progress'}
useVapiCall.js?t=1748874308796:212 Received message: {type: 'status-update', status: 'in-progress'}
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'message' received: {type: 'status-update', status: 'in-progress'}
VapiCall.jsx:577 Direct message received from Vapi: {type: 'status-update', status: 'in-progress'}
VapiCall.jsx:668 Skipping empty message
useVapiCall.js?t=1748874308796:212 Received message: {type: 'status-update', status: 'in-progress'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:536 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:383 SpeechParticles: Processing audio level event: {d14da657-62db-42e7-9573-a38d9cd4a6ae: 0, d45aeb7f-b793-413c-9af1-3fb63ee4e6bf: 0}
SpeechParticles.jsx:402 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'volume-level' received: 0
VapiCall.jsx:1079 Direct volume-level event received from Vapi: 0
VapiCall.jsx:1082 Setting volume level to: 0
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:1096 VapiCall: Dispatched volume change event from direct handler: 0
VapiCall.jsx:1106 Directly updating 0 volume bars in the DOM
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:536 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:383 SpeechParticles: Processing audio level event: {d14da657-62db-42e7-9573-a38d9cd4a6ae: 0, d45aeb7f-b793-413c-9af1-3fb63ee4e6bf: 0}
SpeechParticles.jsx:402 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'volume-level' received: 0
VapiCall.jsx:1079 Direct volume-level event received from Vapi: 0
VapiCall.jsx:1082 Setting volume level to: 0
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:1096 VapiCall: Dispatched volume change event from direct handler: 0
VapiCall.jsx:1106 Directly updating 0 volume bars in the DOM
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: 'd45aeb7f-b793-413c-9af1-3fb63ee4e6bf', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', …}
useVapiCall.js?t=1748874308796:212 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
useVapiCall.js?t=1748874308796:212 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'message' received: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
VapiCall.jsx:577 Direct message received from Vapi: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
VapiCall.jsx:668 Skipping empty message
useVapiCall.js?t=1748874308796:212 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:536 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:383 SpeechParticles: Processing audio level event: {d14da657-62db-42e7-9573-a38d9cd4a6ae: 0, d45aeb7f-b793-413c-9af1-3fb63ee4e6bf: 0}
SpeechParticles.jsx:402 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'volume-level' received: 0
VapiCall.jsx:1079 Direct volume-level event received from Vapi: 0
VapiCall.jsx:1082 Setting volume level to: 0
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:1096 VapiCall: Dispatched volume change event from direct handler: 0
VapiCall.jsx:1106 Directly updating 0 volume bars in the DOM
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:536 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:383 SpeechParticles: Processing audio level event: {d14da657-62db-42e7-9573-a38d9cd4a6ae: 0, d45aeb7f-b793-413c-9af1-3fb63ee4e6bf: 0}
SpeechParticles.jsx:402 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'volume-level' received: 0
VapiCall.jsx:1079 Direct volume-level event received from Vapi: 0
VapiCall.jsx:1082 Setting volume level to: 0
SpeechParticles.jsx:502 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:1096 VapiCall: Dispatched volume change event from direct handler: 0
VapiCall.jsx:1106 Directly updating 0 volume bars in the DOM
VM2147:4 Meeting ended due to ejection: Meeting has ended
value @ VM2147:4
value @ VM2147:4
F @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
msgSigChannel @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
VM2147:4 error in meeting cleanup: TypeError: Cannot read properties of null (reading 'dispose')
value @ VM2147:4
value @ VM2147:4
value @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
Promise.then
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
msgSigChannel @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
useVapiCall.js?t=1748874308796:93 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17488745364500.7050568045012876'}
onError @ useVapiCall.js?t=1748874308796:93
emit @ @vapi-ai_web.js?v=0856315e:6662
emit @ @vapi-ai_web.js?v=0856315e:8501
(anonymous) @ @vapi-ai_web.js?v=0856315e:8590
S.emit @ @vapi-ai_web.js?v=0856315e:2791
value @ @vapi-ai_web.js?v=0856315e:6409
value @ @vapi-ai_web.js?v=0856315e:6266
i2 @ @vapi-ai_web.js?v=0856315e:4564
postMessage
value @ VM2147:4
value @ VM2147:4
value @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
msgSigChannel @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
useVapiCall.js?t=1748874308796:94 Continuing despite Vapi error
onError @ useVapiCall.js?t=1748874308796:94
emit @ @vapi-ai_web.js?v=0856315e:6662
emit @ @vapi-ai_web.js?v=0856315e:8501
(anonymous) @ @vapi-ai_web.js?v=0856315e:8590
S.emit @ @vapi-ai_web.js?v=0856315e:2791
value @ @vapi-ai_web.js?v=0856315e:6409
value @ @vapi-ai_web.js?v=0856315e:6266
i2 @ @vapi-ai_web.js?v=0856315e:4564
postMessage
value @ VM2147:4
value @ VM2147:4
value @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
msgSigChannel @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
useVapiCall.js?t=1748874308796:93 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17488745364500.7050568045012876'}
onError @ useVapiCall.js?t=1748874308796:93
emit @ @vapi-ai_web.js?v=0856315e:6662
emit @ @vapi-ai_web.js?v=0856315e:8501
(anonymous) @ @vapi-ai_web.js?v=0856315e:8590
S.emit @ @vapi-ai_web.js?v=0856315e:2791
value @ @vapi-ai_web.js?v=0856315e:6409
value @ @vapi-ai_web.js?v=0856315e:6266
i2 @ @vapi-ai_web.js?v=0856315e:4564
postMessage
value @ VM2147:4
value @ VM2147:4
value @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
msgSigChannel @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
useVapiCall.js?t=1748874308796:94 Continuing despite Vapi error
onError @ useVapiCall.js?t=1748874308796:94
emit @ @vapi-ai_web.js?v=0856315e:6662
emit @ @vapi-ai_web.js?v=0856315e:8501
(anonymous) @ @vapi-ai_web.js?v=0856315e:8590
S.emit @ @vapi-ai_web.js?v=0856315e:2791
value @ @vapi-ai_web.js?v=0856315e:6409
value @ @vapi-ai_web.js?v=0856315e:6266
i2 @ @vapi-ai_web.js?v=0856315e:4564
postMessage
value @ VM2147:4
value @ VM2147:4
value @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
msgSigChannel @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'error' received: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17488745364500.7050568045012876'}
useVapiCall.js?t=1748874308796:93 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17488745364500.7050568045012876'}
onError @ useVapiCall.js?t=1748874308796:93
emit @ @vapi-ai_web.js?v=0856315e:6662
emit @ @vapi-ai_web.js?v=0856315e:8501
(anonymous) @ @vapi-ai_web.js?v=0856315e:8590
S.emit @ @vapi-ai_web.js?v=0856315e:2791
value @ @vapi-ai_web.js?v=0856315e:6409
value @ @vapi-ai_web.js?v=0856315e:6266
i2 @ @vapi-ai_web.js?v=0856315e:4564
postMessage
value @ VM2147:4
value @ VM2147:4
value @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
msgSigChannel @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
useVapiCall.js?t=1748874308796:94 Continuing despite Vapi error
onError @ useVapiCall.js?t=1748874308796:94
emit @ @vapi-ai_web.js?v=0856315e:6662
emit @ @vapi-ai_web.js?v=0856315e:8501
(anonymous) @ @vapi-ai_web.js?v=0856315e:8590
S.emit @ @vapi-ai_web.js?v=0856315e:2791
value @ @vapi-ai_web.js?v=0856315e:6409
value @ @vapi-ai_web.js?v=0856315e:6266
i2 @ @vapi-ai_web.js?v=0856315e:4564
postMessage
value @ VM2147:4
value @ VM2147:4
value @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
msgSigChannel @ VM2147:4
eval @ VM2147:4
e @ VM2147:4
n @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
eval @ VM2147:4
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
SpeechParticles.jsx:376 SpeechParticles: Received window message event: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17488745364500.7050568045012876', from: 'embedded'}
useVapiCall.js?t=1748874308796:77 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: connecting -> ended
EnhancedPreviewNew.jsx:476 Ending call...
useVapiCall.js?t=1748874308796:77 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:476 Ending call...
VapiCall.jsx:908 🔍 DEBUG: Vapi event 'call-end' received: undefined
useVapiCall.js?t=1748874308796:77 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:476 Ending call...
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
useVapiCall.js?t=1748874308796:890 Component unmounting - performing Vapi cleanup
VapiService.js:18 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js:18 [VapiService] Event listeners removed from Vapi instance
useVapiCall.js?t=1748874308796:899 Stopping active call during cleanup
useVapiCall.js?t=1748874308796:905 Calling onEndCall callback during unmount
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:476 Ending call...
VapiCall.jsx:1361 Removed all event listeners from Vapi instance
VapiCall.jsx:1639 VapiCall component unmounting, performing cleanup...
VapiCall.jsx:1654 Call was not fully initialized, performing simple cleanup
VapiCall.jsx:1664 Skipping onEndCall for non-initialized state to prevent mount/unmount cycle
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
SpeechParticles.jsx:231 SpeechParticles: Stopping microphone tracks
EnhancedPreviewNew.jsx:450 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:451 firmName: LegalScout
EnhancedPreviewNew.jsx:452 titleText: LegalScout
EnhancedPreviewNew.jsx:453 logoUrl: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:454 primaryColor: #aa4b4b
EnhancedPreviewNew.jsx:455 secondaryColor: #46ce93
EnhancedPreviewNew.jsx:456 vapiInstructions: You will guide the user through jurrasic park
EnhancedPreviewNew.jsx:457 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:458 voiceId: sarah
EnhancedPreviewNew.jsx:459 voiceProvider: playht
EnhancedPreviewNew.jsx:460 chatActive: false
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
EnhancedPreviewNew.jsx:532 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
