dashboard:21 ✅ Vapi public key set globally
dashboard:42 ✅ Supabase keys set globally - should load correct assistant by domain
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
dashboard:52 🚀 [EMERGENCY] Starting emergency critical fixes...
dashboard:56 🔧 [EMERGENCY] Adding process polyfill
dashboard:63 ✅ [EMERGENCY] Process polyfill added
dashboard:74 🔧 [EMERGENCY] Development mode: false (forced production)
dashboard:101 ✅ [EMERGENCY] Fetch patched
dashboard:104 🎉 [EMERGENCY] Emergency fixes complete!
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
overrideMethod @ hook.js:608
(anonymous) @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
dashboard:207 Supabase loaded from CDN
dashboard:217 Creating Supabase client from CDN
dashboard:221 Supabase client created from CDN
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js?t=1749064041685:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749064041685:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749064041685:1420 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
initAttorneyProfileManager.js?t=1749064041685:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749064041685:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js?t=1749064041685:44 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: true, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js?t=1749064041685:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js?t=1749064041685:233 [VapiMcpService] Production mode: false
vapiMcpService.js?t=1749064041685:234 [VapiMcpService] Development mode: true
vapiMcpService.js?t=1749064041685:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js?t=1749064041685:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js?t=1749064041685:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js?t=1749064041685:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js?t=1749064041685:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749064041685:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749064041685:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749064041685:128 [initAttorneyProfileManager] Initialization complete
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
disable-automatic-assistant-creation.js:247 [DisableAutomaticAssistantCreation] Services found, applying patches
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: null
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:77 [useStandaloneAttorney] Initializing hook with manager...
useStandaloneAttorney.js:84 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
useStandaloneAttorney.js:92 [useStandaloneAttorney] Attorney is an array, taking first element
useStandaloneAttorney.js:97 [useStandaloneAttorney] Found attorney in localStorage: {id: '873b8f3a-2743-4c8f-861a-aac4503692a6', created_at: '2025-05-09T15:47:34.927+00:00', updated_at: '2025-06-04T13:12:43.515048+00:00', subdomain: 'scout', firm_name: 'LegalScout System', …}
useStandaloneAttorney.js:116 [useStandaloneAttorney] Setting initial attorney: {id: '873b8f3a-2743-4c8f-861a-aac4503692a6', created_at: '2025-05-09T15:47:34.927+00:00', updated_at: '2025-06-04T13:12:43.515048+00:00', subdomain: 'scout', firm_name: 'LegalScout System', …}
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '873b8f3a-2743-4c8f-861a-aac4503692a6', created_at: '2025-05-09T15:47:34.927+00:00', updated_at: '2025-06-04T13:12:43.515048+00:00', subdomain: 'scout', firm_name: 'LegalScout System', …}
DashboardNew.jsx:130 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
DashboardNew.jsx:176 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:177 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:393 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749032804912:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
testSupabase.js?t=1749032804912:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: null
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:77 [useStandaloneAttorney] Initializing hook with manager...
useStandaloneAttorney.js:116 [useStandaloneAttorney] Setting initial attorney: {id: '873b8f3a-2743-4c8f-861a-aac4503692a6', created_at: '2025-05-09T15:47:34.927+00:00', updated_at: '2025-06-04T13:12:43.515048+00:00', subdomain: 'scout', firm_name: 'LegalScout System', …}
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '873b8f3a-2743-4c8f-861a-aac4503692a6', created_at: '2025-05-09T15:47:34.927+00:00', updated_at: '2025-06-04T13:12:43.515048+00:00', subdomain: 'scout', firm_name: 'LegalScout System', …}
DashboardNew.jsx:130 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
DashboardNew.jsx:176 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:177 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:393 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749032804912:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
testSupabase.js?t=1749032804912:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '873b8f3a-2743-4c8f-861a-aac4503692a6', created_at: '2025-05-09T15:47:34.927+00:00', updated_at: '2025-06-04T13:12:43.515048+00:00', subdomain: 'scout', firm_name: 'LegalScout System', …}
index.ts:5 Loaded contentScript
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '873b8f3a-2743-4c8f-861a-aac4503692a6', created_at: '2025-05-09T15:47:34.927+00:00', updated_at: '2025-06-04T13:12:43.515048+00:00', subdomain: 'scout', firm_name: 'LegalScout System', …}
ProfileTab.jsx:54 User object in ProfileTab: null
DashboardNew.jsx:487 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:488 [DashboardNew] Attorney Vapi Assistant ID: null
DashboardNew.jsx:518 [DashboardNew] Updated preview config with assistant ID: null
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:518 [DashboardNew] Updated preview config with assistant ID: null
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
AuthContext.jsx:85 OAuth user data: {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
supabase.js?t=1749032804912:118 Supabase connection test successful!
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
AuthContext.jsx:85 OAuth user data: {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
debugConfig.js:30 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
DashboardNew.jsx:134 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
AutoAssistantReconciler.js?t=1749056309798:276 [AutoReconciler] 🚀 Initializing auto-reconciliation
DashboardNew.jsx:176 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:177 [DashboardNew] Dependencies: user?.id=78f3b504-eb67-4480-9fc2-20cb8ccd59dc, authIsLoading=false, loadAttorneyForUser exists=true
DashboardNew.jsx:390 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
DashboardNew.jsx:201 [DashboardNew] fetchAttorneyData called.
DashboardNew.jsx:282 [DashboardNew] Attempting to load attorney for user: 78f3b504-eb67-4480-9fc2-20cb8ccd59dc
DashboardNew.jsx:287 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
fix-enhance-attorney-manager.js:48 [FixEnhanceAttorneyManager] Fixed loadAttorneyForUser called with userId: 78f3b504-eb67-4480-9fc2-20cb8ccd59dc
fix-enhance-attorney-manager.js:52 [FixEnhanceAttorneyManager] Already have attorney with this user ID, returning current attorney
DashboardNew.jsx:321 [DashboardNew] Attorney loaded successfully: 873b8f3a-2743-4c8f-861a-aac4503692a6
DashboardNew.jsx:325 [DashboardNew] Attorney has no VAPI assistant ID, creating one...
vapiAssistantUtils.js?t=1749057552616:34 Creating VAPI assistant for attorney...
vapiAssistantUtils.js?t=1749057552616:39 MCP not available, keeping existing assistant ID if available
overrideMethod @ hook.js:608
ensureVapiAssistant @ vapiAssistantUtils.js?t=1749057552616:39
fetchAttorneyData @ DashboardNew.jsx:327
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:391
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
vapiAssistantUtils.js?t=1749057552616:48 No existing assistant ID, creating new assistant through Vapi
vapiAssistantUtils.js?t=1749057552616:49 Attorney object before creation: {id: '873b8f3a-2743-4c8f-861a-aac4503692a6', email: '<EMAIL>', vapi_assistant_id: null}
vapiAssistantUtils.js?t=1749057552616:80 Creating assistant with config: {name: 'LegalScout System Legal Assistant', firstMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", firstMessageMode: 'assistant-speaks-first', model: {…}, voice: {…}, …}
vapiAssistantUtils.js?t=1749057552616:85 Using private key for assistant creation: 6734febc...
disable-automatic-assistant-creation.js:184 [DisableAutomaticAssistantCreation] Prevented fetch POST to assistant endpoint, using existing assistant
vapiAssistantUtils.js?t=1749057552616:108 Created VAPI assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.873b8f3a-2743-4c8f-861a-aac4503692a6 with headers: {accept: 'application/json', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-profile: 'public', content-type: 'application/json', …}
consolidated-dashboard-fix.js:206 
            
            
           PATCH https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.873b8f3a-2743-4c8f-861a-aac4503692a6 400 (Bad Request)
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3900
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3921
fulfilled @ @supabase_supabase-js.js?v=158b513d:3873
Promise.then
step @ @supabase_supabase-js.js?v=158b513d:3886
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3888
__awaiter6 @ @supabase_supabase-js.js?v=158b513d:3870
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3911
then @ @supabase_supabase-js.js?v=158b513d:89
vapiAssistantUtils.js?t=1749057552616:124 Error updating attorney with assistant ID: {code: 'PGRST102', details: null, hint: null, message: 'Content-Type not acceptable: application/json, application/json'}
overrideMethod @ hook.js:608
ensureVapiAssistant @ vapiAssistantUtils.js?t=1749057552616:124
await in ensureVapiAssistant
fetchAttorneyData @ DashboardNew.jsx:327
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:391
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
vapiAssistantUtils.js?t=1749057552616:131 Error creating VAPI assistant: Error: Failed to save assistant ID to database: Content-Type not acceptable: application/json, application/json
    at ensureVapiAssistant (vapiAssistantUtils.js?t=1749057552616:125:13)
    at async fetchAttorneyData (DashboardNew.jsx:327:32)
overrideMethod @ hook.js:608
ensureVapiAssistant @ vapiAssistantUtils.js?t=1749057552616:131
await in ensureVapiAssistant
fetchAttorneyData @ DashboardNew.jsx:327
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:391
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
DashboardNew.jsx:338 [DashboardNew] Error creating VAPI assistant: Error: Failed to save assistant ID to database: Content-Type not acceptable: application/json, application/json
    at ensureVapiAssistant (vapiAssistantUtils.js?t=1749057552616:125:13)
    at async fetchAttorneyData (DashboardNew.jsx:327:32)
overrideMethod @ hook.js:608
fetchAttorneyData @ DashboardNew.jsx:338
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:391
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '873b8f3a-2743-4c8f-861a-aac4503692a6', created_at: '2025-05-09T15:47:34.927+00:00', updated_at: '2025-06-04T13:12:43.515048+00:00', subdomain: 'scout', firm_name: 'LegalScout System', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '873b8f3a-2743-4c8f-861a-aac4503692a6', created_at: '2025-05-09T15:47:34.927+00:00', updated_at: '2025-06-04T13:12:43.515048+00:00', subdomain: 'scout', firm_name: 'LegalScout System', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
(anonymous) @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:207 Supabase loaded from CDN
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:217 Creating Supabase client from CDN
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:221 Supabase client created from CDN
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:207 Supabase loaded from CDN
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:217 Creating Supabase client from CDN
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:221 Supabase client created from CDN
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js?t=1749064041685:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749064041685:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749064041685:1420 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
initAttorneyProfileManager.js?t=1749064041685:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749064041685:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js?t=1749064041685:44 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js?t=1749064041685:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js?t=1749064041685:233 [VapiMcpService] Production mode: false
vapiMcpService.js?t=1749064041685:234 [VapiMcpService] Development mode: true
vapiMcpService.js?t=1749064041685:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js?t=1749064041685:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js?t=1749064041685:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js?t=1749064041685:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js?t=1749064041685:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749064041685:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749064041685:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749064041685:128 [initAttorneyProfileManager] Initialization complete
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
disable-automatic-assistant-creation.js:247 [DisableAutomaticAssistantCreation] Services found, applying patches
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js?t=1749064041685:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749064041685:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749064041685:1420 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
initAttorneyProfileManager.js?t=1749064041685:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749064041685:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js?t=1749064041685:44 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js?t=1749064041685:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js?t=1749064041685:233 [VapiMcpService] Production mode: false
vapiMcpService.js?t=1749064041685:234 [VapiMcpService] Development mode: true
vapiMcpService.js?t=1749064041685:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js?t=1749064041685:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js?t=1749064041685:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js?t=1749064041685:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js?t=1749064041685:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749064041685:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749064041685:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749064041685:128 [initAttorneyProfileManager] Initialization complete
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
ActiveCheckHelper.ts:21 received intentional event
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 [DashboardNew] Tab changed to: agent
 [AgentTab] No Vapi assistant ID found, skipping voice sync
 [AgentTab] No Vapi assistant ID found, skipping voice sync
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'scout', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: scout
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: scout
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'scout', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: scout
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: scout
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
testSupabase.js?t=1749032804912:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
disable-automatic-assistant-creation.js:247 [DisableAutomaticAssistantCreation] Services found, applying patches
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
DashboardNew.jsx:1400 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'scout', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: scout
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: scout
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'scout', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: scout
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: scout
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
DashboardNew.jsx:1423 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064583928}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064583928}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064583933}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064583933}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
(anonymous) @ consolidated-dashboard-fix.js:86
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:681
(anonymous) @ DashboardNew.jsx:521
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14602
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
(anonymous) @ consolidated-dashboard-fix.js:86
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:681
(anonymous) @ DashboardNew.jsx:521
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14602
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
(anonymous) @ consolidated-dashboard-fix.js:86
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:681
(anonymous) @ DashboardNew.jsx:521
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14607
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
(anonymous) @ consolidated-dashboard-fix.js:86
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:681
(anonymous) @ DashboardNew.jsx:521
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14607
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584327}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584327}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584329}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584329}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584342}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584342}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584344}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584344}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [UnifiedBannerFix] Ensuring upload interface is visible
 updating page active status
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584538}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584538}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584542}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584542}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584605}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584605}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584613}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584613}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584731}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584731}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584739}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064584739}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
supabase.js?t=1749032804912:118 Supabase connection test successful!
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:207 Supabase loaded from CDN
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:217 Creating Supabase client from CDN
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:221 Supabase client created from CDN
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:681
(anonymous) @ DashboardNew.jsx:521
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14602
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:681
(anonymous) @ DashboardNew.jsx:521
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14602
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
DashboardNew.jsx:684 [DashboardNew] Config sent to 0 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:681
(anonymous) @ DashboardNew.jsx:521
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14607
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:681
(anonymous) @ DashboardNew.jsx:521
basicStateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11723
updateReducer @ chunk-Q72EVS5P.js?v=704ffe31:11814
updateState @ chunk-Q72EVS5P.js?v=704ffe31:12041
useState @ chunk-Q72EVS5P.js?v=704ffe31:12773
useState @ chunk-2N3A5BUM.js?v=704ffe31:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=704ffe31:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=704ffe31:14607
beginWork @ chunk-Q72EVS5P.js?v=704ffe31:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=704ffe31:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=704ffe31:19226
workLoopSync @ chunk-Q72EVS5P.js?v=704ffe31:19165
renderRootSync @ chunk-Q72EVS5P.js?v=704ffe31:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18706
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
DashboardNew.jsx:684 [DashboardNew] Config sent to 0 iframes successfully
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.873b8f3a-2743-4c8f-861a-aac4503692a6 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.873b8f3a-2743-4c8f-861a-aac4503692a6 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
AuthContext.jsx:85 OAuth user data: {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:85 OAuth user data: {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.scout with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.scout with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.78f3b504-eb67-4480-9fc2-20cb8ccd59dc with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
AuthContext.jsx:85 OAuth user data: {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:85 OAuth user data: {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.scout with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.scout with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [AutoReconciler] Invalid attorney object provided: [{…}]
overrideMethod @ installHook.js:1
reconcileAttorney @ AutoAssistantReconciler.js:147
checkAndFixCurrentUser @ AutoAssistantReconciler.js:262
await in checkAndFixCurrentUser
(anonymous) @ AutoAssistantReconciler.js:280
setTimeout
(anonymous) @ consolidated-dashboard-fix.js:86
initializeForDashboard @ AutoAssistantReconciler.js:279
(anonymous) @ DashboardNew.jsx:118
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: [{…}]
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586299}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586299}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586299}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586301}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586301}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: null
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586301}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: LegalScout System
EnhancedPreviewNew.jsx:462 titleText: LegalScout System
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: [{…}]
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Supabase connection test successful!
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586422}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586422}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586422}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: [{…}]
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586507}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586507}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586507}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586507}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586510}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586510}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586510}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586510}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout System
 titleText: LegalScout System
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: [{…}]
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586604}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586604}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: null
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586604}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064586604}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: null
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
debugConfig.js:30 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js?t=1749064041685:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749064041685:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749064041685:1420 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
initAttorneyProfileManager.js?t=1749064041685:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749064041685:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js?t=1749064041685:44 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js?t=1749064041685:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js?t=1749064041685:233 [VapiMcpService] Production mode: false
vapiMcpService.js?t=1749064041685:234 [VapiMcpService] Development mode: true
vapiMcpService.js?t=1749064041685:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js?t=1749064041685:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js?t=1749064041685:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js?t=1749064041685:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js?t=1749064041685:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749064041685:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749064041685:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749064041685:128 [initAttorneyProfileManager] Initialization complete
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
disable-automatic-assistant-creation.js:247 [DisableAutomaticAssistantCreation] Services found, applying patches
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
 [ReactPolyfill] Stopped monitoring React.createContext
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'scout', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: scout
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: scout
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'scout', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: scout
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: scout
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064587899}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064587899}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064587899}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064587899}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064587899}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064587904}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064587904}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064587904}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064587904}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064587904}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588004}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588004}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588004}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588004}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588004}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588007}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588007}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588007}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588007}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588007}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [UnifiedBannerFix] Ensuring upload interface is visible
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588262}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588262}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588262}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588262}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588262}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588308}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588308}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588308}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: null
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588308}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588308}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: null
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
AuthContext.jsx:85 OAuth user data: {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [UnifiedBannerFix] Ensuring upload interface is visible
 OAuth user data: {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: '78f3b504-eb67-4480-9fc2-20cb8ccd59dc', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:12:23.946611Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.scout with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.scout with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.agEttiILid3n3xz9ESTHtXiu_XteOy0pyUWJfLX1EFY', content-type: 'application/json', …}
 Supabase connection test successful!
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: [{…}]
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588735}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588735}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588735}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588735}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588735}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588735}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588737}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588737}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588737}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588737}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588737}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588737}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout System
 titleText: LegalScout System
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: [{…}]
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588866}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588866}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
 [EnhancedPreview] Assistant ID in customizations: null
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588866}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588866}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: null
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588866}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749064588866}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout System', attorneyName: 'Scout', practiceAreas: Array(0), state: undefined, practiceDescription: 'Professional legal assistance powered by AI technology', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: null
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: null, vapi_assistant_id: null, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, theme
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=scout&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
DashboardNew.jsx:383 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:383
setTimeout
(anonymous) @ consolidated-dashboard-fix.js:86
(anonymous) @ DashboardNew.jsx:381
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
 🚀 [PREVIEW CALL START] Starting consultation...
 🎯 [PREVIEW CALL START] Using assistant ID: null
 💬 [PREVIEW CALL START] Welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 🔊 [PREVIEW CALL START] Voice settings: {voiceId: 'sarah', voiceProvider: 'playht'}
 ✅ [PREVIEW CALL START] VapiCall will receive assistantId: null
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: null
 🎬 [PREVIEW CALL RENDER] Assistant ID type: object
 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: true
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: null
 🎬 [PREVIEW CALL RENDER] Assistant ID type: object
 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: true
 [CallDebugger:VapiCall] Debugger initialized
 useVapiCall: Using assistant ID: Not explicitly provided
 useVapiCall: Using assistant ID: Not explicitly provided
 [CallDebugger:VapiCall] Processing call configuration
 [CallDebugger:VapiCall] Using direct configuration {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
 VapiCall: Using direct configuration
 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [useVapiCall] Using provided customInstructions: {firmName: 'LegalScout System', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", voiceId: 'sarah', voiceProvider: 'playht', initialMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", …}
 [useVapiCall] Checking for Vapi public key...
 [useVapiCall] VITE_VAPI_PUBLIC_KEY: Set
 [useVapiCall] window.VITE_VAPI_PUBLIC_KEY: Set
 [useVapiCall] Using API key: 310f0d43...
 [useVapiCall] Initializing Vapi instance with API key: 310f0d43...
 [useVapiCall] Creating Vapi instance directly using official pattern
 [useVapiCall] Loading Vapi SDK using vapiLoader
 [VapiLoader] Vapi SDK already loaded
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 🚀 VapiCall component mounted, preparing to start call...
 📊 Current status: idle
 🤖 Using assistant ID: Not set yet
 🔧 Vapi instance available: false
 ⚙️ Processed config: null
 ⏸️ Not ready to initialize yet - missing assistantId or vapi
 ⏸️ Assistant ID: undefined
 ⏸️ Vapi instance: false
 Status changed to: idle
 CALL_STATUS.CONNECTED value: connected
 🔄 VapiCall component received dossier update: {}
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout System
 titleText: LegalScout System
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: true
 🧹 Cleanup function for not ready state
 [CallDebugger:VapiCall] Processing call configuration
 [CallDebugger:VapiCall] Using direct configuration {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
 VapiCall: Using direct configuration
 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [useVapiCall] Using provided customInstructions: {firmName: 'LegalScout System', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", voiceId: 'sarah', voiceProvider: 'playht', initialMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", …}
 [VapiMcpService] Production mode: false
vapiMcpService.js?t=1749064041685:234 [VapiMcpService] Development mode: true
vapiMcpService.js?t=1749064041685:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js?t=1749064041685:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js?t=1749064041685:246 [VapiMcpService] API key being used: 310f0d43...
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: Not set yet
VapiCall.jsx:1322 🔧 Vapi instance available: false
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1338 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1339 ⏸️ Assistant ID: undefined
VapiCall.jsx:1340 ⏸️ Vapi instance: false
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1857 🔄 VapiCall component received dossier update: {}
useVapiCall.js?t=1749064041685:826 [useVapiCall] Creating Vapi instance with API key: 310f0d43...
vapiLoader.js:165 [VapiLoader] ✅ Vapi instance created with key: 310f0d43...
vapiEmissionsService.js?t=1749064041685:35 VapiEmissionsService: Initialized successfully
vapiEmissionsService.js?t=1749064041685:35 VapiEmissionsService: Initialized successfully
useVapiCall.js?t=1749064041685:831 [useVapiCall] Vapi instance created successfully
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: call-start
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: call-end
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: speech-start
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: speech-end
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: message
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: error
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: volume-level
VapiService.js?t=1749064041685:19 [VapiService] Event listeners set up for Vapi instance
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: Not explicitly provided
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: Not explicitly provided
VapiCall.jsx:1342 🧹 Cleanup function for not ready state
debugConfig.js:30 [useVapiCall] Using provided customInstructions: {firmName: 'LegalScout System', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", voiceId: 'sarah', voiceProvider: 'playht', initialMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", …}
useVapiCall.js?t=1749064041685:1102 Requesting microphone permission...
useVapiCall.js?t=1749064041685:879 Setting up Vapi event listeners
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: call-start
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: call-end
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: speech-start
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: speech-end
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: message
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: error
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: volume-level
VapiService.js?t=1749064041685:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:551 SKIPPING direct event handler setup - using useVapiCall callbacks to prevent conflicts
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: Not set yet
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1338 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1339 ⏸️ Assistant ID: undefined
VapiCall.jsx:1340 ⏸️ Vapi instance: true
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:627 Setting initial message: Hello! I'm Scout, your legal assistant. How can I help you today?
useVapiCall.js?t=1749064041685:629 Setting up call params with custom instructions and assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:653 Direct API parameters found: {}
useVapiCall.js?t=1749064041685:671 Final assistantOverrides: {recordingEnabled: true, context: '', firstMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", firstMessageMode: 'assistant-speaks-first', systemPrompt: 'Present (length: 1938)'}
useVapiCall.js?t=1749064041685:627 Setting initial message: Hello! I'm Scout, your legal assistant. How can I help you today?
useVapiCall.js?t=1749064041685:629 Setting up call params with custom instructions and assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:653 Direct API parameters found: {}
useVapiCall.js?t=1749064041685:671 Final assistantOverrides: {recordingEnabled: true, context: '', firstMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", firstMessageMode: 'assistant-speaks-first', systemPrompt: 'Present (length: 1938)'}
useVapiCall.js?t=1749064041685:627 Setting initial message: Hello! I'm Scout, your legal assistant. How can I help you today?
useVapiCall.js?t=1749064041685:629 Setting up call params with custom instructions and assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:653 Direct API parameters found: {}
useVapiCall.js?t=1749064041685:671 Final assistantOverrides: {recordingEnabled: true, context: '', firstMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", firstMessageMode: 'assistant-speaks-first', systemPrompt: 'Present (length: 1938)'}
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:627 Setting initial message: Hello! I'm Scout, your legal assistant. How can I help you today?
useVapiCall.js?t=1749064041685:629 Setting up call params with custom instructions and assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:653 Direct API parameters found: {}
useVapiCall.js?t=1749064041685:671 Final assistantOverrides: {recordingEnabled: true, context: '', firstMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", firstMessageMode: 'assistant-speaks-first', systemPrompt: 'Present (length: 1938)'}
useVapiCall.js?t=1749064041685:627 Setting initial message: Hello! I'm Scout, your legal assistant. How can I help you today?
useVapiCall.js?t=1749064041685:629 Setting up call params with custom instructions and assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:653 Direct API parameters found: {}
useVapiCall.js?t=1749064041685:671 Final assistantOverrides: {recordingEnabled: true, context: '', firstMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", firstMessageMode: 'assistant-speaks-first', systemPrompt: 'Present (length: 1938)'}
useVapiCall.js?t=1749064041685:627 Setting initial message: Hello! I'm Scout, your legal assistant. How can I help you today?
useVapiCall.js?t=1749064041685:629 Setting up call params with custom instructions and assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:653 Direct API parameters found: {}
useVapiCall.js?t=1749064041685:671 Final assistantOverrides: {recordingEnabled: true, context: '', firstMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", firstMessageMode: 'assistant-speaks-first', systemPrompt: 'Present (length: 1938)'}
VapiCall.jsx:1342 🧹 Cleanup function for not ready state
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1348 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1355 ⏱️ Using initialization delay of 500ms before starting call
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:1107 Available audio input devices: (5) [{…}, {…}, {…}, {…}, {…}]
useVapiCall.js?t=1749064041685:1125 Using audio constraints: {audio: {…}}
useVapiCall.js?t=1749064041685:1129 Microphone permission granted with device: Default - Microphone (Yeti Classic)
useVapiCall.js?t=1749064041685:1177 Starting call with existing Vapi instance using direct pattern
vapiMcpDebugger.js:175 [Vapi MCP] POST https://api.vapi.ai/call/web
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/call/web with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
VapiCall.jsx:1364 🎯 Auto-starting call from VapiCall component after delay
VapiCall.jsx:1365 📋 Current call parameters: {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantOverrides: 'Set', vapi: true, processedConfig: true}
VapiCall.jsx:1402 🎯 [VAPI CALL INIT] Final assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1403 ⚙️ [VAPI CALL INIT] Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1408 🔄 Starting call attempt 1
VapiCall.jsx:190 [VapiCall] Starting call with: {finalAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', attorneyDataAssistantId: undefined, processedConfigAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', propAssistantId: null, forceDefaultAssistant: false, …}
callDebugger.js:73 [CallDebugger:VapiCall] Starting call {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', subdomain: 'scout'}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: idle -> connecting
useVapiCall.js?t=1749064041685:1217 [useVapiCall] Starting call with assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749064041685:1228 [useVapiCall] Starting call using official Vapi Web SDK pattern
useVapiCall.js?t=1749064041685:1230 [useVapiCall] Call started successfully: null
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1749064041685:884 Component unmounting - performing Vapi cleanup
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Event listeners removed from Vapi instance
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
useVapiCall.js?t=1749064041685:879 Setting up Vapi event listeners
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: call-start
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: call-end
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: speech-start
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: speech-end
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: message
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: error
VapiService.js?t=1749064041685:19 [VapiService] Setting up event listener for: volume-level
VapiService.js?t=1749064041685:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:1763 Status changed to: connected
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1768 Call connected - checking for custom welcome message
VapiCall.jsx:1789 mergedCustomInstructions: {firmName: 'LegalScout System', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", voiceId: 'sarah', voiceProvider: 'playht', initialMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", …}
VapiCall.jsx:1790 mergedAssistantOverrides: {firstMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", model: 'Not present', artifactPlan: 'Not present'}
VapiCall.jsx:1819 Found welcome message from mergedCustomInstructions.welcomeMessage: Hello! I'm Scout, your legal assistant. How can I help you today?
VapiCall.jsx:1837 Adding welcome message to UI: Hello! I'm Scout, your legal assistant. How can I help you today?
VapiCall.jsx:1842 Welcome message should be spoken by the assistant automatically
VapiCall.jsx:1845 Voice settings: {voiceId: 'sarah', voiceProvider: 'playht'}
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
VM1386 speech-particles.js:10 Speech particles: DOM already loaded, setting up immediately
SpeechParticles.jsx:47 SpeechParticles: Script loaded successfully
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #4B74AA Assistant: #2C3E50
VM1386 speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#4B74AA', assistantColor: '#2C3E50'}
VM1386 speech-particles.js:525 🎨 Converting user color: #4B74AA
VM1386 speech-particles.js:527 🎨 User HSL: {h: 214, s: 39, l: 48}
VM1386 speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
VM1386 speech-particles.js:537 🎨 Converting assistant color: #2C3E50
VM1386 speech-particles.js:539 🎨 Assistant HSL: {h: 210, s: 29, l: 24}
VM1386 speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
VM1386 speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
speech-particles.js:10 Speech particles: DOM already loaded, setting up immediately
SpeechParticles.jsx:47 SpeechParticles: Script loaded successfully
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #4B74AA Assistant: #2C3E50
speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#4B74AA', assistantColor: '#2C3E50'}
speech-particles.js:525 🎨 Converting user color: #4B74AA
speech-particles.js:527 🎨 User HSL: {h: 214, s: 39, l: 48}
speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:537 🎨 Converting assistant color: #2C3E50
speech-particles.js:539 🎨 Assistant HSL: {h: 210, s: 29, l: 24}
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
VapiCall.jsx:1665 VapiCall: Scrolled conversation area to bottom
VapiCall.jsx:1775 Added force-visible class to call interface
VM1386 speech-particles.js:309 Speech particles: Setting up visualization
VM1386 speech-particles.js:318 Speech particles: Canvas found, initializing visualization
VM1386 speech-particles.js:328 Speech particles: Visualization started
speech-particles.js:309 Speech particles: Setting up visualization
speech-particles.js:318 Speech particles: Canvas found, initializing visualization
speech-particles.js:328 Speech particles: Visualization started
vapiMcpDebugger.js:180 [Vapi MCP] Response: 201 https://api.vapi.ai/call/web
headers-fix.js:33 [HeadersFix] Fetch request to https://c.daily.co/call-machine/versioned/0.72.2/static/call-machine-object-bundle.js with headers: {Accept: 'application/json'}
VapiCall.jsx:1416 ✅ Marking call as initialized
VapiCall.jsx:1421 ✅ Set window.vapiCallActive to true after initialization
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VM1388:4 Preferred mic not found; skipping: true
value @ VM1388:4
value @ VM1388:4
Q @ VM1388:4
eval @ VM1388:4
e.getCamGumConstraints @ VM1388:4
eval @ VM1388:4
e @ VM1388:4
n @ VM1388:4
Promise.then
e @ VM1388:4
n @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
y @ VM1388:4
e.getCamStream @ VM1388:4
eval @ VM1388:4
e @ VM1388:4
n @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
iA @ VM1388:4
G.camOperation @ VM1388:4
eval @ VM1388:4
e @ VM1388:4
n @ VM1388:4
Promise.then
e @ VM1388:4
n @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
dispatch @ VM1388:4
eval @ VM1388:4
e @ VM1388:4
n @ VM1388:4
Promise.then
e @ VM1388:4
n @ VM1388:4
Promise.then
e @ VM1388:4
n @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
dispatch @ VM1388:4
eval @ VM1388:4
e @ VM1388:4
n @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
e @ VM1388:4
n @ VM1388:4
Promise.then
e @ VM1388:4
n @ VM1388:4
Promise.then
e @ VM1388:4
n @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
e @ VM1388:4
n @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
i @ VM1388:4
postMessage
value @ @vapi-ai_web.js?v=51449240:4586
value @ @vapi-ai_web.js?v=51449240:6200
(anonymous) @ @vapi-ai_web.js?v=51449240:5569
h @ @vapi-ai_web.js?v=51449240:192
a2 @ @vapi-ai_web.js?v=51449240:204
Promise.then
h @ @vapi-ai_web.js?v=51449240:196
a2 @ @vapi-ai_web.js?v=51449240:204
(anonymous) @ @vapi-ai_web.js?v=51449240:209
(anonymous) @ @vapi-ai_web.js?v=51449240:201
o.value @ @vapi-ai_web.js?v=51449240:5586
start @ @vapi-ai_web.js?v=51449240:8637
await in start
(anonymous) @ useVapiCall.js?t=1749064041685:1180
Promise.then
(anonymous) @ useVapiCall.js?t=1749064041685:1128
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js?t=1749064041685:759
(anonymous) @ useVapiCall.js?t=1749064041685:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: signaling
VapiCall.jsx:294 Global iframe message received: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
VapiCall.jsx:317 Window message received for processing: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
VM1388:4 daily-js version 0.72.2 is nearing end of support. Please upgrade to a newer version.
value @ VM1388:4
value @ VM1388:4
value @ VM1388:4
eval @ VM1388:4
e @ VM1388:4
n @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
eval @ VM1388:4
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 Window message received for processing: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 [EnhancedPreview] Received ANY message: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
VapiCall.jsx:317 Window message received for processing: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
useVapiCall.js?t=1749064041685:1181 Direct Vapi call started successfully: {id: 'e9c9ba3d-7ec3-439b-a6aa-46c03365af31', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', createdAt: '2025-06-04T19:16:34.673Z', updatedAt: '2025-06-04T19:16:34.673Z', type: 'webCall', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
VapiCall.jsx:317 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490645946190.6459845468558968', …}
VapiCall.jsx:317 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490645946190.6459845468558968', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490645946190.6459845468558968', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490645946190.6459845468558968', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490645946190.6459845468558968', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490645946190.6459845468558968', …}
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1749064041685:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#4B74AA', secondaryColor: '#2C3E50', attorneyData: null, processedConfig: {…}}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490645965550.8997944731283846', error: null, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490645965550.8997944731283846', error: null, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490645965550.8997944731283846', error: null, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
VapiCall.jsx:317 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490645965550.8997944731283846', error: null, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490645965550.8997944731283846', error: null, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-6jslp', what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-6jslp', what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: sfu
VapiCall.jsx:294 Global iframe message received: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-6jslp', what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-6jslp', what: 'iframe-call-message', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-6jslp', what: 'iframe-call-message', …}
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-participant', id: '50e2e14c-a9d7-45e3-beed-7e48af70dfef', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-participant', id: '50e2e14c-a9d7-45e3-beed-7e48af70dfef', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'update-participant', id: '50e2e14c-a9d7-45e3-beed-7e48af70dfef', properties: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-participant', id: '50e2e14c-a9d7-45e3-beed-7e48af70dfef', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'update-participant', id: '50e2e14c-a9d7-45e3-beed-7e48af70dfef', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-participant', id: '3629362f-80f1-4398-97c5-95fa397f23c2', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-participant', id: '3629362f-80f1-4398-97c5-95fa397f23c2', properties: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'update-participant', id: '3629362f-80f1-4398-97c5-95fa397f23c2', properties: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'update-participant', id: '3629362f-80f1-4398-97c5-95fa397f23c2', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'update-participant', id: '3629362f-80f1-4398-97c5-95fa397f23c2', properties: {…}, what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {50e2e14c-a9d7-45e3-beed-7e48af70dfef: 0, 3629362f-80f1-4398-97c5-95fa397f23c2: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {50e2e14c-a9d7-45e3-beed-7e48af70dfef: 0, 3629362f-80f1-4398-97c5-95fa397f23c2: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {50e2e14c-a9d7-45e3-beed-7e48af70dfef: 0, 3629362f-80f1-4398-97c5-95fa397f23c2: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: 'listening', fromId: '50e2e14c-a9d7-45e3-beed-7e48af70dfef', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: 'listening', fromId: '50e2e14c-a9d7-45e3-beed-7e48af70dfef', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: 'listening', fromId: '50e2e14c-a9d7-45e3-beed-7e48af70dfef', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 Window message received for processing: {action: 'app-message', data: 'listening', fromId: '50e2e14c-a9d7-45e3-beed-7e48af70dfef', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: 'listening', fromId: '50e2e14c-a9d7-45e3-beed-7e48af70dfef', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 Call started - setting status to CONNECTED
 Call started - setting status to CONNECTED
 Call started - setting status to CONNECTED
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '3629362f-80f1-4398-97c5-95fa397f23c2', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '3629362f-80f1-4398-97c5-95fa397f23c2', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '3629362f-80f1-4398-97c5-95fa397f23c2', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '3629362f-80f1-4398-97c5-95fa397f23c2', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '3629362f-80f1-4398-97c5-95fa397f23c2', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
 Received message: {type: 'status-update', status: 'in-progress'}
 Received message: {type: 'status-update', status: 'in-progress'}
 Received message: {type: 'status-update', status: 'in-progress'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {50e2e14c-a9d7-45e3-beed-7e48af70dfef: 0, 3629362f-80f1-4398-97c5-95fa397f23c2: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {50e2e14c-a9d7-45e3-beed-7e48af70dfef: 0, 3629362f-80f1-4398-97c5-95fa397f23c2: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '3629362f-80f1-4398-97c5-95fa397f23c2', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '3629362f-80f1-4398-97c5-95fa397f23c2', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '3629362f-80f1-4398-97c5-95fa397f23c2', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
VapiCall.jsx:317 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '3629362f-80f1-4398-97c5-95fa397f23c2', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '3629362f-80f1-4398-97c5-95fa397f23c2', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', …}
useVapiCall.js?t=1749064041685:213 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
useVapiCall.js?t=1749064041685:213 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
useVapiCall.js?t=1749064041685:213 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {50e2e14c-a9d7-45e3-beed-7e48af70dfef: 0, 3629362f-80f1-4398-97c5-95fa397f23c2: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {50e2e14c-a9d7-45e3-beed-7e48af70dfef: 0, 3629362f-80f1-4398-97c5-95fa397f23c2: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {50e2e14c-a9d7-45e3-beed-7e48af70dfef: 0, 3629362f-80f1-4398-97c5-95fa397f23c2: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: {50e2e14c-a9d7-45e3-beed-7e48af70dfef: 0, 3629362f-80f1-4398-97c5-95fa397f23c2: 0}
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 SpeechParticles: Processing audio level event: {50e2e14c-a9d7-45e3-beed-7e48af70dfef: 0, 3629362f-80f1-4398-97c5-95fa397f23c2: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 Meeting ended due to ejection: Meeting has ended
value @ unknown
value @ unknown
F @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 🎯 [SimplePreviewPage] Received message from parent: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 [EnhancedPreview] Received ANY message: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 Window message received for processing: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 SpeechParticles: Received window message event: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17490645946190.6459845468558968'}
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Continuing despite Vapi error
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17490645946190.6459845468558968'}
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Continuing despite Vapi error
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17490645946190.6459845468558968'}
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Continuing despite Vapi error
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 🎯 [SimplePreviewPage] Received message from parent: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
 Window message received for processing: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
SpeechParticles.jsx:384 SpeechParticles: Received window message event: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
useVapiCall.js?t=1749064041685:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: connecting -> ended
EnhancedPreviewNew.jsx:486 Ending call...
useVapiCall.js?t=1749064041685:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
useVapiCall.js?t=1749064041685:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
useVapiCall.js?t=1749064041685:884 Component unmounting - performing Vapi cleanup
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1749064041685:19 [VapiService] Event listeners removed from Vapi instance
useVapiCall.js?t=1749064041685:893 Stopping active call during cleanup
useVapiCall.js?t=1749064041685:899 Calling onEndCall callback during unmount
callDebugger.js:73 [CallDebugger:VapiCall] Call ended {}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
VapiCall.jsx:1469 VapiCall component unmounting, performing cleanup...
VapiCall.jsx:1484 Call was not fully initialized, performing simple cleanup
VapiCall.jsx:1494 Skipping onEndCall for non-initialized state to prevent mount/unmount cycle
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: LegalScout System
EnhancedPreviewNew.jsx:462 titleText: LegalScout System
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout System', titleText: '', theme: 'light', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490645946190.6459845468558968', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17490645946190.6459845468558968', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
