.simple-vapi-call {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 600px;
  border-radius: 12px;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
  position: relative;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.simple-vapi-call.dark-theme {
  background-color: #1e1e1e;
  border-color: #333;
  color: #f0f0f0;
}

/* Status bar */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #e0e0e0;
  z-index: 10;
}

.dark-theme .status-bar {
  background-color: #2d2d2d;
  border-bottom-color: #444;
}

.status-indicator {
  font-size: 14px;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: #eee;
  display: inline-block;
  font-weight: 500;
}

.status-indicator.idle {
  background-color: #e0e0e0;
  color: #555;
}

.status-indicator.connecting {
  background-color: #fff3cd;
  color: #856404;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.dark-theme .status-indicator.connecting {
  background-color: #604800;
  color: #ffd24c;
}

.status-indicator.connected {
  background-color: #d4edda;
  color: #155724;
}

.dark-theme .status-indicator.connected {
  background-color: #1e4a2e;
  color: #8bffb6;
}

.status-indicator.error {
  background-color: #f8d7da;
  color: #721c24;
}

.dark-theme .status-indicator.error {
  background-color: #4c1a1e;
  color: #ff8b96;
}

/* Main content area */
.call-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Messages panel */
.messages-panel {
  flex: 7;
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
  overflow: hidden;
}

.dark-theme .messages-panel {
  background-color: #262626;
}

/* Sidebar panel */
.sidebar-panel {
  flex: 3;
  min-width: 240px;
  border-left: 1px solid #e0e0e0;
  background-color: #f5f5f5;
  padding: 15px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.dark-theme .sidebar-panel {
  background-color: #252525;
  border-left-color: #444;
}

/* Buttons */
.start-call-btn, .end-call-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.start-call-btn {
  background-color: #28a745;
  color: white;
}

.start-call-btn:hover {
  background-color: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.start-call-btn:disabled {
  background-color: #8bc59c;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.dark-theme .start-call-btn {
  background-color: #309c46;
}

.dark-theme .start-call-btn:hover {
  background-color: #25833a;
}

.dark-theme .start-call-btn:disabled {
  background-color: #246332;
  color: #a0c0a8;
}

.end-call-btn {
  background-color: #dc3545;
  color: white;
}

.end-call-btn:hover {
  background-color: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.dark-theme .end-call-btn {
  background-color: #e04252;
}

.dark-theme .end-call-btn:hover {
  background-color: #d32f3f;
}

/* Messages container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: #f9f9f9;
  position: relative;
}

.dark-theme .messages-container {
  background-color: #262626;
}

/* Message animation */
@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-content {
  word-break: break-word;
}

/* Message bubble styles */
.message-bubble {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  margin-bottom: 10px;
  animation: messageAppear 0.3s ease-out;
}

.message-bubble.user {
  align-self: flex-end;
  margin-left: auto;
}

.message-bubble.assistant {
  align-self: flex-start;
  margin-right: auto;
}

.message-bubble .message-content {
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  word-break: break-word;
}

/* User message styling - BLUE */
.message-bubble.user .message-content {
  background-color: #0084ff;
  color: white;
  border-bottom-right-radius: 4px;
}

.dark-theme .message-bubble.user .message-content {
  background-color: #1a75c7;
}

/* Assistant message styling - GRAY */
.message-bubble.assistant .message-content {
  background-color: #e9e9eb;
  color: #333;
  border-bottom-left-radius: 4px;
}

.dark-theme .message-bubble.assistant .message-content {
  background-color: #383838;
  color: #e0e0e0;
}

/* Message form */
.message-form {
  display: flex;
  padding: 12px 15px;
  border-top: 1px solid #e0e0e0;
  background-color: #f9f9f9;
}

.dark-theme .message-form {
  background-color: #2d2d2d;
  border-top-color: #444;
}

.message-form input {
  flex: 1;
  padding: 10px 14px;
  border: 1px solid #ddd;
  border-radius: 20px;
  margin-right: 8px;
  font-size: 14px;
  background-color: white;
  transition: all 0.2s ease;
}

.message-form input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.dark-theme .message-form input {
  background-color: #333;
  border-color: #555;
  color: #f0f0f0;
}

.dark-theme .message-form input:focus {
  border-color: #5e9aea;
  box-shadow: 0 0 0 2px rgba(94, 154, 234, 0.2);
}

.message-form button {
  padding: 10px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.message-form button:hover {
  background-color: #357bd8;
  transform: translateY(-1px);
}

.message-form button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
}

.dark-theme .message-form button {
  background-color: #4a90e2;
}

.dark-theme .message-form button:hover {
  background-color: #357bd8;
}

.dark-theme .message-form button:disabled {
  background-color: #555;
  color: #999;
}

/* Audio indicators */
.audio-indicators {
  background-color: #f0f0f0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.dark-theme .audio-indicators {
  background-color: #2a2a2a;
}

.indicator-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.indicator-label {
  font-size: 13px;
  color: #777;
  font-weight: 500;
}

.dark-theme .indicator-label {
  color: #aaa;
}

/* Volume meter */
.volume-meter-container {
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
  position: relative;
}

.dark-theme .volume-meter-container {
  background-color: #444;
}

.volume-meter-fill {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 5px;
  transition: width 0.1s ease;
}

.dark-theme .volume-meter-fill {
  background-color: #5cc461;
}

/* Speaking indicator */
.speaking-indicator {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: #e0e0e0;
  color: #555;
  font-size: 13px;
  font-weight: 500;
  text-align: center;
}

.speaking-indicator.active {
  background-color: #28a745;
  color: white;
  animation: pulseSpeaking 1.5s infinite;
}

@keyframes pulseSpeaking {
  0% { opacity: 0.8; }
  50% { opacity: 1; }
  100% { opacity: 0.8; }
}

.dark-theme .speaking-indicator {
  background-color: #444;
  color: #ccc;
}

.dark-theme .speaking-indicator.active {
  background-color: #2a9d3d;
  color: white;
}

/* Dossier */
.dossier-container {
  background-color: #f0f0f0;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.dark-theme .dossier-container {
  background-color: #2a2a2a;
}

.dossier-container h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.dark-theme .dossier-container h3 {
  color: #f0f0f0;
}

.dossier-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.dossier-item {
  padding: 8px 12px;
  background-color: #fff;
  border-radius: 6px;
  border-left: 3px solid #4a90e2;
}

.dark-theme .dossier-item {
  background-color: #333;
  border-left-color: #5e9aea;
}

.dossier-item-label {
  font-size: 12px;
  color: #777;
  margin-bottom: 3px;
  font-weight: 500;
}

.dark-theme .dossier-item-label {
  color: #aaa;
}

.dossier-item-value {
  font-size: 14px;
  font-weight: 500;
}

.dark-theme .dossier-item-value {
  color: #e0e0e0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .call-content {
    flex-direction: column;
  }

  .sidebar-panel {
    border-left: none;
    border-top: 1px solid #e0e0e0;
    min-width: auto;
  }

  .dark-theme .sidebar-panel {
    border-top-color: #444;
  }

  .message {
    max-width: 90%;
  }
}

/* Theme transition */
.simple-vapi-call,
.simple-vapi-call * {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
