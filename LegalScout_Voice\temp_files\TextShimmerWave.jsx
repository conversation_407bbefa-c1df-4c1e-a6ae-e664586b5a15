import React from 'react';
import './TextShimmerWave.css';

/**
 * TextShimmerWave component - Creates a colorful shimmer wave effect on text
 * @param {Object} props
 * @param {string} props.text - The text to display with the shimmer effect
 * @param {string} [props.className] - Additional CSS classes to apply
 * @param {Object} [props.style] - Additional inline styles to apply
 * @param {string} [props.as='p'] - The HTML element to render (p, h1, h2, etc.)
 * @param {boolean} [props.rainbow=false] - Whether to use a rainbow gradient
 * @param {Array<string>} [props.colors] - Custom colors for the gradient
 * @returns {JSX.Element}
 */
const TextShimmerWave = ({ 
  text, 
  className = '', 
  style = {}, 
  as = 'p',
  rainbow = true,
  colors = ['#f4367c', '#fdf200', '#06debf', '#0097fd']
}) => {
  const Element = as;
  
  // Combine all classes
  const combinedClassName = `text-shimmer-wave ${rainbow ? 'rainbow' : 'custom-gradient'} ${className}`;
  
  // Set custom colors if provided and not using rainbow
  const customColorStyle = (!rainbow && colors) ? {
    '--color-1': colors[0] || '#f4367c',
    '--color-2': colors[1] || '#fdf200',
    '--color-3': colors[2] || '#06debf',
    '--color-4': colors[3] || '#0097fd',
  } : {};
  
  return (
    <Element 
      className={combinedClassName}
      style={{ ...customColorStyle, ...style }}
    >
      {text}
    </Element>
  );
};

export default TextShimmerWave; 