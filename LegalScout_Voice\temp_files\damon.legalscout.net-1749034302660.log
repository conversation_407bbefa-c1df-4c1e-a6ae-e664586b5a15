production-signin-fix.js:11 [ProductionSignInFix] Starting production sign-in fixes...
production-signin-fix.js:15 [ProductionSignInFix] Fixing environment variables...
production-signin-fix.js:38 [ProductionSignInFix] Environment variables configured
production-signin-fix.js:44 [ProductionSignInFix] Fixing Supabase API key issues...
production-signin-fix.js:90 [ProductionSignInFix] Supabase fetch interceptor installed
production-signin-fix.js:95 [ProductionSignInFix] Fixing Vapi MCP CORS issues...
production-signin-fix.js:115 [ProductionSignInFix] Vapi MCP CORS handler installed
production-signin-fix.js:120 [ProductionSignInFix] Fixing MutationObserver errors...
production-signin-fix.js:150 [ProductionSignInFix] MutationObserver error handler installed
production-signin-fix.js:155 [ProductionSignInFix] Fixing import.meta.env access...
production-signin-fix.js:183 [ProductionSignInFix] import.meta.env configured: {hasSupabaseUrl: true, hasSupabaseKey: true, hasVapiKey: true, mode: 'production'}
production-signin-fix.js:194 [ProductionSignInFix] Initializing fixes...
production-signin-fix.js:205 [ProductionSignInFix] ✅ All production sign-in fixes applied successfully
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:56 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:89 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:117 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:136 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:170 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:210 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:231 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:257 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
home:17 🚀 [EMERGENCY] Starting emergency critical fixes...
home:21 🔧 [EMERGENCY] Adding process polyfill
home:28 ✅ [EMERGENCY] Process polyfill added
home:39 🔧 [EMERGENCY] Development mode: false (forced production)
home:66 ✅ [EMERGENCY] Fetch patched
home:69 🎉 [EMERGENCY] Emergency fixes complete!
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: damon
standalone-attorney-manager-fixed.js:73 [StandaloneAttorneyManager] Loading attorney for subdomain: damon
standalone-attorney-manager-fixed.js:229 [StandaloneAttorneyManager] isValidUUID: Invalid input: undefined
isValidUUID @ standalone-attorney-manager-fixed.js:229
loadFromLocalStorage @ standalone-attorney-manager-fixed.js:405
loadAttorneyBySubdomainIfNeeded @ standalone-attorney-manager-fixed.js:76
initializeVapiConfig @ standalone-attorney-manager-fixed.js:59
await in initializeVapiConfig
StandaloneAttorneyManager @ standalone-attorney-manager-fixed.js:43
(anonymous) @ standalone-attorney-manager-fixed.js:1095
(anonymous) @ standalone-attorney-manager-fixed.js:1098
standalone-attorney-manager-fixed.js:423 [StandaloneAttorneyManager] Found attorney ID in localStorage: 571390ac-5a83-46b2-ad3a-18b9cf39d701
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '571390ac-5a83-46b2-ad3a-18b9cf39d701', idType: 'string', keys: Array(13)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 571390ac-5a83-46b2-ad3a-18b9cf39d701 with assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
standalone-attorney-manager-fixed.js:466 [StandaloneAttorneyManager] Saved attorney to localStorage: 571390ac-5a83-46b2-ad3a-18b9cf39d701
standalone-attorney-manager-fixed.js:78 [StandaloneAttorneyManager] Successfully loaded attorney from localStorage for subdomain: damon
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
production-signin-fix.js:133 [ProductionSignInFix] Invalid MutationObserver target: null
observer.observe @ production-signin-fix.js:133
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:47 Supabase Key configured: eyJhb...K4cRU
supabase.js:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:101 Supabase client initialized successfully with proper headers
supabase.js:104 Testing Supabase connection...
supabase.js:150 Running in production mode
supabase.js:218 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
environmentVerifier.js:58 Environment Variable Verification
production-signin-fix.js:87 Fetch finished loading: PATCH "https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a".
window.fetch @ production-signin-fix.js:87
window.fetch @ production-signin-fix.js:112
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:64
window.fetch @ disable-automatic-assistant-creation.js:211
updateAssistantConfig @ fix-vapi-assistant-config.js:46
(anonymous) @ fix-vapi-assistant-config.js:79
(anonymous) @ fix-vapi-assistant-config.js:82
home:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://damon.legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource. If an opaque response serves your needs, set the request's mode to 'no-cors' to fetch the resource with CORS disabled.
production-signin-fix.js:87 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ production-signin-fix.js:87
window.fetch @ production-signin-fix.js:112
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:294
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
consolidated-dashboard-fix.js:109 [ConsolidatedDashboardFix] Fetch error caught: TypeError: Failed to fetch
    at window.fetch (production-signin-fix.js:87:26)
    at window.fetch (production-signin-fix.js:112:26)
    at window.fetch (consolidated-dashboard-fix.js:105:28)
    at window.fetch (home:64:18)
    at window.fetch (disable-automatic-assistant-creation.js:211:28)
    at window.fetch (headers-fix.js:36:10)
    at KX.send (streamableHttp.js:248:36)
(anonymous) @ consolidated-dashboard-fix.js:109
Promise.catch
window.fetch @ consolidated-dashboard-fix.js:108
window.fetch @ home:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:294
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
vapiMcpService.js:322 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ vapiMcpService.js:322
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
production-signin-fix.js:87 Fetch failed loading: POST "https://mcp.vapi.ai/mcp".
window.fetch @ production-signin-fix.js:87
window.fetch @ production-signin-fix.js:112
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:294
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
 [ProductionSignInFix] Starting production sign-in fixes...
 [ProductionSignInFix] Fixing environment variables...
 [ProductionSignInFix] Environment variables configured
 [ProductionSignInFix] Fixing Supabase API key issues...
 [ProductionSignInFix] Supabase fetch interceptor installed
 [ProductionSignInFix] Fixing Vapi MCP CORS issues...
 [ProductionSignInFix] Vapi MCP CORS handler installed
production-signin-fix.js:120 [ProductionSignInFix] Fixing MutationObserver errors...
production-signin-fix.js:150 [ProductionSignInFix] MutationObserver error handler installed
production-signin-fix.js:155 [ProductionSignInFix] Fixing import.meta.env access...
production-signin-fix.js:183 [ProductionSignInFix] import.meta.env configured: {hasSupabaseUrl: true, hasSupabaseKey: true, hasVapiKey: true, mode: 'production'}
production-signin-fix.js:194 [ProductionSignInFix] Initializing fixes...
production-signin-fix.js:205 [ProductionSignInFix] ✅ All production sign-in fixes applied successfully
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:56 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:89 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:117 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:136 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:170 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:210 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:231 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:257 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:17 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:21 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:28 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:39 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:66 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:69 🎉 [EMERGENCY] Emergency fixes complete!
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: damon
standalone-attorney-manager-fixed.js:73 [StandaloneAttorneyManager] Loading attorney for subdomain: damon
standalone-attorney-manager-fixed.js:406 [StandaloneAttorneyManager] Loaded attorney from localStorage: 571390ac-5a83-46b2-ad3a-18b9cf39d701
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '571390ac-5a83-46b2-ad3a-18b9cf39d701', idType: 'string', keys: Array(13)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 571390ac-5a83-46b2-ad3a-18b9cf39d701 with assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
standalone-attorney-manager-fixed.js:78 [StandaloneAttorneyManager] Successfully loaded attorney from localStorage for subdomain: damon
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
production-signin-fix.js:133 [ProductionSignInFix] Invalid MutationObserver target: null
observer.observe @ production-signin-fix.js:133
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
production-signin-fix.js:87 Fetch finished loading: PATCH "https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a".
window.fetch @ production-signin-fix.js:87
window.fetch @ production-signin-fix.js:112
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64
window.fetch @ disable-automatic-assistant-creation.js:211
updateAssistantConfig @ fix-vapi-assistant-config.js:46
(anonymous) @ fix-vapi-assistant-config.js:79
(anonymous) @ fix-vapi-assistant-config.js:82
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:47 Supabase Key configured: eyJhb...K4cRU
supabase.js:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:101 Supabase client initialized successfully with proper headers
supabase.js:104 Testing Supabase connection...
supabase.js:150 Running in production mode
supabase.js:218 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
environmentVerifier.js:58 Environment Variable Verification
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://damon.legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource. If an opaque response serves your needs, set the request's mode to 'no-cors' to fetch the resource with CORS disabled.
production-signin-fix.js:87 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ production-signin-fix.js:87
window.fetch @ production-signin-fix.js:112
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:294
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
consolidated-dashboard-fix.js:109 [ConsolidatedDashboardFix] Fetch error caught: TypeError: Failed to fetch
    at window.fetch (production-signin-fix.js:87:26)
    at window.fetch (production-signin-fix.js:112:26)
    at window.fetch (consolidated-dashboard-fix.js:105:28)
    at window.fetch (simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64:18)
    at window.fetch (disable-automatic-assistant-creation.js:211:28)
    at window.fetch (headers-fix.js:36:10)
    at KX.send (streamableHttp.js:248:36)
(anonymous) @ consolidated-dashboard-fix.js:109
Promise.catch
window.fetch @ consolidated-dashboard-fix.js:108
window.fetch @ simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:294
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
vapiMcpService.js:322 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ vapiMcpService.js:322
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
vapiLogger.js:103 [10:50:54] [VapiMcpService] Using MCP URL from config {url: 'https://mcp.vapi.ai/sse'}
production-signin-fix.js:87 Fetch failed loading: POST "https://mcp.vapi.ai/mcp".
window.fetch @ production-signin-fix.js:87
window.fetch @ production-signin-fix.js:112
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:294
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
vapiLogger.js:103 [10:50:54] [VapiMcpService] Attempting MCP connection {url: 'https://mcp.vapi.ai/sse'}
vapiLogger.js:97 [10:50:54] [VapiMcpService] Vapi connection failed {error: 'An invalid or illegal string was specified', fallback: 'direct API'}
t @ vapiLogger.js:97
connectionFailed @ vapiLogger.js:149
connect @ EnhancedVapiMcpService.js:109
await in connect
ga @ attorneys.js:413
await in ga
Da @ attorneys.js:309
await in Da
C @ App.jsx:741
(anonymous) @ App.jsx:788
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
J @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiLogger.js:100 [10:50:54] [VapiMcpService] MCP connection failed, this is expected in some environments {errorType: 'DOMException', errorMessage: 'An invalid or illegal string was specified', mcpUrl: 'https://mcp.vapi.ai/sse', fallbackStrategy: 'direct API'}errorMessage: "An invalid or illegal string was specified"errorType: "DOMException"fallbackStrategy: "direct API"mcpUrl: "https://mcp.vapi.ai/sse"[[Prototype]]: Object
t @ vapiLogger.js:100
warn @ vapiLogger.js:118
connect @ EnhancedVapiMcpService.js:112
await in connect
ga @ attorneys.js:413
await in ga
Da @ attorneys.js:309
await in Da
C @ App.jsx:741
(anonymous) @ App.jsx:788
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
J @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiLogger.js:103 [10:50:54] [VapiMcpService] Retrieving assistant {assistantId: '165b4c91-2cd7-4c9f-80f6-f52991ce4693'}
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
vapiLogger.js:103 [10:50:54] [VapiMcpService] Assistant verified in Vapi {id: '165b4c91-2cd7-4c9f-80f6-f52991ce4693', name: 'LegalScout Legal Assistant'}
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://damon.legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource. If an opaque response serves your needs, set the request's mode to 'no-cors' to fetch the resource with CORS disabled.
production-signin-fix.js:87 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ production-signin-fix.js:87
window.fetch @ production-signin-fix.js:112
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:294
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiEmissionsService.js:27
(anonymous) @ useVapiEmissions.js:34
(anonymous) @ useVapiEmissions.js:48
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
consolidated-dashboard-fix.js:109 [ConsolidatedDashboardFix] Fetch error caught: TypeError: Failed to fetch
    at window.fetch (production-signin-fix.js:87:26)
    at window.fetch (production-signin-fix.js:112:26)
    at window.fetch (consolidated-dashboard-fix.js:105:28)
    at window.fetch (simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64:18)
    at window.fetch (disable-automatic-assistant-creation.js:211:28)
    at window.fetch (headers-fix.js:36:10)
    at window.fetch (disable-automatic-assistant-creation.js:211:28)
    at window.fetch (disable-automatic-assistant-creation.js:211:28)
    at KX.send (streamableHttp.js:248:36)
(anonymous) @ consolidated-dashboard-fix.js:109
Promise.catch
window.fetch @ consolidated-dashboard-fix.js:108
window.fetch @ simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:294
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiEmissionsService.js:27
(anonymous) @ useVapiEmissions.js:34
(anonymous) @ useVapiEmissions.js:48
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
vapiMcpService.js:322 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ vapiMcpService.js:322
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiEmissionsService.js:27
(anonymous) @ useVapiEmissions.js:34
(anonymous) @ useVapiEmissions.js:48
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
production-signin-fix.js:87 Fetch failed loading: POST "https://mcp.vapi.ai/mcp".
window.fetch @ production-signin-fix.js:87
window.fetch @ production-signin-fix.js:112
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:294
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiEmissionsService.js:27
(anonymous) @ useVapiEmissions.js:34
(anonymous) @ useVapiEmissions.js:48
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
production-signin-fix.js:87 Fetch finished loading: POST "https://api.vapi.ai/call/web".
window.fetch @ production-signin-fix.js:87
window.fetch @ production-signin-fix.js:112
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ api.js:26
(anonymous) @ api.js:119
await in (anonymous)
callControllerCreateWebCall @ api.js:280
start @ vapi.js:99
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
production-signin-fix.js:87 Fetch finished loading: POST "https://gs.daily.co/rooms/check/vapi/UAY6oFu38mjkszsgKpq4".
window.fetch @ production-signin-fix.js:87
window.fetch @ production-signin-fix.js:112
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damon&theme=dark&useEnhancedPreview=true:64
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
eval @ VM288:4
s @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
r @ VM288:4
g.roomsCheck @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
C @ VM288:4
postMessage
value @ daily-esm.js:7
value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
Promise.then
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
start @ vapi.js:183
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
VM288:4 enumerateDevices took longer than expected: 60
value @ VM288:4
value @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
$ @ VM288:4
g.enumDWrapper @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
_ @ VM288:4
U.camPreferences @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
dispatch @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
C @ VM288:4
postMessage
value @ daily-esm.js:7
value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
Promise.then
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
start @ vapi.js:183
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
VM288:4 enumerateDevices took longer than expected: 85
value @ VM288:4
value @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
g.enumDWrapper @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
nA @ VM288:4
A.stream @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
dispatch @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
C @ VM288:4
postMessage
value @ daily-esm.js:7
value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
Promise.then
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
start @ vapi.js:183
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
VM288:4 enumerateDevices took longer than expected: 73
value @ VM288:4
value @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
g.enumDWrapper @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
b @ VM288:4
g.getCurrentDevices @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
NA @ VM288:4
hA.initialState @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
hA @ VM288:4
wA @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
dispatch @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
C @ VM288:4
postMessage
value @ daily-esm.js:7
value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
Promise.then
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
start @ vapi.js:183
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
VM288:4 Meeting ended in error: bad sigauthz token
value @ VM288:4
value @ VM288:4
p @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
msgSigChannel @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
VM288:4 Signaling connection interrupted by a disconnect.
value @ VM288:4
value @ VM288:4
f @ VM288:4
eval @ VM288:4
g @ VM288:4
o @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
dispatch @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
Promise.then
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
C @ VM288:4
postMessage
value @ daily-esm.js:7
value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
Promise.then
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
start @ vapi.js:183
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490342636630.37441962371074555'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM288:4
value @ VM288:4
value @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
msgSigChannel @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM288:4
value @ VM288:4
value @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
msgSigChannel @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490342636630.37441962371074555'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM288:4
value @ VM288:4
value @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
msgSigChannel @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM288:4
value @ VM288:4
value @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
msgSigChannel @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490342636630.37441962371074555'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM288:4
value @ VM288:4
value @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
msgSigChannel @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM288:4
value @ VM288:4
value @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
msgSigChannel @ VM288:4
eval @ VM288:4
g @ VM288:4
i @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
eval @ VM288:4
useVapiCall.js:1176 {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490342636630.37441962371074555'}
start @ vapi.js:238
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490342636630.37441962371074555'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490342636630.37441962371074555'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490342636630.37441962371074555'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
W4 @ react-dom.production.min.js:282
nn @ react-dom.production.min.js:280
Vc @ react-dom.production.min.js:272
en @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:266
Fetch finished loading: POST "https://o77906.ingest.sentry.io/api/168844/envelope/?sentry_version=7&sentry_key=f10f1c81e5d44a4098416c0867a8b740&sentry_client=sentry.javascript.browser%2F8.55.0".
