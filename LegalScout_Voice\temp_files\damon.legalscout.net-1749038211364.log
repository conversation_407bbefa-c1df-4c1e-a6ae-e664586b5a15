home:11 ✅ Vapi public key set globally
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:56 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:89 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:117 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:136 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:170 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:210 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:231 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:257 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
home:21 🚀 [EMERGENCY] Starting emergency critical fixes...
home:25 🔧 [EMERGENCY] Adding process polyfill
home:32 ✅ [EMERGENCY] Process polyfill added
home:43 🔧 [EMERGENCY] Development mode: false (forced production)
home:70 ✅ [EMERGENCY] Fetch patched
home:73 🎉 [EMERGENCY] Emergency fixes complete!
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:343 [StandaloneAttorneyManager] Using default Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: damon
standalone-attorney-manager-fixed.js:73 [StandaloneAttorneyManager] Loading attorney for subdomain: damon
standalone-attorney-manager-fixed.js:229 [StandaloneAttorneyManager] isValidUUID: Invalid input: undefined
isValidUUID @ standalone-attorney-manager-fixed.js:229
loadFromLocalStorage @ standalone-attorney-manager-fixed.js:405
loadAttorneyBySubdomainIfNeeded @ standalone-attorney-manager-fixed.js:76
initializeVapiConfig @ standalone-attorney-manager-fixed.js:59
await in initializeVapiConfig
StandaloneAttorneyManager @ standalone-attorney-manager-fixed.js:43
(anonymous) @ standalone-attorney-manager-fixed.js:1095
(anonymous) @ standalone-attorney-manager-fixed.js:1098
standalone-attorney-manager-fixed.js:423 [StandaloneAttorneyManager] Found attorney ID in localStorage: 571390ac-5a83-46b2-ad3a-18b9cf39d701
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '571390ac-5a83-46b2-ad3a-18b9cf39d701', idType: 'string', keys: Array(13)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 571390ac-5a83-46b2-ad3a-18b9cf39d701 with assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
standalone-attorney-manager-fixed.js:466 [StandaloneAttorneyManager] Saved attorney to localStorage: 571390ac-5a83-46b2-ad3a-18b9cf39d701
standalone-attorney-manager-fixed.js:78 [StandaloneAttorneyManager] Successfully loaded attorney from localStorage for subdomain: damon
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (dashboard-iframe-manager.js:187:14)
    at dashboard-iframe-manager.js:242:3
    at dashboard-iframe-manager.js:266:3
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:47 Supabase Key configured: eyJhb...K4cRU
supabase.js:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:101 Supabase client initialized successfully with proper headers
supabase.js:104 Testing Supabase connection...
supabase.js:150 Running in production mode
supabase.js:218 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
environmentVerifier.js:58 Environment Variable Verification
consolidated-dashboard-fix.js:105 Fetch finished loading: PATCH "https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
updateAssistantConfig @ fix-vapi-assistant-config.js:46
(anonymous) @ fix-vapi-assistant-config.js:79
(anonymous) @ fix-vapi-assistant-config.js:82
content.js:59 Fetch finished loading: GET "chrome-extension://bjfgambnhccakkhmkepdoekmckoijdlc/content-scripts/content.css".
ry @ content.js:59
iy @ content.js:58
main @ content.js:90
(anonymous) @ content.js:90
(anonymous) @ content.js:90
(anonymous) @ content.js:90
home:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://damon.legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource. If an opaque response serves your needs, set the request's mode to 'no-cors' to fetch the resource with CORS disabled.
consolidated-dashboard-fix.js:105 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
consolidated-dashboard-fix.js:109 [ConsolidatedDashboardFix] Fetch error caught: TypeError: Failed to fetch
    at window.fetch (consolidated-dashboard-fix.js:105:28)
    at window.fetch (home:68:18)
    at window.fetch (disable-automatic-assistant-creation.js:211:28)
    at window.fetch (headers-fix.js:36:10)
    at KX.send (streamableHttp.js:248:36)
(anonymous) @ consolidated-dashboard-fix.js:109
Promise.catch
window.fetch @ consolidated-dashboard-fix.js:108
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
vapiMcpService.js:322 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ vapiMcpService.js:322
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
consolidated-dashboard-fix.js:105 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ fetch.js:23
(anonymous) @ fetch.js:44
o @ fetch.js:4
Promise.then
l @ fetch.js:6
(anonymous) @ fetch.js:7
eD @ fetch.js:3
(anonymous) @ fetch.js:34
then @ PostgrestBuilder.js:65
(anonymous) @ supabase.js:109
supabase.js:115 Supabase query error: No API key found in request
(anonymous) @ supabase.js:115
Promise.then
then @ PostgrestBuilder.js:182
(anonymous) @ supabase.js:109
consolidated-dashboard-fix.js:105 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.damon 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ fetch.js:23
(anonymous) @ fetch.js:44
o @ fetch.js:4
Promise.then
l @ fetch.js:6
(anonymous) @ fetch.js:7
eD @ fetch.js:3
(anonymous) @ fetch.js:34
then @ PostgrestBuilder.js:65
l.then @ supabase.js:197
consolidated-dashboard-fix.js:105 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ fetch.js:23
(anonymous) @ fetch.js:44
o @ fetch.js:4
Promise.then
l @ fetch.js:6
(anonymous) @ fetch.js:7
eD @ fetch.js:3
(anonymous) @ fetch.js:34
then @ PostgrestBuilder.js:65
l.then @ supabase.js:197
consolidated-dashboard-fix.js:105 Fetch failed loading: POST "https://mcp.vapi.ai/mcp".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiServiceManager.js:100
sn @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
ft @ supabaseConfigVerifier.js:83
await in ft
(anonymous) @ App.jsx:681
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
App.jsx:688 Supabase connection failed: No API key found in request
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
Fetch failed loading: GET "<URL>".
Fetch failed loading: GET "<URL>".
Fetch failed loading: GET "<URL>".
Fetch failed loading: GET "<URL>".
Fetch failed loading: GET "<URL>".
consolidated-dashboard-fix.js:105 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user 403 (Forbidden)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ helpers.js:85
ID @ fetch.js:101
Dt @ fetch.js:91
(anonymous) @ GoTrueClient.js:892
_useSession @ GoTrueClient.js:787
await in _useSession
_getUser @ GoTrueClient.js:882
(anonymous) @ GoTrueClient.js:869
(anonymous) @ GoTrueClient.js:748
(anonymous) @ locks.js:89
attorneys.js:95 [AttorneyConfig] No authenticated user found for damon subdomain fallback 
error @ attorneys.js:95
Da @ attorneys.js:164
await in Da
C @ App.jsx:741
(anonymous) @ App.jsx:788
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
consolidated-dashboard-fix.js:105 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ fetch.js:23
(anonymous) @ fetch.js:44
o @ fetch.js:4
Promise.then
l @ fetch.js:6
(anonymous) @ fetch.js:7
eD @ fetch.js:3
(anonymous) @ fetch.js:34
then @ PostgrestBuilder.js:65
l.then @ supabase.js:197
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
autoInitializeFromLocalStorage @ AttorneyProfileManager.js:49
Us @ AttorneyProfileManager.js:34
(anonymous) @ AttorneyProfileManager.js:1527
AttorneyProfileManager.js:402 [AttorneyProfileManager] Error loading attorney by id: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyById @ AttorneyProfileManager.js:402
await in loadAttorneyById
(anonymous) @ AttorneyProfileManager.js:55
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
autoInitializeFromLocalStorage @ AttorneyProfileManager.js:49
Us @ AttorneyProfileManager.js:34
(anonymous) @ AttorneyProfileManager.js:1527
AttorneyProfileManager.js:66 [AttorneyProfileManager] Could not refresh attorney data for auto-sync: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
(anonymous) @ AttorneyProfileManager.js:66
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:76
autoInitializeFromLocalStorage @ AttorneyProfileManager.js:49
Us @ AttorneyProfileManager.js:34
(anonymous) @ AttorneyProfileManager.js:1527
home:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://damon.legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource. If an opaque response serves your needs, set the request's mode to 'no-cors' to fetch the resource with CORS disabled.
consolidated-dashboard-fix.js:105 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiEmissionsService.js:27
(anonymous) @ useVapiEmissions.js:34
(anonymous) @ useVapiEmissions.js:48
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
consolidated-dashboard-fix.js:109 [ConsolidatedDashboardFix] Fetch error caught: TypeError: Failed to fetch
    at window.fetch (consolidated-dashboard-fix.js:105:28)
    at window.fetch (home:68:18)
    at window.fetch (disable-automatic-assistant-creation.js:211:28)
    at window.fetch (headers-fix.js:36:10)
    at window.fetch (disable-automatic-assistant-creation.js:211:28)
    at window.fetch (disable-automatic-assistant-creation.js:211:28)
    at KX.send (streamableHttp.js:248:36)
(anonymous) @ consolidated-dashboard-fix.js:109
Promise.catch
window.fetch @ consolidated-dashboard-fix.js:108
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiEmissionsService.js:27
(anonymous) @ useVapiEmissions.js:34
(anonymous) @ useVapiEmissions.js:48
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:322 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ vapiMcpService.js:322
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiEmissionsService.js:27
(anonymous) @ useVapiEmissions.js:34
(anonymous) @ useVapiEmissions.js:48
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
consolidated-dashboard-fix.js:105 Fetch failed loading: POST "https://mcp.vapi.ai/mcp".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:317
await in _connect
connect @ vapiMcpService.js:258
initialize @ vapiEmissionsService.js:27
(anonymous) @ useVapiEmissions.js:34
(anonymous) @ useVapiEmissions.js:48
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
consolidated-dashboard-fix.js:105 Fetch finished loading: POST "https://api.vapi.ai/call/web".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ api.js:26
(anonymous) @ api.js:119
await in (anonymous)
callControllerCreateWebCall @ api.js:280
start @ vapi.js:99
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
consolidated-dashboard-fix.js:105 Fetch finished loading: GET "https://c.daily.co/call-machine/versioned/0.79.0/static/call-machine-object-bundle.js".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
Promise.then
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
value @ daily-esm.js:7
value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
start @ vapi.js:183
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
consolidated-dashboard-fix.js:105 Fetch finished loading: POST "https://gs.daily.co/rooms/check/vapi/vfWj86JunY3oZPgi0hqP".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ home:68
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
eval @ VM742:4
s @ VM742:4
eval @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
r @ VM742:4
g.roomsCheck @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
C @ VM742:4
postMessage
value @ daily-esm.js:7
value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
Promise.then
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
start @ vapi.js:183
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
VM742:4 enumerateDevices took longer than expected: 111
value @ VM742:4
value @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
$ @ VM742:4
g.enumDWrapper @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
_ @ VM742:4
U.camPreferences @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
dispatch @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
C @ VM742:4
postMessage
value @ daily-esm.js:7
value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
Promise.then
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
start @ vapi.js:183
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
VM742:4 enumerateDevices took longer than expected: 61
value @ VM742:4
value @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
g.enumDWrapper @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
nA @ VM742:4
A.stream @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
dispatch @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
C @ VM742:4
postMessage
value @ daily-esm.js:7
value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
Promise.then
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
start @ vapi.js:183
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
VM742:4 Meeting ended in error: bad sigauthz token
value @ VM742:4
value @ VM742:4
p @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
msgSigChannel @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
VM742:4 Signaling connection interrupted by a disconnect.
value @ VM742:4
value @ VM742:4
f @ VM742:4
eval @ VM742:4
g @ VM742:4
o @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
dispatch @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
Promise.then
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
C @ VM742:4
postMessage
value @ daily-esm.js:7
value @ daily-esm.js:7
(anonymous) @ daily-esm.js:7
G_ @ daily-esm.js:1
s @ daily-esm.js:1
Promise.then
G_ @ daily-esm.js:1
s @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
(anonymous) @ daily-esm.js:1
qn.value @ daily-esm.js:7
start @ vapi.js:183
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490381053690.9815362193027009'}action: "error"callClientId: "17490381053690.9815362193027009"error: undefinederrorMsg: "bad sigauthz token"[[Prototype]]: Object
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM742:4
value @ VM742:4
value @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
msgSigChannel @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM742:4
value @ VM742:4
value @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
msgSigChannel @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490381053690.9815362193027009'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM742:4
value @ VM742:4
value @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
msgSigChannel @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM742:4
value @ VM742:4
value @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
msgSigChannel @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490381053690.9815362193027009'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM742:4
value @ VM742:4
value @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
msgSigChannel @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
(anonymous) @ vapi.js:135
Yt.emit @ daily-esm.js:1
value @ daily-esm.js:7
value @ daily-esm.js:7
a @ daily-esm.js:7
postMessage
value @ VM742:4
value @ VM742:4
value @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
msgSigChannel @ VM742:4
eval @ VM742:4
g @ VM742:4
i @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
eval @ VM742:4
useVapiCall.js:1180 {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490381053690.9815362193027009'}
start @ vapi.js:238
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490381053690.9815362193027009'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490381053690.9815362193027009'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
useVapiCall.js:94 Vapi error: {action: 'error', errorMsg: 'bad sigauthz token', error: undefined, callClientId: '17490381053690.9815362193027009'}
onError @ useVapiCall.js:94
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
useVapiCall.js:95 Continuing despite Vapi error
onError @ useVapiCall.js:95
Kt.emit @ events.js:158
emit @ vapi.js:48
start @ vapi.js:239
await in start
(anonymous) @ useVapiCall.js:1180
Promise.then
(anonymous) @ useVapiCall.js:1128
await in (anonymous)
(anonymous) @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:764
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
Fetch finished loading: POST "https://o77906.ingest.sentry.io/api/168844/envelope/?sentry_version=7&sentry_key=f10f1c81e5d44a4098416c0867a8b740&sentry_client=sentry.javascript.browser%2F8.55.0".
