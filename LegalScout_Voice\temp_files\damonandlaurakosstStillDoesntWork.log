dashboard:21 ✅ Vapi public key set globally
dashboard:42 ✅ Supabase keys set globally - should load correct assistant by domain
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
dashboard:52 🚀 [EMERGENCY] Starting emergency critical fixes...
dashboard:56 🔧 [EMERGENCY] Adding process polyfill
dashboard:63 ✅ [EMERGENCY] Process polyfill added
dashboard:74 🔧 [EMERGENCY] Development mode: false (forced production)
dashboard:101 ✅ [EMERGENCY] Fetch patched
dashboard:104 🎉 [EMERGENCY] Emergency fixes complete!
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:115 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:246 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
overrideMethod @ hook.js:608
(anonymous) @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
dashboard:207 Supabase loaded from CDN
dashboard:217 Creating Supabase client from CDN
dashboard:221 Supabase client created from CDN
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js?t=1749064041685:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749064041685:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749064041685:1420 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
initAttorneyProfileManager.js?t=1749064041685:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749064041685:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js?t=1749064041685:44 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: true, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js?t=1749064041685:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js?t=1749064041685:233 [VapiMcpService] Production mode: false
vapiMcpService.js?t=1749064041685:234 [VapiMcpService] Development mode: true
vapiMcpService.js?t=1749064041685:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js?t=1749064041685:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js?t=1749064041685:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js?t=1749064041685:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js?t=1749064041685:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749064041685:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749064041685:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749064041685:128 [initAttorneyProfileManager] Initialization complete
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:115 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
disable-automatic-assistant-creation.js:225 [DisableAutomaticAssistantCreation] Services found, applying patches
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:115 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: null
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:77 [useStandaloneAttorney] Initializing hook with manager...
useStandaloneAttorney.js:84 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
useStandaloneAttorney.js:92 [useStandaloneAttorney] Attorney is an array, taking first element
useStandaloneAttorney.js:97 [useStandaloneAttorney] Found attorney in localStorage: undefined
useStandaloneAttorney.js:119 [useStandaloneAttorney] No attorney found anywhere
DashboardNew.jsx:130 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
DashboardNew.jsx:176 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:177 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:393 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749032804912:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
testSupabase.js?t=1749032804912:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: null
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:77 [useStandaloneAttorney] Initializing hook with manager...
useStandaloneAttorney.js:84 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
useStandaloneAttorney.js:92 [useStandaloneAttorney] Attorney is an array, taking first element
useStandaloneAttorney.js:97 [useStandaloneAttorney] Found attorney in localStorage: undefined
useStandaloneAttorney.js:119 [useStandaloneAttorney] No attorney found anywhere
DashboardNew.jsx:130 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
DashboardNew.jsx:176 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:177 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:393 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749032804912:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
testSupabase.js?t=1749032804912:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
index.ts:5 Loaded contentScript
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
VM616 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM616 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM616 consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM616 consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
VM616 consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM616 consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
VM616 consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
VM616 consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
VM616 consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
VM616 consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
VM604 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM604 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM604 consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM604 consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
VM604 consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM604 consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
VM604 consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
VM604 consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
VM604 consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
VM604 consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
AuthContext.jsx:85 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
supabase.js?t=1749032804912:118 Supabase connection test successful!
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
AuthContext.jsx:85 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
debugConfig.js:30 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
DashboardNew.jsx:134 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
AutoAssistantReconciler.js?t=1749056309798:276 [AutoReconciler] 🚀 Initializing auto-reconciliation
DashboardNew.jsx:176 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:177 [DashboardNew] Dependencies: user?.id=bafd37ba-a143-40ea-bcf5-e25fe149b55e, authIsLoading=false, loadAttorneyForUser exists=true
DashboardNew.jsx:390 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
DashboardNew.jsx:201 [DashboardNew] fetchAttorneyData called.
DashboardNew.jsx:282 [DashboardNew] Attempting to load attorney for user: bafd37ba-a143-40ea-bcf5-e25fe149b55e
DashboardNew.jsx:287 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
fix-enhance-attorney-manager.js:48 [FixEnhanceAttorneyManager] Fixed loadAttorneyForUser called with userId: bafd37ba-a143-40ea-bcf5-e25fe149b55e
enhance-attorney-manager.js:326 [EnhanceAttorneyManager] Enhanced loadAttorneyForUser called with userId: bafd37ba-a143-40ea-bcf5-e25fe149b55e
enhance-attorney-manager.js:330 [EnhanceAttorneyManager] Already loading by user ID, returning current attorney
DashboardNew.jsx:299 [DashboardNew] No attorney found by user ID, trying email: <EMAIL>
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonandlaurakost%40gmail.com&order=updated_at.desc with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
vapiAssistantUtils.js?t=1749057552616:174 No attorney found for email: <EMAIL>
DashboardNew.jsx:342 [DashboardNew] No attorney profile found for this user or loading failed.
overrideMethod @ hook.js:608
fetchAttorneyData @ DashboardNew.jsx:342
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:391
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
DashboardNew.jsx:346 [DashboardNew] Development mode: Creating development attorney
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: 'dev-1749069470717', user_id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', firm_name: 'Development Law Firm', name: 'Development Attorney', email: '<EMAIL>', …}
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: 'dev-1749069470717', user_id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', firm_name: 'Development Law Firm', name: 'Development Attorney', email: '<EMAIL>', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
DashboardNew.jsx:487 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:488 [DashboardNew] Attorney Vapi Assistant ID: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
DashboardNew.jsx:518 [DashboardNew] Updated preview config with assistant ID: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:518 [DashboardNew] Updated preview config with assistant ID: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:115 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:246 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:115 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:246 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:207 Supabase loaded from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:217 Creating Supabase client from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:221 Supabase client created from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:207 Supabase loaded from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:217 Creating Supabase client from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:221 Supabase client created from CDN
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.bafd37ba-a143-40ea-bcf5-e25fe149b55e with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js?t=1749064041685:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749064041685:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749064041685:1420 [AttorneyProfileManager] Loaded attorney from localStorage: dev-1749069470717
AttorneyProfileManager.js?t=1749064041685:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js?t=1749064041685:410 [AttorneyProfileManager] Setting up Realtime subscription for attorney: dev-1749069470717
AttorneyProfileManager.js?t=1749064041685:443 [AttorneyProfileManager] Realtime subscription set up using channel API
initAttorneyProfileManager.js?t=1749064041685:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749064041685:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js?t=1749064041685:44 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js?t=1749064041685:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js?t=1749064041685:233 [VapiMcpService] Production mode: false
vapiMcpService.js?t=1749064041685:234 [VapiMcpService] Development mode: true
vapiMcpService.js?t=1749064041685:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js?t=1749064041685:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js?t=1749064041685:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js?t=1749064041685:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js?t=1749064041685:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749064041685:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749064041685:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749064041685:118 [initAttorneyProfileManager] Found attorney in localStorage: dev-1749069470717
initAttorneyProfileManager.js?t=1749064041685:128 [initAttorneyProfileManager] Initialization complete
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:115 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
 [DisableAutomaticAssistantCreation] Services found, applying patches
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js?t=1749064041685:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749064041685:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: dev-1749069470717
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Setting up Realtime subscription for attorney: dev-1749069470717
 [AttorneyProfileManager] Realtime subscription set up using channel API
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Found attorney in localStorage: dev-1749069470717
 [initAttorneyProfileManager] Initialization complete
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
SimplePreviewPage.jsx:101 SimplePreviewPage: Starting config load...
SimplePreviewPage.jsx:102 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
SimplePreviewPage.jsx:125 SimplePreviewPage: Loading from Supabase for subdomain: default
SimplePreviewPage.jsx:158 SimplePreviewPage: Falling back to direct Supabase loading
SimplePreviewPage.jsx:61 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749032804912:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
testSupabase.js?t=1749032804912:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
DashboardNew.jsx:1400 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
AutoAssistantReconciler.js?t=1749056309798:147 [AutoReconciler] Invalid attorney object provided: []
overrideMethod @ hook.js:608
reconcileAttorney @ AutoAssistantReconciler.js?t=1749056309798:147
checkAndFixCurrentUser @ AutoAssistantReconciler.js?t=1749056309798:262
await in checkAndFixCurrentUser
(anonymous) @ AutoAssistantReconciler.js?t=1749056309798:280
setTimeout
(anonymous) @ consolidated-dashboard-fix.js:86
initializeForDashboard @ AutoAssistantReconciler.js?t=1749056309798:279
(anonymous) @ DashboardNew.jsx:137
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
disable-automatic-assistant-creation.js:225 [DisableAutomaticAssistantCreation] Services found, applying patches
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:115 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [UnifiedBannerFix] Ensuring upload interface is visible
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474628}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474628}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474630}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474630}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474787}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474787}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474791}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474791}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474802}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474802}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474805}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474805}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474828}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474828}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474836}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474836}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 2 iframes successfully
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474894}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474894}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474899}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069474899}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:679 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475222}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475222}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475235}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475235}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 Supabase connection test successful!
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 Attorney object in ProfileTab: {id: 'dev-1749069470717', user_id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', firm_name: 'Development Law Firm', name: 'Development Attorney', email: '<EMAIL>', …}
 User object in ProfileTab: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
 Updated form data with attorney email from database: <EMAIL>
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
AuthContext.jsx:85 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:85 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: []
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475572}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475572}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475572}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475575}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475575}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475575}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Development Law Firm
 titleText: Development Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475633}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475633}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475633}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: []
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 2 iframes successfully
supabase.js?t=1749032804912:118 Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
debugConfig.js:30 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475804}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475804}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475804}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475804}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475806}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475806}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475806}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475806}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Development Law Firm
 titleText: Development Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475857}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475857}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475857}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069475857}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: []
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
AttorneyProfileManager.js?t=1749064041685:71 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
ActiveCheckHelper.ts:21 received intentional event
DashboardNew.jsx:534 [DashboardNew] Tab changed to: agent
AgentTab.jsx:511 [AgentTab] Loading voice settings from Vapi assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
AgentTab.jsx:511 [AgentTab] Loading voice settings from Vapi assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.dev-1749069470717 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
vapiMcpService.js?t=1749064041685:388 [VapiMcpService] Getting assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiMcpService.js?t=1749064041685:392 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js?t=1749064041685:407 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c → http://localhost:5175/api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiLogger.js:103 [20:37:57] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
EnhancedVapiMcpService.js?t=1749064041685:138 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
vapiMcpService.js?t=1749064041685:388 [VapiMcpService] Getting assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiMcpService.js?t=1749064041685:392 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js?t=1749064041685:407 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c → http://localhost:5175/api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiLogger.js:103 [20:37:57] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
EnhancedVapiMcpService.js?t=1749064041685:138 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.dev-1749069470717 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.dev-1749069470717 400 (Bad Request)
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3900
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3921
fulfilled @ @supabase_supabase-js.js?v=158b513d:3873
Promise.then
step @ @supabase_supabase-js.js?v=158b513d:3886
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3888
__awaiter6 @ @supabase_supabase-js.js?v=158b513d:3870
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3911
then @ @supabase_supabase-js.js?v=158b513d:89
query.then @ supabase.js?t=1749032804912:197
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
AgentTab.jsx:1734 [AgentTab] Error loading assistant mappings: {code: '22P02', details: null, hint: null, message: 'invalid input syntax for type uuid: "dev-1749069470717"'}
overrideMethod @ hook.js:608
loadAvailableAssistants @ AgentTab.jsx:1734
await in loadAvailableAssistants
(anonymous) @ AgentTab.jsx:1777
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.dev-1749069470717 400 (Bad Request)
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3900
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3921
fulfilled @ @supabase_supabase-js.js?v=158b513d:3873
Promise.then
step @ @supabase_supabase-js.js?v=158b513d:3886
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3888
__awaiter6 @ @supabase_supabase-js.js?v=158b513d:3870
(anonymous) @ @supabase_supabase-js.js?v=158b513d:3911
then @ @supabase_supabase-js.js?v=158b513d:89
query.then @ supabase.js?t=1749032804912:197
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:115 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:246 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
AgentTab.jsx:1734 [AgentTab] Error loading assistant mappings: {code: '22P02', details: null, hint: null, message: 'invalid input syntax for type uuid: "dev-1749069470717"'}
overrideMethod @ hook.js:608
loadAvailableAssistants @ AgentTab.jsx:1734
await in loadAvailableAssistants
(anonymous) @ AgentTab.jsx:1777
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
consolidated-dashboard-fix.js:206 
            
            
           GET http://localhost:5175/api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 404 (Not Found)
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ disable-automatic-assistant-creation.js:189
getAssistant @ vapiMcpService.js?t=1749064041685:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpService.js?t=1749064041685:424 [VapiMcpService] Failed to get assistant from /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 404
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js?t=1749064041685:424
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpService.js?t=1749064041685:407 [VapiMcpService] Trying to get assistant from: https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
  GET http://localhost:5175/api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 404 (Not Found)
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
(anonymous) @ disable-automatic-as…ant-creation.js:189
(anonymous) @ disable-automatic-as…ant-creation.js:189
getAssistant @ vapiMcpService.js:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Failed to get assistant from /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 404
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:424
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Trying to get assistant from: https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:207 Supabase loaded from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:217 Creating Supabase client from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:221 Supabase client created from CDN
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
consolidated-dashboard-fix.js:206 
            
            
           GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 401 (Unauthorized)
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:46
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ disable-automatic-assistant-creation.js:189
getAssistant @ vapiMcpService.js?t=1749064041685:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
ActiveCheckHelper.ts:8 updating page active status
vapiMcpDebugger.js:180 [Vapi MCP] Response: 401 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiMcpService.js?t=1749064041685:424 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 401
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js?t=1749064041685:424
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpService.js?t=1749064041685:407 [VapiMcpService] Trying to get assistant from: https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
consolidated-dashboard-fix.js:206 
            
            
           GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 401 (Unauthorized)
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:46
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ disable-automatic-assistant-creation.js:189
getAssistant @ vapiMcpService.js?t=1749064041685:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpDebugger.js:180 [Vapi MCP] Response: 401 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiMcpService.js?t=1749064041685:424 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 401
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js?t=1749064041685:424
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpService.js?t=1749064041685:407 [VapiMcpService] Trying to get assistant from: https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
consolidated-dashboard-fix.js:206 
            
            
           GET https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c net::ERR_FAILED
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ disable-automatic-assistant-creation.js:189
getAssistant @ vapiMcpService.js?t=1749064041685:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
consolidated-dashboard-fix.js:210 [ConsolidatedDashboardFix] Fetch error caught: TypeError: Failed to fetch
    at consolidated-dashboard-fix.js:206:28
    at window.fetch (dashboard:99:18)
    at disable-automatic-assistant-creation.js:189:28
    at window.fetch (headers-fix.js:36:10)
    at window.fetch (vapiNetworkInterceptor.js:34:28)
    at disable-automatic-assistant-creation.js:189:28
    at disable-automatic-assistant-creation.js:189:28
    at VapiMcpService.getAssistant (vapiMcpService.js?t=1749064041685:409:30)
    at async loadAssistantData (AssistantInfoSection.jsx:50:25)
overrideMethod @ hook.js:608
(anonymous) @ consolidated-dashboard-fix.js:210
Promise.catch
(anonymous) @ consolidated-dashboard-fix.js:209
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ disable-automatic-assistant-creation.js:189
getAssistant @ vapiMcpService.js?t=1749064041685:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpService.js?t=1749064041685:427 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js?t=1749064041685:427
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpService.js?t=1749064041685:432 [VapiMcpService] Assistant not found with ID: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js?t=1749064041685:432
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
consolidated-dashboard-fix.js:206 
            
            
           GET https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c net::ERR_FAILED
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ disable-automatic-assistant-creation.js:189
getAssistant @ vapiMcpService.js?t=1749064041685:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
consolidated-dashboard-fix.js:210 [ConsolidatedDashboardFix] Fetch error caught: TypeError: Failed to fetch
    at consolidated-dashboard-fix.js:206:28
    at window.fetch (dashboard:99:18)
    at disable-automatic-assistant-creation.js:189:28
    at window.fetch (headers-fix.js:36:10)
    at window.fetch (vapiNetworkInterceptor.js:34:28)
    at disable-automatic-assistant-creation.js:189:28
    at disable-automatic-assistant-creation.js:189:28
    at VapiMcpService.getAssistant (vapiMcpService.js?t=1749064041685:409:30)
    at async loadAssistantData (AssistantInfoSection.jsx:50:25)
overrideMethod @ hook.js:608
(anonymous) @ consolidated-dashboard-fix.js:210
Promise.catch
(anonymous) @ consolidated-dashboard-fix.js:209
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ disable-automatic-assistant-creation.js:189
getAssistant @ vapiMcpService.js?t=1749064041685:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpService.js?t=1749064041685:427 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js?t=1749064041685:427
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpService.js?t=1749064041685:432 [VapiMcpService] Assistant not found with ID: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js?t=1749064041685:432
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:50
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:37
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
EnhancedVapiMcpService.js?t=1749064041685:149 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
vapiLogger.js:103 [20:37:58] [VapiMcpService] Retrieving assistant {assistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c'}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
EnhancedVapiMcpService.js?t=1749064041685:149 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
vapiLogger.js:103 [20:37:59] [VapiMcpService] Retrieving assistant {assistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c'}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
consolidated-dashboard-fix.js:206 
            
            
           GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 404 (Not Found)
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:46
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ EnhancedVapiMcpService.js?t=1749064041685:259
withRetry @ EnhancedVapiMcpService.js?t=1749064041685:230
getAssistant @ EnhancedVapiMcpService.js?t=1749064041685:257
loadVapiVoiceSettings @ AgentTab.jsx:521
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:552
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpDebugger.js:180 [Vapi MCP] Response: 404 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiLogger.js:100 [20:37:59] [VapiMcpService] Assistant not found in Vapi {assistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c'}
overrideMethod @ hook.js:608
log @ vapiLogger.js:100
assistantNotFound @ vapiLogger.js:141
(anonymous) @ EnhancedVapiMcpService.js?t=1749064041685:269
await in (anonymous)
withRetry @ EnhancedVapiMcpService.js?t=1749064041685:230
getAssistant @ EnhancedVapiMcpService.js?t=1749064041685:257
loadVapiVoiceSettings @ AgentTab.jsx:521
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:552
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=704ffe31:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=704ffe31:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=704ffe31:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
AgentTab.jsx:544 ⚠️ [AgentTab] No voice settings found in Vapi assistant
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
consolidated-dashboard-fix.js:206 
            
            
           GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 404 (Not Found)
(anonymous) @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ disable-automatic-assistant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:46
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ disable-automatic-assistant-creation.js:189
(anonymous) @ EnhancedVapiMcpService.js?t=1749064041685:259
withRetry @ EnhancedVapiMcpService.js?t=1749064041685:230
getAssistant @ EnhancedVapiMcpService.js?t=1749064041685:257
loadVapiVoiceSettings @ AgentTab.jsx:521
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:552
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
vapiMcpDebugger.js:180 [Vapi MCP] Response: 404 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
vapiLogger.js:100 [20:38:00] [VapiMcpService] Assistant not found in Vapi {assistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c'}
overrideMethod @ hook.js:608
log @ vapiLogger.js:100
assistantNotFound @ vapiLogger.js:141
(anonymous) @ EnhancedVapiMcpService.js?t=1749064041685:269
await in (anonymous)
withRetry @ EnhancedVapiMcpService.js?t=1749064041685:230
getAssistant @ EnhancedVapiMcpService.js?t=1749064041685:257
loadVapiVoiceSettings @ AgentTab.jsx:521
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:552
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
AgentTab.jsx:544 ⚠️ [AgentTab] No voice settings found in Vapi assistant
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 Headers fix applied to fetch
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
vapiMcpService.js?t=1749064041685:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749064041685:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js?t=1749064041685:1420 [AttorneyProfileManager] Loaded attorney from localStorage: dev-1749069470717
AttorneyProfileManager.js?t=1749064041685:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js?t=1749064041685:410 [AttorneyProfileManager] Setting up Realtime subscription for attorney: dev-1749069470717
AttorneyProfileManager.js?t=1749064041685:443 [AttorneyProfileManager] Realtime subscription set up using channel API
initAttorneyProfileManager.js?t=1749064041685:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749064041685:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js?t=1749064041685:44 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js?t=1749064041685:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js?t=1749064041685:233 [VapiMcpService] Production mode: false
vapiMcpService.js?t=1749064041685:234 [VapiMcpService] Development mode: true
vapiMcpService.js?t=1749064041685:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js?t=1749064041685:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js?t=1749064041685:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js?t=1749064041685:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js?t=1749064041685:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749064041685:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749064041685:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749064041685:118 [initAttorneyProfileManager] Found attorney in localStorage: dev-1749069470717
initAttorneyProfileManager.js?t=1749064041685:128 [initAttorneyProfileManager] Initialization complete
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:115 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
disable-automatic-assistant-creation.js:225 [DisableAutomaticAssistantCreation] Services found, applying patches
disable-automatic-assistant-creation.js:111 [DisableAutomaticAssistantCreation] Patched vapiMcpService
disable-automatic-assistant-creation.js:115 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
disable-automatic-assistant-creation.js:126 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
disable-automatic-assistant-creation.js:192 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:204 [DisableAutomaticAssistantCreation] All patches applied successfully
DashboardNew.jsx:383 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:383
setTimeout
(anonymous) @ consolidated-dashboard-fix.js:86
(anonymous) @ DashboardNew.jsx:381
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [UnifiedBannerFix] Ensuring upload interface is visible
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481590}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481590}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481590}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481590}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481590}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481592}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481592}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481592}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481592}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481592}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481698}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481698}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481698}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481698}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481698}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481702}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481702}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481702}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481702}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481702}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 [UnifiedBannerFix] Ensuring upload interface is visible
 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.r4f_2usJVYOJrrFtZdAJXda2iwjeI1kqrRJ62wtG1Tc', content-type: 'application/json', …}
 Supabase connection test successful!
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481947}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481947}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481947}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481947}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481947}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481998}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481998}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481998}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481998}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069481998}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482155}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482155}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482155}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482155}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482155}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482155}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482159}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482159}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482159}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482159}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482159}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482159}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Development Law Firm
 titleText: Development Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482303}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482303}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482303}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482303}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482303}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749069482303}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
AttorneyProfileManager.js?t=1749064041685:71 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
