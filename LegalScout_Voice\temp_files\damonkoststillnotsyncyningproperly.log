 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
 [DashboardIframeManager] Iframe observer set up
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 [vite] connecting...
 [vite] connected.
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Mr. Hammond, The phones are working.
 [FixVapiAssistantConfig] - model.messages: [{…}]
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 Headers fix applied to fetch
 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Running in development mode
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: true, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Initialization complete
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [DisableAutomaticAssistantCreation] Services found, applying patches
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 Attorney object in ProfileTab: null
 User object in ProfileTab: null
 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
 Using email from previewConfig or previous state: 
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Initializing hook with manager...
 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
 [useStandaloneAttorney] Attorney is an array, taking first element
 [useStandaloneAttorney] Found attorney in localStorage: undefined
 [useStandaloneAttorney] No attorney found anywhere
 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
 [AutoReconciler] 🚀 Initializing auto-reconciliation
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 Attorney object in ProfileTab: null
 User object in ProfileTab: null
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Initializing hook with manager...
 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
 [useStandaloneAttorney] Attorney is an array, taking first element
 [useStandaloneAttorney] Found attorney in localStorage: undefined
 [useStandaloneAttorney] No attorney found anywhere
 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
 [AutoReconciler] 🚀 Initializing auto-reconciliation
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [UnifiedBannerFix] Ensuring upload interface is visible
 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
 Using email from previewConfig or previous state: 
 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
 Using email from previewConfig or previous state: 
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [UnifiedBannerFix] Ensuring upload interface is visible
 OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=true, loadAttorneyForUser exists=true
 [DashboardNew] Waiting... (authIsLoading: true, userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701, managerReady: true)
 Supabase connection test successful!
 OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
debugConfig.js:30 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
AutoAssistantReconciler.js:135 [AutoReconciler] 🎯 Best match for undefined: "LegalScout Assistant" (score: 123.02172072916667)
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.undefined with headers: {accept: 'application/json', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-profile: 'public', content-type: 'application/json', …}
consolidated-dashboard-fix.js:143 🔧 [EMERGENCY] Blocking undefined attorney ID request
AutoAssistantReconciler.js:197 [AutoReconciler] Error reconciling undefined: undefined
reconcileAttorney @ AutoAssistantReconciler.js:197
await in reconcileAttorney
checkAndFixCurrentUser @ AutoAssistantReconciler.js:252
await in checkAndFixCurrentUser
(anonymous) @ AutoAssistantReconciler.js:270
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
initializeForDashboard @ AutoAssistantReconciler.js:269
(anonymous) @ DashboardNew.jsx:131
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:19356
workLoop @ chunk-Q72EVS5P.js?v=704ffe31:197
flushWork @ chunk-Q72EVS5P.js?v=704ffe31:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=704ffe31:384
 [ReactPolyfill] Stopped monitoring React.createContext
 [DashboardNew] Loading timeout reached, forcing loading state to false
(anonymous) @ DashboardNew.jsx:283
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
(anonymous) @ DashboardNew.jsx:281
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
 Updated form data with OAuth user email: <EMAIL>
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
 Updated form data with OAuth user email: <EMAIL>
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
 Updated form data with OAuth user email: <EMAIL>
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
 [DashboardIframeManager] Iframe observer set up
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 [vite] connecting...
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
 [DashboardIframeManager] Iframe observer set up
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 [vite] connecting...
 [vite] connected.
 [vite] connected.
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Mr. Hammond, The phones are working.
 [FixVapiAssistantConfig] - model.messages: [{…}]
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Mr. Hammond, The phones are working.
 [FixVapiAssistantConfig] - model.messages: [{…}]
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 Headers fix applied to fetch
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Running in development mode
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Initialization complete
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [DisableAutomaticAssistantCreation] Services found, applying patches
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Ensuring upload interface is visible
 [UnifiedBannerFix] Ensuring upload interface is visible
 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 Headers fix applied to fetch
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Running in development mode
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Initialization complete
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [DisableAutomaticAssistantCreation] Services found, applying patches
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862319}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862319}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862324}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862324}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
 Updated form data with OAuth user email: <EMAIL>
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
 Updated form data with OAuth user email: <EMAIL>
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862396}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862396}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862398}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862398}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862413}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862413}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862423}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862423}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862453}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862453}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862461}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862461}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862716}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862716}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [UnifiedBannerFix] Ensuring upload interface is visible
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862726}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862726}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862854}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862854}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862868}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055862868}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
 Updated form data with OAuth user email: <EMAIL>
 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
 Updated form data with OAuth user email: <EMAIL>
The deferred DOM Node could not be resolved to a valid node.
 [UnifiedBannerFix] Ensuring upload interface is visible
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 Supabase connection test successful!
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: null
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863525}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863525}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863525}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863527}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863527}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863527}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [UnifiedBannerFix] Ensuring upload interface is visible
 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 [UnifiedBannerFix] Ensuring upload interface is visible
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 Supabase connection test successful!
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863710}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863710}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863710}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863710}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863712}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863712}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863712}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055863712}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: []
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
DashboardNew.jsx:528 [DashboardNew] Tab changed to: agent
AgentTab.jsx:511 [AgentTab] Loading voice settings from Vapi assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
AgentTab.jsx:511 [AgentTab] Loading voice settings from Vapi assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
vapiLogger.js:103 [16:51:08] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
EnhancedVapiMcpService.js:138 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
vapiLogger.js:103 [16:51:08] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
EnhancedVapiMcpService.js:138 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:214 [DisableAutomaticAssistantCreation] Patched fetch calls
disable-automatic-assistant-creation.js:226 [DisableAutomaticAssistantCreation] All patches applied successfully
disable-automatic-assistant-creation.js:268 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
vapiAssistantService.js?t=1749032804912:45 VapiAssistantService: Attempting to connect to Vapi MCP server
vapiMcpService.js:233 [VapiMcpService] Production mode: false
vapiMcpService.js:234 [VapiMcpService] Development mode: true
vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 6734febc...
vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:246 [VapiMcpService] API key being used: 6734febc...
vapiAssistantService.js?t=1749032804912:51 VapiAssistantService: Successfully connected to Vapi MCP server
vapiMcpService.js:388 [VapiMcpService] Getting assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
vapiMcpService.js:392 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:407 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
headers-fix.js:33 [HeadersFix] Fetch request to /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a → http://localhost:5175/api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
vapiMcpService.js:388 [VapiMcpService] Getting assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
vapiMcpService.js:392 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:407 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
headers-fix.js:33 [HeadersFix] Fetch request to /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a → http://localhost:5175/api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:207 Supabase loaded from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:217 Creating Supabase client from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:221 Supabase client created from CDN
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
EnhancedVapiMcpService.js:149 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
vapiLogger.js:103 [16:51:08] [VapiMcpService] Retrieving assistant {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
dashboard:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
vapiMcpService.js:419 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
loggerUtils.js:133 [2025-06-04T16:51:08.689Z] [VapiAssistantService] Retrieved assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a 
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
EnhancedVapiMcpService.js:149 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
vapiLogger.js:103 [16:51:08] [VapiMcpService] Retrieving assistant {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
vapiMcpService.js:419 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Mr. Hammond, The phones are working.
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
vapiLogger.js:103 [16:51:08] [VapiMcpService] Assistant verified in Vapi {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', name: 'LegalScout Assistant'}
AgentTab.jsx:524 🎵 [AgentTab] Found voice in Vapi assistant: {voiceId: 'sarah', provider: '11labs'}
DashboardNew.jsx:570 Updated preview config: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
DashboardNew.jsx:673 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:660 [DashboardNew] Cannot update attorney - attorney data is invalid or missing ID
handlePreviewConfigUpdate @ DashboardNew.jsx:660
loadVapiVoiceSettings @ AgentTab.jsx:534
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:552
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
AgentTab.jsx:539 ✅ [AgentTab] Voice settings loaded from Vapi: {voiceId: 'sarah', provider: '11labs'}
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055868989}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055868989}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055868989}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055868989}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 [2025-06-04T16:51:09.055Z] [VapiAssistantService] Retrieved assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a 
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: 11labs
 chatActive: false
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: 11labs
 chatActive: false
 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [16:51:09] [VapiMcpService] Assistant verified in Vapi {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', name: 'LegalScout Assistant'}
 🎵 [AgentTab] Found voice in Vapi assistant: {voiceId: 'sarah', provider: '11labs'}
 Updated preview config: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Cannot update attorney - attorney data is invalid or missing ID
handlePreviewConfigUpdate @ DashboardNew.jsx:511
loadVapiVoiceSettings @ AgentTab.jsx:416
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:431
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
AgentTab.jsx:539 ✅ [AgentTab] Voice settings loaded from Vapi: {voiceId: 'sarah', provider: '11labs'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869093}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869093}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869093}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869093}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749032804912:30 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749032804912:41 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:47 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749032804912:61 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749032804912:83 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749032804912:101 Supabase client initialized successfully with proper headers
supabase.js?t=1749032804912:104 Testing Supabase connection...
supabase.js?t=1749032804912:150 Running in development mode
supabase.js?t=1749032804912:218 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Initialization complete
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [DisableAutomaticAssistantCreation] Services found, applying patches
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
SimplePreviewPage.jsx:101 SimplePreviewPage: Starting config load...
SimplePreviewPage.jsx:102 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
SimplePreviewPage.jsx:125 SimplePreviewPage: Loading from Supabase for subdomain: default
SimplePreviewPage.jsx:158 SimplePreviewPage: Falling back to direct Supabase loading
SimplePreviewPage.jsx:61 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749032804912:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
testSupabase.js?t=1749032804912:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
SimplePreviewPage.jsx:101 SimplePreviewPage: Starting config load...
SimplePreviewPage.jsx:102 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
SimplePreviewPage.jsx:125 SimplePreviewPage: Loading from Supabase for subdomain: default
SimplePreviewPage.jsx:158 SimplePreviewPage: Falling back to direct Supabase loading
SimplePreviewPage.jsx:61 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749032804912:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
testSupabase.js?t=1749032804912:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749032804912:56 Supabase URL configured: true
testSupabase.js?t=1749032804912:57 Supabase Key configured: true
testSupabase.js?t=1749032804912:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749032804912:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749032804912:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749032804912:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
 [UnifiedBannerFix] Ensuring upload interface is visible
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869709}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869709}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869709}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869709}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869709}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869711}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869711}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869711}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869711}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869711}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869791}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869791}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869791}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869791}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869791}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869797}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869797}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869797}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869797}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055869797}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870108}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870108}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870108}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870108}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870108}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870134}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870134}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870134}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870134}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870134}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
AuthContext.jsx:85 OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:85 OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.3b-wCuwZVMYa_WDzd-14F9X19BDXjVT80rq8WA90weY', content-type: 'application/json', …}
 Supabase connection test successful!
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: []
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: null
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: null
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [UnifiedBannerFix] Ensuring upload interface is visible
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870475}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870475}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870475}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870475}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870475}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870475}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870479}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870479}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870479}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870479}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870479}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055870479}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme, voiceId, voiceProvider
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: null
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: 11labs
EnhancedPreviewNew.jsx:470 chatActive: false
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: []
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
AgentTab.jsx:1783 [AgentTab] Assistant dropdown changed to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [AgentTab] Switching to assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [AgentTab] Selected assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Updated preview config: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Cannot update attorney - attorney data is invalid or missing ID
handlePreviewConfigUpdate @ DashboardNew.jsx:511
handleSelectAssistant @ AgentTab.jsx:1237
handleAssistantDropdownChange @ AgentTab.jsx:1297
callCallback2 @ chunk-Q72EVS5P.js:3674
invokeGuardedCallbackDev @ chunk-Q72EVS5P.js:3699
invokeGuardedCallback @ chunk-Q72EVS5P.js:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-Q72EVS5P.js:3736
executeDispatch @ chunk-Q72EVS5P.js:7016
processDispatchQueueItemsInOrder @ chunk-Q72EVS5P.js:7036
processDispatchQueue @ chunk-Q72EVS5P.js:7045
dispatchEventsForPlugins @ chunk-Q72EVS5P.js:7053
(anonymous) @ chunk-Q72EVS5P.js:7177
batchedUpdates$1 @ chunk-Q72EVS5P.js:18941
batchedUpdates @ chunk-Q72EVS5P.js:3579
dispatchEventForPluginEventSystem @ chunk-Q72EVS5P.js:7176
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-Q72EVS5P.js:5478
dispatchEvent @ chunk-Q72EVS5P.js:5472
dispatchDiscreteEvent @ chunk-Q72EVS5P.js:5449
handleMouseUp_ @ unknown
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055879175}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055879175}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: undefined, assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055879175}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055879175}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: undefined, assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055879175}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055879175}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: undefined, assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, vapiAssistantId, theme, voiceId, voiceProvider
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 voiceId: sarah
 voiceProvider: 11labs
EnhancedPreviewNew.jsx:470 chatActive: false
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: 11labs
EnhancedPreviewNew.jsx:470 chatActive: false
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: Your Law Firm
EnhancedPreviewNew.jsx:462 titleText: Your Law Firm
EnhancedPreviewNew.jsx:463 logoUrl: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: 
EnhancedPreviewNew.jsx:467 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: 11labs
EnhancedPreviewNew.jsx:470 chatActive: false
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
AgentTab.jsx:1783 [AgentTab] Assistant dropdown changed to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
AgentTab.jsx:1792 [AgentTab] Switching to assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [AgentTab] Selected assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [DashboardNew] Cannot update attorney - attorney data is invalid or missing ID
handlePreviewConfigUpdate @ DashboardNew.jsx:511
handleSelectAssistant @ AgentTab.jsx:1237
handleAssistantDropdownChange @ AgentTab.jsx:1297
callCallback2 @ chunk-Q72EVS5P.js:3674
invokeGuardedCallbackDev @ chunk-Q72EVS5P.js:3699
invokeGuardedCallback @ chunk-Q72EVS5P.js:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-Q72EVS5P.js:3736
executeDispatch @ chunk-Q72EVS5P.js:7016
processDispatchQueueItemsInOrder @ chunk-Q72EVS5P.js:7036
processDispatchQueue @ chunk-Q72EVS5P.js:7045
dispatchEventsForPlugins @ chunk-Q72EVS5P.js:7053
(anonymous) @ chunk-Q72EVS5P.js:7177
batchedUpdates$1 @ chunk-Q72EVS5P.js:18941
batchedUpdates @ chunk-Q72EVS5P.js:3579
dispatchEventForPluginEventSystem @ chunk-Q72EVS5P.js:7176
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-Q72EVS5P.js:5478
dispatchEvent @ chunk-Q72EVS5P.js:5472
dispatchDiscreteEvent @ chunk-Q72EVS5P.js:5449
handleMouseUp_ @ unknown
 Updated preview config: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Updated preview config: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889510}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889510}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: undefined, assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889510}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889510}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: undefined, assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889510}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889510}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: undefined, assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889514}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889514}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: undefined, assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889514}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889514}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: undefined, assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889514}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749055889514}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:678 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
DashboardNew.jsx:785 No attorney or assistant ID available for sync
forceSyncToVapi @ DashboardNew.jsx:785
callCallback2 @ chunk-Q72EVS5P.js?v=704ffe31:3674
invokeGuardedCallbackDev @ chunk-Q72EVS5P.js?v=704ffe31:3699
invokeGuardedCallback @ chunk-Q72EVS5P.js?v=704ffe31:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-Q72EVS5P.js?v=704ffe31:3736
executeDispatch @ chunk-Q72EVS5P.js?v=704ffe31:7016
processDispatchQueueItemsInOrder @ chunk-Q72EVS5P.js?v=704ffe31:7036
processDispatchQueue @ chunk-Q72EVS5P.js?v=704ffe31:7045
dispatchEventsForPlugins @ chunk-Q72EVS5P.js?v=704ffe31:7053
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:7177
batchedUpdates$1 @ chunk-Q72EVS5P.js?v=704ffe31:18941
batchedUpdates @ chunk-Q72EVS5P.js?v=704ffe31:3579
dispatchEventForPluginEventSystem @ chunk-Q72EVS5P.js?v=704ffe31:7176
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-Q72EVS5P.js?v=704ffe31:5478
dispatchEvent @ chunk-Q72EVS5P.js?v=704ffe31:5472
dispatchDiscreteEvent @ chunk-Q72EVS5P.js?v=704ffe31:5449
