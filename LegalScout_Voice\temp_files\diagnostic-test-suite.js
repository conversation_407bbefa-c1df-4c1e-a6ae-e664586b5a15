/**
 * Diagnostic Test Suite for Dashboard Session Agent Change Issues
 * 
 * This script runs comprehensive tests to identify the exact scope of impact
 * and root causes without making any changes to the system.
 */

(function() {
  'use strict';

  const DiagnosticTests = {
    results: {},
    
    log: function(test, status, message, data = null) {
      const timestamp = new Date().toISOString();
      const result = { timestamp, status, message, data };
      
      if (!this.results[test]) {
        this.results[test] = [];
      }
      this.results[test].push(result);
      
      const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
      console.log(`${emoji} [DiagnosticTest:${test}] ${message}`, data || '');
    },

    // Test 1: State Management System Conflicts
    testStateManagementConflicts: function() {
      const test = 'StateManagement';
      
      // Check for multiple attorney managers
      const managers = {
        standalone: !!window.standaloneAttorneyManager,
        attorneyProfile: !!window.AttorneyProfileManager,
        unified: !!window.unifiedAttorneyManager,
        disabled: window.standaloneAttorneyManager?.disabled
      };
      
      const activeManagers = Object.entries(managers).filter(([key, value]) => value && key !== 'disabled').length;
      
      if (activeManagers > 1) {
        this.log(test, 'FAIL', `Multiple active managers detected: ${activeManagers}`, managers);
      } else if (activeManagers === 0) {
        this.log(test, 'FAIL', 'No active attorney managers found', managers);
      } else {
        this.log(test, 'PASS', 'Single attorney manager active', managers);
      }

      // Check localStorage consistency
      const storedAttorney = localStorage.getItem('attorney');
      const managerAttorney = window.standaloneAttorneyManager?.attorney;
      
      if (storedAttorney && managerAttorney) {
        const stored = JSON.parse(storedAttorney);
        const consistent = stored.id === managerAttorney.id && 
                          stored.vapi_assistant_id === managerAttorney.vapi_assistant_id;
        
        this.log(test, consistent ? 'PASS' : 'FAIL', 
          `localStorage vs Manager consistency: ${consistent}`, 
          { stored: stored.id, manager: managerAttorney.id });
      } else {
        this.log(test, 'WARN', 'Missing attorney data in localStorage or manager');
      }
    },

    // Test 2: Iframe Detection and Communication
    testIframeSystem: function() {
      const test = 'IframeSystem';
      
      // Test iframe detection
      const selectors = [
        '#preview-iframe',
        '.preview-iframe', 
        'iframe[title="Agent Preview"]',
        'iframe[src*="simple-preview"]',
        'iframe[src*="preview"]'
      ];
      
      const iframeResults = {};
      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        iframeResults[selector] = elements.length;
      });
      
      const totalIframes = Object.values(iframeResults).reduce((sum, count) => sum + count, 0);
      
      if (totalIframes === 0) {
        this.log(test, 'FAIL', 'No preview iframes found with any selector', iframeResults);
      } else {
        this.log(test, 'PASS', `Found ${totalIframes} iframes`, iframeResults);
      }

      // Test iframe manager availability
      if (window.DashboardIframeManager) {
        const managerCount = window.DashboardIframeManager.getIframeCount();
        this.log(test, managerCount > 0 ? 'PASS' : 'WARN', 
          `DashboardIframeManager reports ${managerCount} iframes`);
      } else {
        this.log(test, 'FAIL', 'DashboardIframeManager not available');
      }
    },

    // Test 3: Assistant Creation and Switching
    testAssistantSystem: function() {
      const test = 'AssistantSystem';
      
      const attorney = window.standaloneAttorneyManager?.attorney;
      
      if (!attorney) {
        this.log(test, 'FAIL', 'No attorney available for assistant testing');
        return;
      }

      // Check assistant ID presence and validity
      const assistantId = attorney.vapi_assistant_id;
      if (!assistantId) {
        this.log(test, 'FAIL', 'Attorney has no assistant ID', { attorneyId: attorney.id });
      } else if (assistantId.includes('mock') || assistantId.includes('dev-')) {
        this.log(test, 'WARN', 'Attorney has mock/dev assistant ID', { assistantId });
      } else {
        this.log(test, 'PASS', 'Attorney has valid assistant ID', { assistantId });
      }

      // Test Vapi service availability
      if (window.vapiServiceManager) {
        const connected = window.vapiServiceManager.connected;
        this.log(test, connected ? 'PASS' : 'FAIL', 
          `Vapi service manager connected: ${connected}`);
      } else {
        this.log(test, 'FAIL', 'Vapi service manager not available');
      }
    },

    // Test 4: React Component State Sync
    testReactStateSync: function() {
      const test = 'ReactStateSync';
      
      // Check if React components are receiving state updates
      const reactElements = document.querySelectorAll('[data-testid], [class*="Tab"], [class*="Dashboard"]');
      
      if (reactElements.length === 0) {
        this.log(test, 'WARN', 'No React components detected in DOM');
        return;
      }

      // Check for error boundaries
      const errorBoundaries = document.querySelectorAll('[class*="Error"], [class*="Boundary"]');
      if (errorBoundaries.length > 0) {
        this.log(test, 'WARN', `${errorBoundaries.length} error boundaries detected`);
      }

      // Check for loading states
      const loadingElements = document.querySelectorAll('[class*="loading"], [class*="Loading"]');
      this.log(test, 'INFO', `${loadingElements.length} loading elements detected`);

      this.log(test, 'PASS', `React components detected: ${reactElements.length}`);
    },

    // Test 5: API Endpoint Health
    testAPIHealth: function() {
      const test = 'APIHealth';
      
      // Test critical endpoints
      const endpoints = [
        '/api/sync-tools/manage-auth-state',
        '/api/attorneys',
        '/api/vapi/assistants'
      ];

      endpoints.forEach(async (endpoint) => {
        try {
          const response = await fetch(endpoint, { method: 'GET' });
          const status = response.status;
          
          if (status === 200) {
            this.log(test, 'PASS', `Endpoint ${endpoint} responding`);
          } else if (status === 404) {
            this.log(test, 'WARN', `Endpoint ${endpoint} not found (${status})`);
          } else {
            this.log(test, 'FAIL', `Endpoint ${endpoint} error (${status})`);
          }
        } catch (error) {
          this.log(test, 'FAIL', `Endpoint ${endpoint} unreachable`, error.message);
        }
      });
    },

    // Test 6: Emergency Script Conflicts
    testEmergencyScriptConflicts: function() {
      const test = 'EmergencyScripts';
      
      const emergencyScripts = [
        'emergency-api-key-fix',
        'critical-production-fix',
        'robust-state-handler',
        'disable-automatic-assistant-creation',
        'controlled-assistant-creation',
        'unified-banner-fix',
        'production-cors-fix',
        'clean-auth-solution'
      ];

      const loadedScripts = emergencyScripts.filter(script => 
        window[script] || document.querySelector(`script[src*="${script}"]`)
      );

      if (loadedScripts.length > 5) {
        this.log(test, 'WARN', `High number of emergency scripts: ${loadedScripts.length}`, loadedScripts);
      } else {
        this.log(test, 'PASS', `Reasonable emergency script count: ${loadedScripts.length}`, loadedScripts);
      }

      // Check for script conflicts
      const fetchOverrides = window.fetch.toString().includes('emergency') || 
                           window.fetch.toString().includes('critical');
      
      if (fetchOverrides) {
        this.log(test, 'WARN', 'Fetch function has been overridden by emergency scripts');
      }
    },

    // Run all tests
    runAllTests: function() {
      console.log('🔍 Starting Diagnostic Test Suite...');
      
      this.testStateManagementConflicts();
      this.testIframeSystem();
      this.testAssistantSystem();
      this.testReactStateSync();
      this.testAPIHealth();
      this.testEmergencyScriptConflicts();
      
      this.generateReport();
    },

    // Generate comprehensive report
    generateReport: function() {
      console.log('\n📊 DIAGNOSTIC REPORT');
      console.log('='.repeat(50));
      
      const summary = {};
      Object.entries(this.results).forEach(([test, results]) => {
        const passes = results.filter(r => r.status === 'PASS').length;
        const fails = results.filter(r => r.status === 'FAIL').length;
        const warns = results.filter(r => r.status === 'WARN').length;
        
        summary[test] = { passes, fails, warns, total: results.length };
        
        console.log(`\n${test}:`);
        results.forEach(result => {
          const emoji = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
          console.log(`  ${emoji} ${result.message}`);
        });
      });
      
      console.log('\n📈 SUMMARY:');
      Object.entries(summary).forEach(([test, stats]) => {
        const health = stats.fails === 0 ? 'HEALTHY' : stats.fails > stats.passes ? 'CRITICAL' : 'DEGRADED';
        console.log(`  ${test}: ${health} (${stats.passes}✅ ${stats.fails}❌ ${stats.warns}⚠️)`);
      });
      
      // Store results globally for further analysis
      window.diagnosticResults = this.results;
      window.diagnosticSummary = summary;
      
      console.log('\n💾 Results stored in window.diagnosticResults and window.diagnosticSummary');
    }
  };

  // Auto-run tests when script loads
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => DiagnosticTests.runAllTests(), 1000);
    });
  } else {
    setTimeout(() => DiagnosticTests.runAllTests(), 1000);
  }

  // Expose for manual testing
  window.DiagnosticTests = DiagnosticTests;

})();
