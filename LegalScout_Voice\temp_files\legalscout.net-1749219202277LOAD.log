critical-production-fix.js:26 Uncaught SyntaxError: Cannot use import statement outside a module (at critical-production-fix.js:26:16)
dashboard:16 ✅ Vapi keys set globally
dashboard:37 ✅ Supabase keys set globally - should load correct assistant by domain
dashboard:48 🚀 [EMERGENCY] Starting emergency critical fixes...
dashboard:52 🔧 [EMERGENCY] Adding process polyfill
dashboard:59 ✅ [EMERGENCY] Process polyfill added
dashboard:70 🔧 [EMERGENCY] Development mode: false (forced production)
dashboard:100 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
dashboard:103 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [Robust<PERSON>tateHand<PERSON>] Initializing comprehensive state management...
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:343 [StandaloneAttorneyManager] Using default Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
    at setupIframeObserver (dashboard-iframe-manager.js:187:14)
    at dashboard-iframe-manager.js:242:3
    at dashboard-iframe-manager.js:266:3
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
production-cors-fix.js:62 Uncaught SyntaxError: Cannot use import statement outside a module (at production-cors-fix.js:62:16)
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
dashboard:1 Refused to load the script 'https://vercel.live/_next-live/feedback/feedback.js' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io". Note that 'script-src-elem' was not explicitly set, so 'script-src' is used as a fallback.

dashboard:1 Refused to load the script 'https://vercel.live/_next-live/feedback/feedback.js' because it violates the following Content Security Policy directive: "script-src-elem 'self' 'unsafe-inline' blob: data: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io".

vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 Supabase client initialized successfully with proper headers
supabase.js:102 Testing Supabase connection...
supabase.js:137 Supabase client ready for use
supabase.js:145 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
environmentVerifier.js:58 Environment Variable Verification
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:98 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:98
send @ streamableHttp.js:248
await in send
(anonymous) @ protocol.js:297
request @ protocol.js:233
connect @ index.js:66
await in connect
_connect @ vapiMcpService.js:326
await in _connect
connect @ vapiMcpService.js:267
initialize @ vapiServiceManager.js:88
$n @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
vapiMcpService.js:331 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
overrideMethod @ hook.js:608
_connect @ vapiMcpService.js:331
await in _connect
connect @ vapiMcpService.js:267
initialize @ vapiServiceManager.js:88
$n @ initAttorneyProfileManager.js:45
(anonymous) @ initAttorneyProfileManager.js:135
index.ts:5 Loaded contentScript
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
dashboard:98 
            
            
           GET https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a 401 (Unauthorized)
window.fetch @ dashboard:98
getAssistant @ vapiMcpService.js:426
await in getAssistant
getVapiAssistant @ robust-state-handler.js:352
await in getVapiAssistant
getAttorneyAssistants @ robust-state-handler.js:296
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:441 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a: 401
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:441
await in getAssistant
getVapiAssistant @ robust-state-handler.js:352
await in getVapiAssistant
getAttorneyAssistants @ robust-state-handler.js:296
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard:98 
            
            
           GET https://dashboard.vapi.ai/api/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a net::ERR_FAILED
window.fetch @ dashboard:98
getAssistant @ vapiMcpService.js:426
await in getAssistant
getVapiAssistant @ robust-state-handler.js:352
await in getVapiAssistant
getAttorneyAssistants @ robust-state-handler.js:296
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:444 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:444
await in getAssistant
getVapiAssistant @ robust-state-handler.js:352
await in getVapiAssistant
getAttorneyAssistants @ robust-state-handler.js:296
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:449 [VapiMcpService] Assistant not found with ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
overrideMethod @ hook.js:608
getAssistant @ vapiMcpService.js:449
await in getAssistant
getVapiAssistant @ robust-state-handler.js:352
await in getVapiAssistant
getAttorneyAssistants @ robust-state-handler.js:296
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:300 [RobustStateHandler] Assistant ID exists but assistant not found in Vapi: f9b97d13-f9c4-40af-a660-62ba5925ff2a
overrideMethod @ hook.js:608
getAttorneyAssistants @ robust-state-handler.js:300
await in getAttorneyAssistants
resolveAssistantState @ robust-state-handler.js:231
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard:98 
            
            
           POST https://api.vapi.ai/assistant 401 (Unauthorized)
window.fetch @ dashboard:98
createAssistant @ vapiMcpService.js:628
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:643 [VapiMcpService] Vapi API error response: {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:643
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:724 [VapiMcpService] Error creating assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:652:19)
    at async createVapiAssistant (robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (robust-state-handler.js:527:29)
    at async createDefaultAssistant (robust-state-handler.js:486:27)
    at async resolveAssistantState (robust-state-handler.js:237:30)
    at async performStateResolution (robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:724
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:586 [RobustStateHandler] Error creating Vapi assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:652:19)
    at async createVapiAssistant (robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (robust-state-handler.js:527:29)
    at async createDefaultAssistant (robust-state-handler.js:486:27)
    at async resolveAssistantState (robust-state-handler.js:237:30)
    at async performStateResolution (robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createVapiAssistant @ robust-state-handler.js:586
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:536 ❌ [RobustStateHandler] Assistant creation attempt 1 failed: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:652:19)
    at async createVapiAssistant (robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (robust-state-handler.js:527:29)
    at async createDefaultAssistant (robust-state-handler.js:486:27)
    at async resolveAssistantState (robust-state-handler.js:237:30)
    at async performStateResolution (robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createVapiAssistantWithRetry @ robust-state-handler.js:536
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
Pe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:611
Xr @ react-dom.production.min.js:162
Bi @ react-dom.production.min.js:163
useState @ react-dom.production.min.js:179
O.useState @ react.production.min.js:26
xc @ DashboardNew.jsx:63
Us @ react-dom.production.min.js:160
Ia @ react-dom.production.min.js:196
G0 @ react-dom.production.min.js:291
X0 @ react-dom.production.min.js:279
j4 @ react-dom.production.min.js:279
zl @ react-dom.production.min.js:279
W0 @ react-dom.production.min.js:267
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard-iframe-manager.js:29 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
Pe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:611
Xr @ react-dom.production.min.js:162
Bi @ react-dom.production.min.js:163
useState @ react-dom.production.min.js:179
O.useState @ react.production.min.js:26
xc @ DashboardNew.jsx:63
Us @ react-dom.production.min.js:160
Ia @ react-dom.production.min.js:196
G0 @ react-dom.production.min.js:291
X0 @ react-dom.production.min.js:279
j4 @ react-dom.production.min.js:279
zl @ react-dom.production.min.js:279
W0 @ react-dom.production.min.js:267
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
Pe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:611
Xr @ react-dom.production.min.js:162
Bi @ react-dom.production.min.js:163
useState @ react-dom.production.min.js:179
O.useState @ react.production.min.js:26
xc @ DashboardNew.jsx:63
Us @ react-dom.production.min.js:160
Ia @ react-dom.production.min.js:196
G0 @ react-dom.production.min.js:291
X0 @ react-dom.production.min.js:279
j4 @ react-dom.production.min.js:279
zl @ react-dom.production.min.js:279
W0 @ react-dom.production.min.js:267
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard-iframe-manager.js:29 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
Pe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:611
Xr @ react-dom.production.min.js:162
Bi @ react-dom.production.min.js:163
useState @ react-dom.production.min.js:179
O.useState @ react.production.min.js:26
xc @ DashboardNew.jsx:63
Us @ react-dom.production.min.js:160
Ia @ react-dom.production.min.js:196
G0 @ react-dom.production.min.js:291
X0 @ react-dom.production.min.js:279
j4 @ react-dom.production.min.js:279
zl @ react-dom.production.min.js:279
W0 @ react-dom.production.min.js:267
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
dashboard:98 
            
            
           POST https://api.vapi.ai/assistant 401 (Unauthorized)
window.fetch @ dashboard:98
createAssistant @ vapiMcpService.js:628
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:643 [VapiMcpService] Vapi API error response: {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:643
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:724 [VapiMcpService] Error creating assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:652:19)
    at async createVapiAssistant (robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (robust-state-handler.js:527:29)
    at async createDefaultAssistant (robust-state-handler.js:486:27)
    at async resolveAssistantState (robust-state-handler.js:237:30)
    at async performStateResolution (robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:724
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:586 [RobustStateHandler] Error creating Vapi assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:652:19)
    at async createVapiAssistant (robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (robust-state-handler.js:527:29)
    at async createDefaultAssistant (robust-state-handler.js:486:27)
    at async resolveAssistantState (robust-state-handler.js:237:30)
    at async performStateResolution (robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createVapiAssistant @ robust-state-handler.js:586
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:536 ❌ [RobustStateHandler] Assistant creation attempt 2 failed: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:652:19)
    at async createVapiAssistant (robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (robust-state-handler.js:527:29)
    at async createDefaultAssistant (robust-state-handler.js:486:27)
    at async resolveAssistantState (robust-state-handler.js:237:30)
    at async performStateResolution (robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createVapiAssistantWithRetry @ robust-state-handler.js:536
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ hook.js:608
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
DashboardNew.jsx:473 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:473
setTimeout
(anonymous) @ DashboardNew.jsx:471
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
critical-production-fix.js:26 Uncaught SyntaxError: Cannot use import statement outside a module (at critical-production-fix.js:26:16)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:16 ✅ Vapi keys set globally
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:37 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:48 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:59 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:70 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:100 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:103 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
critical-production-fix.js:26 Uncaught SyntaxError: Cannot use import statement outside a module (at critical-production-fix.js:26:16)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:16 ✅ Vapi keys set globally
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:37 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:48 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:59 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:70 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:100 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:103 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
dashboard:98 
            
            
           POST https://api.vapi.ai/assistant 401 (Unauthorized)
window.fetch @ dashboard:98
createAssistant @ vapiMcpService.js:628
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
vapiMcpService.js:643 [VapiMcpService] Vapi API error response: {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:643
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
vapiMcpService.js:724 [VapiMcpService] Error creating assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:652:19)
    at async createVapiAssistant (robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (robust-state-handler.js:527:29)
    at async createDefaultAssistant (robust-state-handler.js:486:27)
    at async resolveAssistantState (robust-state-handler.js:237:30)
    at async performStateResolution (robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createAssistant @ vapiMcpService.js:724
await in createAssistant
createVapiAssistant @ robust-state-handler.js:557
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:586 [RobustStateHandler] Error creating Vapi assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:652:19)
    at async createVapiAssistant (robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (robust-state-handler.js:527:29)
    at async createDefaultAssistant (robust-state-handler.js:486:27)
    at async resolveAssistantState (robust-state-handler.js:237:30)
    at async performStateResolution (robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createVapiAssistant @ robust-state-handler.js:586
await in createVapiAssistant
createVapiAssistantWithRetry @ robust-state-handler.js:527
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:536 ❌ [RobustStateHandler] Assistant creation attempt 3 failed: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:652:19)
    at async createVapiAssistant (robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (robust-state-handler.js:527:29)
    at async createDefaultAssistant (robust-state-handler.js:486:27)
    at async resolveAssistantState (robust-state-handler.js:237:30)
    at async performStateResolution (robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createVapiAssistantWithRetry @ robust-state-handler.js:536
await in createVapiAssistantWithRetry
createDefaultAssistant @ robust-state-handler.js:486
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:496 ❌ [RobustStateHandler] Error creating default assistant: Error: Vapi API error: 401  - {"message":"Invalid Key. Hot tip, you may be using the private key instead of the public key, or vice versa.","error":"Unauthorized","statusCode":401}
    at La.createAssistant (vapiMcpService.js:652:19)
    at async createVapiAssistant (robust-state-handler.js:557:18)
    at async createVapiAssistantWithRetry (robust-state-handler.js:527:29)
    at async createDefaultAssistant (robust-state-handler.js:486:27)
    at async resolveAssistantState (robust-state-handler.js:237:30)
    at async performStateResolution (robust-state-handler.js:104:34)
    at async window.resolveAttorneyState (robust-state-handler.js:78:24)
    at async DashboardNew.jsx:193:31
overrideMethod @ hook.js:608
createDefaultAssistant @ robust-state-handler.js:496
await in createDefaultAssistant
resolveAssistantState @ robust-state-handler.js:237
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
robust-state-handler.js:255 ❌ [RobustStateHandler] Failed to create default assistant
overrideMethod @ hook.js:608
resolveAssistantState @ robust-state-handler.js:255
await in resolveAssistantState
performStateResolution @ robust-state-handler.js:104
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
(anonymous) @ DashboardNew.jsx:193
await in (anonymous)
(anonymous) @ DashboardNew.jsx:219
ql @ react-dom.production.min.js:243
jn @ react-dom.production.min.js:285
(anonymous) @ react-dom.production.min.js:281
Z @ scheduler.production.min.js:13
j @ scheduler.production.min.js:14
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
overrideMethod @ installHook.js:1
(anonymous) @ robust-state-handler.js:34
setTimeout
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught 
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
production-cors-fix.js:62 Uncaught 
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:187 Uncaught 
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
production-cors-fix.js:62 Uncaught 
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: [{…}]
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 🧹 [CleanAuthSolution] Starting clean authentication solution...
 🧹 [CleanAuthSolution] Executing clean authentication solution...
 🧹 [CleanAuthSolution] Disabling conflicting scripts...
 ✅ [CleanAuthSolution] Conflicting scripts disabled
 🧹 [CleanAuthSolution] Restoring original fetch function...
 ✅ [CleanAuthSolution] Original fetch restored
 🧹 [CleanAuthSolution] Checking for corrupted auth state...
 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
 🧹 [CleanAuthSolution] Setting up auth state monitoring...
 🧹 [CleanAuthSolution] Testing the solution...
 ✅ [CleanAuthSolution] Clean authentication solution complete
 ✅ [CleanAuthSolution] Auth state monitoring set up
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
 ✅ [CleanAuthSolution] Auth state monitoring set up
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 Environment Variable Verification
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 Environment Variable Verification
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
  POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ simple-preview:98
send @ vendor-157aa9b0.js:55
await in send
(anonymous) @ vendor-157aa9b0.js:9
request @ vendor-157aa9b0.js:9
connect @ vendor-157aa9b0.js:9
await in connect
_connect @ pages-9652b6f8.js:51
await in _connect
connect @ pages-9652b6f8.js:51
initialize @ index-59fe08da.js:17
$n @ index-59fe08da.js:172
(anonymous) @ index-59fe08da.js:172
 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ pages-9652b6f8.js:51
await in _connect
connect @ pages-9652b6f8.js:51
initialize @ index-59fe08da.js:17
$n @ index-59fe08da.js:172
(anonymous) @ index-59fe08da.js:172
simple-preview:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'https://legalscout.net' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
  POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ simple-preview:98
send @ vendor-157aa9b0.js:55
await in send
(anonymous) @ vendor-157aa9b0.js:9
request @ vendor-157aa9b0.js:9
connect @ vendor-157aa9b0.js:9
await in connect
_connect @ pages-9652b6f8.js:51
await in _connect
connect @ pages-9652b6f8.js:51
initialize @ index-59fe08da.js:17
$n @ index-59fe08da.js:172
(anonymous) @ index-59fe08da.js:172
 [VapiMcpService] MCP connection failed, falling back to direct API: Failed to fetch
_connect @ pages-9652b6f8.js:51
await in _connect
connect @ pages-9652b6f8.js:51
initialize @ index-59fe08da.js:17
$n @ index-59fe08da.js:172
(anonymous) @ index-59fe08da.js:172
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: https://legalscout.net/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
