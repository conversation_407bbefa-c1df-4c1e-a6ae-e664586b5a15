callback:21 ✅ Vapi public key set globally
callback:42 ✅ Supabase keys set globally - should load correct assistant by domain
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
callback:52 🚀 [EMERGENCY] Starting emergency critical fixes...
callback:56 🔧 [EMERGENCY] Adding process polyfill
callback:63 ✅ [EMERGENCY] Process polyfill added
callback:74 🔧 [EMERGENCY] Development mode: false (forced production)
callback:101 ✅ [EMERGENCY] Fetch patched
callback:104 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
callback:126 
            
            
           GET http://localhost:5175/disable-automatic-assistant-creation.js net::ERR_ABORTED 404 (Not Found)
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
overrideMethod @ hook.js:608
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
callback:209 Supabase loaded from CDN
callback:219 Creating Supabase client from CDN
callback:223 Supabase client created from CDN
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js?t=1749080350685:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js?t=1749080350685:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749080350685:46 Supabase Key configured: eyJhb...K4cRU
supabase.js?t=1749080350685:60 Development mode detected, using fallback Supabase configuration
supabase.js?t=1749080350685:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js?t=1749080350685:92 Supabase client initialized successfully with proper headers
supabase.js?t=1749080350685:95 Testing Supabase connection...
supabase.js?t=1749080350685:130 Supabase client ready for use
supabase.js?t=1749080350685:138 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js?t=1749080350685:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
initAttorneyProfileManager.js?t=1749080350685:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js?t=1749080350685:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js:42 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js:61 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js:233 [VapiMcpService] Production mode: false
vapiMcpService.js:234 [VapiMcpService] Development mode: true
vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:91 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js?t=1749080350685:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js?t=1749080350685:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js?t=1749080350685:128 [initAttorneyProfileManager] Initialization complete
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthCallback.jsx:15 Auth callback - environment mode: development
AuthCallback.jsx:18 Using real authentication in all environments
supabase.js?t=1749080350685:60 Development mode detected, using fallback Supabase configuration
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749080350685:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749080350685:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749080350685:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749080350685:64 Testing Supabase connection...
testSupabase.js?t=1749080350685:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749080350685:56 Supabase URL configured: true
testSupabase.js?t=1749080350685:57 Supabase Key configured: true
testSupabase.js?t=1749080350685:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749080350685:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749080350685:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749080350685:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
AuthCallback.jsx:15 Auth callback - environment mode: development
AuthCallback.jsx:18 Using real authentication in all environments
supabase.js?t=1749080350685:60 Development mode detected, using fallback Supabase configuration
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js?t=1749080350685:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js?t=1749080350685:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749080350685:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749080350685:64 Testing Supabase connection...
testSupabase.js?t=1749080350685:55 === SUPABASE CONFIG TEST ===
testSupabase.js?t=1749080350685:56 Supabase URL configured: true
testSupabase.js?t=1749080350685:57 Supabase Key configured: true
testSupabase.js?t=1749080350685:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js?t=1749080350685:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js?t=1749080350685:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js?t=1749080350685:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:790 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
index.ts:5 Loaded contentScript
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
robust-state-handler.js:21 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
callback#:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
AuthContext.jsx:92 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:95 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:110 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:123 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
callback:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:92 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:95 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:110 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:123 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
callback:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
(anonymous) @ supabase.js?t=1749080350685:100
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:179 Auth state changed: INITIAL_SESSION
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
supabase.js?t=1749080350685:106 Supabase query error: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ supabase.js?t=1749080350685:106
Promise.then
then @ @supabase_supabase-js.js?v=fb518282:192
(anonymous) @ supabase.js?t=1749080350685:100
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
supabaseConfigVerifier.js?t=1749080350685:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js?t=1749080350685:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js?t=1749080350685:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
supabaseConfigVerifier.js?t=1749080350685:145 ❌ Supabase configuration verification failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ supabaseConfigVerifier.js?t=1749080350685:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js?t=1749080350685:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
supabaseConfigVerifier.js?t=1749080350685:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js?t=1749080350685:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
App.jsx:688 Supabase connection failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
App.jsx:816 Error fetching attorney subdomains: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ hook.js:608
fetchSubdomains @ App.jsx:816
await in fetchSubdomains
(anonymous) @ App.jsx:832
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
supabaseConfigVerifier.js?t=1749080350685:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js?t=1749080350685:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js?t=1749080350685:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
supabaseConfigVerifier.js?t=1749080350685:145 ❌ Supabase configuration verification failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ supabaseConfigVerifier.js?t=1749080350685:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js?t=1749080350685:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
supabaseConfigVerifier.js?t=1749080350685:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js?t=1749080350685:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
App.jsx:688 Supabase connection failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
AuthCallback.jsx:105 Auth callback error: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ hook.js:608
handleCallback @ AuthCallback.jsx:105
await in handleCallback
(anonymous) @ AuthCallback.jsx:112
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
AuthCallback.jsx:105 Auth callback error: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ hook.js:608
handleCallback @ AuthCallback.jsx:105
await in handleCallback
(anonymous) @ AuthCallback.jsx:112
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
overrideMethod @ hook.js:608
(anonymous) @ robust-state-handler.js:34
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
robust-state-handler.js:38 🔄 [RobustStateHandler] Supabase available, trying fallback initialization...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
ActiveCheckHelper.ts:21 received intentional event
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:77 [useStandaloneAttorney] Initializing hook with manager...
useStandaloneAttorney.js:84 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
useStandaloneAttorney.js:119 [useStandaloneAttorney] No attorney found anywhere
DashboardNew.jsx:134 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
AutoAssistantReconciler.js?t=1749080350685:276 [AutoReconciler] 🚀 Initializing auto-reconciliation
DashboardNew.jsx:174 [DashboardNew] 🛡️ AUTOMATED robust state handler starting (UNCONDITIONAL)...
DashboardNew.jsx:231 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:232 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
DashboardNew.jsx:480 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
DashboardNew.jsx:256 [DashboardNew] fetchAttorneyData called.
DashboardNew.jsx:327 [DashboardNew] Attempting to load attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
DashboardNew.jsx:332 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
DashboardNew.jsx:335 [DashboardNew] 🛡️ Checking for robust state handler...
DashboardNew.jsx:338 [DashboardNew] ✅ Robust state handler found, calling it...
robust-state-handler.js:53 🔍 [RobustStateHandler] Resolving state for email: <EMAIL>
robust-state-handler.js:93 🔄 [RobustStateHandler] Attempt 1/3 for <EMAIL>
robust-state-handler.js:142 🔍 [RobustStateHandler] Looking for attorney: <EMAIL>
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:77 [useStandaloneAttorney] Initializing hook with manager...
useStandaloneAttorney.js:84 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
useStandaloneAttorney.js:119 [useStandaloneAttorney] No attorney found anywhere
DashboardNew.jsx:134 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
AutoAssistantReconciler.js?t=1749080350685:276 [AutoReconciler] 🚀 Initializing auto-reconciliation
DashboardNew.jsx:174 [DashboardNew] 🛡️ AUTOMATED robust state handler starting (UNCONDITIONAL)...
DashboardNew.jsx:231 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:232 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
DashboardNew.jsx:480 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
DashboardNew.jsx:256 [DashboardNew] fetchAttorneyData called.
DashboardNew.jsx:327 [DashboardNew] Attempting to load attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
DashboardNew.jsx:332 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
DashboardNew.jsx:335 [DashboardNew] 🛡️ Checking for robust state handler...
DashboardNew.jsx:338 [DashboardNew] ✅ Robust state handler found, calling it...
robust-state-handler.js:53 🔍 [RobustStateHandler] Resolving state for email: <EMAIL>
robust-state-handler.js:69 ⏳ [RobustStateHandler] Operation already in progress, waiting...
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
VM938 simple-preview:4 ✅ Vapi public key set globally
VM938 simple-preview:25 ✅ Supabase keys set globally - should load correct assistant by domain
VM942 simple-preview:4 ✅ Vapi public key set globally
VM942 simple-preview:25 ✅ Supabase keys set globally - should load correct assistant by domain
VM944 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM944 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM944 consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM944 consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
VM944 consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM944 consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
VM944 consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
VM944 consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
VM944 consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
VM944 consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
VM943 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM943 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM943 consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM943 consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
VM943 consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM943 consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
VM943 consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
VM943 consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
VM943 consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
VM943 consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
VM946 simple-preview:2 🚀 [EMERGENCY] Starting emergency critical fixes...
VM946 simple-preview:6 🔧 [EMERGENCY] Adding process polyfill
VM946 simple-preview:13 ✅ [EMERGENCY] Process polyfill added
VM946 simple-preview:24 🔧 [EMERGENCY] Development mode: false (forced production)
VM946 simple-preview:51 ✅ [EMERGENCY] Fetch patched
VM946 simple-preview:54 🎉 [EMERGENCY] Emergency fixes complete!
VM948 simple-preview:2 🚀 [EMERGENCY] Starting emergency critical fixes...
VM948 simple-preview:6 🔧 [EMERGENCY] Adding process polyfill
VM948 simple-preview:13 ✅ [EMERGENCY] Process polyfill added
VM948 simple-preview:24 🔧 [EMERGENCY] Development mode: false (forced production)
VM948 simple-preview:51 ✅ [EMERGENCY] Fetch patched
VM948 simple-preview:54 🎉 [EMERGENCY] Emergency fixes complete!
VM952 robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
VM951 robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
simple-preview:126 
            
            
           GET http://localhost:5175/disable-automatic-assistant-creation.js net::ERR_ABORTED 404 (Not Found)
simple-preview:126 
            
            
           GET http://localhost:5175/disable-automatic-assistant-creation.js net::ERR_ABORTED 404 (Not Found)
VM953 controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
VM953 controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
VM954 controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
VM954 controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
VM955 standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
VM955 standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
VM955 standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
VM955 standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
VM957 fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
VM957 fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
VM957 fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
VM956 standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
VM956 standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
VM956 standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
VM956 standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
VM958 fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
VM958 fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
VM958 fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
VM959 fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
VM959 fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
VM959 fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
VM960 enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
VM960 enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
VM960 enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
VM961 fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
VM961 fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
VM961 fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
VM962 fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
VM962 fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
VM962 fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
VM963 enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
VM963 enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
VM963 enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
VM964 fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
VM964 fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
VM964 fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
VM964 fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
robust-state-handler.js:117 ❌ [RobustStateHandler] Attempt 1 failed: Error: Error finding attorney: No API key found in request
    at findOrCreateAttorney (robust-state-handler.js:166:15)
    at async performStateResolution (VM951 robust-state-handler.js:96:26)
overrideMethod @ hook.js:608
performStateResolution @ robust-state-handler.js:117
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
fetchAttorneyData @ DashboardNew.jsx:341
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
robust-state-handler.js:123 ⏳ [RobustStateHandler] Retrying in 2000ms...
ActiveCheckHelper.ts:8 updating page active status
DashboardNew.jsx:188 [DashboardNew] 🛡️ User found: <EMAIL>
DashboardNew.jsx:191 [DashboardNew] 🛡️ Calling AUTOMATED robust state handler for: <EMAIL>
robust-state-handler.js:53 🔍 [RobustStateHandler] Resolving state for email: <EMAIL>
robust-state-handler.js:69 ⏳ [RobustStateHandler] Operation already in progress, waiting...
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
DashboardNew.jsx:188 [DashboardNew] 🛡️ User found: <EMAIL>
DashboardNew.jsx:191 [DashboardNew] 🛡️ Calling AUTOMATED robust state handler for: <EMAIL>
robust-state-handler.js:53 🔍 [RobustStateHandler] Resolving state for email: <EMAIL>
robust-state-handler.js:69 ⏳ [RobustStateHandler] Operation already in progress, waiting...
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
robust-state-handler.js:93 🔄 [RobustStateHandler] Attempt 2/3 for <EMAIL>
robust-state-handler.js:142 🔍 [RobustStateHandler] Looking for attorney: <EMAIL>
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
robust-state-handler.js:117 ❌ [RobustStateHandler] Attempt 2 failed: Error: Error finding attorney: No API key found in request
    at findOrCreateAttorney (robust-state-handler.js:166:15)
    at async performStateResolution (VM951 robust-state-handler.js:96:26)
overrideMethod @ hook.js:608
performStateResolution @ robust-state-handler.js:117
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
fetchAttorneyData @ DashboardNew.jsx:341
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
robust-state-handler.js:123 ⏳ [RobustStateHandler] Retrying in 4000ms...
robust-state-handler.js:93 🔄 [RobustStateHandler] Attempt 3/3 for <EMAIL>
robust-state-handler.js:142 🔍 [RobustStateHandler] Looking for attorney: <EMAIL>
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
robust-state-handler.js:117 ❌ [RobustStateHandler] Attempt 3 failed: Error: Error finding attorney: No API key found in request
    at findOrCreateAttorney (robust-state-handler.js:166:15)
    at async performStateResolution (VM951 robust-state-handler.js:96:26)
overrideMethod @ hook.js:608
performStateResolution @ robust-state-handler.js:117
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
fetchAttorneyData @ DashboardNew.jsx:341
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
robust-state-handler.js:130 ❌ [RobustStateHandler] All retries failed for: <EMAIL>
overrideMethod @ hook.js:608
performStateResolution @ robust-state-handler.js:130
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
fetchAttorneyData @ DashboardNew.jsx:341
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:342 [DashboardNew] 🔍 Robust state handler result: {success: false, error: 'Error finding attorney: No API key found in request', attorney: null, assistants: Array(0), needsCreation: true}
DashboardNew.jsx:367 [DashboardNew] ⚠️ Robust state handler failed: Error finding attorney: No API key found in request
overrideMethod @ hook.js:608
fetchAttorneyData @ DashboardNew.jsx:367
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:377 [DashboardNew] 🔄 Using fallback attorney loading logic...
fix-enhance-attorney-manager.js:48 [FixEnhanceAttorneyManager] Fixed loadAttorneyForUser called with userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701
enhance-attorney-manager.js:326 [EnhanceAttorneyManager] Enhanced loadAttorneyForUser called with userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701
enhance-attorney-manager.js:330 [EnhanceAttorneyManager] Already loading by user ID, returning current attorney
DashboardNew.jsx:342 [DashboardNew] 🔍 Robust state handler result: {success: false, error: 'Error finding attorney: No API key found in request', attorney: null, assistants: Array(0), needsCreation: true}
DashboardNew.jsx:367 [DashboardNew] ⚠️ Robust state handler failed: Error finding attorney: No API key found in request
overrideMethod @ hook.js:608
fetchAttorneyData @ DashboardNew.jsx:367
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:377 [DashboardNew] 🔄 Using fallback attorney loading logic...
fix-enhance-attorney-manager.js:48 [FixEnhanceAttorneyManager] Fixed loadAttorneyForUser called with userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701
fix-enhance-attorney-manager.js:58 [FixEnhanceAttorneyManager] Already loading by user ID, returning current attorney
DashboardNew.jsx:194 [DashboardNew] 🛡️ AUTOMATED robust state handler result: {success: false, error: 'Error finding attorney: No API key found in request', attorney: null, assistants: Array(0), needsCreation: true}
DashboardNew.jsx:212 [DashboardNew] ⚠️ AUTOMATED: Robust state handler failed: Error finding attorney: No API key found in request
overrideMethod @ hook.js:608
runRobustStateHandler @ DashboardNew.jsx:212
await in runRobustStateHandler
(anonymous) @ DashboardNew.jsx:223
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:194 [DashboardNew] 🛡️ AUTOMATED robust state handler result: {success: false, error: 'Error finding attorney: No API key found in request', attorney: null, assistants: Array(0), needsCreation: true}
DashboardNew.jsx:212 [DashboardNew] ⚠️ AUTOMATED: Robust state handler failed: Error finding attorney: No API key found in request
overrideMethod @ hook.js:608
runRobustStateHandler @ DashboardNew.jsx:212
await in runRobustStateHandler
(anonymous) @ DashboardNew.jsx:223
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:389 [DashboardNew] No attorney found by user ID, trying email: <EMAIL>
DashboardNew.jsx:389 [DashboardNew] No attorney found by user ID, trying email: <EMAIL>
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com&order=updated_at.desc 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
vapiAssistantUtils.js?t=1749080350685:157 Error fetching attorney by email: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ hook.js:608
findAttorneyByEmailAndEnsureVapiAssistant @ vapiAssistantUtils.js?t=1749080350685:157
await in findAttorneyByEmailAndEnsureVapiAssistant
fetchAttorneyData @ DashboardNew.jsx:392
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
vapiAssistantUtils.js?t=1749080350685:181 Error in findAttorneyByEmailAndEnsureVapiAssistant: Error: Supabase fetch error: No API key found in request
    at findAttorneyByEmailAndEnsureVapiAssistant (vapiAssistantUtils.js?t=1749080350685:158:13)
    at async fetchAttorneyData (DashboardNew.jsx:392:30)
overrideMethod @ hook.js:608
findAttorneyByEmailAndEnsureVapiAssistant @ vapiAssistantUtils.js?t=1749080350685:181
await in findAttorneyByEmailAndEnsureVapiAssistant
fetchAttorneyData @ DashboardNew.jsx:392
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:405 [DashboardNew] Error finding attorney by email: Error: Supabase fetch error: No API key found in request
    at findAttorneyByEmailAndEnsureVapiAssistant (vapiAssistantUtils.js?t=1749080350685:158:13)
    at async fetchAttorneyData (DashboardNew.jsx:392:30)
overrideMethod @ hook.js:608
fetchAttorneyData @ DashboardNew.jsx:405
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:432 [DashboardNew] No attorney profile found for this user or loading failed.
overrideMethod @ hook.js:608
fetchAttorneyData @ DashboardNew.jsx:432
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:435 💀 [DashboardNew] NO ATTORNEY PROFILE FOUND - FORCING ROBUST STATE HANDLER!
overrideMethod @ hook.js:608
fetchAttorneyData @ DashboardNew.jsx:435
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:440 💀 [DashboardNew] LAST CHANCE: Forcing robust state handler for: <EMAIL>
robust-state-handler.js:53 🔍 [RobustStateHandler] Resolving state for email: <EMAIL>
robust-state-handler.js:93 🔄 [RobustStateHandler] Attempt 1/3 for <EMAIL>
robust-state-handler.js:142 🔍 [RobustStateHandler] Looking for attorney: <EMAIL>
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com&order=updated_at.desc 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
vapiAssistantUtils.js?t=1749080350685:157 Error fetching attorney by email: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ hook.js:608
findAttorneyByEmailAndEnsureVapiAssistant @ vapiAssistantUtils.js?t=1749080350685:157
await in findAttorneyByEmailAndEnsureVapiAssistant
fetchAttorneyData @ DashboardNew.jsx:392
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
vapiAssistantUtils.js?t=1749080350685:181 Error in findAttorneyByEmailAndEnsureVapiAssistant: Error: Supabase fetch error: No API key found in request
    at findAttorneyByEmailAndEnsureVapiAssistant (vapiAssistantUtils.js?t=1749080350685:158:13)
    at async fetchAttorneyData (DashboardNew.jsx:392:30)
overrideMethod @ hook.js:608
findAttorneyByEmailAndEnsureVapiAssistant @ vapiAssistantUtils.js?t=1749080350685:181
await in findAttorneyByEmailAndEnsureVapiAssistant
fetchAttorneyData @ DashboardNew.jsx:392
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:405 [DashboardNew] Error finding attorney by email: Error: Supabase fetch error: No API key found in request
    at findAttorneyByEmailAndEnsureVapiAssistant (vapiAssistantUtils.js?t=1749080350685:158:13)
    at async fetchAttorneyData (DashboardNew.jsx:392:30)
overrideMethod @ hook.js:608
fetchAttorneyData @ DashboardNew.jsx:405
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:432 [DashboardNew] No attorney profile found for this user or loading failed.
overrideMethod @ hook.js:608
fetchAttorneyData @ DashboardNew.jsx:432
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:435 💀 [DashboardNew] NO ATTORNEY PROFILE FOUND - FORCING ROBUST STATE HANDLER!
overrideMethod @ hook.js:608
fetchAttorneyData @ DashboardNew.jsx:435
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:440 💀 [DashboardNew] LAST CHANCE: Forcing robust state handler for: <EMAIL>
robust-state-handler.js:53 🔍 [RobustStateHandler] Resolving state for email: <EMAIL>
robust-state-handler.js:69 ⏳ [RobustStateHandler] Operation already in progress, waiting...
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
robust-state-handler.js:117 ❌ [RobustStateHandler] Attempt 1 failed: Error: Error finding attorney: No API key found in request
    at findOrCreateAttorney (robust-state-handler.js:166:15)
    at async performStateResolution (VM951 robust-state-handler.js:96:26)
overrideMethod @ hook.js:608
performStateResolution @ robust-state-handler.js:117
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
fetchAttorneyData @ DashboardNew.jsx:441
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
robust-state-handler.js:123 ⏳ [RobustStateHandler] Retrying in 2000ms...
robust-state-handler.js:93 🔄 [RobustStateHandler] Attempt 2/3 for <EMAIL>
robust-state-handler.js:142 🔍 [RobustStateHandler] Looking for attorney: <EMAIL>
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
robust-state-handler.js:117 ❌ [RobustStateHandler] Attempt 2 failed: Error: Error finding attorney: No API key found in request
    at findOrCreateAttorney (robust-state-handler.js:166:15)
    at async performStateResolution (VM951 robust-state-handler.js:96:26)
overrideMethod @ hook.js:608
performStateResolution @ robust-state-handler.js:117
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
fetchAttorneyData @ DashboardNew.jsx:441
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
robust-state-handler.js:123 ⏳ [RobustStateHandler] Retrying in 4000ms...
DashboardNew.jsx:473 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:473
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
(anonymous) @ DashboardNew.jsx:471
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
VM972 simple-preview:4 ✅ Vapi public key set globally
VM972 simple-preview:25 ✅ Supabase keys set globally - should load correct assistant by domain
VM976 simple-preview:4 ✅ Vapi public key set globally
VM976 simple-preview:25 ✅ Supabase keys set globally - should load correct assistant by domain
VM980 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM980 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM980 consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM980 consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
VM980 consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM980 consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
VM980 consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
VM980 consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
VM980 consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
VM980 consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
VM979 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM979 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM979 consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM979 consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
VM979 consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM979 consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
VM979 consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
VM979 consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
VM979 consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
VM979 consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
VM981 simple-preview:2 🚀 [EMERGENCY] Starting emergency critical fixes...
VM981 simple-preview:6 🔧 [EMERGENCY] Adding process polyfill
VM981 simple-preview:13 ✅ [EMERGENCY] Process polyfill added
VM981 simple-preview:24 🔧 [EMERGENCY] Development mode: false (forced production)
VM981 simple-preview:51 ✅ [EMERGENCY] Fetch patched
VM981 simple-preview:54 🎉 [EMERGENCY] Emergency fixes complete!
VM982 simple-preview:2 🚀 [EMERGENCY] Starting emergency critical fixes...
VM982 simple-preview:6 🔧 [EMERGENCY] Adding process polyfill
VM982 simple-preview:13 ✅ [EMERGENCY] Process polyfill added
VM982 simple-preview:24 🔧 [EMERGENCY] Development mode: false (forced production)
VM982 simple-preview:51 ✅ [EMERGENCY] Fetch patched
VM982 simple-preview:54 🎉 [EMERGENCY] Emergency fixes complete!
VM986 robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
VM988 robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
simple-preview:126 
            
            
           GET http://localhost:5175/disable-automatic-assistant-creation.js net::ERR_ABORTED 404 (Not Found)
simple-preview:126 
            
            
           GET http://localhost:5175/disable-automatic-assistant-creation.js net::ERR_ABORTED 404 (Not Found)
VM989 controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
VM989 controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
VM990 controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
VM990 controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
VM991 standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
VM991 standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
VM991 standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
VM991 standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
VM992 standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
VM992 standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
VM992 standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
VM992 standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
VM993 fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
VM993 fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
VM993 fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
VM994 fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
VM994 fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
VM994 fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
VM995 fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
VM995 fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
VM995 fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
VM996 fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
VM996 fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
VM996 fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
VM997 enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
VM997 enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
VM997 enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
VM998 enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
VM998 enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
VM998 enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
VM999 fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
VM999 fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
VM999 fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
VM1000 fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
VM1000 fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
VM1000 fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
VM1001 fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
VM1001 fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
VM1001 fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
VM1001 fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
VM1002 fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
VM1002 fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
VM1002 fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
VM1002 fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
VM1003 unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
VM1003 unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
VM1003 unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
VM1003 unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
VM1003 unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
VM1003 unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
VM1003 unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
VM1004 unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
VM1004 unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
VM1004 unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
VM1004 unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
VM1004 unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
VM1004 unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
VM1004 unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
VM1006 dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
VM980 consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ VM980 consolidated-dashboard-fix.js:25
setupIframeObserver @ VM1006 dashboard-iframe-manager.js:187
(anonymous) @ VM1006 dashboard-iframe-manager.js:242
(anonymous) @ VM1006 dashboard-iframe-manager.js:266
VM1006 dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
VM1006 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
VM1006 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
VM1006 dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
VM1005 dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
VM979 consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ VM979 consolidated-dashboard-fix.js:25
setupIframeObserver @ VM1005 dashboard-iframe-manager.js:187
(anonymous) @ VM1005 dashboard-iframe-manager.js:242
(anonymous) @ VM1005 dashboard-iframe-manager.js:266
VM1005 dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
VM1005 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
VM1005 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
VM1005 dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
VM1007 simple-preview:8 Supabase loaded from CDN
VM1007 simple-preview:18 Creating Supabase client from CDN
VM1007 simple-preview:22 Supabase client created from CDN
VM1009 simple-preview:8 Supabase loaded from CDN
VM1009 simple-preview:18 Creating Supabase client from CDN
VM1009 simple-preview:22 Supabase client created from CDN
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
callback:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
callback:95 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
VM986 robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
VM988 robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
VM1002 fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
VM1002 fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
VM1002 fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
VM1002 fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
VM1001 fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
VM1001 fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
VM1001 fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
VM1001 fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
VM1019 reactPolyfill.js:16 [ReactPolyfill] Created window.React object
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
VM1019 reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
VM1019 reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
VM1019 reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
VM1314 vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
VM1314 vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
VM1162 supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
VM1162 supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
VM1162 supabase.js:46 Supabase Key configured: eyJhb...K4cRU
VM1162 supabase.js:60 Development mode detected, using fallback Supabase configuration
VM1162 supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
VM1162 supabase.js:92 Supabase client initialized successfully with proper headers
VM1162 supabase.js:95 Testing Supabase connection...
VM1162 supabase.js:130 Supabase client ready for use
VM1162 supabase.js:138 Attaching Supabase client to window.supabase
VM1235 vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
VM1187 vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Initialization complete
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
VM1031 reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
VM1031 reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
VM1031 reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
VM1349 vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
VM1349 vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
VM1236 supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
VM1236 supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
VM1236 supabase.js:46 Supabase Key configured: eyJhb...K4cRU
VM1236 supabase.js:60 Development mode detected, using fallback Supabase configuration
VM1236 supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
VM1236 supabase.js:92 Supabase client initialized successfully with proper headers
VM1236 supabase.js:95 Testing Supabase connection...
VM1236 supabase.js:130 Supabase client ready for use
VM1236 supabase.js:138 Attaching Supabase client to window.supabase
VM1302 vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
VM1283 vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
VM1051 initAttorneyProfileManager.js:16 [initAttorneyProfileManager] Initializing attorney profile manager
VM1051 initAttorneyProfileManager.js:25 [initAttorneyProfileManager] Initializing Vapi service manager
VM1157 vapiServiceManager.js:42 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
VM1157 vapiServiceManager.js:61 [VapiServiceManager] Trying to connect to real Vapi service
VM1302 vapiMcpService.js:233 [VapiMcpService] Production mode: false
VM1302 vapiMcpService.js:234 [VapiMcpService] Development mode: true
VM1302 vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
VM1302 vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
VM1302 vapiMcpService.js:246 [VapiMcpService] API key being used: 310f0d43...
VM1050 environmentVerifier.js:58 Environment Variable Verification
VM1050 environmentVerifier.js:59 All required variables present: ✅ Yes
VM1050 environmentVerifier.js:65 Variables status:
VM1050 environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
VM1050 environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
VM1050 environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
VM1159 vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
VM1158 vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
VM1158 vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
VM1052 initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
VM1157 vapiServiceManager.js:91 [VapiServiceManager] Connected to Vapi MCP server
VM1051 initAttorneyProfileManager.js:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
VM1051 initAttorneyProfileManager.js:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
VM1051 initAttorneyProfileManager.js:128 [initAttorneyProfileManager] Initialization complete
VM1004 unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
VM1004 unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
VM1004 unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
VM1004 unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
VM1004 unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
 [UnifiedBannerFix] Ensuring upload interface is visible
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [UnifiedBannerFix] Ensuring upload interface is visible
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🔗 [RobustStateHandler] Dependencies ready, initializing...
 🚀 [RobustStateHandler] Starting robust state handling...
 ✅ [RobustStateHandler] Robust state handling initialized
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
VM1148 testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
VM1149 supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
VM1149 supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
VM1149 supabaseConfigVerifier.js:64 Testing Supabase connection...
VM1114 debugConfig.js:30 [App] Subdomain detected: default
VM1114 debugConfig.js:30 [App] Is attorney subdomain: false
VM1038 App.jsx:708 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
VM1118 AuthContext.jsx:63 🔐 [AuthContext] Starting auth initialization...
VM1118 AuthContext.jsx:334 Using real authentication in all environments
VM1114 debugConfig.js:30 [App] App component unmounted undefined
VM1138 SimplePreviewPage.jsx:90 SimplePreviewPage: Starting config load...
VM1138 SimplePreviewPage.jsx:91 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
VM1138 SimplePreviewPage.jsx:109 SimplePreviewPage: Loading from Supabase for subdomain: default
VM1138 SimplePreviewPage.jsx:131 SimplePreviewPage: Falling back to direct Supabase loading
VM1138 SimplePreviewPage.jsx:63 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
VM1138 SimplePreviewPage.jsx:198 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM1114 debugConfig.js:30 [App] App component mounted undefined
VM1149 supabaseConfigVerifier.js:119 🔧 Development mode detected, initializing Supabase configuration...
VM1149 supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
VM1149 supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
VM1149 supabaseConfigVerifier.js:64 Testing Supabase connection...
VM1148 testSupabase.js:55 === SUPABASE CONFIG TEST ===
VM1148 testSupabase.js:56 Supabase URL configured: true
VM1148 testSupabase.js:57 Supabase Key configured: true
VM1148 testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
VM1149 supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
VM1149 supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
VM1149 supabaseConfigVerifier.js:64 Testing Supabase connection...
VM1114 debugConfig.js:30 [App] Subdomain detected: default
VM1114 debugConfig.js:30 [App] Is attorney subdomain: false
VM1038 App.jsx:708 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
VM1118 AuthContext.jsx:63 🔐 [AuthContext] Starting auth initialization...
VM1118 AuthContext.jsx:334 Using real authentication in all environments
DashboardNew.jsx:1513 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
VM1085 SimplePreviewPage.jsx:198 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM1085 SimplePreviewPage.jsx:198 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM1085 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614541}
VM1085 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1085 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1085 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1138 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614541}
VM1138 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1138 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1138 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1085 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614614}
VM1085 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1085 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1085 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1138 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614614}
VM1138 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1138 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1138 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
VM988 robust-state-handler.js:21 🔗 [RobustStateHandler] Dependencies ready, initializing...
 🚀 [RobustStateHandler] Starting robust state handling...
 ✅ [RobustStateHandler] Robust state handling initialized
 [UnifiedBannerFix] Ensuring upload interface is visible
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ :5175/simple-preview…ncedPreview=true:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
(anonymous) @ supabase.js:100
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614766}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614766}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614767}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614767}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614789}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614789}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614791}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614791}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Supabase query error: No API key found in request
(anonymous) @ supabase.js:106
Promise.then
then @ @supabase_supabase-js.js:192
(anonymous) @ supabase.js:100
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
VM1085 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614838}
VM1085 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1085 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1085 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1138 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614838}
VM1138 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1138 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1138 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
useSyncTools.js:516 Development mode: Using mock consistency check result
VM1101 SyncContext.jsx:137 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
VM1232 useSyncTools.js:516 Development mode: Using mock consistency check result
VM1085 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614855}
VM1085 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1085 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1085 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1138 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614855}
VM1138 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1138 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1138 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1085 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614862}
VM1085 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1085 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1085 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1138 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614862}
VM1138 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1138 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1138 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
VM1085 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614865}
VM1085 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1085 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1085 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1138 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080614865}
VM1138 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1138 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1138 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
VM1004 unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
VM1138 SimplePreviewPage.jsx:198 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM1138 SimplePreviewPage.jsx:198 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
VM1085 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615221}
VM1085 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1085 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1085 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1138 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615221}
VM1138 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1138 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1138 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1085 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615223}
VM1085 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1085 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1085 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
VM1138 SimplePreviewPage.jsx:174 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615223}
VM1138 SimplePreviewPage.jsx:176 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
VM1138 SimplePreviewPage.jsx:178 🎯 [SimplePreviewPage] Config updated successfully
VM1138 SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Sent response back to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
ActiveCheckHelper.ts:21 received intentional event
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
DashboardNew.jsx:624 [DashboardNew] Tab changed to: agent
AgentTab.jsx:511 [AgentTab] Loading voice settings from Vapi assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
AgentTab.jsx:511 [AgentTab] Loading voice settings from Vapi assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VM1118 AuthContext.jsx:168 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
VM1068 AuthContext.jsx:168 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 [23:43:35] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
 [Vapi MCP] GET https://api.vapi.ai/assistant
 [23:43:35] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
 [Vapi MCP] GET https://api.vapi.ai/assistant
VM1068 AuthContext.jsx:78 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
VM1068 AuthContext.jsx:79 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
VM1068 AuthContext.jsx:88 🔐 [AuthContext] Found OAuth email: <EMAIL>
VM1068 AuthContext.jsx:97 🔐 [AuthContext] Handling auth state for refresh...
VM1101 SyncContext.jsx:116 SyncContext: Handling auth state for action: refresh
VM1101 SyncContext.jsx:117 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
VM981 simple-preview:45 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
VM980 consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
VM1101 SyncContext.jsx:137 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
VM1232 useSyncTools.js:516 Development mode: Using mock consistency check result
VM1463 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM1463 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM1463 consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM1463 consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
VM1463 consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM1463 consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
VM1463 consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
VM1463 consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
VM1463 consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
VM1463 consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
VM1465 simple-preview:2 🚀 [EMERGENCY] Starting emergency critical fixes...
VM1465 simple-preview:6 🔧 [EMERGENCY] Adding process polyfill
VM1465 simple-preview:13 ✅ [EMERGENCY] Process polyfill added
VM1465 simple-preview:24 🔧 [EMERGENCY] Development mode: false (forced production)
VM1465 simple-preview:51 ✅ [EMERGENCY] Fetch patched
VM1465 simple-preview:54 🎉 [EMERGENCY] Emergency fixes complete!
VM1068 AuthContext.jsx:78 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
VM1068 AuthContext.jsx:79 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
VM1068 AuthContext.jsx:88 🔐 [AuthContext] Found OAuth email: <EMAIL>
VM1068 AuthContext.jsx:97 🔐 [AuthContext] Handling auth state for refresh...
VM1101 SyncContext.jsx:116 SyncContext: Handling auth state for action: refresh
VM1101 SyncContext.jsx:117 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
VM981 simple-preview:45 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
VM980 consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
VM1101 SyncContext.jsx:137 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
VM1232 useSyncTools.js:516 Development mode: Using mock consistency check result
VM1068 AuthContext.jsx:142 Auth state changed: INITIAL_SESSION
VM1068 AuthContext.jsx:144 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
VM1068 AuthContext.jsx:145 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
VM1068 AuthContext.jsx:154 Found OAuth email (auth change): <EMAIL>
VM1467 robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
simple-preview:126 
            
            
           GET http://localhost:5175/disable-automatic-assistant-creation.js net::ERR_ABORTED 404 (Not Found)
VM1118 AuthContext.jsx:78 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
VM1118 AuthContext.jsx:79 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
VM1118 AuthContext.jsx:88 🔐 [AuthContext] Found OAuth email: <EMAIL>
VM1118 AuthContext.jsx:97 🔐 [AuthContext] Handling auth state for refresh...
VM1154 SyncContext.jsx:116 SyncContext: Handling auth state for action: refresh
VM1154 SyncContext.jsx:117 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
VM982 simple-preview:45 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
VM979 consolidated-dashboard-fix.js:105 🔧 [EMERGENCY] Bypassing failing API endpoint
VM1471 controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
VM1471 controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
VM1154 SyncContext.jsx:137 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
VM1300 useSyncTools.js:516 Development mode: Using mock consistency check result
VM1118 AuthContext.jsx:78 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
VM1118 AuthContext.jsx:79 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
VM1118 AuthContext.jsx:88 🔐 [AuthContext] Found OAuth email: <EMAIL>
VM1118 AuthContext.jsx:97 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ :5175/simple-preview…ncedPreview=true:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ :5175/simple-preview…ncedPreview=true:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 SimplePreviewPage: Error loading attorney data from Supabase: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:66
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Supabase error details: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:67
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: No attorney data found for subdomain: default
loadConfig @ SimplePreviewPage.jsx:156
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:624
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ❌ Supabase configuration verification failed: No API key found in request
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:624
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615574}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615574}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615574}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615579}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615579}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615579}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ :5175/simple-preview…ncedPreview=true:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 🔄 [RobustStateHandler] Attempt 3/3 for <EMAIL>
 🔍 [RobustStateHandler] Looking for attorney: <EMAIL>
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ :5175/simple-preview…ncedPreview=true:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ :5175/simple-preview…ncedPreview=true:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 Error fetching attorney subdomains: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
fetchSubdomains @ App.jsx:723
await in fetchSubdomains
(anonymous) @ App.jsx:735
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 SimplePreviewPage: Error loading attorney data from Supabase: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:66
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Supabase error details: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:67
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: No attorney data found for subdomain: default
loadConfig @ SimplePreviewPage.jsx:156
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:626
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Supabase connection failed: No API key found in request
(anonymous) @ App.jsx:633
Promise.then
(anonymous) @ App.jsx:626
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ :5175/simple-preview…ncedPreview=true:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ :5175/simple-preview…ncedPreview=true:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
(anonymous) @ supabase.js:100
 Error fetching attorney subdomains: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
fetchSubdomains @ App.jsx:723
await in fetchSubdomains
(anonymous) @ App.jsx:735
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Supabase query error: No API key found in request
(anonymous) @ supabase.js:106
Promise.then
then @ @supabase_supabase-js.js:192
(anonymous) @ supabase.js:100
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ :5175/simple-preview…ncedPreview=true:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:624
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ❌ Supabase configuration verification failed: No API key found in request
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:624
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [AgentTab] Error loading assistant mappings: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ installHook.js:1
loadAvailableAssistants @ AgentTab.jsx:1252
await in loadAvailableAssistants
(anonymous) @ AgentTab.jsx:1285
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ :5175/simple-preview…ncedPreview=true:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 SimplePreviewPage: Error loading attorney data from Supabase: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:66
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Supabase error details: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:67
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: No attorney data found for subdomain: default
loadConfig @ SimplePreviewPage.jsx:156
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
VM1136 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
VM1136 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM1112 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM1112 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM1112 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM1112 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.695b5caf-4884-456d-a3b1-7765427b6095 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
VM980 consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ VM980 consolidated-dashboard-fix.js:206
window.fetch @ VM981 simple-preview:49
(anonymous) @ VM1171 @supabase_supabase-js.js:3900
(anonymous) @ VM1171 @supabase_supabase-js.js:3921
fulfilled @ VM1171 @supabase_supabase-js.js:3873
Promise.then
step @ VM1171 @supabase_supabase-js.js:3886
(anonymous) @ VM1171 @supabase_supabase-js.js:3888
__awaiter6 @ VM1171 @supabase_supabase-js.js:3870
(anonymous) @ VM1171 @supabase_supabase-js.js:3911
then @ VM1171 @supabase_supabase-js.js:89
VM1136 EnhancedPreviewNew.jsx:124 [EnhancedPreview] Component mounted and ready to receive messages
VM1136 EnhancedPreviewNew.jsx:125 [EnhancedPreview] Initial assistant ID: null
VM1136 EnhancedPreviewNew.jsx:322 EnhancedPreview: Sent ready message to parent
VM1136 EnhancedPreviewNew.jsx:369 [EnhancedPreviewNew] State updated:
VM1136 EnhancedPreviewNew.jsx:370 firmName: Your Law Firm
VM1136 EnhancedPreviewNew.jsx:371 titleText: Your Law Firm
VM1136 EnhancedPreviewNew.jsx:372 logoUrl: /PRIMARY CLEAR.png
VM1136 EnhancedPreviewNew.jsx:373 primaryColor: #4B74AA
VM1136 EnhancedPreviewNew.jsx:374 secondaryColor: #2C3E50
VM1136 EnhancedPreviewNew.jsx:375 vapiInstructions: 
VM1136 EnhancedPreviewNew.jsx:376 vapiAssistantId: null
VM1136 EnhancedPreviewNew.jsx:377 voiceId: sarah
VM1136 EnhancedPreviewNew.jsx:378 voiceProvider: playht
VM1136 EnhancedPreviewNew.jsx:379 chatActive: false
VM1136 EnhancedPreviewNew.jsx:124 [EnhancedPreview] Component mounted and ready to receive messages
VM1136 EnhancedPreviewNew.jsx:125 [EnhancedPreview] Initial assistant ID: null
VM1136 EnhancedPreviewNew.jsx:322 EnhancedPreview: Sent ready message to parent
VM1136 EnhancedPreviewNew.jsx:369 [EnhancedPreviewNew] State updated:
VM1136 EnhancedPreviewNew.jsx:370 firmName: Your Law Firm
VM1136 EnhancedPreviewNew.jsx:371 titleText: Your Law Firm
VM1136 EnhancedPreviewNew.jsx:372 logoUrl: /PRIMARY CLEAR.png
VM1136 EnhancedPreviewNew.jsx:373 primaryColor: #4B74AA
VM1136 EnhancedPreviewNew.jsx:374 secondaryColor: #2C3E50
VM1136 EnhancedPreviewNew.jsx:375 vapiInstructions: 
VM1136 EnhancedPreviewNew.jsx:376 vapiAssistantId: null
VM1136 EnhancedPreviewNew.jsx:377 voiceId: sarah
VM1136 EnhancedPreviewNew.jsx:378 voiceProvider: playht
VM1136 EnhancedPreviewNew.jsx:379 chatActive: false
VM1475 enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
VM1475 enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
VM1475 enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
VM1136 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
VM1136 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM1136 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
VM1136 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM1112 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM1112 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM1112 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [AgentTab] Error loading assistant mappings: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ installHook.js:1
loadAvailableAssistants @ AgentTab.jsx:1252
await in loadAvailableAssistants
(anonymous) @ AgentTab.jsx:1285
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:626
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Supabase connection failed: No API key found in request
(anonymous) @ App.jsx:633
Promise.then
(anonymous) @ App.jsx:626
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615699}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615699}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615699}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615699}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615699}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615699}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615699}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080615699}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
VM1112 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM979 consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ VM979 consolidated-dashboard-fix.js:206
window.fetch @ VM982 simple-preview:49
(anonymous) @ VM1261 @supabase_supabase-js.js:3900
(anonymous) @ VM1261 @supabase_supabase-js.js:3921
fulfilled @ VM1261 @supabase_supabase-js.js:3873
Promise.then
step @ VM1261 @supabase_supabase-js.js:3886
(anonymous) @ VM1261 @supabase_supabase-js.js:3888
__awaiter6 @ VM1261 @supabase_supabase-js.js:3870
(anonymous) @ VM1261 @supabase_supabase-js.js:3911
then @ VM1261 @supabase_supabase-js.js:89
VM1138 SimplePreviewPage.jsx:66 SimplePreviewPage: Error loading attorney data from Supabase: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ VM1138 SimplePreviewPage.jsx:66
await in loadAttorneyFromSupabase
loadConfig @ VM1138 SimplePreviewPage.jsx:132
(anonymous) @ VM1138 SimplePreviewPage.jsx:170
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ VM1064 chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ VM1064 chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ VM1064 chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19531
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM1138 SimplePreviewPage.jsx:67 SimplePreviewPage: Supabase error details: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ VM1138 SimplePreviewPage.jsx:67
await in loadAttorneyFromSupabase
loadConfig @ VM1138 SimplePreviewPage.jsx:132
(anonymous) @ VM1138 SimplePreviewPage.jsx:170
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ VM1064 chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ VM1064 chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ VM1064 chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19531
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM1138 SimplePreviewPage.jsx:156 SimplePreviewPage: No attorney data found for subdomain: default
loadConfig @ VM1138 SimplePreviewPage.jsx:156
await in loadConfig
(anonymous) @ VM1138 SimplePreviewPage.jsx:170
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ VM1064 chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ VM1064 chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ VM1064 chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19531
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM1138 SimplePreviewPage.jsx:161 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
VM1138 SimplePreviewPage.jsx:162 SimplePreviewPage: Setting config with firm name: Your Law Firm
VM1138 SimplePreviewPage.jsx:163 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
VM1138 SimplePreviewPage.jsx:167 SimplePreviewPage: Config updated from database, forcing re-render
VM979 consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ VM979 consolidated-dashboard-fix.js:206
window.fetch @ VM982 simple-preview:49
(anonymous) @ VM1261 @supabase_supabase-js.js:3900
(anonymous) @ VM1261 @supabase_supabase-js.js:3921
fulfilled @ VM1261 @supabase_supabase-js.js:3873
Promise.then
step @ VM1261 @supabase_supabase-js.js:3886
(anonymous) @ VM1261 @supabase_supabase-js.js:3888
__awaiter6 @ VM1261 @supabase_supabase-js.js:3870
(anonymous) @ VM1261 @supabase_supabase-js.js:3911
then @ VM1261 @supabase_supabase-js.js:89
VM1136 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
VM1136 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM1136 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
VM1136 EnhancedPreviewNew.jsx:418 Using default logo path: /PRIMARY CLEAR.png
VM1112 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM1112 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM1112 Button.jsx:220 Button component received mascot URL: /PRIMARY CLEAR.png
VM1112 Button.jsx:230 Using relative mascot path: /PRIMARY CLEAR.png
VM1149 supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ VM1149 supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ VM1149 supabaseConfigVerifier.js:141
(anonymous) @ VM1038 App.jsx:624
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ VM1064 chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ VM1064 chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ VM1064 chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ VM1064 chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19518
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM1149 supabaseConfigVerifier.js:145 ❌ Supabase configuration verification failed: No API key found in request
(anonymous) @ VM1149 supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ VM1149 supabaseConfigVerifier.js:141
(anonymous) @ VM1038 App.jsx:624
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ VM1064 chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ VM1064 chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ VM1064 chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ VM1064 chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19518
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM1476 fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
VM1476 fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
VM1476 fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
VM979 consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ VM979 consolidated-dashboard-fix.js:206
window.fetch @ VM982 simple-preview:49
(anonymous) @ VM1261 @supabase_supabase-js.js:3900
(anonymous) @ VM1261 @supabase_supabase-js.js:3921
fulfilled @ VM1261 @supabase_supabase-js.js:3873
Promise.then
step @ VM1261 @supabase_supabase-js.js:3886
(anonymous) @ VM1261 @supabase_supabase-js.js:3888
__awaiter6 @ VM1261 @supabase_supabase-js.js:3870
(anonymous) @ VM1261 @supabase_supabase-js.js:3911
then @ VM1261 @supabase_supabase-js.js:89
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
EnhancedVapiMcpService.js:149 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
vapiLogger.js:103 [23:43:35] [VapiMcpService] Retrieving assistant {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
VM1149 supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ VM1149 supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ VM1038 App.jsx:626
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ VM1064 chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ VM1064 chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ VM1064 chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ VM1064 chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19518
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM1038 App.jsx:633 Supabase connection failed: No API key found in request
(anonymous) @ VM1038 App.jsx:633
Promise.then
(anonymous) @ VM1038 App.jsx:626
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ VM1064 chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ VM1064 chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ VM1064 chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ VM1064 chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19518
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM979 consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ VM979 consolidated-dashboard-fix.js:206
window.fetch @ VM982 simple-preview:49
(anonymous) @ VM1261 @supabase_supabase-js.js:3900
(anonymous) @ VM1261 @supabase_supabase-js.js:3921
fulfilled @ VM1261 @supabase_supabase-js.js:3873
Promise.then
step @ VM1261 @supabase_supabase-js.js:3886
(anonymous) @ VM1261 @supabase_supabase-js.js:3888
__awaiter6 @ VM1261 @supabase_supabase-js.js:3870
(anonymous) @ VM1261 @supabase_supabase-js.js:3911
then @ VM1261 @supabase_supabase-js.js:89
VM1477 fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
VM1477 fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
VM1477 fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
VM1477 fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
VM1149 supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ VM1149 supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ VM1149 supabaseConfigVerifier.js:141
(anonymous) @ VM1038 App.jsx:624
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ VM1064 chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ VM1064 chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ VM1064 chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19531
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM1149 supabaseConfigVerifier.js:145 ❌ Supabase configuration verification failed: No API key found in request
(anonymous) @ VM1149 supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ VM1149 supabaseConfigVerifier.js:141
(anonymous) @ VM1038 App.jsx:624
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ VM1064 chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ VM1064 chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ VM1064 chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19531
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM1003 unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
VM1478 unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
VM1478 unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
VM1478 unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
VM1478 unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
VM1478 unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
VM1478 unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
VM1478 unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
VM1479 dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
VM1463 consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ VM1463 consolidated-dashboard-fix.js:25
setupIframeObserver @ VM1479 dashboard-iframe-manager.js:187
(anonymous) @ VM1479 dashboard-iframe-manager.js:242
(anonymous) @ VM1479 dashboard-iframe-manager.js:266
VM1479 dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
VM1479 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
VM1479 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
VM1479 dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ callback:99
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3900
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3921
fulfilled @ @supabase_supabase-js.js?v=fb518282:3873
Promise.then
step @ @supabase_supabase-js.js?v=fb518282:3886
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3888
__awaiter6 @ @supabase_supabase-js.js?v=fb518282:3870
(anonymous) @ @supabase_supabase-js.js?v=fb518282:3911
then @ @supabase_supabase-js.js?v=fb518282:89
VM979 consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ VM979 consolidated-dashboard-fix.js:206
window.fetch @ VM982 simple-preview:49
(anonymous) @ VM1261 @supabase_supabase-js.js:3900
(anonymous) @ VM1261 @supabase_supabase-js.js:3921
fulfilled @ VM1261 @supabase_supabase-js.js:3873
Promise.then
step @ VM1261 @supabase_supabase-js.js:3886
(anonymous) @ VM1261 @supabase_supabase-js.js:3888
__awaiter6 @ VM1261 @supabase_supabase-js.js:3870
(anonymous) @ VM1261 @supabase_supabase-js.js:3911
then @ VM1261 @supabase_supabase-js.js:89
robust-state-handler.js:117 ❌ [RobustStateHandler] Attempt 3 failed: Error: Error finding attorney: No API key found in request
    at findOrCreateAttorney (robust-state-handler.js:166:15)
    at async performStateResolution (VM1467 robust-state-handler.js:96:26)
overrideMethod @ hook.js:608
performStateResolution @ robust-state-handler.js:117
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
fetchAttorneyData @ DashboardNew.jsx:441
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
robust-state-handler.js:130 ❌ [RobustStateHandler] All retries failed for: <EMAIL>
overrideMethod @ hook.js:608
performStateResolution @ robust-state-handler.js:130
await in performStateResolution
window.resolveAttorneyState @ robust-state-handler.js:74
fetchAttorneyData @ DashboardNew.jsx:441
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:481
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:19356
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
VM1149 supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ VM1149 supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ VM1038 App.jsx:626
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ VM1064 chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ VM1064 chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ VM1064 chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19531
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM1038 App.jsx:633 Supabase connection failed: No API key found in request
(anonymous) @ VM1038 App.jsx:633
Promise.then
(anonymous) @ VM1038 App.jsx:626
commitHookEffectListMount @ VM1064 chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ VM1064 chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ VM1064 chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ VM1064 chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ VM1064 chunk-Q72EVS5P.js:19531
flushPassiveEffects @ VM1064 chunk-Q72EVS5P.js:19475
(anonymous) @ VM1064 chunk-Q72EVS5P.js:19356
workLoop @ VM1064 chunk-Q72EVS5P.js:197
flushWork @ VM1064 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM1064 chunk-Q72EVS5P.js:384
VM1004 unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
EnhancedVapiMcpService.js:149 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
vapiLogger.js:103 [23:43:35] [VapiMcpService] Retrieving assistant {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
vapiMcpDebugger.js:175 [Vapi MCP] GET https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
vapiLogger.js:103 [23:43:35] [VapiMcpService] Assistant verified in Vapi {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', name: 'LegalScout Assistant'}
AgentTab.jsx:524 🎵 [AgentTab] Found voice in Vapi assistant: {voiceId: 'sarah', provider: '11labs'}
DashboardNew.jsx:756 [DashboardNew] Cannot update attorney - attorney data is invalid or missing ID
overrideMethod @ hook.js:608
handlePreviewConfigUpdate @ DashboardNew.jsx:756
loadVapiVoiceSettings @ AgentTab.jsx:534
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:552
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=4e23eb9f:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=4e23eb9f:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19444
commitRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=4e23eb9f:9135
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:18655
AgentTab.jsx:539 ✅ [AgentTab] Voice settings loaded from Vapi: {voiceId: 'sarah', provider: '11labs'}
DashboardNew.jsx:666 Updated preview config: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] No preview iframes found to send config to Error Component Stack
    at DashboardNew (VM1124 DashboardNew.jsx:47:20)
    at RenderedRoute (VM1029 react-router-dom.js:5722:26)
    at Routes (VM1029 react-router-dom.js:6454:3)
    at main (<anonymous>)
    at div (<anonymous>)
    at App (VM1038 App.jsx:375:20)
    at LegalScoutApp (<anonymous>)
    at Provider (VM1118 AuthContext.jsx:27:16)
    at AuthProvider (VM1118 AuthContext.jsx:54:32)
    at InnerAuthProvider (VM1044 SyncAuthProvider.jsx:21:30)
    at Provider (VM1154 SyncContext.jsx:21:16)
    at SyncProvider (VM1154 SyncContext.jsx:33:32)
    at SyncAuthProvider (VM1044 SyncAuthProvider.jsx:34:29)
    at AttorneyStateProvider (VM1046 AttorneyStateContext.jsx:31:41)
    at Provider (VM1045 ThemeContext.jsx:20:16)
    at ThemeProvider (VM1045 ThemeContext.jsx:41:33)
    at Router (VM1029 react-router-dom.js:6397:13)
    at BrowserRouter (VM1029 react-router-dom.js:8631:3)
    at ErrorBoundary (VM1042 ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (VM1043 ProductionErrorBoundary.jsx:7:5)
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:107
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:669
basicStateReducer @ chunk-Q72EVS5P.js?v=4e23eb9f:11723
updateReducer @ chunk-Q72EVS5P.js?v=4e23eb9f:11814
updateState @ chunk-Q72EVS5P.js?v=4e23eb9f:12041
useState @ chunk-Q72EVS5P.js?v=4e23eb9f:12773
useState @ chunk-2N3A5BUM.js?v=4e23eb9f:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=4e23eb9f:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=4e23eb9f:14602
beginWork @ chunk-Q72EVS5P.js?v=4e23eb9f:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=4e23eb9f:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=4e23eb9f:19226
workLoopSync @ chunk-Q72EVS5P.js?v=4e23eb9f:19165
renderRootSync @ chunk-Q72EVS5P.js?v=4e23eb9f:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:18706
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:666 Updated preview config: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] No preview iframes found to send config to Error Component Stack
    at DashboardNew (VM1124 DashboardNew.jsx:47:20)
    at RenderedRoute (VM1029 react-router-dom.js:5722:26)
    at Routes (VM1029 react-router-dom.js:6454:3)
    at main (<anonymous>)
    at div (<anonymous>)
    at App (VM1038 App.jsx:375:20)
    at LegalScoutApp (<anonymous>)
    at Provider (VM1118 AuthContext.jsx:27:16)
    at AuthProvider (VM1118 AuthContext.jsx:54:32)
    at InnerAuthProvider (VM1044 SyncAuthProvider.jsx:21:30)
    at Provider (VM1154 SyncContext.jsx:21:16)
    at SyncProvider (VM1154 SyncContext.jsx:33:32)
    at SyncAuthProvider (VM1044 SyncAuthProvider.jsx:34:29)
    at AttorneyStateProvider (VM1046 AttorneyStateContext.jsx:31:41)
    at Provider (VM1045 ThemeContext.jsx:20:16)
    at ThemeProvider (VM1045 ThemeContext.jsx:41:33)
    at Router (VM1029 react-router-dom.js:6397:13)
    at BrowserRouter (VM1029 react-router-dom.js:8631:3)
    at ErrorBoundary (VM1042 ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (VM1043 ProductionErrorBoundary.jsx:7:5)
overrideMethod @ hook.js:600
log @ dashboard-iframe-manager.js:29
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:107
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:669
basicStateReducer @ chunk-Q72EVS5P.js?v=4e23eb9f:11723
updateReducer @ chunk-Q72EVS5P.js?v=4e23eb9f:11814
updateState @ chunk-Q72EVS5P.js?v=4e23eb9f:12041
useState @ chunk-Q72EVS5P.js?v=4e23eb9f:12773
useState @ chunk-2N3A5BUM.js?v=4e23eb9f:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=4e23eb9f:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=4e23eb9f:14607
beginWork @ chunk-Q72EVS5P.js?v=4e23eb9f:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=4e23eb9f:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=4e23eb9f:19226
workLoopSync @ chunk-Q72EVS5P.js?v=4e23eb9f:19165
renderRootSync @ chunk-Q72EVS5P.js?v=4e23eb9f:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:18706
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:774 [DashboardNew] Config sent to 0 iframes successfully
DashboardNew.jsx:774 [DashboardNew] Config sent to 0 iframes successfully
vapiMcpDebugger.js:180 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
vapiLogger.js:103 [23:43:36] [VapiMcpService] Assistant verified in Vapi {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', name: 'LegalScout Assistant'}
AgentTab.jsx:524 🎵 [AgentTab] Found voice in Vapi assistant: {voiceId: 'sarah', provider: '11labs'}
DashboardNew.jsx:756 [DashboardNew] Cannot update attorney - attorney data is invalid or missing ID
overrideMethod @ hook.js:608
handlePreviewConfigUpdate @ DashboardNew.jsx:756
loadVapiVoiceSettings @ AgentTab.jsx:534
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:552
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=4e23eb9f:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=4e23eb9f:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=4e23eb9f:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=4e23eb9f:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=4e23eb9f:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=4e23eb9f:19444
commitRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=4e23eb9f:9135
(anonymous) @ chunk-Q72EVS5P.js?v=4e23eb9f:18655
AgentTab.jsx:539 ✅ [AgentTab] Voice settings loaded from Vapi: {voiceId: 'sarah', provider: '11labs'}
DashboardNew.jsx:666 Updated preview config: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] No preview iframes found to send config to Error Component Stack
    at DashboardNew (VM1124 DashboardNew.jsx:47:20)
    at RenderedRoute (VM1029 react-router-dom.js:5722:26)
    at Routes (VM1029 react-router-dom.js:6454:3)
    at main (<anonymous>)
    at div (<anonymous>)
    at App (VM1038 App.jsx:375:20)
    at LegalScoutApp (<anonymous>)
    at Provider (VM1118 AuthContext.jsx:27:16)
    at AuthProvider (VM1118 AuthContext.jsx:54:32)
    at InnerAuthProvider (VM1044 SyncAuthProvider.jsx:21:30)
    at Provider (VM1154 SyncContext.jsx:21:16)
    at SyncProvider (VM1154 SyncContext.jsx:33:32)
    at SyncAuthProvider (VM1044 SyncAuthProvider.jsx:34:29)
    at AttorneyStateProvider (VM1046 AttorneyStateContext.jsx:31:41)
    at Provider (VM1045 ThemeContext.jsx:20:16)
    at ThemeProvider (VM1045 ThemeContext.jsx:41:33)
    at Router (VM1029 react-router-dom.js:6397:13)
    at BrowserRouter (VM1029 react-router-dom.js:8631:3)
    at ErrorBoundary (VM1042 ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (VM1043 ProductionErrorBoundary.jsx:7:5)
overrideMethod @ hook.js:608
log @ dashboard-iframe-manager.js:29
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:107
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:669
basicStateReducer @ chunk-Q72EVS5P.js?v=4e23eb9f:11723
updateReducer @ chunk-Q72EVS5P.js?v=4e23eb9f:11814
updateState @ chunk-Q72EVS5P.js?v=4e23eb9f:12041
useState @ chunk-Q72EVS5P.js?v=4e23eb9f:12773
useState @ chunk-2N3A5BUM.js?v=4e23eb9f:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=4e23eb9f:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=4e23eb9f:14602
beginWork @ chunk-Q72EVS5P.js?v=4e23eb9f:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=4e23eb9f:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=4e23eb9f:19226
workLoopSync @ chunk-Q72EVS5P.js?v=4e23eb9f:19165
renderRootSync @ chunk-Q72EVS5P.js?v=4e23eb9f:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:18706
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:666 Updated preview config: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] No preview iframes found to send config to Error Component Stack
    at DashboardNew (VM1124 DashboardNew.jsx:47:20)
    at RenderedRoute (VM1029 react-router-dom.js:5722:26)
    at Routes (VM1029 react-router-dom.js:6454:3)
    at main (<anonymous>)
    at div (<anonymous>)
    at App (VM1038 App.jsx:375:20)
    at LegalScoutApp (<anonymous>)
    at Provider (VM1118 AuthContext.jsx:27:16)
    at AuthProvider (VM1118 AuthContext.jsx:54:32)
    at InnerAuthProvider (VM1044 SyncAuthProvider.jsx:21:30)
    at Provider (VM1154 SyncContext.jsx:21:16)
    at SyncProvider (VM1154 SyncContext.jsx:33:32)
    at SyncAuthProvider (VM1044 SyncAuthProvider.jsx:34:29)
    at AttorneyStateProvider (VM1046 AttorneyStateContext.jsx:31:41)
    at Provider (VM1045 ThemeContext.jsx:20:16)
    at ThemeProvider (VM1045 ThemeContext.jsx:41:33)
    at Router (VM1029 react-router-dom.js:6397:13)
    at BrowserRouter (VM1029 react-router-dom.js:8631:3)
    at ErrorBoundary (VM1042 ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (VM1043 ProductionErrorBoundary.jsx:7:5)
overrideMethod @ hook.js:600
log @ dashboard-iframe-manager.js:29
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:107
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:771
(anonymous) @ DashboardNew.jsx:669
basicStateReducer @ chunk-Q72EVS5P.js?v=4e23eb9f:11723
updateReducer @ chunk-Q72EVS5P.js?v=4e23eb9f:11814
updateState @ chunk-Q72EVS5P.js?v=4e23eb9f:12041
useState @ chunk-Q72EVS5P.js?v=4e23eb9f:12773
useState @ chunk-2N3A5BUM.js?v=4e23eb9f:1066
DashboardNew @ DashboardNew.jsx:63
renderWithHooks @ chunk-Q72EVS5P.js?v=4e23eb9f:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=4e23eb9f:14607
beginWork @ chunk-Q72EVS5P.js?v=4e23eb9f:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=4e23eb9f:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=4e23eb9f:19226
workLoopSync @ chunk-Q72EVS5P.js?v=4e23eb9f:19165
renderRootSync @ chunk-Q72EVS5P.js?v=4e23eb9f:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=4e23eb9f:18706
workLoop @ chunk-Q72EVS5P.js?v=4e23eb9f:197
flushWork @ chunk-Q72EVS5P.js?v=4e23eb9f:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=4e23eb9f:384
DashboardNew.jsx:774 [DashboardNew] Config sent to 0 iframes successfully
DashboardNew.jsx:774 [DashboardNew] Config sent to 0 iframes successfully
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
ActiveCheckHelper.ts:8 updating page active status
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
