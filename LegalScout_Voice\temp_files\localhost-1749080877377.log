dashboard:21 ✅ Vapi public key set globally
dashboard:42 ✅ Supabase keys set globally - should load correct assistant by domain
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
dashboard:52 🚀 [EMERGENCY] Starting emergency critical fixes...
dashboard:56 🔧 [EMERGENCY] Adding process polyfill
dashboard:63 ✅ [EMERGENCY] Process polyfill added
dashboard:74 🔧 [EMERGENCY] Development mode: false (forced production)
dashboard:101 ✅ [EMERGENCY] Fetch patched
dashboard:104 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
dashboard:126 
            
            
           GET http://localhost:5176/disable-automatic-assistant-creation.js net::ERR_ABORTED 404 (Not Found)
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
overrideMethod @ hook.js:608
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
dashboard:209 Supabase loaded from CDN
dashboard:219 Creating Supabase client from CDN
dashboard:223 Supabase client created from CDN
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:60 Development mode detected, using fallback Supabase configuration
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:92 Supabase client initialized successfully with proper headers
supabase.js:95 Testing Supabase connection...
supabase.js:130 Supabase client ready for use
supabase.js:138 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
initAttorneyProfileManager.js:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js:42 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: true, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js:61 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js:233 [VapiMcpService] Production mode: false
vapiMcpService.js:234 [VapiMcpService] Development mode: true
vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Set'isDevelopmenttrueorigin'http://localhost:5176'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:91 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js:128 [initAttorneyProfileManager] Initialization complete
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 Attorney object in ProfileTab: null
 User object in ProfileTab: null
 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
 Using email from previewConfig or previous state: 
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Initializing hook with manager...
 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
 [useStandaloneAttorney] No attorney found anywhere
 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
 [DashboardNew] 🛡️ AUTOMATED robust state handler starting (UNCONDITIONAL)...
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: test-attorney
debugConfig.js:30 [App] Is attorney subdomain: true
App.jsx:787 🔍 [App.jsx] This is an attorney subdomain, loading profile
App.jsx:729 🚀 [App.jsx] Starting attorney profile load for subdomain: test-attorney
debugConfig.js:30 [App] 🚀 Starting attorney profile load for subdomain: test-attorney
App.jsx:733 📞 [App.jsx] Calling getAttorneyConfigAsync with subdomain: test-attorney
debugConfig.js:30 [App] 📞 Calling getAttorneyConfigAsync with subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 🚀 getAttorneyConfigAsync called {subdomain: 'test-attorney', currentPath: '/dashboard', hostname: 'localhost'}
attorneys.js:93 [AttorneyConfig] 🔍 Loading attorney config for subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 🔧 Normalized subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 🌐 Current hostname: localhost
attorneys.js:93 [AttorneyConfig] 🌐 Current pathname: /dashboard
attorneys.js:93 [AttorneyConfig] 📊 Querying Supabase with: {table: 'attorneys', field: 'subdomain', value: 'test-attorney', originalSubdomain: 'test-attorney'}
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: null
useStandaloneAttorney.js:25 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:77 [useStandaloneAttorney] Initializing hook with manager...
useStandaloneAttorney.js:84 [useStandaloneAttorney] Manager has no attorney, checking localStorage...
useStandaloneAttorney.js:119 [useStandaloneAttorney] No attorney found anywhere
DashboardNew.jsx:130 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
DashboardNew.jsx:174 [DashboardNew] 🛡️ AUTOMATED robust state handler starting (UNCONDITIONAL)...
DashboardNew.jsx:231 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:232 [DashboardNew] Dependencies: user?.id=undefined, authIsLoading=true, loadAttorneyForUser exists=true
DashboardNew.jsx:483 [DashboardNew] Waiting... (authIsLoading: true, userId: undefined, managerReady: true)
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
testSupabase.js:55 === SUPABASE CONFIG TEST ===
testSupabase.js:56 Supabase URL configured: true
testSupabase.js:57 Supabase Key configured: true
testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: test-attorney
debugConfig.js:30 [App] Is attorney subdomain: true
App.jsx:787 🔍 [App.jsx] This is an attorney subdomain, loading profile
App.jsx:729 🚀 [App.jsx] Starting attorney profile load for subdomain: test-attorney
debugConfig.js:30 [App] 🚀 Starting attorney profile load for subdomain: test-attorney
App.jsx:733 📞 [App.jsx] Calling getAttorneyConfigAsync with subdomain: test-attorney
debugConfig.js:30 [App] 📞 Calling getAttorneyConfigAsync with subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 🚀 getAttorneyConfigAsync called {subdomain: 'test-attorney', currentPath: '/dashboard', hostname: 'localhost'}
attorneys.js:93 [AttorneyConfig] 🔍 Loading attorney config for subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 🔧 Normalized subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 🌐 Current hostname: localhost
attorneys.js:93 [AttorneyConfig] 🌐 Current pathname: /dashboard
attorneys.js:93 [AttorneyConfig] 📊 Querying Supabase with: {table: 'attorneys', field: 'subdomain', value: 'test-attorney', originalSubdomain: 'test-attorney'}
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:455 Using real authentication in all environments
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
index.ts:5 Loaded contentScript
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
VM1789 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM1789 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM1789 consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM1789 consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
VM1789 consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM1789 consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
VM1789 consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
VM1789 consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
VM1789 consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
VM1789 consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
VM1790 consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
VM1790 consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
VM1790 consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
VM1790 consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
VM1790 consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
VM1790 consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
VM1790 consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
VM1790 consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
VM1790 consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
VM1790 consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:21 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
AuthContext.jsx:156 🔐 [AuthContext] No session found
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
(anonymous) @ supabase.js:100
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
unified-banner-fix.js:186 [UnifiedBannerFix] Banner state changed from visible to hidden, handling removal
unified-banner-fix.js:18 [UnifiedBannerFix] Banner removal initiated
supabase.js:106 Supabase query error: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ supabase.js:106
Promise.then
then @ @supabase_supabase-js.js?v=306847c5:192
(anonymous) @ supabase.js:100
AuthContext.jsx:156 🔐 [AuthContext] No session found
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:179 Auth state changed: INITIAL_SESSION
DashboardNew.jsx:184 [DashboardNew] 🛡️ No authenticated user found, skipping robust state handler
DashboardNew.jsx:184 [DashboardNew] 🛡️ No authenticated user found, skipping robust state handler
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
supabaseConfigVerifier.js:145 ❌ Supabase configuration verification failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.test-attorney 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.test-attorney 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
attorneys.js:93 [AttorneyConfig] 📊 Subdomain query results: {queryResults: null, error: 'No API key found in request', resultCount: 0, hasSupabase: true, supabaseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co'}
attorneys.js:94 [AttorneyConfig] ⚠️ No attorney found for subdomain: test-attorney
overrideMethod @ hook.js:608
warn @ attorneys.js:94
getAttorneyConfigAsync @ attorneys.js:195
await in getAttorneyConfigAsync
loadAttorneyProfile @ App.jsx:741
(anonymous) @ App.jsx:788
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] Supabase query result: {found: false, error: 'No API key found in request', firmName: undefined, assistantId: undefined, attorneyId: undefined, …}
attorneys.js:93 [AttorneyConfig] Attorney not found in database, trying JSON config 
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
App.jsx:688 Supabase connection failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
App.jsx:816 Error fetching attorney subdomains: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
overrideMethod @ hook.js:608
fetchSubdomains @ App.jsx:816
await in fetchSubdomains
(anonymous) @ App.jsx:832
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
supabaseConfigVerifier.js:145 ❌ Supabase configuration verification failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] 📊 Subdomain query results: {queryResults: null, error: 'No API key found in request', resultCount: 0, hasSupabase: true, supabaseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co'}
attorneys.js:94 [AttorneyConfig] ⚠️ No attorney found for subdomain: test-attorney
overrideMethod @ hook.js:608
warn @ attorneys.js:94
getAttorneyConfigAsync @ attorneys.js:195
await in getAttorneyConfigAsync
loadAttorneyProfile @ App.jsx:741
(anonymous) @ App.jsx:788
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] Supabase query result: {found: false, error: 'No API key found in request', firmName: undefined, assistantId: undefined, attorneyId: undefined, …}
attorneys.js:93 [AttorneyConfig] Attorney not found in database, trying JSON config 
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
overrideMethod @ hook.js:608
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
App.jsx:688 Supabase connection failed: No API key found in request
overrideMethod @ hook.js:608
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
DashboardNew.jsx:130 [DashboardNew] ⏳ Waiting for auth to complete before initializing auto-reconciler
attorneys.js:93 [AttorneyConfig] 🔄 Falling back to local config for subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 📦 Fallback config: {firmName: 'LegalScout', isFallback: true}
App.jsx:742 ✅ [App.jsx] Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
debugConfig.js:30 [App] ✅ Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
App.jsx:765 ✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true
debugConfig.js:30 [App] ✅ Valid profile found, keeping isAttorneySubdomain as true undefined
App.jsx:780 🏁 [App.jsx] Attorney profile loading complete
debugConfig.js:30 [App] 🏁 Attorney profile loading complete undefined
attorneys.js:93 [AttorneyConfig] 🔄 Falling back to local config for subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 📦 Fallback config: {firmName: 'LegalScout', isFallback: true}
App.jsx:742 ✅ [App.jsx] Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
debugConfig.js:30 [App] ✅ Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
App.jsx:765 ✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true
debugConfig.js:30 [App] ✅ Valid profile found, keeping isAttorneySubdomain as true undefined
App.jsx:780 🏁 [App.jsx] Attorney profile loading complete
debugConfig.js:30 [App] 🏁 Attorney profile loading complete undefined
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
DashboardNew.jsx:473 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:473
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
(anonymous) @ DashboardNew.jsx:471
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: null
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: null
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
ProfileTab.jsx:69 Email sources: {userEmail: undefined, userMetadataEmail: undefined, userIdentityEmail: undefined, attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:100 Using email from previewConfig or previous state: 
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
overrideMethod @ hook.js:608
(anonymous) @ robust-state-handler.js:34
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
robust-state-handler.js:38 🔄 [RobustStateHandler] Supabase available, trying fallback initialization...
supabase.js:95 Testing Supabase connection...
supabase.js:130 Supabase client ready for use
supabase.js:138 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
initAttorneyProfileManager.js:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js:42 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js:61 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js:233 [VapiMcpService] Production mode: false
vapiMcpService.js:234 [VapiMcpService] Development mode: true
vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:246 [VapiMcpService] API key being used: 310f0d43...
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Set'isDevelopmenttrueorigin'http://localhost:5176'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:91 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js:128 [initAttorneyProfileManager] Initialization complete
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
SimplePreviewPage.jsx:251 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
robust-state-handler.js:21 🔗 [RobustStateHandler] Dependencies ready, initializing...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833268}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 🔐 [AuthContext] No session found
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: test-attorney
 [App] Is attorney subdomain: true
 🔍 [App.jsx] This is an attorney subdomain, loading profile
 🚀 [App.jsx] Starting attorney profile load for subdomain: test-attorney
 [App] 🚀 Starting attorney profile load for subdomain: test-attorney
 📞 [App.jsx] Calling getAttorneyConfigAsync with subdomain: test-attorney
 [App] 📞 Calling getAttorneyConfigAsync with subdomain: test-attorney
 [AttorneyConfig] 🚀 getAttorneyConfigAsync called {subdomain: 'test-attorney', currentPath: '/simple-preview', hostname: 'localhost'}
 [AttorneyConfig] 🔍 Loading attorney config for subdomain: test-attorney
 [AttorneyConfig] 🔧 Normalized subdomain: test-attorney
 [AttorneyConfig] 🌐 Current hostname: localhost
 [AttorneyConfig] 🌐 Current pathname: /simple-preview
 [AttorneyConfig] 📊 Querying Supabase with: {table: 'attorneys', field: 'subdomain', value: 'test-attorney', originalSubdomain: 'test-attorney'}
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: test-attorney
 [App] Is attorney subdomain: true
 🔍 [App.jsx] This is an attorney subdomain, loading profile
 🚀 [App.jsx] Starting attorney profile load for subdomain: test-attorney
 [App] 🚀 Starting attorney profile load for subdomain: test-attorney
 📞 [App.jsx] Calling getAttorneyConfigAsync with subdomain: test-attorney
 [App] 📞 Calling getAttorneyConfigAsync with subdomain: test-attorney
 [AttorneyConfig] 🚀 getAttorneyConfigAsync called {subdomain: 'test-attorney', currentPath: '/simple-preview', hostname: 'localhost'}
 [AttorneyConfig] 🔍 Loading attorney config for subdomain: test-attorney
 [AttorneyConfig] 🔧 Normalized subdomain: test-attorney
 [AttorneyConfig] 🌐 Current hostname: localhost
 [AttorneyConfig] 🌐 Current pathname: /simple-preview
 [AttorneyConfig] 📊 Querying Supabase with: {table: 'attorneys', field: 'subdomain', value: 'test-attorney', originalSubdomain: 'test-attorney'}
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
(anonymous) @ supabase.js:100
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833396}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833396}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833619}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833619}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833622}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833622}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 Supabase query error: No API key found in request
(anonymous) @ supabase.js:106
Promise.then
then @ @supabase_supabase-js.js:192
(anonymous) @ supabase.js:100
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833631}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833631}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833632}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833632}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🔗 [RobustStateHandler] Dependencies ready, initializing...
 🚀 [RobustStateHandler] Starting robust state handling...
 ✅ [RobustStateHandler] Robust state handling initialized
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 🔐 [AuthContext] No session found
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 [UnifiedBannerFix] Ensuring upload interface is visible
 Auth state changed: INITIAL_SESSION
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833678}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833678}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833688}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833688}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 🔐 [AuthContext] No session found
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 SimplePreviewPage: Error loading attorney data from Supabase: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:66
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Supabase error details: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:67
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: No attorney data found for subdomain: default
loadConfig @ SimplePreviewPage.jsx:156
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:624
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ❌ Supabase configuration verification failed: No API key found in request
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:624
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔐 [AuthContext] No session found
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.test-attorney 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833770}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833770}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833770}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833772}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833772}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833772}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.test-attorney 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
attorneys.js:93 [AttorneyConfig] 📊 Subdomain query results: {queryResults: null, error: 'No API key found in request', resultCount: 0, hasSupabase: true, supabaseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co'}
attorneys.js:94 [AttorneyConfig] ⚠️ No attorney found for subdomain: test-attorney
warn @ attorneys.js:94
getAttorneyConfigAsync @ attorneys.js:195
await in getAttorneyConfigAsync
loadAttorneyProfile @ App.jsx:741
(anonymous) @ App.jsx:788
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] Supabase query result: {found: false, error: 'No API key found in request', firmName: undefined, assistantId: undefined, attorneyId: undefined, …}
attorneys.js:93 [AttorneyConfig] Attorney not found in database, trying JSON config 
SimplePreviewPage.jsx:70 SimplePreviewPage: Error loading attorney data from Supabase: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:70
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:159
(anonymous) @ SimplePreviewPage.jsx:214
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
SimplePreviewPage.jsx:71 SimplePreviewPage: Supabase error details: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:71
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:159
(anonymous) @ SimplePreviewPage.jsx:214
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
SimplePreviewPage.jsx:195 SimplePreviewPage: No attorney data found for subdomain: default
loadConfig @ SimplePreviewPage.jsx:195
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:214
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
App.jsx:688 Supabase connection failed: No API key found in request
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
AuthContext.jsx:179 Auth state changed: INITIAL_SESSION
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
(anonymous) @ supabase.js:100
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
App.jsx:816 Error fetching attorney subdomains: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
fetchSubdomains @ App.jsx:816
await in fetchSubdomains
(anonymous) @ App.jsx:832
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] 📊 Subdomain query results: {queryResults: null, error: 'No API key found in request', resultCount: 0, hasSupabase: true, supabaseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co'}
attorneys.js:94 [AttorneyConfig] ⚠️ No attorney found for subdomain: test-attorney
warn @ attorneys.js:94
getAttorneyConfigAsync @ attorneys.js:195
await in getAttorneyConfigAsync
loadAttorneyProfile @ App.jsx:741
(anonymous) @ App.jsx:788
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] Supabase query result: {found: false, error: 'No API key found in request', firmName: undefined, assistantId: undefined, attorneyId: undefined, …}
attorneys.js:93 [AttorneyConfig] Attorney not found in database, trying JSON config 
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
supabase.js:106 Supabase query error: No API key found in request
(anonymous) @ supabase.js:106
Promise.then
then @ @supabase_supabase-js.js?v=306847c5:192
(anonymous) @ supabase.js:100
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
supabaseConfigVerifier.js:145 ❌ Supabase configuration verification failed: No API key found in request
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
App.jsx:688 Supabase connection failed: No API key found in request
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] 🔄 Falling back to local config for subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 📦 Fallback config: {firmName: 'LegalScout', isFallback: true}
App.jsx:742 ✅ [App.jsx] Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
debugConfig.js:30 [App] ✅ Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
App.jsx:765 ✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true
debugConfig.js:30 [App] ✅ Valid profile found, keeping isAttorneySubdomain as true undefined
App.jsx:780 🏁 [App.jsx] Attorney profile loading complete
debugConfig.js:30 [App] 🏁 Attorney profile loading complete undefined
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [AttorneyConfig] 🔄 Falling back to local config for subdomain: test-attorney
 [AttorneyConfig] 📦 Fallback config: {firmName: 'LegalScout', isFallback: true}
 ✅ [App.jsx] Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
 [App] ✅ Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
 ✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true
 [App] ✅ Valid profile found, keeping isAttorneySubdomain as true undefined
 🏁 [App.jsx] Attorney profile loading complete
 [App] 🏁 Attorney profile loading complete undefined
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.test-attorney 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:624
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ❌ Supabase configuration verification failed: No API key found in request
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:624
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Error loading attorney data from Supabase: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:66
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Supabase error details: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:67
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: No attorney data found for subdomain: default
loadConfig @ SimplePreviewPage.jsx:156
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:626
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Supabase connection failed: No API key found in request
(anonymous) @ App.jsx:633
Promise.then
(anonymous) @ App.jsx:626
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Error fetching attorney subdomains: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
fetchSubdomains @ App.jsx:723
await in fetchSubdomains
(anonymous) @ App.jsx:735
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [AttorneyConfig] 📊 Subdomain query results: {queryResults: null, error: 'No API key found in request', resultCount: 0, hasSupabase: true, supabaseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co'}
 [AttorneyConfig] ⚠️ No attorney found for subdomain: test-attorney
warn @ attorneys.js:94
getAttorneyConfigAsync @ attorneys.js:195
await in getAttorneyConfigAsync
loadAttorneyProfile @ App.jsx:665
(anonymous) @ App.jsx:706
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [AttorneyConfig] Supabase query result: {found: false, error: 'No API key found in request', firmName: undefined, assistantId: undefined, attorneyId: undefined, …}
 [AttorneyConfig] Attorney not found in database, trying JSON config 
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 SimplePreviewPage: Error loading attorney data from Supabase: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:66
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Supabase error details: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:67
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: No attorney data found for subdomain: default
loadConfig @ SimplePreviewPage.jsx:156
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.test-attorney 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [AttorneyConfig] 🔄 Falling back to local config for subdomain: test-attorney
 [AttorneyConfig] 📦 Fallback config: {firmName: 'LegalScout', isFallback: true}
 ✅ [App.jsx] Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
 [App] ✅ Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
 ✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true
 [App] ✅ Valid profile found, keeping isAttorneySubdomain as true undefined
 🏁 [App.jsx] Attorney profile loading complete
 [App] 🏁 Attorney profile loading complete undefined
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833934}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833934}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833934}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833934}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833936}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833936}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833936}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080833936}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
attorneys.js:93 [AttorneyConfig] 📊 Subdomain query results: {queryResults: null, error: 'No API key found in request', resultCount: 0, hasSupabase: true, supabaseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co'}
attorneys.js:94 [AttorneyConfig] ⚠️ No attorney found for subdomain: test-attorney
warn @ attorneys.js:94
getAttorneyConfigAsync @ attorneys.js:195
await in getAttorneyConfigAsync
loadAttorneyProfile @ App.jsx:741
(anonymous) @ App.jsx:788
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] Supabase query result: {found: false, error: 'No API key found in request', firmName: undefined, assistantId: undefined, attorneyId: undefined, …}
attorneys.js:93 [AttorneyConfig] Attorney not found in database, trying JSON config 
attorneys.js:93 [AttorneyConfig] 🔄 Falling back to local config for subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 📦 Fallback config: {firmName: 'LegalScout', isFallback: true}
App.jsx:742 ✅ [App.jsx] Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
debugConfig.js:30 [App] ✅ Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
App.jsx:765 ✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true
debugConfig.js:30 [App] ✅ Valid profile found, keeping isAttorneySubdomain as true undefined
App.jsx:780 🏁 [App.jsx] Attorney profile loading complete
debugConfig.js:30 [App] 🏁 Attorney profile loading complete undefined
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
supabaseConfigVerifier.js:145 ❌ Supabase configuration verification failed: No API key found in request
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
App.jsx:688 Supabase connection failed: No API key found in request
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080834084}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080834084}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080834084}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080834084}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080834096}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080834096}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080834096}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080834096}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
(anonymous) @ robust-state-handler.js:34
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
robust-state-handler.js:38 🔄 [RobustStateHandler] Supabase available, trying fallback initialization...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
(anonymous) @ robust-state-handler.js:34
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
robust-state-handler.js:38 🔄 [RobustStateHandler] Supabase available, trying fallback initialization...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
ActiveCheckHelper.ts:21 received intentional event
DashboardNew.jsx:624 [DashboardNew] Tab changed to: agent
AgentTab.jsx:506 [AgentTab] No Vapi assistant ID found, skipping voice sync
AgentTab.jsx:506 [AgentTab] No Vapi assistant ID found, skipping voice sync
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:21 ✅ Vapi public key set globally
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:42 ✅ Supabase keys set globally - should load correct assistant by domain
consolidated-dashboard-fix.js:9 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
consolidated-dashboard-fix.js:19 [ConsolidatedDashboardFix] Fixing DOM manipulation...
consolidated-dashboard-fix.js:66 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
consolidated-dashboard-fix.js:99 [ConsolidatedDashboardFix] Fixing CORS issues...
consolidated-dashboard-fix.js:218 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
consolidated-dashboard-fix.js:237 [ConsolidatedDashboardFix] Fixing banner issues...
consolidated-dashboard-fix.js:271 [ConsolidatedDashboardFix] Fixing React context issues...
consolidated-dashboard-fix.js:311 [ConsolidatedDashboardFix] Fixing CSP issues...
consolidated-dashboard-fix.js:332 [ConsolidatedDashboardFix] Ensuring environment variables...
consolidated-dashboard-fix.js:358 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:52 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:56 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:63 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:74 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:101 ✅ [EMERGENCY] Fetch patched
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:104 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:13 🛡️ [RobustStateHandler] Initializing comprehensive state management...
simple-preview:126 
            
            
           GET http://localhost:5176/disable-automatic-assistant-creation.js net::ERR_ABORTED 404 (Not Found)
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
consolidated-dashboard-fix.js:25 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:209 Supabase loaded from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:219 Creating Supabase client from CDN
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:223 Supabase client created from CDN
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
robust-state-handler.js:24 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:60 Development mode detected, using fallback Supabase configuration
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:92 Supabase client initialized successfully with proper headers
supabase.js:95 Testing Supabase connection...
supabase.js:130 Supabase client ready for use
supabase.js:138 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Set'isDevelopmenttrueorigin'http://localhost:5176'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Initialization complete
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 updating page active status
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: test-attorney
 [App] Is attorney subdomain: true
 🔍 [App.jsx] This is an attorney subdomain, loading profile
 🚀 [App.jsx] Starting attorney profile load for subdomain: test-attorney
 [App] 🚀 Starting attorney profile load for subdomain: test-attorney
 📞 [App.jsx] Calling getAttorneyConfigAsync with subdomain: test-attorney
 [App] 📞 Calling getAttorneyConfigAsync with subdomain: test-attorney
 [AttorneyConfig] 🚀 getAttorneyConfigAsync called {subdomain: 'test-attorney', currentPath: '/simple-preview', hostname: 'localhost'}
 [AttorneyConfig] 🔍 Loading attorney config for subdomain: test-attorney
 [AttorneyConfig] 🔧 Normalized subdomain: test-attorney
 [AttorneyConfig] 🌐 Current hostname: localhost
 [AttorneyConfig] 🌐 Current pathname: /simple-preview
 [AttorneyConfig] 📊 Querying Supabase with: {table: 'attorneys', field: 'subdomain', value: 'test-attorney', originalSubdomain: 'test-attorney'}
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: test-attorney
 [App] Is attorney subdomain: true
 🔍 [App.jsx] This is an attorney subdomain, loading profile
 🚀 [App.jsx] Starting attorney profile load for subdomain: test-attorney
 [App] 🚀 Starting attorney profile load for subdomain: test-attorney
 📞 [App.jsx] Calling getAttorneyConfigAsync with subdomain: test-attorney
 [App] 📞 Calling getAttorneyConfigAsync with subdomain: test-attorney
 [AttorneyConfig] 🚀 getAttorneyConfigAsync called {subdomain: 'test-attorney', currentPath: '/simple-preview', hostname: 'localhost'}
 [AttorneyConfig] 🔍 Loading attorney config for subdomain: test-attorney
 [AttorneyConfig] 🔧 Normalized subdomain: test-attorney
 [AttorneyConfig] 🌐 Current hostname: localhost
 [AttorneyConfig] 🌐 Current pathname: /simple-preview
 [AttorneyConfig] 📊 Querying Supabase with: {table: 'attorneys', field: 'subdomain', value: 'test-attorney', originalSubdomain: 'test-attorney'}
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message
 🔗 [RobustStateHandler] Dependencies ready, initializing...
 🚀 [RobustStateHandler] Starting robust state handling...
 ✅ [RobustStateHandler] Robust state handling initialized
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858446}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858446}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858446}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858446}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858446}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858450}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858450}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858450}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858450}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858450}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858533}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858533}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858533}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858533}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858533}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858537}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858537}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858537}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858537}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858537}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🔐 [AuthContext] No session found
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
(anonymous) @ supabase.js:100
 Supabase query error: No API key found in request
(anonymous) @ supabase.js:106
Promise.then
then @ @supabase_supabase-js.js:192
(anonymous) @ supabase.js:100
 🔐 [AuthContext] No session found
 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
 Auth state changed: INITIAL_SESSION
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 SimplePreviewPage: Error loading attorney data from Supabase: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:66
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Supabase error details: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:67
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:132
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: No attorney data found for subdomain: default
loadConfig @ SimplePreviewPage.jsx:156
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:170
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: /PRIMARY CLEAR.png
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.test-attorney 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858678}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858678}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858678}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858678}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858678}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858678}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858683}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858683}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858683}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858683}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858683}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858683}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:624
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 ❌ Supabase configuration verification failed: No API key found in request
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:624
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [AttorneyConfig] 📊 Subdomain query results: {queryResults: null, error: 'No API key found in request', resultCount: 0, hasSupabase: true, supabaseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co'}
 [AttorneyConfig] ⚠️ No attorney found for subdomain: test-attorney
warn @ attorneys.js:94
getAttorneyConfigAsync @ attorneys.js:195
await in getAttorneyConfigAsync
loadAttorneyProfile @ App.jsx:665
(anonymous) @ App.jsx:706
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [AttorneyConfig] Supabase query result: {found: false, error: 'No API key found in request', firmName: undefined, assistantId: undefined, attorneyId: undefined, …}
 [AttorneyConfig] Attorney not found in database, trying JSON config 
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview:99
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.test-attorney 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:70 SimplePreviewPage: Error loading attorney data from Supabase: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:70
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:159
(anonymous) @ SimplePreviewPage.jsx:214
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
SimplePreviewPage.jsx:71 SimplePreviewPage: Supabase error details: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
loadAttorneyFromSupabase @ SimplePreviewPage.jsx:71
await in loadAttorneyFromSupabase
loadConfig @ SimplePreviewPage.jsx:159
(anonymous) @ SimplePreviewPage.jsx:214
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
SimplePreviewPage.jsx:195 SimplePreviewPage: No attorney data found for subdomain: default
loadConfig @ SimplePreviewPage.jsx:195
await in loadConfig
(anonymous) @ SimplePreviewPage.jsx:214
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: Your Law Firm
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
App.jsx:816 Error fetching attorney subdomains: {message: 'No API key found in request', hint: 'No `apikey` request header or url param was found.'}
fetchSubdomains @ App.jsx:816
await in fetchSubdomains
(anonymous) @ App.jsx:832
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
App.jsx:688 Supabase connection failed: No API key found in request
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] 📊 Subdomain query results: {queryResults: null, error: 'No API key found in request', resultCount: 0, hasSupabase: true, supabaseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co'}
attorneys.js:94 [AttorneyConfig] ⚠️ No attorney found for subdomain: test-attorney
warn @ attorneys.js:94
getAttorneyConfigAsync @ attorneys.js:195
await in getAttorneyConfigAsync
loadAttorneyProfile @ App.jsx:741
(anonymous) @ App.jsx:788
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] Supabase query result: {found: false, error: 'No API key found in request', firmName: undefined, assistantId: undefined, attorneyId: undefined, …}
attorneys.js:93 [AttorneyConfig] Attorney not found in database, trying JSON config 
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
supabaseConfigVerifier.js:145 ❌ Supabase configuration verification failed: No API key found in request
(anonymous) @ supabaseConfigVerifier.js:145
Promise.then
initializeSupabaseConfig @ supabaseConfigVerifier.js:141
(anonymous) @ App.jsx:677
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
consolidated-dashboard-fix.js:206 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:99
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3900
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3921
fulfilled @ @supabase_supabase-js.js?v=306847c5:3873
Promise.then
step @ @supabase_supabase-js.js?v=306847c5:3886
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3888
__awaiter6 @ @supabase_supabase-js.js?v=306847c5:3870
(anonymous) @ @supabase_supabase-js.js?v=306847c5:3911
then @ @supabase_supabase-js.js?v=306847c5:89
supabaseConfigVerifier.js:83 ❌ Supabase connection test failed: No API key found in request
verifySupabaseConfig @ supabaseConfigVerifier.js:83
await in verifySupabaseConfig
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
App.jsx:688 Supabase connection failed: No API key found in request
(anonymous) @ App.jsx:688
Promise.then
(anonymous) @ App.jsx:681
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
attorneys.js:93 [AttorneyConfig] 🔄 Falling back to local config for subdomain: test-attorney
attorneys.js:93 [AttorneyConfig] 📦 Fallback config: {firmName: 'LegalScout', isFallback: true}
App.jsx:742 ✅ [App.jsx] Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
debugConfig.js:30 [App] ✅ Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
App.jsx:765 ✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true
debugConfig.js:30 [App] ✅ Valid profile found, keeping isAttorneySubdomain as true undefined
App.jsx:780 🏁 [App.jsx] Attorney profile loading complete
debugConfig.js:30 [App] 🏁 Attorney profile loading complete undefined
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
 [AttorneyConfig] 🔄 Falling back to local config for subdomain: test-attorney
 [AttorneyConfig] 📦 Fallback config: {firmName: 'LegalScout', isFallback: true}
 ✅ [App.jsx] Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
 [App] ✅ Attorney profile loaded successfully: {hasProfile: true, firmName: 'LegalScout', id: undefined, subdomain: undefined, isFallback: true}
 ✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true
 [App] ✅ Valid profile found, keeping isAttorneySubdomain as true undefined
 🏁 [App.jsx] Attorney profile loading complete
 [App] 🏁 Attorney profile loading complete undefined
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858901}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858901}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858901}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858901}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858901}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858901}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 [EnhancedPreview] Assistant ID in customizations: undefined
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858937}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858937}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858937}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858937}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858937}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749080858937}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Your Law Firm', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', practiceDescription: 'Your AI legal assistant is ready to help', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: undefined
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: undefined, vapi_assistant_id: undefined, assistantId: undefined}
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, informationGathering, logoUrl, mascot, theme
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5176/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
AgentTab.jsx:1789 [AgentTab] Assistant dropdown changed to: create_new
AgentTab.jsx:1842 [AgentTab] Error creating new assistant: TypeError: Cannot read properties of null (reading 'firm_name')
    at handleCreateNewAssistant (AgentTab.jsx:1812:36)
    at async handleAssistantDropdownChange (AgentTab.jsx:1793:7)
overrideMethod @ hook.js:608
handleCreateNewAssistant @ AgentTab.jsx:1842
await in handleCreateNewAssistant
handleAssistantDropdownChange @ AgentTab.jsx:1793
callCallback2 @ chunk-Q72EVS5P.js?v=306847c5:3674
invokeGuardedCallbackDev @ chunk-Q72EVS5P.js?v=306847c5:3699
invokeGuardedCallback @ chunk-Q72EVS5P.js?v=306847c5:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-Q72EVS5P.js?v=306847c5:3736
executeDispatch @ chunk-Q72EVS5P.js?v=306847c5:7016
processDispatchQueueItemsInOrder @ chunk-Q72EVS5P.js?v=306847c5:7036
processDispatchQueue @ chunk-Q72EVS5P.js?v=306847c5:7045
dispatchEventsForPlugins @ chunk-Q72EVS5P.js?v=306847c5:7053
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:7177
batchedUpdates$1 @ chunk-Q72EVS5P.js?v=306847c5:18941
batchedUpdates @ chunk-Q72EVS5P.js?v=306847c5:3579
dispatchEventForPluginEventSystem @ chunk-Q72EVS5P.js?v=306847c5:7176
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-Q72EVS5P.js?v=306847c5:5478
dispatchEvent @ chunk-Q72EVS5P.js?v=306847c5:5472
dispatchDiscreteEvent @ chunk-Q72EVS5P.js?v=306847c5:5449
handleMouseUp_ @ unknown
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
robust-state-handler.js:34 [RobustStateHandler] Timed out waiting for dependencies, trying fallback...
(anonymous) @ robust-state-handler.js:34
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
robustStateHandler @ robust-state-handler.js:32
(anonymous) @ robust-state-handler.js:650
robust-state-handler.js:38 🔄 [RobustStateHandler] Supabase available, trying fallback initialization...
robust-state-handler.js:46 🚀 [RobustStateHandler] Starting robust state handling...
robust-state-handler.js:648 ✅ [RobustStateHandler] Robust state handling initialized
