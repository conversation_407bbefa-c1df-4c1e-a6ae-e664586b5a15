// Find and navigate to the right tabs
(function() {
  'use strict';

  console.log('🔄 TAB NAVIGATION & FIELD DISCOVERY');
  console.log('='.repeat(40));

  // Check current tab context
  const activeTabs = document.querySelectorAll('.config-tab.active, .tab.active, .active');
  console.log('📋 Currently active tabs:');
  activeTabs.forEach((tab, index) => {
    console.log(`${index + 1}. "${tab.textContent?.trim()}" (${tab.className})`);
  });

  // Find all available tabs
  const allTabs = document.querySelectorAll('.config-tab, button[title], .tab-button');
  console.log('\n🎯 Available tabs to click:');
  allTabs.forEach((tab, index) => {
    const text = tab.textContent?.trim() || tab.title || tab.getAttribute('aria-label');
    const isActive = tab.classList.contains('active');
    console.log(`${index + 1}. "${text}" ${isActive ? '(ACTIVE)' : ''}`);
  });

  // Function to switch tabs and find fields
  window.exploreAllTabs = function() {
    console.log('\n🔍 EXPLORING ALL TABS FOR FIELDS...');
    
    const tabs = document.querySelectorAll('.config-tab, button[title]');
    let tabIndex = 0;
    
    function exploreNextTab() {
      if (tabIndex >= tabs.length) {
        console.log('✅ Finished exploring all tabs');
        return;
      }
      
      const tab = tabs[tabIndex];
      const tabName = tab.textContent?.trim() || tab.title;
      
      console.log(`\n📂 Clicking tab: "${tabName}"`);
      tab.click();
      
      // Wait a bit for content to load, then check for fields
      setTimeout(() => {
        console.log(`🔍 Fields in "${tabName}" tab:`);
        
        // Look for practice area fields
        const practiceFields = document.querySelectorAll('select[id*="practice"], select[name*="practice"], select[id*="Practice"]');
        if (practiceFields.length > 0) {
          console.log('  🎯 PRACTICE AREA FIELDS FOUND:');
          practiceFields.forEach((field, i) => {
            console.log(`    ${i + 1}. ID: ${field.id}, Name: ${field.name}, Value: "${field.value}"`);
          });
        }
        
        // Look for template/schema fields
        const templateFields = document.querySelectorAll('textarea[id*="schema"], textarea[id*="prompt"], textarea[name*="schema"], textarea[name*="prompt"]');
        if (templateFields.length > 0) {
          console.log('  📝 TEMPLATE FIELDS FOUND:');
          templateFields.forEach((field, i) => {
            console.log(`    ${i + 1}. ID: ${field.id}, Name: ${field.name}, Length: ${field.value?.length || 0}`);
          });
        }
        
        // Look for any interesting fields
        const allFields = document.querySelectorAll('input, textarea, select');
        const interestingFields = Array.from(allFields).filter(field => {
          const id = field.id?.toLowerCase() || '';
          const name = field.name?.toLowerCase() || '';
          return id.includes('practice') || id.includes('template') || id.includes('schema') || 
                 id.includes('prompt') || name.includes('practice') || name.includes('template') ||
                 name.includes('schema') || name.includes('prompt');
        });
        
        if (interestingFields.length > 0) {
          console.log('  🎯 INTERESTING FIELDS:');
          interestingFields.forEach((field, i) => {
            console.log(`    ${i + 1}. ${field.tagName} - ID: ${field.id}, Name: ${field.name}`);
          });
        }
        
        tabIndex++;
        setTimeout(exploreNextTab, 1000); // Wait 1 second before next tab
      }, 500);
    }
    
    exploreNextTab();
  };

  // Quick check of current visible fields
  console.log('\n📋 CURRENT VISIBLE FIELDS:');
  const visibleFields = document.querySelectorAll('input:not([style*="display: none"]), textarea:not([style*="display: none"]), select:not([style*="display: none"])');
  visibleFields.forEach((field, index) => {
    if (field.offsetParent !== null) { // Actually visible
      const id = field.id || 'no-id';
      const name = field.name || 'no-name';
      const type = field.type || field.tagName.toLowerCase();
      console.log(`${index + 1}. ${type.toUpperCase()} - ID: ${id}, Name: ${name}`);
    }
  });

  console.log('\n🎯 Run window.exploreAllTabs() to find all fields!');

})();
VM7857:5 🔄 TAB NAVIGATION & FIELD DISCOVERY
VM7857:6 ========================================
VM7857:10 📋 Currently active tabs:
VM7857:12 1. "" (sidebar-pin active)
VM7857:12 2. "Agent" (active)
VM7857:12 3. "" (config-tab active)
VM7857:17 
🎯 Available tabs to click:
VM7857:21 1. "Collapse sidebar" 
VM7857:21 2. "Unpin sidebar" (ACTIVE)
VM7857:21 3. "LinkedIn" 
VM7857:21 4. "Facebook" 
VM7857:21 5. "Twitter" 
VM7857:21 6. "Email" 
VM7857:21 7. "SMS" 
VM7857:21 8. "Instructions" (ACTIVE)
VM7857:21 9. "Text" 
VM7857:21 10. "Appearance" 
VM7857:21 11. "Voice" 
VM7857:21 12. "Show Diagnostics" 
VM7857:21 13. "Refresh Assistant Data" 
VM7857:21 14. "Refresh" 
VM7857:21 15. "Sync Assistant" 
VM7857:91 
📋 CURRENT VISIBLE FIELDS:
VM7857:98 1. SELECT-ONE - ID: assistant-select, Name: no-name
VM7857:98 2. TEXTAREA - ID: vapiInstructions, Name: vapiInstructions
VM7857:98 3. TEXTAREA - ID: welcomeMessage, Name: welcomeMessage
VM7857:98 4. TEXTAREA - ID: vapiContext, Name: vapiContext
VM7857:102 
🎯 Run window.exploreAllTabs() to find all fields!
undefined
// Quick way to find the practice area and template fields
window.exploreAllTabs();
VM7857:26 
🔍 EXPLORING ALL TABS FOR FIELDS...
VM7857:40 
📂 Clicking tab: "Collapse sidebar"
undefined
VM7857:45 🔍 Fields in "Collapse sidebar" tab:
VM7857:40 
📂 Clicking tab: "Unpin sidebar"
VM7857:45 🔍 Fields in "Unpin sidebar" tab:
VM7857:40 
📂 Clicking tab: "LinkedIn"
VM7857:45 🔍 Fields in "LinkedIn" tab:
VM7857:40 
📂 Clicking tab: "Facebook"
VM7857:45 🔍 Fields in "Facebook" tab:
VM7857:40 
📂 Clicking tab: "Twitter"
VM7857:45 🔍 Fields in "Twitter" tab:
VM7857:40 
📂 Clicking tab: "Email"
VM7857:45 🔍 Fields in "Email" tab:
VM7857:40 
📂 Clicking tab: "SMS"
VM7857:45 🔍 Fields in "SMS" tab:
VM7857:40 
📂 Clicking tab: "Instructions"
VM7857:45 🔍 Fields in "Instructions" tab:
VM7857:40 
📂 Clicking tab: "Text"
VM7857:45 🔍 Fields in "Text" tab:
VM7857:50   🎯 PRACTICE AREA FIELDS FOUND:
VM7857:52     1. ID: practiceAreaSelect, Name: , Value: ""
VM7857:76   🎯 INTERESTING FIELDS:
VM7857:78     1. SELECT - ID: practiceAreaSelect, Name: 
VM7857:78     2. TEXTAREA - ID: practiceDescription, Name: practiceDescription
VM7857:40 
📂 Clicking tab: "Appearance"
VM7857:45 🔍 Fields in "Appearance" tab:
VM7857:76   🎯 INTERESTING FIELDS:
VM7857:78     1. INPUT - ID: practiceAreaBackgroundOpacity, Name: practiceAreaBackgroundOpacity
VM7857:40 
📂 Clicking tab: "Voice"
VM7857:45 🔍 Fields in "Voice" tab:
VM7857:40 
📂 Clicking tab: "Show Diagnostics"
VM7857:45 🔍 Fields in "Show Diagnostics" tab:
VM7857:40 
📂 Clicking tab: "Refresh Assistant Data"
VM7857:45 🔍 Fields in "Refresh Assistant Data" tab:
VM7857:40 
📂 Clicking tab: "Refresh"
 Refreshing preview iframe
 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
 🚨 [CriticalProductionFix] Starting critical production fixes...
 ✅ Vapi keys set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
 🎉 [EMERGENCY] Emergency fixes complete!
 🛡️ [RobustStateHandler] Initializing comprehensive state management...
 🛡️ [RobustStateHandler] Initialization attempt 1/3
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
production-cors-fix.js:11 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
client.ts:19 [vite] connecting...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:235 Supabase loaded from CDN
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:245 Creating Supabase client from CDN
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:249 Supabase client created from CDN
client.ts:155 [vite] connected.
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM7631 SyncContext.jsx:136:24)
    at async Object.callback (VM7602 AuthContext.jsx:164:34)
    at async VM7678 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM7678 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM7678 @supabase_supabase-js.js:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM7631 SyncContext.jsx:136:24)
    at async Object.callback (VM7602 AuthContext.jsx:164:34)
    at async VM7678 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM7678 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM7678 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM7631 SyncContext.jsx:136:24)
    at async Object.callback (VM7602 AuthContext.jsx:164:34)
    at async VM7678 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM7678 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM7678 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
VM7857:45 🔍 Fields in "Refresh" tab:
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:60 Development mode detected, using fallback Supabase configuration
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 Supabase client initialized successfully with proper headers
supabase.js:102 Testing Supabase connection...
supabase.js:137 Supabase client ready for use
supabase.js:145 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Setting up Realtime subscription for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Realtime subscription set up using channel API
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 [VapiMcpService] Using direct API key for server operations: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] 🚫 Disabled by clean auth solution
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5174'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Found attorney in localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [initAttorneyProfileManager] Initialization complete
 🔑 [EmergencyApiKeyFix] Setting global environment variables...
 ✅ [EmergencyApiKeyFix] Global environment variables set
 🔧 [EmergencyApiKeyFix] Creating API key helper function...
 ✅ [EmergencyApiKeyFix] API key helper function created
 🌐 [EmergencyApiKeyFix] Overriding fetch for correct API key usage...
 ✅ [EmergencyApiKeyFix] Fetch override applied
 🔧 [EmergencyApiKeyFix] Fixing existing service instances...
 [EmergencyApiKeyFix] Updating VapiMcpService API key...
 ✅ [EmergencyApiKeyFix] Existing services updated
 🎉 [EmergencyApiKeyFix] Emergency API key fix complete!
 🚀 [CriticalProductionFix] Initializing all critical fixes...
 🔑 [CriticalProductionFix] Fixing Vapi API key configuration...
 ✅ [CriticalProductionFix] Vapi API key configuration fixed
 🔧 [CriticalProductionFix] Fixing environment variables...
 ✅ [CriticalProductionFix] Environment variables fixed
 🌐 [CriticalProductionFix] Fixing CORS issues...
 ✅ [CriticalProductionFix] CORS issues fixed with enhanced fetch override
 🛡️ [CriticalProductionFix] Fixing CSP issues...
 ✅ [CriticalProductionFix] CSP issues fixed
 📦 [CriticalProductionFix] Fixing import statement issues...
 ✅ [CriticalProductionFix] Import statement issues fixed
 🎉 [CriticalProductionFix] All critical fixes applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [ProductionCorsFix] 🚀 Initializing all production fixes...
 [ProductionCorsFix] 🌍 Ensuring production environment variables...
 [ProductionCorsFix] ✅ Environment variables configured
 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
 [ProductionCorsFix] ✅ API endpoint fixes applied
 [ProductionCorsFix] 🛡️ Fixing CSP issues...
 [ProductionCorsFix] ✅ CSP issues addressed
 [ProductionCorsFix] 🚨 Enhancing error handling...
 [ProductionCorsFix] ✅ Enhanced error handling installed
 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
 [ProductionCorsFix] ✅ Direct API mode configured
 [ProductionCorsFix] 🎉 All production fixes initialized successfully
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: true, supabaseHasFrom: true}
 
📂 Clicking tab: "Sync Assistant"
 🔄 [DashboardNew] Force syncing attorney to Vapi...
 🔄 [DashboardNew] Attorney ID: 87756a2c-a398-43f2-889a-b8815684df71
 🔄 [DashboardNew] Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damon
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: damon
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 🔐 [AuthContext] Starting auth initialization...
 Using real authentication in all environments
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 [DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750537}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750537}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750537}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750537}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750537}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750549}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750549}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750549}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750549}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750549}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750554}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750554}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750554}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750554}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235750554}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 Auth state error details: Empty response from server
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🔧 [EnhancedSyncHelpers] Updating assistant with instructions: {assistantId: 'cd0b44b7...', instructionsLength: 1507, instructionsPreview: 'You are Scout, the AI legal assistant for LegalSco…You help potential clients understand their le...'}
 🔧 [EnhancedVapiMcpService] Sending PATCH request with payload: {
  "name": "LegalScout",
  "firstMessage": "yooo swassuuup damon",
  "firstMessageMode": "assistant-speaks-first",
  "model": {
    "provider": "openai",
    "model": "gpt-4o",
    "messages": [
      {
        "role": "system",
        "content": "You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.\n\n[Legal Consultation Process]\n1. Greet warmly and ask how you can help with their legal matter\n2. Listen to their legal concern and ask clarifying questions\n3. Collect relevant case details, timeline, and contact information\n4. Provide general legal guidance (not specific legal advice)\n5. Explain next steps for connecting with an attorney\n\n[Information to Collect]\n- Client contact information (name, email, phone)\n- Nature of legal issue and practice area\n- Timeline and urgency of the matter\n- Key facts and circumstances\n- Previous legal action taken\n- Desired outcome or goals\n- Budget considerations if appropriate\n\n[Communication Guidelines]\n- Be professional, empathetic, and helpful\n- Ask one question at a time to avoid overwhelming clients\n- Clarify complex legal terms in simple language\n- Never provide specific legal advice - only general information\n- Always emphasize the importance of consulting with a qualified attorney\n\n[Completion]\nConclude with: \"Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon.\"\n\nNote: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney."
      }
    ]
  },
  "voice": {
    "provider": "openai",
    "voiceId": "echo"
  },
  "transcriber": {
    "provider": "deepgram",
    "model": "nova-3"
  },
  "backgroundSound": "off",
  "backgroundDenoisingEnabled": false,
  "silenceTimeoutSeconds": 30,
  "maxDurationSeconds": 600,
  "clientMessages": [
    "conversation-update",
    "function-call",
    "hang",
    "model-output",
    "speech-update",
    "status-update",
    "transcript",
    "tool-calls",
    "user-interrupted",
    "voice-input"
  ]
}
 🔧 [EnhancedVapiMcpService] Instructions field: {hasInstructions: false, instructionsLength: 0, instructionsPreview: 'undefined...'}
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [CriticalProductionFix] Using SECRET key for server operations: server
 [CriticalProductionFix] SECRET key value: 6734febc...
 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EmergencyApiKeyFix] Using SECRET key for assistant creation/update
 [EmergencyApiKeyFix] Using API key: 6734febc...
 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 🔐 [AuthContext] Found OAuth email: <EMAIL>
 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 🔐 [AuthContext] Found OAuth email: <EMAIL>
 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:179 Auth state changed: INITIAL_SESSION
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:19356
workLoop @ chunk-Q72EVS5P.js?v=306847c5:197
flushWork @ chunk-Q72EVS5P.js?v=306847c5:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=306847c5:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
supabase.js:116 Supabase connection test successful!
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
debugConfig.js:30 [App] Available subdomains for testing: (4) ['damon', 'damonkost', 'generalcounselonline', 'scout']
SimplePreviewPage.jsx:80 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: 'damon', firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'yooo swassuuup damon', practice_description: ''}
SimplePreviewPage.jsx:89 SimplePreviewPage: Complete attorney data from database: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T18:24:45.488936+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
SimplePreviewPage.jsx:163 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
SimplePreviewPage.jsx:168 SimplePreviewPage: Mapped preview config: {firmName: 'LegalScout', titleText: 'LegalScout', welcomeMessage: 'yooo swassuuup damon', practiceDescription: 'Your AI legal assistant is ready to help'}
SimplePreviewPage.jsx:187 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:188 SimplePreviewPage: Config after database merge: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:189 SimplePreviewPage: Key fields from database: {firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'yooo swassuuup damon'}
SimplePreviewPage.jsx:201 SimplePreviewPage: Final config: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
SimplePreviewPage.jsx:202 SimplePreviewPage: Setting config with firm name: LegalScout
SimplePreviewPage.jsx:203 SimplePreviewPage: Setting config with welcome message: yooo swassuuup damon
SimplePreviewPage.jsx:210 SimplePreviewPage: Config updated from database, forcing re-render
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: LegalScout
EnhancedPreviewNew.jsx:462 titleText: LegalScout
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Legal Consultation Process]
1. Greet warmly and ask how you can help with their legal matter
2. Listen to their legal concern and ask clarifying questions
3. Collect relevant case details, timeline, and contact information
4. Provide general legal guidance (not specific legal advice)
5. Explain next steps for connecting with an attorney

[Information to Collect]
- Client contact information (name, email, phone)
- Nature of legal issue and practice area
- Timeline and urgency of the matter
- Key facts and circumstances
- Previous legal action taken
- Desired outcome or goals
- Budget considerations if appropriate

[Communication Guidelines]
- Be professional, empathetic, and helpful
- Ask one question at a time to avoid overwhelming clients
- Clarify complex legal terms in simple language
- Never provide specific legal advice - only general information
- Always emphasize the importance of consulting with a qualified attorney

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.
EnhancedPreviewNew.jsx:467 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
EnhancedPreviewNew.jsx:139 [EnhancedPreview] Component mounted and ready to receive messages
EnhancedPreviewNew.jsx:140 [EnhancedPreview] Initial assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:400 EnhancedPreview: Sent ready message to parent
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: LegalScout
EnhancedPreviewNew.jsx:462 titleText: LegalScout
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #4B74AA
EnhancedPreviewNew.jsx:465 secondaryColor: #2C3E50
EnhancedPreviewNew.jsx:466 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Legal Consultation Process]
1. Greet warmly and ask how you can help with their legal matter
2. Listen to their legal concern and ask clarifying questions
3. Collect relevant case details, timeline, and contact information
4. Provide general legal guidance (not specific legal advice)
5. Explain next steps for connecting with an attorney

[Information to Collect]
- Client contact information (name, email, phone)
- Nature of legal issue and practice area
- Timeline and urgency of the matter
- Key facts and circumstances
- Previous legal action taken
- Desired outcome or goals
- Budget considerations if appropriate

[Communication Guidelines]
- Be professional, empathetic, and helpful
- Ask one question at a time to avoid overwhelming clients
- Clarify complex legal terms in simple language
- Never provide specific legal advice - only general information
- Always emphasize the importance of consulting with a qualified attorney

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.
EnhancedPreviewNew.jsx:467 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: playht
EnhancedPreviewNew.jsx:470 chatActive: false
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received PREVIEW_READY message from iframe
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 3 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: 'damon', firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'yooo swassuuup damon', practice_description: ''}
 SimplePreviewPage: Complete attorney data from database: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T18:24:45.488936+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'LegalScout', titleText: 'LegalScout', welcomeMessage: 'yooo swassuuup damon', practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: 'LegalScout', title_text: 'LegalScout', welcome_message: 'yooo swassuuup damon'}
 SimplePreviewPage: Final config: {firmName: 'LegalScout', titleText: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: LegalScout
 SimplePreviewPage: Setting config with welcome message: yooo swassuuup damon
 SimplePreviewPage: Config updated from database, forcing re-render
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751011}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751011}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751011}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751011}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751011}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751011}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751015}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751015}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751015}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751015}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751015}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751015}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 🔗 [RobustStateHandler] Dependencies ready, initializing...
 🚀 [RobustStateHandler] Starting robust state handling...
 ✅ [RobustStateHandler] Robust state handling initialized
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Unified banner fix initialized successfully
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🔍 Fields in "Sync Assistant" tab:
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [ProductionCorsFix] 🧪 Testing API connectivity...
 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [CriticalProductionFix] Using SECRET key for server operations: server
 [CriticalProductionFix] SECRET key value: 6734febc...
 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
 [EmergencyApiKeyFix] Using API key: 6734febc...
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Legal Consultation Process]
1. Greet warmly and ask how you can help with their legal matter
2. Listen to their legal concern and ask clarifying questions
3. Collect relevant case details, timeline, and contact information
4. Provide general legal guidance (not specific legal advice)
5. Explain next steps for connecting with an attorney

[Information to Collect]
- Client contact information (name, email, phone)
- Nature of legal issue and practice area
- Timeline and urgency of the matter
- Key facts and circumstances
- Previous legal action taken
- Desired outcome or goals
- Budget considerations if appropriate

[Communication Guidelines]
- Be professional, empathetic, and helpful
- Ask one question at a time to avoid overwhelming clients
- Clarify complex legal terms in simple language
- Never provide specific legal advice - only general information
- Always emphasize the importance of consulting with a qualified attorney

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.
 vapiAssistantId: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 voiceId: echo
 voiceProvider: openai
 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751119}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751119}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751119}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751119}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751119}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751119}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751123}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751123}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751123}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751123}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751123}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751123}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751126}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751126}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751126}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751126}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751126}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751126}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751130}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751130}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751130}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751130}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751130}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749235751130}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 3 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
clean-auth-solution.js:191 ✅ [CleanAuthSolution] Test passed - Supabase working correctly
production-cors-fix.js:167 [ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed
DashboardNew.jsx:905 ✅ [DashboardNew] Sync completed: {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout', voice: {…}, createdAt: '2025-06-06T17:03:54.051Z', …}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:137 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:169 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AttorneyProfileManager.js:385 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
VM7857:33 ✅ Finished exploring all tabs
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=306847c5:6204:9)
    at async @supabase_supabase-js.js?v=306847c5:6455:11
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 Auth state error details: Empty response from server
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=306847c5:6204:9)
    at async @supabase_supabase-js.js?v=306847c5:6455:11
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=306847c5:6204:9)
    at async @supabase_supabase-js.js?v=306847c5:6455:11
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=306847c5:6204:9)
    at async @supabase_supabase-js.js?v=306847c5:6455:11
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=306847c5:6204
await in _recoverAndRefresh
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6455
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=306847c5:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=306847c5:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=306847c5:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AttorneyProfileManager.js:57 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
AttorneyProfileManager.js:1407 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:71 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
