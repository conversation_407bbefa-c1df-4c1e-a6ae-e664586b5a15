// FIX SCRIPT: Fix Sidebar Visibility in Briefs Tab
window.fixSidebarInBriefs = function() {
  console.log('🔧 FIX SCRIPT: Fix Sidebar Visibility in Briefs Tab');
  console.log('='.repeat(50));
  
  // Step 1: Check current layout state
  console.log('📋 STEP 1: Current Layout State...');
  
  const dashboardContainer = document.querySelector('.dashboard-container');
  const sidebar = document.querySelector('.dashboard-sidebar');
  const mainContent = document.querySelector('.dashboard-main');
  const tabContent = document.querySelector('.tab-content');
  
  console.log('Layout Elements:');
  console.log('- Dashboard container classes:', dashboardContainer?.className || 'NOT FOUND');
  console.log('- Sidebar classes:', sidebar?.className || 'NOT FOUND');
  console.log('- Main content classes:', mainContent?.className || 'NOT FOUND');
  console.log('- Tab content classes:', tabContent?.className || 'NOT FOUND');
  
  // Check computed styles
  if (sidebar) {
    const sidebarStyles = window.getComputedStyle(sidebar);
    console.log('\nSidebar Computed Styles:');
    console.log('- Display:', sidebarStyles.display);
    console.log('- Visibility:', sidebarStyles.visibility);
    console.log('- Opacity:', sidebarStyles.opacity);
    console.log('- Position:', sidebarStyles.position);
    console.log('- Left:', sidebarStyles.left);
    console.log('- Width:', sidebarStyles.width);
    console.log('- Z-index:', sidebarStyles.zIndex);
    console.log('- Transform:', sidebarStyles.transform);
  }
  
  // Step 2: Check viewport and responsive state
  console.log('\n📋 STEP 2: Viewport and Responsive State...');
  console.log('- Window width:', window.innerWidth);
  console.log('- Window height:', window.innerHeight);
  console.log('- Is mobile breakpoint (<768px):', window.innerWidth < 768);
  console.log('- Is tablet breakpoint (<1024px):', window.innerWidth < 1024);
  
  // Step 3: Navigate to Briefs and check what happens
  console.log('\n📋 STEP 3: Testing Briefs Navigation...');
  
  const briefsTab = Array.from(document.querySelectorAll('button')).find(btn => 
    btn.textContent?.trim() === 'Briefs'
  );
  
  if (briefsTab) {
    // First navigate to Profile to reset state
    const profileTab = Array.from(document.querySelectorAll('button')).find(btn => 
      btn.textContent?.trim() === 'Profile'
    );
    
    if (profileTab) {
      profileTab.click();
      console.log('✅ Clicked Profile tab (reset)');
      
      setTimeout(() => {
        // Check sidebar state after Profile
        const sidebarAfterProfile = document.querySelector('.dashboard-sidebar');
        if (sidebarAfterProfile) {
          const profileStyles = window.getComputedStyle(sidebarAfterProfile);
          console.log('\nSidebar state after Profile:');
          console.log('- Display:', profileStyles.display);
          console.log('- Visibility:', profileStyles.visibility);
          console.log('- Left:', profileStyles.left);
        }
        
        // Now click Briefs
        briefsTab.click();
        console.log('✅ Clicked Briefs tab');
        
        setTimeout(() => {
          // Check sidebar state after Briefs
          const sidebarAfterBriefs = document.querySelector('.dashboard-sidebar');
          if (sidebarAfterBriefs) {
            const briefsStyles = window.getComputedStyle(sidebarAfterBriefs);
            console.log('\nSidebar state after Briefs:');
            console.log('- Display:', briefsStyles.display);
            console.log('- Visibility:', briefsStyles.visibility);
            console.log('- Left:', briefsStyles.left);
            console.log('- Classes:', sidebarAfterBriefs.className);
            
            // Check if sidebar is actually hidden
            const isHidden = briefsStyles.display === 'none' || 
                           briefsStyles.visibility === 'hidden' || 
                           briefsStyles.opacity === '0' ||
                           parseInt(briefsStyles.left) < -100;
            
            console.log('- Is sidebar hidden?', isHidden);
            
            if (isHidden) {
              console.log('\n🔧 APPLYING FIXES...');
              
              // Fix 1: Remove any problematic classes
              sidebarAfterBriefs.classList.remove('hidden', 'collapsed');
              console.log('✅ Removed hidden/collapsed classes');
              
              // Fix 2: Force display
              sidebarAfterBriefs.style.display = 'flex';
              sidebarAfterBriefs.style.visibility = 'visible';
              sidebarAfterBriefs.style.opacity = '1';
              console.log('✅ Forced sidebar visibility');
              
              // Fix 3: Check for mobile state issues
              if (window.innerWidth < 768) {
                console.log('📱 Mobile viewport detected');
                sidebarAfterBriefs.style.position = 'fixed';
                sidebarAfterBriefs.style.left = '0';
                sidebarAfterBriefs.style.zIndex = '998';
                console.log('✅ Applied mobile sidebar fixes');
              } else {
                console.log('🖥️ Desktop viewport detected');
                sidebarAfterBriefs.style.position = 'relative';
                sidebarAfterBriefs.style.left = 'auto';
                console.log('✅ Applied desktop sidebar fixes');
              }
              
              // Fix 4: Ensure main content doesn't overlap
              const mainAfterFix = document.querySelector('.dashboard-main');
              if (mainAfterFix && window.innerWidth >= 768) {
                mainAfterFix.style.marginLeft = '204px'; // Standard sidebar width
                console.log('✅ Adjusted main content margin');
              }
              
            } else {
              console.log('✅ Sidebar is visible - no fixes needed');
            }
          }
          
          // Step 4: Test navigation between tabs
          console.log('\n📋 STEP 4: Testing Tab Navigation...');
          
          setTimeout(() => {
            // Test Agent tab
            const agentTab = Array.from(document.querySelectorAll('button')).find(btn => 
              btn.textContent?.trim() === 'Agent'
            );
            
            if (agentTab) {
              agentTab.click();
              console.log('✅ Tested Agent tab navigation');
              
              setTimeout(() => {
                // Go back to Briefs
                briefsTab.click();
                console.log('✅ Returned to Briefs tab');
                
                // Final check
                const finalSidebar = document.querySelector('.dashboard-sidebar');
                if (finalSidebar) {
                  const finalStyles = window.getComputedStyle(finalSidebar);
                  console.log('\n📊 FINAL SIDEBAR STATE:');
                  console.log('- Display:', finalStyles.display);
                  console.log('- Visibility:', finalStyles.visibility);
                  console.log('- Left:', finalStyles.left);
                  
                  const stillHidden = finalStyles.display === 'none' || 
                                   finalStyles.visibility === 'hidden' || 
                                   parseInt(finalStyles.left) < -100;
                  
                  if (stillHidden) {
                    console.log('⚠️ Sidebar still hidden after fixes');
                    console.log('💡 This may require a CSS rule update');
                  } else {
                    console.log('🎉 Sidebar is now visible!');
                  }
                }
                
              }, 1000);
            }
          }, 1000);
          
        }, 1000);
      }, 500);
    }
  } else {
    console.log('❌ Briefs tab not found');
  }
};

// Additional helper function to permanently fix the CSS
window.addSidebarFix = function() {
  console.log('🔧 Adding permanent sidebar fix...');
  
  // Create a style element to override problematic CSS
  const style = document.createElement('style');
  style.id = 'sidebar-fix';
  style.textContent = `
    /* Ensure sidebar is always visible */
    .dashboard-sidebar {
      display: flex !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
    
    /* Fix for mobile */
    @media (max-width: 767px) {
      .dashboard-sidebar {
        position: fixed !important;
        left: 0 !important;
        z-index: 998 !important;
      }
    }
    
    /* Fix for desktop */
    @media (min-width: 768px) {
      .dashboard-sidebar {
        position: relative !important;
        left: auto !important;
      }
      
      .dashboard-main {
        margin-left: 204px !important;
      }
    }
  `;
  
  // Remove existing fix if present
  const existingFix = document.getElementById('sidebar-fix');
  if (existingFix) {
    existingFix.remove();
  }
  
  // Add the fix
  document.head.appendChild(style);
  console.log('✅ Permanent sidebar fix applied');
};

// Run the diagnostic
window.fixSidebarInBriefs();
VM3613:3 🔧 FIX SCRIPT: Fix Sidebar Visibility in Briefs Tab
VM3613:4 ==================================================
VM3613:7 📋 STEP 1: Current Layout State...
VM3613:14 Layout Elements:
VM3613:15 - Dashboard container classes: dashboard-container dark 
VM3613:16 - Sidebar classes: dashboard-sidebar  pinned 
VM3613:17 - Main content classes: dashboard-main 
VM3613:18 - Tab content classes: tab-content  full-width
VM3613:23 
Sidebar Computed Styles:
VM3613:24 - Display: block
VM3613:25 - Visibility: visible
VM3613:26 - Opacity: 1
VM3613:27 - Position: relative
VM3613:28 - Left: 0px
VM3613:29 - Width: 1.0989px
VM3613:30 - Z-index: auto
VM3613:31 - Transform: none
VM3613:35 
📋 STEP 2: Viewport and Responsive State...
VM3613:36 - Window width: 1806
VM3613:37 - Window height: 1354
VM3613:38 - Is mobile breakpoint (<768px): false
VM3613:39 - Is tablet breakpoint (<1024px): false
VM3613:42 
📋 STEP 3: Testing Briefs Navigation...
DashboardNew.jsx:624 [DashboardNew] Tab changed to: profile
VM3613:56 ✅ Clicked Profile tab (reset)
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T19:56:58.998Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:53 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T19:56:58.998Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
undefined
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: '<EMAIL>', previewConfigEmail: undefined}
ProfileTab.jsx:80 Updated form data with attorney email from database: <EMAIL>
VM3613:63 
Sidebar state after Profile:
VM3613:64 - Display: block
VM3613:65 - Visibility: visible
VM3613:66 - Left: 0px
DashboardNew.jsx:624 [DashboardNew] Tab changed to: consultations
VM3613:71 ✅ Clicked Briefs tab
ConsultationsTab.jsx:79 Fetching consultations for attorney: 87756a2c-a398-43f2-889a-b8815684df71
ConsultationsTab.jsx:79 Fetching consultations for attorney: 87756a2c-a398-43f2-889a-b8815684df71
VM3613:78 
Sidebar state after Briefs:
VM3613:79 - Display: block
VM3613:80 - Visibility: visible
VM3613:81 - Left: 0px
VM3613:82 - Classes: dashboard-sidebar  pinned 
VM3613:90 - Is sidebar hidden? false
VM3613:127 ✅ Sidebar is visible - no fixes needed
VM3613:132 
📋 STEP 4: Testing Tab Navigation...
AuthContext.jsx:179 Auth state changed: TOKEN_REFRESHED
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: TOKEN_REFRESHED
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: TOKEN_REFRESHED
clean-auth-solution.js:135 🧹 [CleanAuthSolution] Token refreshed successfully
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: TOKEN_REFRESHED
clean-auth-solution.js:135 🧹 [CleanAuthSolution] Token refreshed successfully
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: TOKEN_REFRESHED
clean-auth-solution.js:135 🧹 [CleanAuthSolution] Token refreshed successfully
AuthContext.jsx:179 Auth state changed: TOKEN_REFRESHED
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: TOKEN_REFRESHED
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: TOKEN_REFRESHED
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: TOKEN_REFRESHED
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_callRefreshToken @ @supabase_supabase-js.js?v=306847c5:6232
await in _callRefreshToken
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6399
_useSession @ @supabase_supabase-js.js?v=306847c5:5575
await in _useSession
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6390
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_callRefreshToken @ @supabase_supabase-js.js?v=306847c5:6232
await in _callRefreshToken
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6399
_useSession @ @supabase_supabase-js.js?v=306847c5:5575
await in _useSession
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6390
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM3298 SyncContext.jsx:136:24)
    at async Object.callback (VM3268 AuthContext.jsx:164:34)
    at async VM3335 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM3335 @supabase_supabase-js.js:6268:7)
    at async SupabaseAuthClient._callRefreshToken (VM3335 @supabase_supabase-js.js:6232:7)
    at async VM3335 @supabase_supabase-js.js:6399:17
    at async SupabaseAuthClient._useSession (VM3335 @supabase_supabase-js.js:5575:14)
    at async VM3335 @supabase_supabase-js.js:6390:20
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
_callRefreshToken @ @supabase_supabase-js.js?v=306847c5:6232
await in _callRefreshToken
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6399
_useSession @ @supabase_supabase-js.js?v=306847c5:5575
await in _useSession
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6390
(anonymous) @ @supabase_supabase-js.js?v=306847c5:5542
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4828
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM3298 SyncContext.jsx:136:24)
    at async Object.callback (VM3268 AuthContext.jsx:164:34)
    at async VM3335 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM3335 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM3335 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM3298 SyncContext.jsx:136:24)
    at async Object.callback (VM3268 AuthContext.jsx:164:34)
    at async VM3335 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM3335 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM3335 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
DashboardNew.jsx:624 [DashboardNew] Tab changed to: agent
VM3613:142 ✅ Tested Agent tab navigation
AgentTab.jsx:487 [AgentTab] Loading ALL settings from Vapi assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
AgentTab.jsx:581 [AgentTab] No logo found in attorney data
AgentTab.jsx:1825 [AgentTab] Loading assistants using Vapi MCP Server for attorney: 87756a2c-a398-43f2-889a-b8815684df71
AgentTab.jsx:487 [AgentTab] Loading ALL settings from Vapi assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
AgentTab.jsx:581 [AgentTab] No logo found in attorney data
AgentTab.jsx:1825 [AgentTab] Loading assistants using Vapi MCP Server for attorney: 87756a2c-a398-43f2-889a-b8815684df71
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 1 new preview iframes in added content
dashboard-iframe-manager.js:29 [DashboardIframeManager] Handling new iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe already ready
vapiMcpService.js:405 [VapiMcpService] Getting assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:409 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:424 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 → http://localhost:5174/api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:548 [VapiMcpService] Listing assistants
vapiMcpService.js:552 [VapiMcpService] Using direct API to list assistants
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant → http://localhost:5174/api/vapi-proxy/assistant
vapiMcpService.js:405 [VapiMcpService] Getting assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:409 [VapiMcpService] Using direct API to get assistant
vapiMcpService.js:424 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2 → http://localhost:5174/api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
vapiMcpService.js:548 [VapiMcpService] Listing assistants
vapiMcpService.js:552 [VapiMcpService] Using direct API to list assistants
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant → http://localhost:5174/api/vapi-proxy/assistant
vapiLogger.js:103 [19:57:56] [VapiMcpService] Using fast loading mode for production-cors-fix - direct API to avoid CORS issues 
EnhancedVapiMcpService.js:194 [EnhancedVapiMcpService] Initializing StreamableHTTP MCP client...
vapiLogger.js:103 [19:57:56] [VapiMcpService] Using fast loading mode for production-cors-fix - direct API to avoid CORS issues 
EnhancedVapiMcpService.js:194 [EnhancedVapiMcpService] Initializing StreamableHTTP MCP client...
VM3633 emergency-api-key-fix.js:11 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
VM3636 critical-production-fix.js:15 🚨 [CriticalProductionFix] Starting critical production fixes...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:32 ✅ Vapi keys set globally
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:53 ✅ Supabase keys set globally - should load correct assistant by domain
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:64 🚀 [EMERGENCY] Starting emergency critical fixes...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:68 🔧 [EMERGENCY] Adding process polyfill
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:75 ✅ [EMERGENCY] Process polyfill added
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:86 🔧 [EMERGENCY] Development mode: false (forced production)
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:116 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:119 🎉 [EMERGENCY] Emergency fixes complete!
VM3639 robust-state-handler.js:19 🛡️ [RobustStateHandler] Initializing comprehensive state management...
VM3639 robust-state-handler.js:38 🛡️ [RobustStateHandler] Initialization attempt 1/3
VM3640 disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
VM3640 disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
VM3640 disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
VM3640 disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
VM3641 controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
VM3641 controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
VM3642 standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
VM3642 standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
VM3642 standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
VM3642 standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: default
VM3643 fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
VM3643 fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
VM3643 fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
vapiMcpService.js:436 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
VM3644 fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
VM3644 fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
VM3644 fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
AgentTab.jsx:1831 [AgentTab] Retrieved assistants from Vapi: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
AgentTab.jsx:1843 [AgentTab] Found attorney's assigned assistant: LegalScout
AgentTab.jsx:1864 [AgentTab] Final assistant list: [{…}]
VM3645 enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
VM3645 enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
VM3645 enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
VM3646 fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
VM3646 fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
VM3646 fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
VM3647 fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
VM3647 fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
VM3647 fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
VM3647 fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
VM3648 unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
VM3648 unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
VM3648 unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
VM3648 unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
VM3648 unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
VM3648 unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
VM3652 dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
VM3652 dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
VM3652 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
VM3652 dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
VM3652 dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
VM3653 production-cors-fix.js:11 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
VM3654 clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
VM3654 clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
VM3654 clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
VM3654 clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
VM3654 clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
VM3654 clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
VM3654 clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
VM3654 clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
VM3654 clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
VM3654 clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
VM3654 clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
VM3626 client:229 [vite] connecting...
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
VM3626 client:325 [vite] connected.
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'http://localhost:5174' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:114 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
send @ @modelcontextprotocol_sdk_client_streamableHttp__js.js?v=306847c5:250
await in send
(anonymous) @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6676
request @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6613
connect @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6785
await in connect
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:219
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
setTimeout
(anonymous) @ VM3613:134
setTimeout
(anonymous) @ VM3613:73
setTimeout
window.fixSidebarInBriefs @ VM3613:58
(anonymous) @ VM3613:231
EnhancedVapiMcpService.js:226 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ hook.js:608
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:226
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=306847c5:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=306847c5:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=306847c5:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
setTimeout
(anonymous) @ VM3613:134
setTimeout
(anonymous) @ VM3613:73
setTimeout
window.fixSidebarInBriefs @ VM3613:58
(anonymous) @ VM3613:231
EnhancedVapiMcpService.js:154 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:235 Supabase loaded from CDN
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:245 Creating Supabase client from CDN
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:249 Supabase client created from CDN
dashboard:1 Access to fetch at 'https://mcp.vapi.ai/mcp' from origin 'http://localhost:5174' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
dashboard:114 
            
            
           POST https://mcp.vapi.ai/mcp net::ERR_FAILED
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
send @ @modelcontextprotocol_sdk_client_streamableHttp__js.js?v=306847c5:250
await in send
(anonymous) @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6676
request @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6613
connect @ @modelcontextprotocol_sdk_client_index__js.js?v=306847c5:6785
await in connect
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:219
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
setTimeout
(anonymous) @ VM3613:134
setTimeout
(anonymous) @ VM3613:73
setTimeout
window.fixSidebarInBriefs @ VM3613:58
(anonymous) @ VM3613:231
EnhancedVapiMcpService.js:226 [EnhancedVapiMcpService] StreamableHTTP MCP client failed: Failed to fetch
overrideMethod @ hook.js:608
initializeStreamableHTTPClient @ EnhancedVapiMcpService.js:226
await in initializeStreamableHTTPClient
setupDirectConnection @ EnhancedVapiMcpService.js:141
connect @ EnhancedVapiMcpService.js:76
loadVapiAssistantSettings @ AgentTab.jsx:495
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:541
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=306847c5:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=306847c5:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=306847c5:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=306847c5:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=306847c5:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=306847c5:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=306847c5:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=306847c5:19444
commitRoot @ chunk-Q72EVS5P.js?v=306847c5:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=306847c5:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=306847c5:9135
(anonymous) @ chunk-Q72EVS5P.js?v=306847c5:18655
setTimeout
(anonymous) @ VM3613:134
setTimeout
(anonymous) @ VM3613:73
setTimeout
window.fixSidebarInBriefs @ VM3613:58
(anonymous) @ VM3613:231
EnhancedVapiMcpService.js:154 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
VM3652 dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
vapiMcpService.js:436 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
VM3654 clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
VM3654 clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
dashboard:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
AgentTab.jsx:1831 [AgentTab] Retrieved assistants from Vapi: (17) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
AgentTab.jsx:1843 [AgentTab] Found attorney's assigned assistant: LegalScout
AgentTab.jsx:1864 [AgentTab] Final assistant list: [{…}]
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM3298 SyncContext.jsx:136:24)
    at async Object.callback (VM3268 AuthContext.jsx:164:34)
    at async VM3335 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM3335 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM3335 @supabase_supabase-js.js:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM3298 SyncContext.jsx:136:24)
    at async Object.callback (VM3268 AuthContext.jsx:164:34)
    at async VM3335 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM3335 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM3335 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (VM3298 SyncContext.jsx:136:24)
    at async Object.callback (VM3268 AuthContext.jsx:164:34)
    at async VM3335 @supabase_supabase-js.js:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (VM3335 @supabase_supabase-js.js:6268:7)
    at async BroadcastChannel.<anonymous> (VM3335 @supabase_supabase-js.js:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=306847c5:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=306847c5:6261
(anonymous) @ @supabase_supabase-js.js?v=306847c5:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
EnhancedVapiMcpService.js:165 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
vapiLogger.js:103 [19:57:57] [VapiMcpService] Retrieving assistant {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
VM3660 reactPolyfill.js:16 [ReactPolyfill] Created window.React object
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
VM3660 reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
VM3660 reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
VM3660 reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Supabase client ready for use
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Setting up Realtime subscription for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Realtime subscription set up using channel API
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 [VapiMcpService] Using direct API key for server operations: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] 🚫 Disabled by clean auth solution
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5174'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Found attorney in localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [initAttorneyProfileManager] Initialization complete
 🔑 [EmergencyApiKeyFix] Setting global environment variables...
 ✅ [EmergencyApiKeyFix] Global environment variables set
 🔧 [EmergencyApiKeyFix] Creating API key helper function...
 ✅ [EmergencyApiKeyFix] API key helper function created
 🌐 [EmergencyApiKeyFix] Overriding fetch for correct API key usage...
 ✅ [EmergencyApiKeyFix] Fetch override applied
 🔧 [EmergencyApiKeyFix] Fixing existing service instances...
 [EmergencyApiKeyFix] Updating VapiMcpService API key...
 ✅ [EmergencyApiKeyFix] Existing services updated
 🎉 [EmergencyApiKeyFix] Emergency API key fix complete!
 🚀 [CriticalProductionFix] Initializing all critical fixes...
 🔑 [CriticalProductionFix] Fixing Vapi API key configuration...
 ✅ [CriticalProductionFix] Vapi API key configuration fixed
 🔧 [CriticalProductionFix] Fixing environment variables...
 ✅ [CriticalProductionFix] Environment variables fixed
 🌐 [CriticalProductionFix] Fixing CORS issues...
 ✅ [CriticalProductionFix] CORS issues fixed with enhanced fetch override
 🛡️ [CriticalProductionFix] Fixing CSP issues...
VM3636 critical-production-fix.js:170 ✅ [CriticalProductionFix] CSP issues fixed
VM3636 critical-production-fix.js:208 📦 [CriticalProductionFix] Fixing import statement issues...
VM3636 critical-production-fix.js:227 ✅ [CriticalProductionFix] Import statement issues fixed
VM3636 critical-production-fix.js:241 🎉 [CriticalProductionFix] All critical fixes applied successfully
VM3648 unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
VM3648 unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
VM3648 unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
VM3648 unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
VM3653 production-cors-fix.js:181 [ProductionCorsFix] 🚀 Initializing all production fixes...
VM3653 production-cors-fix.js:59 [ProductionCorsFix] 🌍 Ensuring production environment variables...
VM3653 production-cors-fix.js:84 [ProductionCorsFix] ✅ Environment variables configured
VM3653 production-cors-fix.js:15 [ProductionCorsFix] 🔗 Fixing API endpoint URLs...
VM3653 production-cors-fix.js:54 [ProductionCorsFix] ✅ API endpoint fixes applied
VM3653 production-cors-fix.js:89 [ProductionCorsFix] 🛡️ Fixing CSP issues...
VM3653 production-cors-fix.js:109 [ProductionCorsFix] ✅ CSP issues addressed
VM3653 production-cors-fix.js:114 [ProductionCorsFix] 🚨 Enhancing error handling...
VM3653 production-cors-fix.js:131 [ProductionCorsFix] ✅ Enhanced error handling installed
VM3653 production-cors-fix.js:136 [ProductionCorsFix] 🎯 Forcing direct API mode for production...
VM3653 production-cors-fix.js:149 [ProductionCorsFix] ✅ Direct API mode configured
VM3653 production-cors-fix.js:193 [ProductionCorsFix] 🎉 All production fixes initialized successfully
EnhancedVapiMcpService.js:165 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
vapiLogger.js:103 [19:57:57] [VapiMcpService] Retrieving assistant {assistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'}
production-cors-fix.js:47 [ProductionCorsFix] 📡 Enhanced Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:67 [CriticalProductionFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
critical-production-fix.js:44 [CriticalProductionFix] Using SECRET key for server operations: server
critical-production-fix.js:45 [CriticalProductionFix] SECRET key value: 6734febc...
emergency-api-key-fix.js:71 [EmergencyApiKeyFix] Intercepting Vapi API request: https://api.vapi.ai/assistant/cd0b44b7-397e-410d-8835-ce9c3ba584b2
emergency-api-key-fix.js:84 [EmergencyApiKeyFix] Using SECRET key for assistant retrieval
emergency-api-key-fix.js:109 [EmergencyApiKeyFix] Using API key: 6734febc...
VM3669 ProductionErrorBoundary.jsx:61 [ErrorBoundary] React polyfills applied
VM3669 ProductionErrorBoundary.jsx:61 [ErrorBoundary] React polyfills applied
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
DashboardNew.jsx:624 [DashboardNew] Tab changed to: consultations
VM3613:147 ✅ Returned to Briefs tab
VM3613:153 
📊 FINAL SIDEBAR STATE:
VM3613:154 - Display: block
VM3613:155 - Visibility: visible
VM3613:156 - Left: 0px
VM3613:166 🎉 Sidebar is now visible!
ConsultationsTab.jsx:79 Fetching consultations for attorney: 87756a2c-a398-43f2-889a-b8815684df71
ConsultationsTab.jsx:79 Fetching consultations for attorney: 87756a2c-a398-43f2-889a-b8815684df71
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: SIGNED_IN
clean-auth-solution.js:133 🧹 [CleanAuthSolution] User signed in: <EMAIL>
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:107 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:179 Auth state changed: SIGNED_IN
AuthContext.jsx:182 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:185 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:200 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:213 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
 🔍 [VapiDirectApiService] Retrieved complete assistant data: {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout', hasFirstMessage: true, hasInstructions: true, voice: {…}, …}
 [19:57:57] [VapiMcpService] Assistant verified in Vapi {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout'}
 ✅ [VapiDirectApiService] Using direct API data (complete)
 📋 [AgentTab] Found complete assistant data: {firstMessage: 'yooo swassuuup damon...', hasSystemPrompt: true, systemPromptLength: 1507, voice: {…}, source: 'direct-api'}
 [DashboardNew] Calling updateAttorney with: {vapi_instructions: 'You are Scout, the AI legal assistant for LegalSco…ing to inform the user about finding an attorney.'}
 [StandaloneAttorneyManager] Updating attorney
 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AgentTab] ALL settings loaded from Vapi (complete data)
 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T19:57:57.842Z', subdomain: 'damon', firm_name: 'LegalScout', …}
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Auth state error details: Empty response from server
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
overrideMethod @ installHook.js:1
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
  POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview:114
window.fetch @ emergency-api-key-fix.js:126
window.fetch @ critical-production-fix.js:112
window.fetch @ production-cors-fix.js:51
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877890}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877890}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877890}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877890}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877915}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877915}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877915}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877915}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T19:57:57.842Z', subdomain: 'damon', firm_name: 'LegalScout', …}
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 🔍 [VapiDirectApiService] Retrieved complete assistant data: {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout', hasFirstMessage: true, hasInstructions: true, voice: {…}, …}
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
(anonymous) @ AuthContext.jsx:164
(anonymous) @ @supabase_supabase-js.js:6263
_notifyAllSubscribers @ @supabase_supabase-js.js:6261
(anonymous) @ @supabase_supabase-js.js:4955
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
 Development mode: Using mock consistency check result
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 2 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Fetching consultations for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 2 potential preview iframes
 [DashboardIframeManager] Found 2 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877984}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877984}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877984}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877984}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877990}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877990}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877990}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239877990}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
vapiLogger.js:103 [19:57:58] [VapiMcpService] Assistant verified in Vapi {id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', name: 'LegalScout'}
VapiDirectApiService.js:129 ✅ [VapiDirectApiService] Using direct API data (complete)
AgentTab.jsx:504 📋 [AgentTab] Found complete assistant data: {firstMessage: 'yooo swassuuup damon...', hasSystemPrompt: true, systemPromptLength: 1507, voice: {…}, source: 'direct-api'}
DashboardNew.jsx:666 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:743 [DashboardNew] Calling updateAttorney with: {vapi_instructions: 'You are Scout, the AI legal assistant for LegalSco…ing to inform the user about finding an attorney.'}
standalone-attorney-manager-fixed.js:478 [StandaloneAttorneyManager] Updating attorney
standalone-attorney-manager-fixed.js:859 [StandaloneAttorneyManager] validateAttorneyData: Received data: {hasId: true, idValue: '87756a2c-a398-43f2-889a-b8815684df71', idType: 'string', keys: Array(44)}
standalone-attorney-manager-fixed.js:918 [StandaloneAttorneyManager] Validated attorney data: 87756a2c-a398-43f2-889a-b8815684df71 with assistant: cd0b44b7-397e-410d-8835-ce9c3ba584b2
standalone-attorney-manager-fixed.js:466 [StandaloneAttorneyManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AgentTab.jsx:531 ✅ [AgentTab] ALL settings loaded from Vapi (complete data)
DashboardNew.jsx:746 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T19:57:58.009Z', subdomain: 'damon', firm_name: 'LegalScout', …}
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878008}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878008}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878008}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878008}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
useStandaloneAttorney.js:124 [useStandaloneAttorney] Attorney updated via subscription: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-06T19:57:58.009Z', subdomain: 'damon', firm_name: 'LegalScout', …}
ConsultationsTab.jsx:79 Fetching consultations for attorney: 87756a2c-a398-43f2-889a-b8815684df71
DashboardNew.jsx:577 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:578 [DashboardNew] Attorney Vapi Assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
DashboardNew.jsx:608 [DashboardNew] Updated preview config with assistant ID: cd0b44b7-397e-410d-8835-ce9c3ba584b2
DashboardNew.jsx:769 [DashboardNew] Using enhanced iframe manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sending config to preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 2 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
dashboard-iframe-manager.js:29 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878077}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878077}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878077}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878077}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878081}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878081}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878081}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749239878081}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: '', …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: cd0b44b7-397e-410d-8835-ce9c3ba584b2
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5174/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:774 [DashboardNew] Config sent to 2 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'light', vapiAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:220 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
