Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
 🚀 [PREVIEW CALL START] Starting consultation...
 🎯 [PREVIEW CALL START] Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 💬 [PREVIEW CALL START] Welcome message: Hello! How can I help you today?
 🔊 [PREVIEW CALL START] Voice settings: {voiceId: 'alloy', voiceProvider: 'openai'}
 ✅ [PREVIEW CALL START] VapiCall will receive assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🎬 [PREVIEW CALL RENDER] Assistant ID type: string
 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🎬 [PREVIEW CALL RENDER] Assistant ID type: string
 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: false
 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [CallDebugger:VapiCall] Processing call configuration
 [CallDebugger:VapiCall] Using direct configuration {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
 VapiCall: Using direct configuration
 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [useVapiCall] Checking for Vapi public key...
 [useVapiCall] VITE_VAPI_PUBLIC_KEY: Set
 [useVapiCall] window.VITE_VAPI_PUBLIC_KEY: Set
 [useVapiCall] Using API key: 310f0d43...
 [useVapiCall] Initializing Vapi instance with API key: 310f0d43...
 [useVapiCall] Creating Vapi instance directly using official pattern
 [useVapiCall] Loading Vapi SDK using vapiLoader
 [VapiLoader] Vapi SDK already loaded
 🚀 VapiCall component mounted, preparing to start call...
 📊 Current status: idle
 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🔧 Vapi instance available: false
 ⚙️ Processed config: null
 ⏸️ Not ready to initialize yet - missing assistantId or vapi
 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 ⏸️ Vapi instance: false
 Status changed to: idle
 CALL_STATUS.CONNECTED value: connected
 🔄 VapiCall component received dossier update: {}
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #aa4b4b
 secondaryColor: #46ce93
 vapiInstructions: You are a legal assistant helping clients with their legal needs.
 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 voiceId: alloy
 voiceProvider: openai
 chatActive: true
 🧹 Cleanup function for not ready state
 [CallDebugger:VapiCall] Processing call configuration
 [CallDebugger:VapiCall] Using direct configuration {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'}
 VapiCall: Using direct configuration
 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🚀 VapiCall component mounted, preparing to start call...
 📊 Current status: idle
 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🔧 Vapi instance available: false
 ⚙️ Processed config: null
 ⏸️ Not ready to initialize yet - missing assistantId or vapi
 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 ⏸️ Vapi instance: false
 Status changed to: idle
 CALL_STATUS.CONNECTED value: connected
 🔄 VapiCall component received dossier update: {}
 [useVapiCall] Creating Vapi instance with API key: 310f0d43...
 [VapiLoader] ✅ Vapi instance created with key: 310f0d43...
 [useVapiCall] Vapi instance created successfully
 [VapiService] Setting up event listener for: call-start
 [VapiService] Setting up event listener for: call-end
 [VapiService] Setting up event listener for: speech-start
 [VapiService] Setting up event listener for: speech-end
 [VapiService] Setting up event listener for: message
 [VapiService] Setting up event listener for: error
 [VapiService] Setting up event listener for: volume-level
 [VapiService] Event listeners set up for Vapi instance
 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🧹 Cleanup function for not ready state
 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:525 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:1098 Requesting microphone permission...
useVapiCall.js?t=1748990302238:875 Setting up Vapi event listeners
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: call-start
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: call-end
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: speech-start
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: speech-end
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: message
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: error
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: volume-level
VapiService.js?t=1748990302238:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:551 SKIPPING direct event handler setup - using useVapiCall callbacks to prevent conflicts
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1348 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1355 ⏱️ Using initialization delay of 500ms before starting call
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
useVapiCall.js?t=1748990302238:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1469 VapiCall component unmounting, performing cleanup...
VapiCall.jsx:1484 Call was not fully initialized, performing simple cleanup
VapiCall.jsx:1494 Skipping onEndCall for non-initialized state to prevent mount/unmount cycle
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1348 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1355 ⏱️ Using initialization delay of 500ms before starting call
useVapiCall.js?t=1748990302238:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:1103 Available audio input devices: (5) [{…}, {…}, {…}, {…}, {…}]
useVapiCall.js?t=1748990302238:1121 Using audio constraints: {audio: {…}}
useVapiCall.js?t=1748990302238:1125 Microphone permission granted with device: Default - Microphone (Yeti Classic) (046d:0ab7)
useVapiCall.js?t=1748990302238:1173 Starting call with existing Vapi instance using direct pattern
vapiMcpDebugger.js:175 [Vapi MCP] POST https://api.vapi.ai/call/web
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/call/web with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
vapiMcpDebugger.js:180 [Vapi MCP] Response: 201 https://api.vapi.ai/call/web
consolidated-dashboard-fix.js:105 Fetch finished loading: POST "https://api.vapi.ai/call/web".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:71
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:46
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ @vapi-ai_web.js?v=51449240:6951
(anonymous) @ @vapi-ai_web.js?v=51449240:6996
await in (anonymous)
callControllerCreateWebCall @ @vapi-ai_web.js?v=51449240:7179
start @ @vapi-ai_web.js?v=51449240:8554
(anonymous) @ useVapiCall.js?t=1748990302238:1176
Promise.then
(anonymous) @ useVapiCall.js?t=1748990302238:1124
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js?t=1748990302238:759
(anonymous) @ useVapiCall.js?t=1748990302238:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
headers-fix.js:33 [HeadersFix] Fetch request to https://c.daily.co/call-machine/versioned/0.72.2/static/call-machine-object-bundle.js with headers: {Accept: 'application/json'}
VapiCall.jsx:1364 🎯 Auto-starting call from VapiCall component after delay
VapiCall.jsx:1365 📋 Current call parameters: {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', assistantOverrides: 'Set', vapi: true, processedConfig: true}
VapiCall.jsx:1402 🎯 [VAPI CALL INIT] Final assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1403 ⚙️ [VAPI CALL INIT] Processed config: {previewConfig: {…}, vapiConfig: {…}}
VapiCall.jsx:1408 🔄 Starting call attempt 1
VapiCall.jsx:190 [VapiCall] Starting call with: {finalAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', attorneyDataAssistantId: undefined, processedConfigAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', propAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', forceDefaultAssistant: false, …}
callDebugger.js:73 [CallDebugger:VapiCall] Starting call {assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', subdomain: 'damonkost'}
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> connecting
useVapiCall.js?t=1748990302238:1213 [useVapiCall] Starting call with assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1748990302238:1224 [useVapiCall] Starting call using official Vapi Web SDK pattern
useVapiCall.js?t=1748990302238:1226 [useVapiCall] Call started successfully: null
useVapiCall.js?t=1748990302238:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1748990302238:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
consolidated-dashboard-fix.js:105 Fetch finished loading: GET "https://c.daily.co/call-machine/versioned/0.72.2/static/call-machine-object-bundle.js".
window.fetch @ consolidated-dashboard-fix.js:105
window.fetch @ simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:71
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-assistant-creation.js:211
window.fetch @ disable-automatic-assistant-creation.js:211
(anonymous) @ @vapi-ai_web.js?v=51449240:4800
h @ @vapi-ai_web.js?v=51449240:192
a2 @ @vapi-ai_web.js?v=51449240:204
(anonymous) @ @vapi-ai_web.js?v=51449240:209
(anonymous) @ @vapi-ai_web.js?v=51449240:201
o.value @ @vapi-ai_web.js?v=51449240:4813
(anonymous) @ @vapi-ai_web.js?v=51449240:4778
h @ @vapi-ai_web.js?v=51449240:192
a2 @ @vapi-ai_web.js?v=51449240:204
Promise.then
h @ @vapi-ai_web.js?v=51449240:196
a2 @ @vapi-ai_web.js?v=51449240:204
(anonymous) @ @vapi-ai_web.js?v=51449240:209
(anonymous) @ @vapi-ai_web.js?v=51449240:201
o.value @ @vapi-ai_web.js?v=51449240:4780
(anonymous) @ @vapi-ai_web.js?v=51449240:4760
h @ @vapi-ai_web.js?v=51449240:192
a2 @ @vapi-ai_web.js?v=51449240:204
(anonymous) @ @vapi-ai_web.js?v=51449240:209
(anonymous) @ @vapi-ai_web.js?v=51449240:201
o.value @ @vapi-ai_web.js?v=51449240:4762
value @ @vapi-ai_web.js?v=51449240:4735
value @ @vapi-ai_web.js?v=51449240:4716
(anonymous) @ @vapi-ai_web.js?v=51449240:5530
(anonymous) @ @vapi-ai_web.js?v=51449240:5527
h @ @vapi-ai_web.js?v=51449240:192
a2 @ @vapi-ai_web.js?v=51449240:204
(anonymous) @ @vapi-ai_web.js?v=51449240:209
(anonymous) @ @vapi-ai_web.js?v=51449240:201
o.value @ @vapi-ai_web.js?v=51449240:5546
(anonymous) @ @vapi-ai_web.js?v=51449240:5554
h @ @vapi-ai_web.js?v=51449240:192
a2 @ @vapi-ai_web.js?v=51449240:204
(anonymous) @ @vapi-ai_web.js?v=51449240:209
(anonymous) @ @vapi-ai_web.js?v=51449240:201
o.value @ @vapi-ai_web.js?v=51449240:5586
start @ @vapi-ai_web.js?v=51449240:8637
await in start
(anonymous) @ useVapiCall.js?t=1748990302238:1176
Promise.then
(anonymous) @ useVapiCall.js?t=1748990302238:1124
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js?t=1748990302238:759
(anonymous) @ useVapiCall.js?t=1748990302238:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
useVapiCall.js?t=1748990302238:880 Component unmounting - performing Vapi cleanup
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Event listeners removed from Vapi instance
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:35 SpeechParticles: Script already loaded, configuring...
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #aa4b4b Assistant: #46ce93
speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#aa4b4b', assistantColor: '#46ce93'}
speech-particles.js:525 🎨 Converting user color: #aa4b4b
speech-particles.js:527 🎨 User HSL: {h: 0, s: 39, l: 48}
speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:537 🎨 Converting assistant color: #46ce93
speech-particles.js:539 🎨 Assistant HSL: {h: 154, s: 58, l: 54}
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
SpeechParticles.jsx:128 SpeechParticles: Skipping microphone setup - Vapi call is active
useVapiCall.js?t=1748990302238:875 Setting up Vapi event listeners
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: call-start
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: call-end
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: speech-start
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: speech-end
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: message
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: error
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: volume-level
VapiService.js?t=1748990302238:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:1763 Status changed to: connected
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1768 Call connected - checking for custom welcome message
VapiCall.jsx:1789 mergedCustomInstructions: {firmName: 'LegalScout', welcomeMessage: 'Hello! How can I help you today?', voiceId: 'alloy', voiceProvider: 'openai', initialMessage: 'Hello! How can I help you today?', …}
VapiCall.jsx:1790 mergedAssistantOverrides: {firstMessage: 'Hello! How can I help you today?', model: 'Not present', artifactPlan: 'Not present'}
VapiCall.jsx:1819 Found welcome message from mergedCustomInstructions.welcomeMessage: Hello! How can I help you today?
VapiCall.jsx:1837 Adding welcome message to UI: Hello! How can I help you today?
VapiCall.jsx:1842 Welcome message should be spoken by the assistant automatically
VapiCall.jsx:1845 Voice settings: {voiceId: 'alloy', voiceProvider: 'openai'}
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:35 SpeechParticles: Script already loaded, configuring...
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #aa4b4b Assistant: #46ce93
speech-particles.js:522 🎨 Speech particles: Setting custom colors {userColor: '#aa4b4b', assistantColor: '#46ce93'}
speech-particles.js:525 🎨 Converting user color: #aa4b4b
speech-particles.js:527 🎨 User HSL: {h: 0, s: 39, l: 48}
speech-particles.js:533 🎨 Speech particles: Updated user colors to {name: 'User Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:537 🎨 Converting assistant color: #46ce93
speech-particles.js:539 🎨 Assistant HSL: {h: 154, s: 58, l: 54}
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to {name: 'Assistant Colors (Custom)', quiet: {…}, loud: {…}}
speech-particles.js:548 🎨 Final color palettes: {user: {…}, assistant: {…}, ambient: {…}}
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
SpeechParticles.jsx:128 SpeechParticles: Skipping microphone setup - Vapi call is active
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SpeechParticles.jsx:382 SpeechParticles: Received window message event: {action: 'loaded', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
VapiCall.jsx:317 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
SpeechParticles.jsx:382 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'module', …}
useVapiCall.js?t=1748990302238:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1748990302238:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
SpeechParticles.jsx:128 SpeechParticles: Skipping microphone setup - Vapi call is active
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SpeechParticles.jsx:382 SpeechParticles: Received window message event: {action: 'call-machine-initialized', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SpeechParticles.jsx:382 SpeechParticles: Received window message event: {action: 'daily-main-executed', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
VapiCall.jsx:1416 ✅ Marking call as initialized
VapiCall.jsx:1421 ✅ Set window.vapiCallActive to true after initialization
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
VapiCall.jsx:317 Window message received for processing: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
SpeechParticles.jsx:382 SpeechParticles: Received window message event: {action: 'transmit-log', level: 'log', code: 1011, stats: {…}, what: 'iframe-call-message', …}
useVapiCall.js?t=1748990302238:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
useVapiCall.js?t=1748990302238:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
VapiCall.jsx:1665 VapiCall: Scrolled conversation area to bottom
VapiCall.jsx:1775 Added force-visible class to call interface
VM20682:4 XHR finished loading: POST "https://gs.daily.co/rooms/check/vapi/0H1jjf8gbhBXOgeRLOEL".
l._end @ VM20682:4
l.end @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
e @ VM20682:4
n @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
I @ VM20682:4
e.roomsCheck @ VM20682:4
eval @ VM20682:4
e @ VM20682:4
n @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
e @ VM20682:4
n @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
e @ VM20682:4
n @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
e @ VM20682:4
n @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
eval @ VM20682:4
i @ VM20682:4
postMessage
value @ @vapi-ai_web.js?v=51449240:4586
value @ @vapi-ai_web.js?v=51449240:6200
(anonymous) @ @vapi-ai_web.js?v=51449240:5569
h @ @vapi-ai_web.js?v=51449240:192
a2 @ @vapi-ai_web.js?v=51449240:204
Promise.then
h @ @vapi-ai_web.js?v=51449240:196
a2 @ @vapi-ai_web.js?v=51449240:204
(anonymous) @ @vapi-ai_web.js?v=51449240:209
(anonymous) @ @vapi-ai_web.js?v=51449240:201
o.value @ @vapi-ai_web.js?v=51449240:5586
start @ @vapi-ai_web.js?v=51449240:8637
await in start
(anonymous) @ useVapiCall.js?t=1748990302238:1176
Promise.then
(anonymous) @ useVapiCall.js?t=1748990302238:1124
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js?t=1748990302238:759
(anonymous) @ useVapiCall.js?t=1748990302238:766
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=704ffe31:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=704ffe31:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=704ffe31:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=704ffe31:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=704ffe31:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=704ffe31:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=704ffe31:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=704ffe31:19444
commitRoot @ chunk-Q72EVS5P.js?v=704ffe31:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=704ffe31:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=704ffe31:9135
(anonymous) @ chunk-Q72EVS5P.js?v=704ffe31:18655
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SpeechParticles.jsx:382 SpeechParticles: Received window message event: {action: 'access-state-updated', access: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SpeechParticles.jsx:382 SpeechParticles: Received window message event: {action: 'receive-settings-updated', receiveSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
VapiCall.jsx:317 Window message received for processing: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SpeechParticles.jsx:382 SpeechParticles: Received window message event: {action: 'send-settings-updated', sendSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Preferred mic not found; skipping: true
value @ unknown
value @ unknown
Q @ unknown
eval @ unknown
e.getCamGumConstraints @ unknown
eval @ unknown
e @ unknown
n @ unknown
Promise.then
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
y @ unknown
e.getCamStream @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
iA @ unknown
G.camOperation @ unknown
eval @ unknown
e @ unknown
n @ unknown
Promise.then
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
dispatch @ unknown
eval @ unknown
e @ unknown
n @ unknown
Promise.then
e @ unknown
n @ unknown
Promise.then
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
dispatch @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
e @ unknown
n @ unknown
Promise.then
e @ unknown
n @ unknown
Promise.then
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
i @ unknown
postMessage
value @ @vapi-ai_web.js:4586
value @ @vapi-ai_web.js:6200
(anonymous) @ @vapi-ai_web.js:5569
h @ @vapi-ai_web.js:192
a2 @ @vapi-ai_web.js:204
Promise.then
h @ @vapi-ai_web.js:196
a2 @ @vapi-ai_web.js:204
(anonymous) @ @vapi-ai_web.js:209
(anonymous) @ @vapi-ai_web.js:201
o.value @ @vapi-ai_web.js:5586
start @ @vapi-ai_web.js:8637
await in start
(anonymous) @ useVapiCall.js:1176
Promise.then
(anonymous) @ useVapiCall.js:1124
await in (anonymous)
loadSubdomainConfig @ useVapiCall.js:759
(anonymous) @ useVapiCall.js:766
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'selected-devices-updated', devices: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
 [EnhancedPreview] Received ANY message: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
 Window message received for processing: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
 SpeechParticles: Received window message event: {action: 'started-camera', camera: {…}, mic: {…}, speaker: {…}, what: 'iframe-call-message', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Received ANY message: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: signaling
 Global iframe message received: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 Window message received for processing: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 SpeechParticles: Received window message event: {action: 'network-connection', type: 'signaling', event: 'connected', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 daily-js version 0.72.2 is nearing end of support. Please upgrade to a newer version.
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'meeting-session-summary-updated', meetingSession: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Received ANY message: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 Window message received for processing: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 Direct Vapi call started successfully: {id: 'f0decf74-8488-4322-a05a-249868a57a9b', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', createdAt: '2025-06-04T02:07:00.067Z', updatedAt: '2025-06-04T02:07:00.067Z', type: 'webCall', …}
 SpeechParticles: Received window message event: {action: 'joined-meeting', participants: {…}, internal: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
 [EnhancedPreview] Received ANY message: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
 Window message received for processing: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
 SpeechParticles: Received window message event: {action: 'join-meeting', properties: {…}, preloadCache: {…}, what: 'iframe-call-message', from: 'embedded', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Received ANY message: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490028201500.5849728633704725', …}
 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490028201500.5849728633704725', …}
 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', interval: 100, what: 'iframe-call-message', from: 'module', callClientId: '17490028201500.5849728633704725', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Received ANY message: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490028201500.5849728633704725', …}
 Window message received for processing: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490028201500.5849728633704725', …}
 KrispSDK - The KrispSDK is duplicated. Please ensure that the SDK is only imported once.
 SpeechParticles: Received window message event: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'module', callClientId: '17490028201500.5849728633704725', …}
 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 🎨 VapiCall: Rendering SpeechParticles with colors: {primaryColor: '#aa4b4b', secondaryColor: '#46ce93', attorneyData: null, processedConfig: {…}}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490028216460.8143319819626418', error: null, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Received ANY message: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490028216460.8143319819626418', error: null, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490028216460.8143319819626418', error: null, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 Window message received for processing: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490028216460.8143319819626418', error: null, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 SpeechParticles: Received window message event: {action: 'start-remote-participants-audio-level-observer', callbackStamp: '17490028216460.8143319819626418', error: null, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'meeting-session-state-updated', meetingSessionState: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-sd2s8', what: 'iframe-call-message', …}
 [EnhancedPreview] Received ANY message: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-sd2s8', what: 'iframe-call-message', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: sfu
 Global iframe message received: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-sd2s8', what: 'iframe-call-message', …}
 Window message received for processing: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-sd2s8', what: 'iframe-call-message', …}
 SpeechParticles: Received window message event: {action: 'network-connection', type: 'sfu', event: 'connected', sfu_id: 'sfu-prod-d5bd7b649-sd2s8', what: 'iframe-call-message', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-joined', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-counts-updated', participantCounts: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-participant', id: '2412ac61-83ec-43f7-9909-03f5552faec3', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Received ANY message: {action: 'update-participant', id: '2412ac61-83ec-43f7-9909-03f5552faec3', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'update-participant', id: '2412ac61-83ec-43f7-9909-03f5552faec3', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 Window message received for processing: {action: 'update-participant', id: '2412ac61-83ec-43f7-9909-03f5552faec3', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 SpeechParticles: Received window message event: {action: 'update-participant', id: '2412ac61-83ec-43f7-9909-03f5552faec3', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-participant', id: '2f77120f-2702-4753-b633-e80f266432a4', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Received ANY message: {action: 'update-participant', id: '2f77120f-2702-4753-b633-e80f266432a4', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'update-participant', id: '2f77120f-2702-4753-b633-e80f266432a4', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 Window message received for processing: {action: 'update-participant', id: '2f77120f-2702-4753-b633-e80f266432a4', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 SpeechParticles: Received window message event: {action: 'update-participant', id: '2f77120f-2702-4753-b633-e80f266432a4', properties: {…}, what: 'iframe-call-message', from: 'module', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Received ANY message: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
 Window message received for processing: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
 SpeechParticles: Received window message event: {action: 'app-msg', data: 'playable', to: '*', what: 'iframe-call-message', from: 'module', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: 'listening', fromId: '2f77120f-2702-4753-b633-e80f266432a4', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: 'listening', fromId: '2f77120f-2702-4753-b633-e80f266432a4', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: 'listening', fromId: '2f77120f-2702-4753-b633-e80f266432a4', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 Window message received for processing: {action: 'app-message', data: 'listening', fromId: '2f77120f-2702-4753-b633-e80f266432a4', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 Call started - setting status to CONNECTED
 Call started - setting status to CONNECTED
 Call started - setting status to CONNECTED
 SpeechParticles: Received window message event: {action: 'app-message', data: 'listening', fromId: '2f77120f-2702-4753-b633-e80f266432a4', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '2412ac61-83ec-43f7-9909-03f5552faec3', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '2412ac61-83ec-43f7-9909-03f5552faec3', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '2412ac61-83ec-43f7-9909-03f5552faec3', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '2412ac61-83ec-43f7-9909-03f5552faec3', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 Received message: {type: 'status-update', status: 'in-progress'}
 Received message: {type: 'status-update', status: 'in-progress'}
 Received message: {type: 'status-update', status: 'in-progress'}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"in-progress"}', fromId: '2412ac61-83ec-43f7-9909-03f5552faec3', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Received window message event: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '2412ac61-83ec-43f7-9909-03f5552faec3', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Received ANY message: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '2412ac61-83ec-43f7-9909-03f5552faec3', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '2412ac61-83ec-43f7-9909-03f5552faec3', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 Window message received for processing: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '2412ac61-83ec-43f7-9909-03f5552faec3', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
 Received message: {type: 'status-update', status: 'ended', endedReason: 'pipeline-error-eleven-labs-quota-exceeded'}
 SpeechParticles: Received window message event: {action: 'app-message', data: '{"type":"status-update","status":"ended","endedReason":"pipeline-error-eleven-labs-quota-exceeded"}', fromId: '2412ac61-83ec-43f7-9909-03f5552faec3', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1 XHR finished loading: GET "https://c.daily.co/static/krisp/v1.4.4/weights/model_32.kw".
_fetchFile @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
_0x3c473b @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
(anonymous) @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
(anonymous) @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
_0x352259 @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
mallocModel @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
(anonymous) @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
(anonymous) @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
_0x23d8ae @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
initWasm @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
_0x876828.<computed> @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
self.<computed> @ blob:http://localhost:5175/1da76243-561e-4ffe-8f2d-fd3ed5db97f1:1
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 🎯 [SimplePreviewPage] Received message from parent: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Custom volume event received: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 VapiCall: Dispatched volume change event: 0
 SpeechParticles: Received window message event: {action: 'remote-participants-audio-level', participantsAudioLevel: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 SpeechParticles: Processing audio level event: {2412ac61-83ec-43f7-9909-03f5552faec3: 0, 2f77120f-2702-4753-b633-e80f266432a4: 0}
 SpeechParticles: Calculated assistant audio level: 0
 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
 Meeting ended due to ejection: Meeting has ended
value @ unknown
value @ unknown
F @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 🎯 [SimplePreviewPage] Received message from parent: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 [EnhancedPreview] Received ANY message: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 Window message received for processing: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17490028201500.5849728633704725'}
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Continuing despite Vapi error
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17490028201500.5849728633704725'}
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Continuing despite Vapi error
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Vapi error: {action: 'error', errorMsg: 'Meeting has ended', error: {…}, callClientId: '17490028201500.5849728633704725'}
onError @ useVapiCall.js:94
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 Continuing despite Vapi error
onError @ useVapiCall.js:95
emit @ @vapi-ai_web.js:6662
emit @ @vapi-ai_web.js:8501
(anonymous) @ @vapi-ai_web.js:8590
S.emit @ @vapi-ai_web.js:2791
value @ @vapi-ai_web.js:6409
value @ @vapi-ai_web.js:6266
i2 @ @vapi-ai_web.js:4564
postMessage
value @ unknown
value @ unknown
value @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
msgSigChannel @ unknown
eval @ unknown
e @ unknown
n @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
eval @ unknown
 SpeechParticles: Received window message event: {action: 'error', errorMsg: 'Meeting has ended', preserveIframe: false, error: {…}, what: 'iframe-call-message', …}
 🎯 [SimplePreviewPage] Received message from parent: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 Global iframe message received: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Window message received for processing: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 Call ended
 [CallDebugger:VapiCall] Call ended {}
 [CallDebugger:VapiCall] Call status changed: connecting -> ended
 Ending call...
 Call ended
 [CallDebugger:VapiCall] Call ended {}
 [CallDebugger:VapiCall] Call status changed: ended -> ended
 Ending call...
 Call ended
 [CallDebugger:VapiCall] Call ended {}
 [CallDebugger:VapiCall] Call status changed: ended -> ended
 Ending call...
 SpeechParticles: Received window message event: {action: 'left-meeting', what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Component unmounting - performing Vapi cleanup
 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
 [VapiService] Event listeners removed from Vapi instance
 Stopping active call during cleanup
 Calling onEndCall callback during unmount
 [CallDebugger:VapiCall] Call ended {}
 [CallDebugger:VapiCall] Call status changed: ended -> ended
 Ending call...
 VapiCall component unmounting, performing cleanup...
 Call was still initializing during unmount, cancelling initialization
 Skipping onEndCall during initialization cancellation to prevent mount/unmount cycle
 SpeechParticles: Cleaning up component
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: 
 primaryColor: #aa4b4b
 secondaryColor: #46ce93
 vapiInstructions: You are a legal assistant helping clients with their legal needs.
 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
 voiceId: alloy
 voiceProvider: openai
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: 'LegalScout', theme: 'dark', vapiAssistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', primaryColor: '#aa4b4b', …}
 Using default logo path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 🎯 [SimplePreviewPage] Received message from parent: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'participant-updated', participant: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 🎯 [SimplePreviewPage] Received message from parent: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Received ANY message: {action: 'input-settings-updated', inputSettings: {…}, what: 'iframe-call-message', callClientId: '17490028201500.5849728633704725', from: 'embedded'}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
 🎯 [SimplePreviewPage] Received message from parent: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Received ANY message: {action: 'update-input-settings', inputSettings: {…}, what: 'iframe-call-message', from: 'embedded', callClientId: '17490028201500.5849728633704725', …}
 [EnhancedPreview] Message source: other
 [EnhancedPreview] Message type: undefined
speech-particles.js:345 [Violation] 'requestAnimationFrame' handler took 102ms
