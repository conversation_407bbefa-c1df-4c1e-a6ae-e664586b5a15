﻿callback:16 🚀 [EMERGENCY] Starting emergency critical fixes...
callback:20 🔧 [EMERGENCY] Adding process polyfill
callback:27 ✅ [EMERGENCY] Process polyfill added
callback:38 🔧 [EMERGENCY] Development mode: false (forced production)
callback:65 ✅ [EMERGENCY] Fetch patched
callback:68 🎉 [EMERGENCY] Emergency fixes complete!
callback:149 Supabase loaded from CDN
callback:159 Creating Supabase client from CDN
callback:163 Supabase client created from CDN
reactPolyfill.js:16 [ReactPolyfill] Created window.React object
reactPolyfill.js:28 [ReactPolyfill] Added Children to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Component to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Fragment to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Profiler to window.React
reactPolyfill.js:28 [ReactPolyfill] Added PureComponent to window.React
reactPolyfill.js:28 [ReactPolyfill] Added StrictMode to window.React
reactPolyfill.js:28 [ReactPolyfill] Added Suspense to window.React
reactPolyfill.js:28 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
reactPolyfill.js:28 [ReactPolyfill] Added act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added cloneElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createFactory to window.React
reactPolyfill.js:28 [ReactPolyfill] Added createRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added forwardRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added isValidElement to window.React
reactPolyfill.js:28 [ReactPolyfill] Added lazy to window.React
reactPolyfill.js:28 [ReactPolyfill] Added memo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added startTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added unstable_act to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useCallback to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useContext to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDebugValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useDeferredValue to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useId to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useImperativeHandle to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useInsertionEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useLayoutEffect to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useMemo to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useReducer to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useRef to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useState to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useSyncExternalStore to window.React
reactPolyfill.js:28 [ReactPolyfill] Added useTransition to window.React
reactPolyfill.js:28 [ReactPolyfill] Added version to window.React
reactPolyfill.js:79 [ReactPolyfill] Created global LayoutGroupContext
reactPolyfill.js:82 [ReactPolyfill] Enhanced React polyfill applied successfully
headers-fix.js:39 Headers fix applied to fetch
supabase.js:28 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
supabase.js:39 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:45 Supabase Key configured: eyJhb...K4cRU
supabase.js:59 Development mode detected, using fallback Supabase configuration
supabase.js:81 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:98 Supabase client initialized successfully with proper headers
supabase.js:101 Testing Supabase connection...
supabase.js:147 Running in development mode
supabase.js:215 Attaching Supabase client to window.supabase
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1409 [AttorneyProfileManager] Loaded attorney from localStorage: undefined
initAttorneyProfileManager.js:16 [initAttorneyProfileManager] Initializing attorney profile manager
initAttorneyProfileManager.js:25 [initAttorneyProfileManager] Initializing Vapi service manager
vapiServiceManager.js:44 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
vapiServiceManager.js:73 [VapiServiceManager] Trying to connect to real Vapi service
vapiMcpService.js:236 [VapiMcpService] Production mode: false
vapiMcpService.js:237 [VapiMcpService] Development mode: true
vapiMcpService.js:238 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:255 [VapiMcpService] Using fast loading mode for development - direct API to avoid CORS issues
environmentVerifier.js:58 Environment Variable Verification
environmentVerifier.js:59 All required variables present: ✅ Yes
environmentVerifier.js:65 Variables status:
environmentVerifier.js:67 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
environmentVerifier.js:67 - VITE_SUPABASE_KEY: ✅ Present (****)
environmentVerifier.js:67 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
vapiNetworkInterceptor.js:95 [Vapi Network Interceptor] Installed
vapiMcpDebugger.js:204 [Vapi MCP] Environment Check
vapiMcpDebugger.js:205 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Set'isDevelopmenttrueorigin'http://localhost:5174'Object
initVapiDebugger.js:99 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
vapiServiceManager.js:103 [VapiServiceManager] Connected to Vapi MCP server
initAttorneyProfileManager.js:54 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
initAttorneyProfileManager.js:102 [initAttorneyProfileManager] Connected to Vapi using direct mode
initAttorneyProfileManager.js:128 [initAttorneyProfileManager] Initialization complete
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
AuthCallback.jsx:15 Auth callback - environment mode: development
AuthCallback.jsx:18 Using real authentication in all environments
supabase.js:59 Development mode detected, using fallback Supabase configuration
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
testSupabase.js:55 === SUPABASE CONFIG TEST ===
testSupabase.js:56 Supabase URL configured: true
testSupabase.js:57 Supabase Key configured: true
testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:791 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
debugConfig.js:30 [App] App component unmounted undefined
AuthCallback.jsx:15 Auth callback - environment mode: development
AuthCallback.jsx:18 Using real authentication in all environments
supabase.js:59 Development mode detected, using fallback Supabase configuration
debugConfig.js:30 [App] App component mounted undefined
supabaseConfigVerifier.js:119 🔧 Development mode detected, initializing Supabase configuration...
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
testSupabase.js:55 === SUPABASE CONFIG TEST ===
testSupabase.js:56 Supabase URL configured: true
testSupabase.js:57 Supabase Key configured: true
testSupabase.js:60 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabaseConfigVerifier.js:20 🔍 Verifying Supabase configuration...
supabaseConfigVerifier.js:32 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
supabaseConfigVerifier.js:64 Testing Supabase connection...
debugConfig.js:30 [App] Subdomain detected: default
debugConfig.js:30 [App] Is attorney subdomain: false
App.jsx:791 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
AuthContext.jsx:438 Using real authentication in all environments
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.Cn23oKkr0AKwCmNvXZ3yyqqaU8Gsh38iRaj1rZvi-j8', content-type: 'application/json', …}
AuthContext.jsx:85 OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
callback:59 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
supabase.js:115 Supabase connection test successful!
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:118:34)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthContext.jsx:85 OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:88 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:103 Found OAuth email: <EMAIL>
AuthContext.jsx:116 AuthContext (initAuth): Handling auth state for refresh
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
callback:59 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: INITIAL_SESSION
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=0856315e:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=0856315e:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=0856315e:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:118:34)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:118
await in initAuth
(anonymous) @ AuthContext.jsx:157
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=0856315e:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=0856315e:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=0856315e:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.Cn23oKkr0AKwCmNvXZ3yyqqaU8Gsh38iRaj1rZvi-j8', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.Cn23oKkr0AKwCmNvXZ3yyqqaU8Gsh38iRaj1rZvi-j8', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.Cn23oKkr0AKwCmNvXZ3yyqqaU8Gsh38iRaj1rZvi-j8', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.Cn23oKkr0AKwCmNvXZ3yyqqaU8Gsh38iRaj1rZvi-j8', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.Cn23oKkr0AKwCmNvXZ3yyqqaU8Gsh38iRaj1rZvi-j8', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.Cn23oKkr0AKwCmNvXZ3yyqqaU8Gsh38iRaj1rZvi-j8', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
debugConfig.js:30 [App] Available subdomains for testing: (14) ['attorney-00923', 'attorney-04739', 'attorney-27037', 'attorney-43981', 'attorney-45038', 'attorney-56840', 'attorney-83666', 'attorney-96878', 'damon', 'damonkost', 'default', 'general-counsel-online', 'kostlaw', 'scout']
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:680 Supabase is properly configured and connected! [{…}]
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.Cn23oKkr0AKwCmNvXZ3yyqqaU8Gsh38iRaj1rZvi-j8', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js:143 ✅ Supabase configuration verified and working
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.Cn23oKkr0AKwCmNvXZ3yyqqaU8Gsh38iRaj1rZvi-j8', content-type: 'application/json', …}
headers-fix.js:33 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…HNlfQ.Cn23oKkr0AKwCmNvXZ3yyqqaU8Gsh38iRaj1rZvi-j8', content-type: 'application/json', …}
supabaseConfigVerifier.js:92 ✅ Supabase connection test successful!
App.jsx:680 Supabase is properly configured and connected! [{…}]
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Session refreshed successfully'}
AuthCallback.jsx:92 Attorney profile found, redirecting to dashboard
AuthCallback.jsx:92 Attorney profile found, redirecting to dashboard
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: [{…}]
DashboardNew.jsx:136 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:137 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
DashboardNew.jsx:350 [DashboardNew] Waiting... (authIsLoading: false, userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701, managerReady: false)
ProfileTab.jsx:53 Attorney object in ProfileTab: null
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: [{…}]
DashboardNew.jsx:136 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:137 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
DashboardNew.jsx:350 [DashboardNew] Waiting... (authIsLoading: false, userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701, managerReady: false)
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:53 Attorney object in ProfileTab: [{…}]
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
DashboardNew.jsx:444 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:445 [DashboardNew] Attorney Vapi Assistant ID: undefined
DashboardNew.jsx:475 [DashboardNew] Updated preview config with assistant ID: undefined
DashboardNew.jsx:475 [DashboardNew] Updated preview config with assistant ID: undefined
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
callback:59 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
DashboardNew.jsx:646 Found 0 iframes on the page
DashboardNew.jsx:700 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:700
setTimeout
sendConfigToPreviewIframe @ DashboardNew.jsx:635
(anonymous) @ DashboardNew.jsx:478
basicStateReducer @ chunk-Q72EVS5P.js?v=0856315e:11723
updateReducer @ chunk-Q72EVS5P.js?v=0856315e:11814
updateState @ chunk-Q72EVS5P.js?v=0856315e:12041
useState @ chunk-Q72EVS5P.js?v=0856315e:12773
useState @ chunk-2N3A5BUM.js?v=0856315e:1066
DashboardNew @ DashboardNew.jsx:62
renderWithHooks @ chunk-Q72EVS5P.js?v=0856315e:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=0856315e:14602
beginWork @ chunk-Q72EVS5P.js?v=0856315e:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=0856315e:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=0856315e:19226
workLoopSync @ chunk-Q72EVS5P.js?v=0856315e:19165
renderRootSync @ chunk-Q72EVS5P.js?v=0856315e:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=0856315e:18706
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
DashboardNew.jsx:646 Found 0 iframes on the page
DashboardNew.jsx:700 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:700
setTimeout
sendConfigToPreviewIframe @ DashboardNew.jsx:635
(anonymous) @ DashboardNew.jsx:478
basicStateReducer @ chunk-Q72EVS5P.js?v=0856315e:11723
updateReducer @ chunk-Q72EVS5P.js?v=0856315e:11814
updateState @ chunk-Q72EVS5P.js?v=0856315e:12041
useState @ chunk-Q72EVS5P.js?v=0856315e:12773
useState @ chunk-2N3A5BUM.js?v=0856315e:1066
DashboardNew @ DashboardNew.jsx:62
renderWithHooks @ chunk-Q72EVS5P.js?v=0856315e:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=0856315e:14607
beginWork @ chunk-Q72EVS5P.js?v=0856315e:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=0856315e:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=0856315e:19226
workLoopSync @ chunk-Q72EVS5P.js?v=0856315e:19165
renderRootSync @ chunk-Q72EVS5P.js?v=0856315e:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=0856315e:18706
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=0856315e:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=0856315e:6261
(anonymous) @ @supabase_supabase-js.js?v=0856315e:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=0856315e:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=0856315e:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=0856315e:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=0856315e:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=0856315e:6261
(anonymous) @ @supabase_supabase-js.js?v=0856315e:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:64 [useStandaloneAttorney] Manager still not ready after 5 seconds
overrideMethod @ hook.js:608
(anonymous) @ useStandaloneAttorney.js:64
setTimeout
(anonymous) @ useStandaloneAttorney.js:61
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=0856315e:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=0856315e:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=0856315e:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=0856315e:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:64 [useStandaloneAttorney] Manager still not ready after 5 seconds
overrideMethod @ hook.js:608
(anonymous) @ useStandaloneAttorney.js:64
setTimeout
(anonymous) @ useStandaloneAttorney.js:61
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=0856315e:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=0856315e:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=0856315e:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
DashboardNew.jsx:340 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
(anonymous) @ DashboardNew.jsx:340
setTimeout
(anonymous) @ DashboardNew.jsx:338
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=0856315e:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=0856315e:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=0856315e:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=0856315e:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=0856315e:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=0856315e:19475
(anonymous) @ chunk-Q72EVS5P.js?v=0856315e:19356
workLoop @ chunk-Q72EVS5P.js?v=0856315e:197
flushWork @ chunk-Q72EVS5P.js?v=0856315e:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=0856315e:384
ProfileTab.jsx:53 Attorney object in ProfileTab: [{…}]
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:53 Attorney object in ProfileTab: [{…}]
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
callback:59 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
callback:59 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=0856315e:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=0856315e:6261
(anonymous) @ @supabase_supabase-js.js?v=0856315e:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=0856315e:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=0856315e:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=0856315e:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=0856315e:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=0856315e:6261
(anonymous) @ @supabase_supabase-js.js?v=0856315e:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=0856315e:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=0856315e:6261
(anonymous) @ @supabase_supabase-js.js?v=0856315e:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=0856315e:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=0856315e:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=0856315e:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=0856315e:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=0856315e:6261
(anonymous) @ @supabase_supabase-js.js?v=0856315e:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
DashboardNew.jsx:1334 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
callback:59 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
DashboardNew.jsx:1357 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
ProfileTab.jsx:53 Attorney object in ProfileTab: [{…}]
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=0856315e:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=0856315e:6261
(anonymous) @ @supabase_supabase-js.js?v=0856315e:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=0856315e:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=0856315e:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=0856315e:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=0856315e:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=0856315e:6261
(anonymous) @ @supabase_supabase-js.js?v=0856315e:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
AuthContext.jsx:162 Auth state changed: SIGNED_IN
AuthContext.jsx:165 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:168 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:183 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:196 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
headers-fix.js:33 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
callback:59 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5174/api/sync-tools/manage-auth-state
useSyncTools.js:237 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=0856315e:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=0856315e:6261
(anonymous) @ @supabase_supabase-js.js?v=0856315e:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:198:34)
    at async @supabase_supabase-js.js?v=0856315e:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=0856315e:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=0856315e:4955:9)
overrideMethod @ hook.js:608
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:198
(anonymous) @ @supabase_supabase-js.js?v=0856315e:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=0856315e:6261
(anonymous) @ @supabase_supabase-js.js?v=0856315e:4955
useSyncTools.js:248 Using client-side fallback for auth state management
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Client-side fallback: Login handled successfully'}
useSyncTools.js:516 Development mode: Using mock consistency check result
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
AuthContext.jsx:203 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Client-side fallback: Login handled successfully'}
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
ProfileTab.jsx:53 Attorney object in ProfileTab: [{…}]
ProfileTab.jsx:54 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
ProfileTab.jsx:69 Email sources: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', attorneyEmail: undefined, previewConfigEmail: undefined}
ProfileTab.jsx:85 Updated form data with OAuth user email: <EMAIL>
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
DashboardNew.jsx:646 Found 2 iframes on the page
DashboardNew.jsx:659 Found preview iframe: http://localhost:5174/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:674 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:675 [DashboardNew] Config being sent: {firmName: undefined, attorneyName: undefined, practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
DashboardNew.jsx:676 [DashboardNew] Assistant ID in config: undefined
