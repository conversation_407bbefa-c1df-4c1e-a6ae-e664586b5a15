dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: Object
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: Object
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: Object
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: Object
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: Object
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: Object
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: Object
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: Object
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, welcomeMessage, vapiInstructions, vapiAssistantId, theme, voiceId, voiceProvider
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=damonkost&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! Array(1)
AuthContext.jsx:203 AuthContext: Auth state sync result: Object
AuthContext.jsx:203 AuthContext: Auth state sync result: Object
AuthContext.jsx:203 AuthContext: Auth state sync result: Object
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: Object
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: Object
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
unified-banner-fix.js:37 [UnifiedBannerFix] Ensuring upload interface is visible
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:475 🚀 [PREVIEW CALL START] Starting consultation...
EnhancedPreviewNew.jsx:476 🎯 [PREVIEW CALL START] Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:477 💬 [PREVIEW CALL START] Welcome message: Hello! How can I help you today?
EnhancedPreviewNew.jsx:478 🔊 [PREVIEW CALL START] Voice settings: Object
EnhancedPreviewNew.jsx:479 ✅ [PREVIEW CALL START] VapiCall will receive assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:686 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:687 🎬 [PREVIEW CALL RENDER] Assistant ID type: string
EnhancedPreviewNew.jsx:688 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: false
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:686 🎬 [PREVIEW CALL RENDER] Rendering VapiCall with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:687 🎬 [PREVIEW CALL RENDER] Assistant ID type: string
EnhancedPreviewNew.jsx:688 🎬 [PREVIEW CALL RENDER] Assistant ID is null/undefined: false
callDebugger.js:75 [CallDebugger:VapiCall] Debugger initialized
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
callDebugger.js:75 [CallDebugger:VapiCall] Processing call configuration
callDebugger.js:73 [CallDebugger:VapiCall] Using direct configuration Object
VapiCall.jsx:134 VapiCall: Using direct configuration
callDebugger.js:75 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:139 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:525 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:795 [useVapiCall] Checking for Vapi public key...
useVapiCall.js?t=1749037189096:803 [useVapiCall] VITE_VAPI_PUBLIC_KEY: Set
useVapiCall.js?t=1749037189096:804 [useVapiCall] window.VITE_VAPI_PUBLIC_KEY: Set
useVapiCall.js?t=1749037189096:805 [useVapiCall] Using API key: 310f0d43...
useVapiCall.js?t=1749037189096:815 [useVapiCall] Initializing Vapi instance with API key: 310f0d43...
useVapiCall.js?t=1749037189096:817 [useVapiCall] Creating Vapi instance directly using official pattern
useVapiCall.js?t=1749037189096:821 [useVapiCall] Loading Vapi SDK using vapiLoader
vapiLoader.js:18 [VapiLoader] Vapi SDK already loaded
vapiMcpService.js:233 [VapiMcpService] Production mode: false
vapiMcpService.js:234 [VapiMcpService] Development mode: true
vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:246 [VapiMcpService] API key being used: 310f0d43...
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: false
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1338 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1339 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1340 ⏸️ Vapi instance: false
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1857 🔄 VapiCall component received dossier update: Object
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: LegalScout
EnhancedPreviewNew.jsx:462 titleText: LegalScout
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #aa4b4b
EnhancedPreviewNew.jsx:465 secondaryColor: #46ce93
EnhancedPreviewNew.jsx:466 vapiInstructions: You are a legal assistant helping clients with their legal needs.
EnhancedPreviewNew.jsx:467 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: 11labs
EnhancedPreviewNew.jsx:470 chatActive: true
VapiCall.jsx:1342 🧹 Cleanup function for not ready state
callDebugger.js:75 [CallDebugger:VapiCall] Processing call configuration
callDebugger.js:73 [CallDebugger:VapiCall] Using direct configuration Object
VapiCall.jsx:134 VapiCall: Using direct configuration
callDebugger.js:75 [CallDebugger:VapiCall] Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:139 VapiCall: Setting processed config with assistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:525 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
vapiMcpService.js:233 [VapiMcpService] Production mode: false
vapiMcpService.js:234 [VapiMcpService] Development mode: true
vapiMcpService.js:235 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
vapiMcpService.js:245 [VapiMcpService] Using direct API mode for development or forced direct mode
vapiMcpService.js:246 [VapiMcpService] API key being used: 310f0d43...
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: false
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1338 ⏸️ Not ready to initialize yet - missing assistantId or vapi
VapiCall.jsx:1339 ⏸️ Assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1340 ⏸️ Vapi instance: false
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1857 🔄 VapiCall component received dossier update: Object
useVapiCall.js?t=1749037189096:826 [useVapiCall] Creating Vapi instance with API key: 310f0d43...
vapiLoader.js:165 [VapiLoader] ✅ Vapi instance created with key: 310f0d43...
vapiEmissionsService.js?t=1749032804912:35 VapiEmissionsService: Initialized successfully
vapiEmissionsService.js?t=1749032804912:35 VapiEmissionsService: Initialized successfully
useVapiCall.js?t=1749037189096:831 [useVapiCall] Vapi instance created successfully
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: call-start
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: call-end
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: speech-start
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: speech-end
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: message
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: error
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: volume-level
VapiService.js?t=1748990302238:19 [VapiService] Event listeners set up for Vapi instance
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1342 🧹 Cleanup function for not ready state
debugConfig.js:30 [useVapiCall] Using explicit assistantId without any overrides (including voice): f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:525 useVapiCall: Using explicit assistantId without overrides: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:1102 Requesting microphone permission...
useVapiCall.js?t=1749037189096:879 Setting up Vapi event listeners
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: call-start
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: call-end
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: speech-start
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: speech-end
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: message
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: error
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: volume-level
VapiService.js?t=1748990302238:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:551 SKIPPING direct event handler setup - using useVapiCall callbacks to prevent conflicts
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: null
VapiCall.jsx:1348 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1355 ⏱️ Using initialization delay of 500ms before starting call
VapiCall.jsx:1763 Status changed to: idle
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:538 Setting up call params with explicit assistantId and no overrides at all: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1469 VapiCall component unmounting, performing cleanup...
VapiCall.jsx:1484 Call was not fully initialized, performing simple cleanup
VapiCall.jsx:1494 Skipping onEndCall for non-initialized state to prevent mount/unmount cycle
VapiCall.jsx:1319 🚀 VapiCall component mounted, preparing to start call...
VapiCall.jsx:1320 📊 Current status: idle
VapiCall.jsx:1321 🤖 Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1322 🔧 Vapi instance available: true
VapiCall.jsx:1323 ⚙️ Processed config: Object
VapiCall.jsx:1348 ✅ Set window.vapiCallActive to initializing
VapiCall.jsx:1355 ⏱️ Using initialization delay of 500ms before starting call
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:1107 Available audio input devices: Array(5)
useVapiCall.js?t=1749037189096:1125 Using audio constraints: Object
useVapiCall.js?t=1749037189096:1129 Microphone permission granted with device: Default - Microphone (Yeti Classic)
useVapiCall.js?t=1749037189096:1177 Starting call with existing Vapi instance using direct pattern
vapiMcpDebugger.js:175 [Vapi MCP] POST https://api.vapi.ai/call/web
headers-fix.js:33 [HeadersFix] Fetch request to https://api.vapi.ai/call/web with headers: Object
VapiCall.jsx:1364 🎯 Auto-starting call from VapiCall component after delay
VapiCall.jsx:1365 📋 Current call parameters: Object
VapiCall.jsx:1402 🎯 [VAPI CALL INIT] Final assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:1403 ⚙️ [VAPI CALL INIT] Processed config: Object
VapiCall.jsx:1408 🔄 Starting call attempt 1
VapiCall.jsx:190 [VapiCall] Starting call with: Object
callDebugger.js:73 [CallDebugger:VapiCall] Starting call Object
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: idle -> connecting
useVapiCall.js?t=1749037189096:1217 [useVapiCall] Starting call with assistant: f9b97d13-f9c4-40af-a660-62ba5925ff2a
useVapiCall.js?t=1749037189096:1228 [useVapiCall] Starting call using official Vapi Web SDK pattern
useVapiCall.js?t=1749037189096:1230 [useVapiCall] Call started successfully: null
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: Object
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: Object
useVapiCall.js?t=1749037189096:884 Component unmounting - performing Vapi cleanup
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Event listeners removed from Vapi instance
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
useVapiCall.js?t=1749037189096:879 Setting up Vapi event listeners
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: call-start
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: call-end
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: speech-start
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: speech-end
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: message
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: error
VapiService.js?t=1748990302238:19 [VapiService] Setting up event listener for: volume-level
VapiService.js?t=1748990302238:19 [VapiService] Event listeners set up for Vapi instance
VapiCall.jsx:1763 Status changed to: connected
VapiCall.jsx:1764 CALL_STATUS.CONNECTED value: connected
VapiCall.jsx:1768 Call connected - checking for custom welcome message
VapiCall.jsx:1789 mergedCustomInstructions: Object
VapiCall.jsx:1790 mergedAssistantOverrides: Object
VapiCall.jsx:1819 Found welcome message from mergedCustomInstructions.welcomeMessage: Hello! How can I help you today?
VapiCall.jsx:1837 Adding welcome message to UI: Hello! How can I help you today?
VapiCall.jsx:1842 Welcome message should be spoken by the assistant automatically
VapiCall.jsx:1845 Voice settings: Object
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
SpeechParticles.jsx:17 SpeechParticles: Initializing component
SpeechParticles.jsx:28 SpeechParticles: Canvas found, loading script
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: Object
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: Object
SpeechParticles.jsx:129 SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead
speech-particles.js:10 Speech particles: DOM already loaded, setting up immediately
SpeechParticles.jsx:47 SpeechParticles: Script loaded successfully
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #aa4b4b Assistant: #46ce93
speech-particles.js:522 🎨 Speech particles: Setting custom colors Object
speech-particles.js:525 🎨 Converting user color: #aa4b4b
speech-particles.js:527 🎨 User HSL: Object
speech-particles.js:533 🎨 Speech particles: Updated user colors to Object
speech-particles.js:537 🎨 Converting assistant color: #46ce93
speech-particles.js:539 🎨 Assistant HSL: Object
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to Object
speech-particles.js:548 🎨 Final color palettes: Object
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
speech-particles.js:10 Speech particles: DOM already loaded, setting up immediately
SpeechParticles.jsx:47 SpeechParticles: Script loaded successfully
SpeechParticles.jsx:60 SpeechParticles: updateAudioSource function is available
SpeechParticles.jsx:64 🎨 SpeechParticles: About to set colors - User: #aa4b4b Assistant: #46ce93
speech-particles.js:522 🎨 Speech particles: Setting custom colors Object
speech-particles.js:525 🎨 Converting user color: #aa4b4b
speech-particles.js:527 🎨 User HSL: Object
speech-particles.js:533 🎨 Speech particles: Updated user colors to Object
speech-particles.js:537 🎨 Converting assistant color: #46ce93
speech-particles.js:539 🎨 Assistant HSL: Object
speech-particles.js:545 🎨 Speech particles: Updated assistant colors to Object
speech-particles.js:548 🎨 Final color palettes: Object
SpeechParticles.jsx:69 🎨 SpeechParticles: Colors configured successfully
SpeechParticles.jsx:89 SpeechParticles: Added user interaction listeners for audio context
VapiCall.jsx:1665 VapiCall: Scrolled conversation area to bottom
VapiCall.jsx:1775 Added force-visible class to call interface
speech-particles.js:309 Speech particles: Setting up visualization
speech-particles.js:318 Speech particles: Canvas found, initializing visualization
speech-particles.js:328 Speech particles: Visualization started
speech-particles.js:309 Speech particles: Setting up visualization
speech-particles.js:318 Speech particles: Canvas found, initializing visualization
speech-particles.js:328 Speech particles: Visualization started
VapiCall.jsx:1416 ✅ Marking call as initialized
VapiCall.jsx:1421 ✅ Set window.vapiCallActive to true after initialization
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: Object
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: Object
vapiMcpDebugger.js:180 [Vapi MCP] Response: 201 https://api.vapi.ai/call/web
headers-fix.js:33 [HeadersFix] Fetch request to https://c.daily.co/call-machine/versioned/0.72.2/static/call-machine-object-bundle.js with headers: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
VM1131:4 Preferred mic not found; skipping: true
value @ VM1131:4
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: signaling
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
VM1131:4 daily-js version 0.72.2 is nearing end of support. Please upgrade to a newer version.
value @ VM1131:4
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
useVapiCall.js?t=1749037189096:1181 Direct Vapi call started successfully: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: Object
useVapiCall.js?t=1749037189096:30 useVapiCall: Using assistant ID: f9b97d13-f9c4-40af-a660-62ba5925ff2a
VapiCall.jsx:2252 🎨 VapiCall: Rendering SpeechParticles with colors: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: sfu
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
useVapiCall.js?t=1749037189096:56 Call started - setting status to CONNECTED
useVapiCall.js?t=1749037189096:56 Call started - setting status to CONNECTED
useVapiCall.js?t=1749037189096:56 Call started - setting status to CONNECTED
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
useVapiCall.js?t=1749037189096:213 Received message: Object
useVapiCall.js?t=1749037189096:213 Received message: Object
useVapiCall.js?t=1749037189096:213 Received message: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
useVapiCall.js?t=1749037189096:213 Received message: Object
useVapiCall.js?t=1749037189096:213 Received message: Object
useVapiCall.js?t=1749037189096:213 Received message: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:510 SpeechParticles: Custom volume event received: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VapiCall.jsx:514 VapiCall: Dispatched volume change event: 0
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SpeechParticles.jsx:391 SpeechParticles: Processing audio level event: Object
SpeechParticles.jsx:410 SpeechParticles: Calculated assistant audio level: 0
speech-particles.js:470 Speech particles: updateAudioSource called with amplitude: null frequency: null speaker: null
speech-particles.js:480 Speech particles: Global state updated to externalAmplitude: null externalFrequency: null externalSpeaker: null
VM1131:4 Meeting ended due to ejection: Meeting has ended
value @ VM1131:4
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
useVapiCall.js?t=1749037189096:94 Vapi error: Object
onError @ useVapiCall.js?t=1749037189096:94
useVapiCall.js?t=1749037189096:95 Continuing despite Vapi error
onError @ useVapiCall.js?t=1749037189096:95
useVapiCall.js?t=1749037189096:94 Vapi error: Object
onError @ useVapiCall.js?t=1749037189096:94
useVapiCall.js?t=1749037189096:95 Continuing despite Vapi error
onError @ useVapiCall.js?t=1749037189096:95
useVapiCall.js?t=1749037189096:94 Vapi error: Object
onError @ useVapiCall.js?t=1749037189096:94
useVapiCall.js?t=1749037189096:95 Continuing despite Vapi error
onError @ useVapiCall.js?t=1749037189096:95
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
useVapiCall.js?t=1749037189096:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended Object
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: connecting -> ended
EnhancedPreviewNew.jsx:486 Ending call...
useVapiCall.js?t=1749037189096:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended Object
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
useVapiCall.js?t=1749037189096:78 Call ended
callDebugger.js:73 [CallDebugger:VapiCall] Call ended Object
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: Object
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: other
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: undefined
VapiCall.jsx:294 Global iframe message received: Object
VapiCall.jsx:317 Window message received for processing: Object
SpeechParticles.jsx:384 SpeechParticles: Received window message event: Object
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
useVapiCall.js?t=1749037189096:884 Component unmounting - performing Vapi cleanup
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for call-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for call-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for speech-start (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for speech-end (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for message (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for error (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Could not remove event listener for volume-level (not critical): The "listener" argument must be of type Function. Received type undefined
VapiService.js?t=1748990302238:19 [VapiService] Event listeners removed from Vapi instance
useVapiCall.js?t=1749037189096:893 Stopping active call during cleanup
useVapiCall.js?t=1749037189096:899 Calling onEndCall callback during unmount
callDebugger.js:73 [CallDebugger:VapiCall] Call ended Object
callDebugger.js:75 [CallDebugger:VapiCall] Call status changed: ended -> ended
EnhancedPreviewNew.jsx:486 Ending call...
VapiCall.jsx:1469 VapiCall component unmounting, performing cleanup...
VapiCall.jsx:1473 Call was still initializing during unmount, cancelling initialization
VapiCall.jsx:1478 Skipping onEndCall during initialization cancellation to prevent mount/unmount cycle
SpeechParticles.jsx:106 SpeechParticles: Cleaning up component
EnhancedPreviewNew.jsx:460 [EnhancedPreviewNew] State updated:
EnhancedPreviewNew.jsx:461 firmName: LegalScout
EnhancedPreviewNew.jsx:462 titleText: LegalScout
EnhancedPreviewNew.jsx:463 logoUrl: 
EnhancedPreviewNew.jsx:464 primaryColor: #aa4b4b
EnhancedPreviewNew.jsx:465 secondaryColor: #46ce93
EnhancedPreviewNew.jsx:466 vapiInstructions: You are a legal assistant helping clients with their legal needs.
EnhancedPreviewNew.jsx:467 vapiAssistantId: f9b97d13-f9c4-40af-a660-62ba5925ff2a
EnhancedPreviewNew.jsx:468 voiceId: sarah
EnhancedPreviewNew.jsx:469 voiceProvider: 11labs
EnhancedPreviewNew.jsx:470 chatActive: false
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
