/**
 * Test Runner for Dashboard Session Agent Change Fixes
 * 
 * Validates that our surgical fixes are working correctly
 */

(function() {
  'use strict';

  const TestRunner = {
    results: {},
    
    log: function(test, status, message, data = null) {
      if (!this.results[test]) {
        this.results[test] = [];
      }
      
      this.results[test].push({
        status,
        message,
        data,
        timestamp: new Date().toISOString()
      });
      
      const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
      console.log(`${emoji} [Test:${test}] ${message}`, data || '');
    },

    // Test iframe detection and communication
    testIframeSystem: function() {
      const test = 'IframeSystem';
      
      // Test 1: Iframe manager exists
      if (!window.DashboardIframeManager) {
        this.log(test, 'FAIL', 'DashboardIframeManager not available');
        return;
      }
      this.log(test, 'PASS', 'DashboardIframeManager available');
      
      // Test 2: Can find iframes
      const iframeCount = window.DashboardIframeManager.getIframeCount();
      if (iframeCount === 0) {
        this.log(test, 'WARN', 'No iframes detected (may be normal if no preview loaded)');
      } else {
        this.log(test, 'PASS', `Found ${iframeCount} preview iframes`);
      }
      
      // Test 3: Can send config
      if (typeof window.DashboardIframeManager.sendConfigToPreview === 'function') {
        this.log(test, 'PASS', 'sendConfigToPreview function available');
      } else {
        this.log(test, 'FAIL', 'sendConfigToPreview function missing');
      }
    },

    // Test assistant selection functionality
    testAssistantSelection: function() {
      const test = 'AssistantSelection';
      
      // Test 1: Handler exists
      if (!window.handleAssistantSelection) {
        this.log(test, 'FAIL', 'handleAssistantSelection function not available');
        return;
      }
      this.log(test, 'PASS', 'handleAssistantSelection function available');
      
      // Test 2: Attorney data available
      let attorney = null;
      if (window.standaloneAttorneyManager && !window.standaloneAttorneyManager.disabled) {
        attorney = window.standaloneAttorneyManager.attorney;
      } else {
        try {
          const stored = localStorage.getItem('attorney');
          if (stored) {
            attorney = JSON.parse(stored);
          }
        } catch (error) {
          // Ignore
        }
      }
      
      if (!attorney) {
        this.log(test, 'WARN', 'No attorney data available for testing');
        return;
      }
      this.log(test, 'PASS', 'Attorney data available for testing');
      
      // Test 3: Current assistant ID
      if (attorney.vapi_assistant_id) {
        this.log(test, 'PASS', `Current assistant ID: ${attorney.vapi_assistant_id}`);
      } else {
        this.log(test, 'WARN', 'No assistant ID set for attorney');
      }
    },

    // Test state propagation
    testStatePropagation: function() {
      const test = 'StatePropagation';
      
      // Test 1: Manager state sync
      if (window.standaloneAttorneyManager && !window.standaloneAttorneyManager.disabled) {
        const managerAttorney = window.standaloneAttorneyManager.attorney;
        const storedAttorney = localStorage.getItem('attorney');
        
        if (managerAttorney && storedAttorney) {
          try {
            const stored = JSON.parse(storedAttorney);
            if (managerAttorney.id === stored.id && 
                managerAttorney.vapi_assistant_id === stored.vapi_assistant_id) {
              this.log(test, 'PASS', 'Manager and localStorage in sync');
            } else {
              this.log(test, 'WARN', 'Manager and localStorage out of sync');
            }
          } catch (error) {
            this.log(test, 'FAIL', 'Corrupted localStorage data');
          }
        } else {
          this.log(test, 'WARN', 'Missing manager or localStorage data');
        }
      } else {
        this.log(test, 'WARN', 'StandaloneAttorneyManager not available');
      }
      
      // Test 2: Event listeners
      const hasAttorneyListener = window.addEventListener.toString().includes('attorneyStateChanged');
      if (hasAttorneyListener) {
        this.log(test, 'PASS', 'Attorney state change listeners detected');
      } else {
        this.log(test, 'WARN', 'No attorney state change listeners detected');
      }
    },

    // Test React integration
    testReactIntegration: function() {
      const test = 'ReactIntegration';
      
      // Test 1: React elements present
      const reactElements = document.querySelectorAll('[data-reactroot], [class*="react"], [class*="React"]');
      if (reactElements.length > 0) {
        this.log(test, 'PASS', `Found ${reactElements.length} React elements`);
      } else {
        this.log(test, 'WARN', 'No React elements detected');
      }
      
      // Test 2: Event dispatching capability
      try {
        const testEvent = new CustomEvent('testEvent', { detail: { test: true } });
        window.dispatchEvent(testEvent);
        this.log(test, 'PASS', 'Event dispatching functional');
      } catch (error) {
        this.log(test, 'FAIL', 'Event dispatching failed', error.message);
      }
    },

    // Test API connectivity
    testAPIConnectivity: function() {
      const test = 'APIConnectivity';
      
      // Test 1: Supabase availability
      if (window.supabase) {
        this.log(test, 'PASS', 'Supabase client available');
        
        // Test basic connectivity (non-destructive)
        window.supabase.from('attorneys').select('count', { count: 'exact', head: true })
          .then(result => {
            if (result.error) {
              this.log(test, 'WARN', 'Supabase connectivity issues', result.error.message);
            } else {
              this.log(test, 'PASS', 'Supabase connectivity confirmed');
            }
          })
          .catch(error => {
            this.log(test, 'WARN', 'Supabase test failed', error.message);
          });
      } else {
        this.log(test, 'FAIL', 'Supabase client not available');
      }
      
      // Test 2: Vapi service availability
      if (window.vapiServiceManager) {
        this.log(test, 'PASS', 'Vapi service manager available');
      } else {
        this.log(test, 'WARN', 'Vapi service manager not available');
      }
    },

    // Simulate assistant change to test end-to-end functionality
    testAssistantChange: function() {
      const test = 'AssistantChange';
      
      if (!window.handleAssistantSelection) {
        this.log(test, 'FAIL', 'Cannot test - handleAssistantSelection not available');
        return;
      }
      
      // Get current attorney
      let attorney = null;
      if (window.standaloneAttorneyManager && !window.standaloneAttorneyManager.disabled) {
        attorney = window.standaloneAttorneyManager.attorney;
      } else {
        try {
          const stored = localStorage.getItem('attorney');
          if (stored) {
            attorney = JSON.parse(stored);
          }
        } catch (error) {
          // Ignore
        }
      }
      
      if (!attorney || !attorney.vapi_assistant_id) {
        this.log(test, 'WARN', 'Cannot test - no attorney with assistant ID available');
        return;
      }
      
      // Store original assistant ID
      const originalAssistantId = attorney.vapi_assistant_id;
      const testAssistantId = 'test-' + Date.now();
      
      this.log(test, 'INFO', `Testing assistant change from ${originalAssistantId} to ${testAssistantId}`);
      
      // Test the change
      try {
        const result = window.handleAssistantSelection(testAssistantId);
        
        if (result) {
          this.log(test, 'PASS', 'Assistant selection function executed successfully');
          
          // Verify the change
          setTimeout(() => {
            let updatedAttorney = null;
            if (window.standaloneAttorneyManager && !window.standaloneAttorneyManager.disabled) {
              updatedAttorney = window.standaloneAttorneyManager.attorney;
            } else {
              try {
                const stored = localStorage.getItem('attorney');
                if (stored) {
                  updatedAttorney = JSON.parse(stored);
                }
              } catch (error) {
                // Ignore
              }
            }
            
            if (updatedAttorney && updatedAttorney.vapi_assistant_id === testAssistantId) {
              this.log(test, 'PASS', 'Assistant ID updated successfully');
              
              // Restore original assistant ID
              window.handleAssistantSelection(originalAssistantId);
              this.log(test, 'INFO', 'Restored original assistant ID');
            } else {
              this.log(test, 'FAIL', 'Assistant ID not updated in state');
            }
          }, 500);
          
        } else {
          this.log(test, 'FAIL', 'Assistant selection function returned false');
        }
      } catch (error) {
        this.log(test, 'FAIL', 'Assistant selection function threw error', error.message);
      }
    },

    // Run all tests
    runAllTests: function() {
      console.log('🧪 Starting Test Runner for Dashboard Session Agent Change Fixes...');
      
      this.testIframeSystem();
      this.testAssistantSelection();
      this.testStatePropagation();
      this.testReactIntegration();
      this.testAPIConnectivity();
      
      // Run the end-to-end test after a delay
      setTimeout(() => {
        this.testAssistantChange();
        this.generateTestReport();
      }, 1000);
    },

    // Generate test report
    generateTestReport: function() {
      console.log('\n📋 TEST REPORT');
      console.log('='.repeat(50));
      
      let totalTests = 0;
      let passedTests = 0;
      let failedTests = 0;
      let warnedTests = 0;
      
      Object.entries(this.results).forEach(([testName, results]) => {
        console.log(`\n${testName}:`);
        
        results.forEach(result => {
          totalTests++;
          const emoji = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
          console.log(`  ${emoji} ${result.message}`);
          
          if (result.status === 'PASS') passedTests++;
          else if (result.status === 'FAIL') failedTests++;
          else warnedTests++;
        });
      });
      
      console.log('\n📊 SUMMARY:');
      console.log(`  Total Tests: ${totalTests}`);
      console.log(`  ✅ Passed: ${passedTests}`);
      console.log(`  ❌ Failed: ${failedTests}`);
      console.log(`  ⚠️ Warnings: ${warnedTests}`);
      
      const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
      console.log(`  📈 Success Rate: ${successRate}%`);
      
      if (failedTests === 0) {
        console.log('\n🎉 All critical tests passed! Fixes appear to be working.');
      } else if (failedTests <= 2) {
        console.log('\n⚡ Most tests passed. Minor issues detected.');
      } else {
        console.log('\n🚨 Multiple test failures. Additional fixes may be needed.');
      }
      
      // Store results
      window.testResults = this.results;
      window.testSummary = { totalTests, passedTests, failedTests, warnedTests, successRate };
    }
  };

  // Auto-run tests when script loads
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => TestRunner.runAllTests(), 3000);
    });
  } else {
    setTimeout(() => TestRunner.runAllTests(), 3000);
  }

  // Expose for manual testing
  window.TestRunner = TestRunner;

})();
