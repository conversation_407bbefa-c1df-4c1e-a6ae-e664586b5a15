 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
 [DashboardIframeManager] Iframe observer set up
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
 [DashboardIframeManager] Iframe observer set up
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: [{…}]
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: [{…}]
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [UnifiedBannerFix] Ensuring upload interface is visible
 [UnifiedBannerFix] Ensuring upload interface is visible
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/user with headers: {Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', Accept: 'application/json', Content-Type: 'application/json', Prefer: 'return=representation', …}
 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ installHook.js:1
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:523
(anonymous) @ DashboardNew.jsx:391
basicStateReducer @ chunk-Q72EVS5P.js:11723
updateReducer @ chunk-Q72EVS5P.js:11814
updateState @ chunk-Q72EVS5P.js:12041
useState @ chunk-Q72EVS5P.js:12773
useState @ chunk-2N3A5BUM.js:1066
DashboardNew @ DashboardNew.jsx:68
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ installHook.js:1
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:523
(anonymous) @ DashboardNew.jsx:391
basicStateReducer @ chunk-Q72EVS5P.js:11723
updateReducer @ chunk-Q72EVS5P.js:11814
updateState @ chunk-Q72EVS5P.js:12041
useState @ chunk-Q72EVS5P.js:12773
useState @ chunk-2N3A5BUM.js:1066
DashboardNew @ DashboardNew.jsx:68
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ installHook.js:1
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:523
(anonymous) @ DashboardNew.jsx:391
basicStateReducer @ chunk-Q72EVS5P.js:11723
updateReducer @ chunk-Q72EVS5P.js:11814
updateState @ chunk-Q72EVS5P.js:12041
useState @ chunk-Q72EVS5P.js:12773
useState @ chunk-2N3A5BUM.js:1066
DashboardNew @ DashboardNew.jsx:68
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardIframeManager] Message timeout, retrying (1/3)
overrideMethod @ installHook.js:1
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:81
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
(anonymous) @ dashboard-iframe-manager.js:77
sendMessageToIframe @ dashboard-iframe-manager.js:53
(anonymous) @ dashboard-iframe-manager.js:118
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:523
(anonymous) @ DashboardNew.jsx:391
basicStateReducer @ chunk-Q72EVS5P.js:11723
updateReducer @ chunk-Q72EVS5P.js:11814
updateState @ chunk-Q72EVS5P.js:12041
useState @ chunk-Q72EVS5P.js:12773
useState @ chunk-2N3A5BUM.js:1066
DashboardNew @ DashboardNew.jsx:68
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.bafd37ba-a143-40ea-bcf5-e25fe149b55e with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 Headers fix applied to fetch
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Running in development mode
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: dev-1749067284651
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Setting up Realtime subscription for attorney: dev-1749067284651
 [AttorneyProfileManager] Realtime subscription set up using channel API
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Found attorney in localStorage: dev-1749067284651
 [initAttorneyProfileManager] Initialization complete
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ installHook.js:1
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:523
(anonymous) @ DashboardNew.jsx:391
basicStateReducer @ chunk-Q72EVS5P.js:11723
updateReducer @ chunk-Q72EVS5P.js:11814
updateState @ chunk-Q72EVS5P.js:12041
useState @ chunk-Q72EVS5P.js:12773
useState @ chunk-2N3A5BUM.js:1066
DashboardNew @ DashboardNew.jsx:68
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ installHook.js:1
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:523
(anonymous) @ DashboardNew.jsx:391
basicStateReducer @ chunk-Q72EVS5P.js:11723
updateReducer @ chunk-Q72EVS5P.js:11814
updateState @ chunk-Q72EVS5P.js:12041
useState @ chunk-Q72EVS5P.js:12773
useState @ chunk-2N3A5BUM.js:1066
DashboardNew @ DashboardNew.jsx:68
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardNew] Config sent to 0 iframes successfully
 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ installHook.js:1
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:523
(anonymous) @ DashboardNew.jsx:391
basicStateReducer @ chunk-Q72EVS5P.js:11723
updateReducer @ chunk-Q72EVS5P.js:11814
updateState @ chunk-Q72EVS5P.js:12041
useState @ chunk-Q72EVS5P.js:12773
useState @ chunk-2N3A5BUM.js:1066
DashboardNew @ DashboardNew.jsx:68
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardIframeManager] Failed to send config to iframe: Iframe or contentWindow not available
overrideMethod @ installHook.js:1
log @ dashboard-iframe-manager.js:29
(anonymous) @ dashboard-iframe-manager.js:124
Promise.catch
(anonymous) @ dashboard-iframe-manager.js:123
sendConfigToPreviewIframes @ dashboard-iframe-manager.js:117
sendConfigToPreview @ dashboard-iframe-manager.js:224
sendConfigToPreviewIframe @ DashboardNew.jsx:523
(anonymous) @ DashboardNew.jsx:391
basicStateReducer @ chunk-Q72EVS5P.js:11723
updateReducer @ chunk-Q72EVS5P.js:11814
updateState @ chunk-Q72EVS5P.js:12041
useState @ chunk-Q72EVS5P.js:12773
useState @ chunk-2N3A5BUM.js:1066
DashboardNew @ DashboardNew.jsx:68
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardNew] Config sent to 0 iframes successfully
 [DisableAutomaticAssistantCreation] Services found, applying patches
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [ReactPolyfill] Stopped monitoring React.createContext
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 received intentional event
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
 [DashboardNew] Tab changed to: agent
 [AgentTab] Loading voice settings from Vapi assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [AgentTab] Loading voice settings from Vapi assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [AutoReconciler] Invalid attorney object provided: []
overrideMethod @ installHook.js:1
reconcileAttorney @ AutoAssistantReconciler.js:147
checkAndFixCurrentUser @ AutoAssistantReconciler.js:262
await in checkAndFixCurrentUser
(anonymous) @ AutoAssistantReconciler.js:280
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
initializeForDashboard @ AutoAssistantReconciler.js:279
(anonymous) @ DashboardNew.jsx:118
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [UnifiedBannerFix] Ensuring upload interface is visible
 [UnifiedBannerFix] Ensuring upload interface is visible
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 Headers fix applied to fetch
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Running in development mode
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: dev-1749067284651
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Setting up Realtime subscription for attorney: dev-1749067284651
 [AttorneyProfileManager] Realtime subscription set up using channel API
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Found attorney in localStorage: dev-1749067284651
 [initAttorneyProfileManager] Initialization complete
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067480047}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067480051}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DisableAutomaticAssistantCreation] Services found, applying patches
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Ensuring upload interface is visible
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 ✅ Vapi public key set globally
 ✅ Supabase keys set globally - should load correct assistant by domain
 [VapiMcpService] Getting assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [VapiMcpService] Using direct API to get assistant
 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [HeadersFix] Fetch request to /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c → http://localhost:5175/api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [20:04:41] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
 [Vapi MCP] GET https://api.vapi.ai/assistant
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
 [VapiMcpService] Getting assistant: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [VapiMcpService] Using direct API to get assistant
 [VapiMcpService] Trying to get assistant from: /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [HeadersFix] Fetch request to /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c → http://localhost:5175/api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [20:04:41] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
 [EnhancedVapiMcpService] Testing endpoint: https://api.vapi.ai/assistant
 [Vapi MCP] GET https://api.vapi.ai/assistant
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067480572}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067480574}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 updating page active status
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481147}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481147}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...
 [ConsolidatedDashboardFix] Fixing DOM manipulation...
 [ConsolidatedDashboardFix] Fixing CSP eval blocking...
 [ConsolidatedDashboardFix] Fixing CORS issues...
 [ConsolidatedDashboardFix] Preventing duplicate assistant creation...
 [ConsolidatedDashboardFix] Fixing banner issues...
 [ConsolidatedDashboardFix] Fixing React context issues...
 [ConsolidatedDashboardFix] Fixing CSP issues...
 [ConsolidatedDashboardFix] Ensuring environment variables...
 [ConsolidatedDashboardFix] ✅ All fixes applied successfully
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481275}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481275}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 🚀 [EMERGENCY] Starting emergency critical fixes...
 🔧 [EMERGENCY] Adding process polyfill
 ✅ [EMERGENCY] Process polyfill added
 🔧 [EMERGENCY] Development mode: false (forced production)
 ✅ [EMERGENCY] Fetch patched
 🎉 [EMERGENCY] Emergency fixes complete!
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481338}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481338}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481342}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481342}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 Supabase connection test successful!
  GET http://localhost:5175/api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 404 (Not Found)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ disable-automatic-as…ant-creation.js:189
getAssistant @ vapiMcpService.js:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Failed to get assistant from /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 404
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:424
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Trying to get assistant from: https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DisableAutomaticAssistantCreation] Starting fix...
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation disabled
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481450}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481450}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481463}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481463}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [UnifiedBannerFix] Ensuring upload interface is visible
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
 [20:04:41] [VapiMcpService] Retrieving assistant {assistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c'}
 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
 [ControlledAssistantCreation] Starting controlled assistant creation system...
 [ControlledAssistantCreation] Controlled assistant creation system ready
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
  GET http://localhost:5175/api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 404 (Not Found)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ disable-automatic-as…ant-creation.js:189
getAssistant @ vapiMcpService.js:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Failed to get assistant from /api/vapi-proxy/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 404
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:424
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Trying to get assistant from: https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [Vapi MCP] Response: 200 https://api.vapi.ai/assistant
 [EnhancedVapiMcpService] Found working endpoint: https://api.vapi.ai
 [20:04:41] [VapiMcpService] Retrieving assistant {assistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c'}
 [Vapi MCP] GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [HeadersFix] Fetch request to https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564', Content-Type: 'application/json', Accept: 'application/json'}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [StandaloneAttorneyManager] Initializing...
 [StandaloneAttorneyManager] Using cached Vapi configuration
 [StandaloneAttorneyManager] Global instance created
 [StandaloneAttorneyManager] Current subdomain: default
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [FixValidateAttorneyData] Starting fix...
 [FixValidateAttorneyData] validateAttorneyData method already exists
 [FixValidateAttorneyData] Fix applied immediately
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.dev-1749067284651 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [FixVapiAssistantSwitch] Starting fix...
 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
 [FixVapiAssistantSwitch] Fix applied successfully
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.dev-1749067284651 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [EnhanceAttorneyManager] Starting enhancement...
 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
  GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:46
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ disable-automatic-as…ant-creation.js:189
getAssistant @ vapiMcpService.js:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [FixEnhanceAttorneyManager] Starting fix...
 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
 [FixEnhanceAttorneyManager] Fix applied successfully
 [Vapi MCP] Response: 401 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 401
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:424
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Trying to get assistant from: https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [HeadersFix] Fetch request to https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481747}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481747}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481747}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481752}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481752}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481752}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481784}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481784}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481784}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Development Law Firm
 titleText: Development Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481806}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481806}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481806}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.dev-1749067284651 400 (Bad Request)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
query.then @ supabase.js:197
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481864}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481864}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067481864}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [AgentTab] Error loading assistant mappings: {code: '22P02', details: null, hint: null, message: 'invalid input syntax for type uuid: "dev-1749067284651"'}
overrideMethod @ installHook.js:1
loadAvailableAssistants @ AgentTab.jsx:1253
await in loadAvailableAssistants
(anonymous) @ AgentTab.jsx:1286
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorney_assistants?select=assistant_id&attorney_id=eq.dev-1749067284651 400 (Bad Request)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
(anonymous) @ @supabase_supabase-js.js:3900
(anonymous) @ @supabase_supabase-js.js:3921
fulfilled @ @supabase_supabase-js.js:3873
Promise.then
step @ @supabase_supabase-js.js:3886
(anonymous) @ @supabase_supabase-js.js:3888
__awaiter6 @ @supabase_supabase-js.js:3870
(anonymous) @ @supabase_supabase-js.js:3911
then @ @supabase_supabase-js.js:89
query.then @ supabase.js:197
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
  GET https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c net::ERR_FAILED
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ disable-automatic-as…ant-creation.js:189
getAssistant @ vapiMcpService.js:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [ConsolidatedDashboardFix] Fetch error caught: 
overrideMethod @ installHook.js:1
(anonymous) @ consolidated-dashboard-fix.js:210
Promise.catch
window.fetch @ consolidated-dashboard-fix.js:209
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ disable-automatic-as…ant-creation.js:189
getAssistant @ vapiMcpService.js:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:427
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Assistant not found with ID: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:432
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [FixVapiAssistantConfig] Starting fix...
 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
 [FixVapiAssistantConfig] Sending update request to Vapi API...
 [FixVapiAssistantConfig] Fix script loaded
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [AgentTab] Error loading assistant mappings: {code: '22P02', details: null, hint: null, message: 'invalid input syntax for type uuid: "dev-1749067284651"'}
overrideMethod @ installHook.js:1
loadAvailableAssistants @ AgentTab.jsx:1253
await in loadAvailableAssistants
(anonymous) @ AgentTab.jsx:1286
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [UnifiedBannerFix] Starting unified banner fix...
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [UnifiedBannerFix] Unified banner fix script loaded
 [DashboardIframeManager] Initializing Dashboard Iframe Manager
 [ConsolidatedDashboardFix] Invalid MutationObserver target, skipping
MutationObserver.observe @ consolidated-dashboard-fix.js:25
setupIframeObserver @ dashboard-iframe-manager.js:187
(anonymous) @ dashboard-iframe-manager.js:242
(anonymous) @ dashboard-iframe-manager.js:266
 [DashboardIframeManager] Iframe observer set up
 [DashboardIframeManager] Found 0 potential preview iframes
 [DashboardIframeManager] Found 0 accessible preview iframes
 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
 Supabase loaded from CDN
 Creating Supabase client from CDN
 Supabase client created from CDN
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
 [FixVapiAssistantConfig] - model.messages: [{…}]
 Supabase connection test successful!
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067482953}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067482953}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067482953}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067482953}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067482955}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067482955}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067482955}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067482955}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Development Law Firm
 titleText: Development Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067483103}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067483103}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067483103}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067483103}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [UnifiedBannerFix] Ensuring upload interface is visible
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 ✅ Supabase connection test successful!
 Supabase is properly configured and connected! [{…}]
  GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 404 (Not Found)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:46
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ disable-automatic-as…ant-creation.js:189
(anonymous) @ EnhancedVapiMcpService.js:259
withRetry @ EnhancedVapiMcpService.js:230
getAssistant @ EnhancedVapiMcpService.js:257
loadVapiVoiceSettings @ AgentTab.jsx:408
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:431
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
  GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 401 (Unauthorized)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:46
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ disable-automatic-as…ant-creation.js:189
getAssistant @ vapiMcpService.js:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [Vapi MCP] Response: 404 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [20:04:43] [VapiMcpService] Assistant not found in Vapi {assistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c'}
overrideMethod @ installHook.js:1
log @ vapiLogger.js:100
assistantNotFound @ vapiLogger.js:141
(anonymous) @ EnhancedVapiMcpService.js:269
await in (anonymous)
withRetry @ EnhancedVapiMcpService.js:230
getAssistant @ EnhancedVapiMcpService.js:257
loadVapiVoiceSettings @ AgentTab.jsx:408
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:431
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 ⚠️ [AgentTab] No voice settings found in Vapi assistant
 [Vapi MCP] Response: 401 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [VapiMcpService] Failed to get assistant from https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c: 401
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:424
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Trying to get assistant from: https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [HeadersFix] Fetch request to https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c with headers: {Authorization: 'Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7', Content-Type: 'application/json', Accept: 'application/json'}
  GET https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c 404 (Not Found)
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:46
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ disable-automatic-as…ant-creation.js:189
(anonymous) @ EnhancedVapiMcpService.js:259
withRetry @ EnhancedVapiMcpService.js:230
getAssistant @ EnhancedVapiMcpService.js:257
loadVapiVoiceSettings @ AgentTab.jsx:408
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:431
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [Vapi MCP] Response: 404 https://api.vapi.ai/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [20:04:43] [VapiMcpService] Assistant not found in Vapi {assistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c'}
overrideMethod @ installHook.js:1
log @ vapiLogger.js:100
assistantNotFound @ vapiLogger.js:141
(anonymous) @ EnhancedVapiMcpService.js:269
await in (anonymous)
withRetry @ EnhancedVapiMcpService.js:230
getAssistant @ EnhancedVapiMcpService.js:257
loadVapiVoiceSettings @ AgentTab.jsx:408
await in loadVapiVoiceSettings
(anonymous) @ AgentTab.jsx:431
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 ⚠️ [AgentTab] No voice settings found in Vapi assistant
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
  GET https://dashboard.vapi.ai/api/assistant/8d962209-530e-45d2-b2d6-17ed1ef55b3c net::ERR_FAILED
window.fetch @ consolidated-dashboard-fix.js:206
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ disable-automatic-as…ant-creation.js:189
getAssistant @ vapiMcpService.js:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [ConsolidatedDashboardFix] Fetch error caught: 
overrideMethod @ installHook.js:1
(anonymous) @ consolidated-dashboard-fix.js:210
Promise.catch
window.fetch @ consolidated-dashboard-fix.js:209
window.fetch @ dashboard:99
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ headers-fix.js:36
window.fetch @ vapiNetworkInterceptor.js:34
window.fetch @ disable-automatic-as…ant-creation.js:189
window.fetch @ disable-automatic-as…ant-creation.js:189
getAssistant @ vapiMcpService.js:409
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Error getting assistant from endpoint: Failed to fetch
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:427
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [VapiMcpService] Assistant not found with ID: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
overrideMethod @ installHook.js:1
getAssistant @ vapiMcpService.js:432
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:53
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:44
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
commitRootImpl @ chunk-Q72EVS5P.js:19444
commitRoot @ chunk-Q72EVS5P.js:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js:9135
(anonymous) @ chunk-Q72EVS5P.js:18655
 [ReactPolyfill] Created window.React object
 [ReactPolyfill] Added Children to window.React
 [ReactPolyfill] Added Component to window.React
 [ReactPolyfill] Added Fragment to window.React
 [ReactPolyfill] Added Profiler to window.React
 [ReactPolyfill] Added PureComponent to window.React
 [ReactPolyfill] Added StrictMode to window.React
 [ReactPolyfill] Added Suspense to window.React
 [ReactPolyfill] Added __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED to window.React
 [ReactPolyfill] Added act to window.React
 [ReactPolyfill] Added cloneElement to window.React
 [ReactPolyfill] Added createContext to window.React
 [ReactPolyfill] Added createElement to window.React
 [ReactPolyfill] Added createFactory to window.React
 [ReactPolyfill] Added createRef to window.React
 [ReactPolyfill] Added forwardRef to window.React
 [ReactPolyfill] Added isValidElement to window.React
 [ReactPolyfill] Added lazy to window.React
 [ReactPolyfill] Added memo to window.React
 [ReactPolyfill] Added startTransition to window.React
 [ReactPolyfill] Added unstable_act to window.React
 [ReactPolyfill] Added useCallback to window.React
 [ReactPolyfill] Added useContext to window.React
 [ReactPolyfill] Added useDebugValue to window.React
 [ReactPolyfill] Added useDeferredValue to window.React
 [ReactPolyfill] Added useEffect to window.React
 [ReactPolyfill] Added useId to window.React
 [ReactPolyfill] Added useImperativeHandle to window.React
 [ReactPolyfill] Added useInsertionEffect to window.React
 [ReactPolyfill] Added useLayoutEffect to window.React
 [ReactPolyfill] Added useMemo to window.React
 [ReactPolyfill] Added useReducer to window.React
 [ReactPolyfill] Added useRef to window.React
 [ReactPolyfill] Added useState to window.React
 [ReactPolyfill] Added useSyncExternalStore to window.React
 [ReactPolyfill] Added useTransition to window.React
 [ReactPolyfill] Added version to window.React
 [ReactPolyfill] Created global LayoutGroupContext
 [ReactPolyfill] Enhanced React polyfill applied successfully
 Headers fix applied to fetch
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}
 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase Key configured: eyJhb...K4cRU
 Development mode detected, using fallback Supabase configuration
 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 Supabase client initialized successfully with proper headers
 Testing Supabase connection...
 Running in development mode
 Attaching Supabase client to window.supabase
 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000}
 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
 [AttorneyProfileManager] Loaded attorney from localStorage: dev-1749067284651
 [AttorneyProfileManager] Auto-initializing from localStorage
 [AttorneyProfileManager] Setting up Realtime subscription for attorney: dev-1749067284651
 [AttorneyProfileManager] Realtime subscription set up using channel API
 [initAttorneyProfileManager] Initializing attorney profile manager
 [initAttorneyProfileManager] Initializing Vapi service manager
 [VapiServiceManager] Initializing with API key: **** (Attorney Dashboard: false, Preview: false, Production: false, Force MCP: false, Force Direct: false, Fast Loading: undefined)
 [VapiServiceManager] Trying to connect to real Vapi service
 [VapiMcpService] Production mode: false
 [VapiMcpService] Development mode: true
 [VapiMcpService] Using API key for MCP server operations: 310f0d43...
 [VapiMcpService] Using direct API mode for development or forced direct mode
 [VapiMcpService] API key being used: 310f0d43...
 Environment Variable Verification
 All required variables present: ✅ Yes
 Variables status:
 - VITE_SUPABASE_URL: ✅ Present (https://utopqxsvudgrtiwenlzl.supabase.co)
 - VITE_SUPABASE_KEY: ✅ Present (****)
 - VITE_VAPI_PUBLIC_KEY: ✅ Present (****)
 [Vapi Network Interceptor] Installed
 [Vapi MCP] Environment Check
 (index)Value(index)ValueVITE_VAPI_PUBLIC_KEY'Set'VITE_VAPI_SECRET_KEY'Set'VAPI_TOKEN'Not set'localStorage_vapi_api_key'Not set'isDevelopmenttrueorigin'http://localhost:5175'Object
 🔧 Vapi Debugger initialized. Type VapiDebug.help() for available commands.
 [VapiServiceManager] Connected to Vapi MCP server
 [initAttorneyProfileManager] Vapi service manager initialized: {initialized: true, useMock: false, connected: true, connectionMode: 'direct'}
 [initAttorneyProfileManager] Connected to Vapi using direct mode
 [initAttorneyProfileManager] Found attorney in localStorage: dev-1749067284651
 [initAttorneyProfileManager] Initialization complete
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [DisableAutomaticAssistantCreation] Services found, applying patches
 [DisableAutomaticAssistantCreation] Patched vapiMcpService
 [DisableAutomaticAssistantCreation] DISABLED - Allowing real assistant creation
 [DisableAutomaticAssistantCreation] Global createVapiAssistant patching DISABLED - robust handler will manage creation
 [DisableAutomaticAssistantCreation] Patched fetch calls
 [DisableAutomaticAssistantCreation] All patches applied successfully
 [UnifiedBannerFix] Simple banner system created
 [UnifiedBannerFix] Event listeners set up
 [UnifiedBannerFix] Interface state monitoring started
 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
 [UnifiedBannerFix] Unified banner fix initialized successfully
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [App] App component unmounted undefined
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'default', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading from Supabase for subdomain: default
 SimplePreviewPage: Falling back to direct Supabase loading
 SimplePreviewPage: Loading attorney data from Supabase for subdomain: default
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [App] App component mounted undefined
 🔧 Development mode detected, initializing Supabase configuration...
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 === SUPABASE CONFIG TEST ===
 Supabase URL configured: true
 Supabase Key configured: true
 Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
 🔍 Verifying Supabase configuration...
 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: '[HIDDEN]', VITE_SUPABASE_ANON_KEY: '[HIDDEN]', REACT_APP_SUPABASE_KEY: '[HIDDEN]', …}
 Testing Supabase connection...
 [App] Subdomain detected: default
 [App] Is attorney subdomain: false
 🏠 [App.jsx] This is not an attorney subdomain, skipping profile load
 Using real authentication in all environments
 [DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 Auth state changed: SIGNED_IN
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 AuthContext: Handling auth state change for event: SIGNED_IN
 SyncContext: Handling auth state for action: login
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485287}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485287}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485287}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485287}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485287}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485294}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485294}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485294}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485294}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485294}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [UnifiedBannerFix] Ensuring upload interface is visible
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485388}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485388}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485388}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485388}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485388}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485392}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485392}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485392}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485392}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485392}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [UnifiedBannerFix] Ensuring upload interface is visible
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485487}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485487}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485487}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485487}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485487}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ installHook.js:1
(anonymous) @ DashboardNew.jsx:287
setTimeout
window.setTimeout @ consolidated-dashboard-fix.js:86
(anonymous) @ DashboardNew.jsx:285
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485528}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485528}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485528}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485528}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067485528}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', content-type: 'application/json', …}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
 OAuth user data: {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email: <EMAIL>
 AuthContext (initAuth): Handling auth state for refresh
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e'}
 [HeadersFix] Fetch request to /api/sync-tools/manage-auth-state with headers: {Content-Type: 'application/json', Accept: 'application/json'}
 🔧 [EMERGENCY] Fixed relative URL: /api/sync-tools/manage-auth-state → http://localhost:5175/api/sync-tools/manage-auth-state
 🔧 [EMERGENCY] Bypassing failing API endpoint
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Emergency fallback active'}
 Development mode: Using mock consistency check result
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): {id: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T19:58:14.658817Z', …}
 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
 Found OAuth email (auth change): <EMAIL>
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.default with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&limit=1 with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 [HeadersFix] Fetch request to https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=subdomain&subdomain=not.is.null with headers: {accept: 'application/json', accept-profile: 'public', apikey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzd…AwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU', authorization: 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09…sc2V9.fBhqm4c-0LymzHQ84316bCPt9anL6w2RGQckQr7EgcA', content-type: 'application/json', …}
 Supabase connection test successful!
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [App] Available subdomains for testing: (2) ['damonkost', 'scout']
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: null
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Your Law Firm
 titleText: Your Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: null
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487259}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487259}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487259}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487259}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487259}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487259}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487262}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487262}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487262}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487262}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487262}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487262}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Config sent to 3 iframes successfully
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Development Law Firm
 titleText: Development Law Firm
 logoUrl: 
 primaryColor: #4B74AA
 secondaryColor: #2C3E50
 vapiInstructions: 
 vapiAssistantId: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 ✅ Supabase connection test successful!
 ✅ Supabase configuration verified and working
 SimplePreviewPage: Successfully loaded attorney data from Supabase: {subdomain: undefined, firm_name: undefined, title_text: undefined, welcome_message: undefined, practice_description: undefined}
 SimplePreviewPage: Complete attorney data from database: []
 SimplePreviewPage: Attorney data loaded successfully, mapping to preview config...
 SimplePreviewPage: Mapped preview config: {firmName: 'Your Law Firm', titleText: '', welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?", practiceDescription: 'Your AI legal assistant is ready to help'}
 SimplePreviewPage: Config before database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Config after database merge: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Key fields from database: {firm_name: undefined, title_text: undefined, welcome_message: undefined}
 SimplePreviewPage: Final config: {firmName: 'Your Law Firm', titleText: '', attorneyName: 'Your Name', practiceAreas: Array(0), state: '', …}
 SimplePreviewPage: Setting config with firm name: Your Law Firm
 SimplePreviewPage: Setting config with welcome message: Hello! I'm Scout, your legal assistant. How can I help you today?
 SimplePreviewPage: Config updated from database, forcing re-render
 [DashboardIframeManager] Received PREVIEW_READY message from iframe
 [DashboardNew] Using enhanced iframe manager
 [DashboardIframeManager] Sending config to preview iframes
 [DashboardIframeManager] Found 3 potential preview iframes
 [DashboardIframeManager] Found 3 accessible preview iframes
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [DashboardIframeManager] Sent message PREVIEW_CONFIG_UPDATE to iframe
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Your Law Firm', titleText: '', theme: 'dark', vapiAssistantId: null, primaryColor: '#4B74AA', …}
 Using default logo path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 Button component received mascot URL: /PRIMARY CLEAR.png
 Using relative mascot path: /PRIMARY CLEAR.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487412}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487412}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487412}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Sent response back to parent
 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487412}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
SimplePreviewPage.jsx:220 🎯 [SimplePreviewPage] Received message from parent: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487412}
SimplePreviewPage.jsx:224 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
SimplePreviewPage.jsx:226 🎯 [SimplePreviewPage] Config updated successfully
SimplePreviewPage.jsx:236 🎯 [SimplePreviewPage] Sent response back to parent
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: {type: 'PREVIEW_CONFIG_UPDATE', config: {…}, timestamp: 1749067487412}
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: PREVIEW_CONFIG_UPDATE
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: {firmName: 'Development Law Firm', attorneyName: 'Development Attorney', practiceAreas: undefined, state: undefined, practiceDescription: undefined, …}
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', assistantId: undefined}
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: 8d962209-530e-45d2-b2d6-17ed1ef55b3c
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText (from firmName), attorneyName, vapiAssistantId
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
dashboard-iframe-manager.js:29 [DashboardIframeManager] Received response for PREVIEW_CONFIG_UPDATE
dashboard-iframe-manager.js:29 [DashboardIframeManager] Config sent successfully to iframe: http://localhost:5175/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:684 [DashboardNew] Config sent to 3 iframes successfully
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'Development Law Firm', titleText: '', theme: 'dark', vapiAssistantId: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', primaryColor: '#4B74AA', …}
EnhancedPreviewNew.jsx:542 Using default logo path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
Button.jsx:209 Button component received mascot URL: /PRIMARY CLEAR.png
Button.jsx:225 Using relative mascot path: /PRIMARY CLEAR.png
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
AuthContext.jsx:123 AuthContext (initAuth): Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Emergency fallback active'}
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
supabaseConfigVerifier.js?t=1749032804912:143 ✅ Supabase configuration verified and working
supabaseConfigVerifier.js?t=1749032804912:92 ✅ Supabase connection test successful!
App.jsx:683 Supabase is properly configured and connected! [{…}]
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
reactPolyfill.js:70 [ReactPolyfill] Stopped monitoring React.createContext
