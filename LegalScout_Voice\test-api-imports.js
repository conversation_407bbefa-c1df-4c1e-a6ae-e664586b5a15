/**
 * Test API imports to find the problematic file
 */

console.log('Testing API imports...');

try {
  console.log('1. Testing basic imports...');
  
  console.log('2. Testing website-import...');
  const websiteImport = await import('./api/website-import.js');
  console.log('✅ website-import.js imported successfully');
  
  console.log('3. Testing debug-website-import...');
  const debugWebsiteImport = await import('./api/debug-website-import.js');
  console.log('✅ debug-website-import.js imported successfully');
  
  console.log('4. Testing main API handler...');
  const apiHandler = await import('./api/index.js');
  console.log('✅ api/index.js imported successfully');
  
  console.log('🎉 All imports successful!');
  
} catch (error) {
  console.error('❌ Import failed:', error);
  console.error('Stack trace:', error.stack);
}
