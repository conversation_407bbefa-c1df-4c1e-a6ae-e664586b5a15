<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Import.meta Fix</title>
    
    <!-- Load the polyfill first -->
    <script src="/import-meta-polyfill.js"></script>
    
    <!-- Test script that would normally cause import.meta errors -->
    <script>
        console.log('🧪 Testing import.meta fix...');
        
        // Test 1: Direct access to import.meta.env (should work with polyfill)
        try {
            console.log('Test 1: Accessing window.import.meta.env');
            const env = window.import.meta.env;
            console.log('✅ Success! Environment variables:', Object.keys(env));
        } catch (e) {
            console.error('❌ Test 1 failed:', e.message);
        }
        
        // Test 2: Using the safe accessor function
        try {
            console.log('Test 2: Using getSafeImportMetaEnv()');
            const env = getSafeImportMetaEnv();
            console.log('✅ Success! Environment variables:', Object.keys(env));
        } catch (e) {
            console.error('❌ Test 2 failed:', e.message);
        }
        
        // Test 3: Check specific environment variables
        try {
            console.log('Test 3: Checking specific environment variables');
            const env = getSafeImportMetaEnv();
            console.log('  VITE_VAPI_PUBLIC_KEY:', env.VITE_VAPI_PUBLIC_KEY ? 'SET' : 'NOT SET');
            console.log('  VITE_VAPI_SECRET_KEY:', env.VITE_VAPI_SECRET_KEY ? 'SET' : 'NOT SET');
            console.log('  VITE_SUPABASE_URL:', env.VITE_SUPABASE_URL ? 'SET' : 'NOT SET');
            console.log('✅ Test 3 passed');
        } catch (e) {
            console.error('❌ Test 3 failed:', e.message);
        }
        
        console.log('🎉 Import.meta fix test completed!');
    </script>
    
    <!-- Test loading one of the previously problematic scripts -->
    <script src="/production-debug-vapi.js"></script>
</head>
<body>
    <h1>Import.meta Fix Test</h1>
    <p>Check the browser console for test results.</p>
    <p>If you see "🎉 Import.meta fix test completed!" without errors, the fix is working!</p>
</body>
</html>
