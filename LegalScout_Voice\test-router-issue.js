/**
 * Test to isolate the router issue
 */

console.log('Testing router issue...');

try {
  console.log('1. Testing basic Express import...');
  const express = await import('express');
  console.log('✅ Express imported successfully');
  
  console.log('2. Creating Express app...');
  const app = express.default();
  console.log('✅ Express app created successfully');
  
  console.log('3. Testing basic middleware...');
  app.use(express.default.json());
  console.log('✅ Basic middleware added successfully');
  
  console.log('4. Testing simple route...');
  app.get('/test', (req, res) => {
    res.json({ message: 'test' });
  });
  console.log('✅ Simple route added successfully');
  
  console.log('5. Testing router creation...');
  const router = express.default.Router();
  console.log('✅ Router created successfully');
  
  console.log('6. Testing router route...');
  router.get('/router-test', (req, res) => {
    res.json({ message: 'router test' });
  });
  console.log('✅ Router route added successfully');
  
  console.log('7. Testing router mounting...');
  app.use('/api', router);
  console.log('✅ Router mounted successfully');
  
  console.log('🎉 All router tests passed!');
  
} catch (error) {
  console.error('❌ Router test failed:', error);
  console.error('Stack trace:', error.stack);
}
