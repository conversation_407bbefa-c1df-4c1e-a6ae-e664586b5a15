<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Test</title>
    <!-- Load Supabase JS from CDN -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div id="result"></div>
    
    <script>
        // Wait for everything to load
        window.onload = function() {
            console.log('Window loaded');
            
            const resultDiv = document.getElementById('result');
            
            // Supabase credentials
            const SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
            const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
            
            try {
                // Check if Supabase is defined
                if (typeof supabase === 'undefined') {
                    resultDiv.innerHTML += '<p>Supabase global object not found, creating client manually</p>';
                    
                    // Create Supabase client
                    const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
                    
                    resultDiv.innerHTML += '<p>Supabase client created successfully</p>';
                    
                    // Test a simple query
                    supabaseClient
                        .from('attorneys')
                        .select('id, name, firm_name')
                        .limit(5)
                        .then(({ data, error }) => {
                            if (error) {
                                resultDiv.innerHTML += `<p>Error: ${error.message}</p>`;
                            } else {
                                resultDiv.innerHTML += `<p>Query successful! Found ${data.length} attorneys</p>`;
                                resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                            }
                        })
                        .catch(error => {
                            resultDiv.innerHTML += `<p>Unexpected error: ${error.message}</p>`;
                        });
                } else {
                    resultDiv.innerHTML += '<p>Supabase global object found</p>';
                    
                    // Test a simple query using the global object
                    supabase
                        .from('attorneys')
                        .select('id, name, firm_name')
                        .limit(5)
                        .then(({ data, error }) => {
                            if (error) {
                                resultDiv.innerHTML += `<p>Error: ${error.message}</p>`;
                            } else {
                                resultDiv.innerHTML += `<p>Query successful! Found ${data.length} attorneys</p>`;
                                resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                            }
                        })
                        .catch(error => {
                            resultDiv.innerHTML += `<p>Unexpected error: ${error.message}</p>`;
                        });
                }
            } catch (error) {
                resultDiv.innerHTML += `<p>Error initializing Supabase: ${error.message}</p>`;
            }
        };
    </script>
</body>
</html>
