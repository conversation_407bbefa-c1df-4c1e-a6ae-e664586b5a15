/**
 * Test script to verify Vapi API key and endpoints
 */

const API_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const BASE_URL = 'https://api.vapi.ai';

async function testVapiAPI() {
  console.log('Testing Vapi API...');
  console.log('API Key:', API_KEY.substring(0, 8) + '...');
  console.log('Base URL:', BASE_URL);

  // Test 1: List assistants
  try {
    console.log('\n--- Test 1: List Assistants ---');
    const response = await fetch(`${BASE_URL}/assistants`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    console.log('Response status:', response.status, response.statusText);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const data = await response.json();
      console.log('Success! Assistants:', data);
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Error listing assistants:', error);
  }

  // Test 2: Create assistant
  try {
    console.log('\n--- Test 2: Create Assistant ---');
    const assistantConfig = {
      name: 'Test Assistant',
      instructions: 'You are a test assistant.',
      firstMessage: 'Hello, I am a test assistant.',
      firstMessageMode: 'assistant-speaks-first',
      llm: {
        provider: 'openai',
        model: 'gpt-4o'
      },
      voice: {
        provider: 'playht',
        voiceId: 'sarah'
      },
      transcriber: {
        provider: 'deepgram',
        model: 'nova-3'
      }
    };

    console.log('Assistant config:', JSON.stringify(assistantConfig, null, 2));

    const response = await fetch(`${BASE_URL}/assistants`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(assistantConfig)
    });

    console.log('Response status:', response.status, response.statusText);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const data = await response.json();
      console.log('Success! Created assistant:', data);
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Error creating assistant:', error);
  }
}

// Run the test
testVapiAPI().catch(console.error);
