/**
 * Website Import Test Suite
 * Tests each component of the website import system
 */

// Test configuration
const TEST_CONFIG = {
  testUrl: 'https://www.smithlawfirm.com', // Example law firm
  localApiUrl: 'http://localhost:5174/api/website-import',
  jinaUrl: 'https://r.jina.ai/',
  openaiUrl: 'https://api.openai.com/v1/chat/completions'
};

// Test results storage
const testResults = {
  jinaTest: null,
  openaiTest: null,
  apiTest: null,
  mcpBlockerTest: null,
  overall: { passed: 0, failed: 0 }
};

/**
 * Test 1: Jina AI Reader API
 */
async function testJinaReader() {
  console.log('\n🧪 Testing Jina AI Reader...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.jinaUrl}${TEST_CONFIG.testUrl}`, {
      headers: {
        'Accept': 'application/json',
        'X-With-Generated-Alt': 'true'
      }
    });

    if (!response.ok) {
      throw new Error(`Jina API error: ${response.status} ${response.statusText}`);
    }

    const content = await response.text();
    
    if (content && content.length > 100) {
      console.log('✅ Jina AI Reader: SUCCESS');
      console.log(`   Content length: ${content.length} characters`);
      console.log(`   Sample: ${content.substring(0, 200)}...`);
      testResults.jinaTest = { success: true, contentLength: content.length };
      testResults.overall.passed++;
      return content;
    } else {
      throw new Error('Jina returned empty or too short content');
    }
  } catch (error) {
    console.log('❌ Jina AI Reader: FAILED');
    console.log(`   Error: ${error.message}`);
    testResults.jinaTest = { success: false, error: error.message };
    testResults.overall.failed++;
    return null;
  }
}

/**
 * Test 2: OpenAI API (with mock key check)
 */
async function testOpenAI(content) {
  console.log('\n🧪 Testing OpenAI API...');
  
  // Check if we have an API key
  const hasApiKey = process.env.OPENAI_API_KEY && 
                   process.env.OPENAI_API_KEY !== 'your_openai_api_key_here';
  
  if (!hasApiKey) {
    console.log('⚠️ OpenAI API: SKIPPED (No API key configured)');
    console.log('   Add your OpenAI API key to .env file');
    testResults.openaiTest = { success: false, error: 'No API key' };
    testResults.overall.failed++;
    return null;
  }

  try {
    const response = await fetch(TEST_CONFIG.openaiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        response_format: { type: "json_object" },
        messages: [
          {
            role: 'system',
            content: 'Extract basic info from this law firm content. Return JSON with: {"firmName": "string", "test": true}'
          },
          {
            role: 'user',
            content: content ? content.substring(0, 1000) : 'Test law firm content'
          }
        ],
        max_tokens: 500
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenAI API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    const extractedData = JSON.parse(result.choices[0].message.content);
    
    console.log('✅ OpenAI API: SUCCESS');
    console.log(`   Extracted: ${JSON.stringify(extractedData, null, 2)}`);
    testResults.openaiTest = { success: true, data: extractedData };
    testResults.overall.passed++;
    return extractedData;
  } catch (error) {
    console.log('❌ OpenAI API: FAILED');
    console.log(`   Error: ${error.message}`);
    testResults.openaiTest = { success: false, error: error.message };
    testResults.overall.failed++;
    return null;
  }
}

/**
 * Test 3: Local API Endpoint
 */
async function testLocalAPI() {
  console.log('\n🧪 Testing Local API Endpoint...');
  
  try {
    const response = await fetch(TEST_CONFIG.localApiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: TEST_CONFIG.testUrl })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Local API: SUCCESS');
      console.log(`   Firm: ${result.data.firmName || 'Not extracted'}`);
      console.log(`   Confidence: ${result.data.extractionConfidence || 'N/A'}`);
      testResults.apiTest = { success: true, data: result.data };
      testResults.overall.passed++;
      return result.data;
    } else {
      throw new Error(result.error || 'API returned failure');
    }
  } catch (error) {
    console.log('❌ Local API: FAILED');
    console.log(`   Error: ${error.message}`);
    testResults.apiTest = { success: false, error: error.message };
    testResults.overall.failed++;
    return null;
  }
}

/**
 * Test 4: MCP Blocker
 */
async function testMCPBlocker() {
  console.log('\n🧪 Testing MCP Blocker...');
  
  try {
    // Import the MCP blocker
    const mcpBlocker = await import('./src/utils/mcpBlocker.js').then(m => m.default);
    
    // Test blocking
    mcpBlocker.startBlocking();
    
    if (!mcpBlocker.isCurrentlyBlocking()) {
      throw new Error('MCP blocker failed to start');
    }
    
    // Test fetch blocking
    try {
      await fetch('https://mcp.vapi.ai/test');
      throw new Error('MCP request was not blocked');
    } catch (error) {
      if (error.message.includes('MCP_BLOCKED_DURING_IMPORT')) {
        console.log('✅ MCP blocking works correctly');
      } else {
        throw error;
      }
    }
    
    // Test stopping
    mcpBlocker.stopBlocking();
    
    if (mcpBlocker.isCurrentlyBlocking()) {
      throw new Error('MCP blocker failed to stop');
    }
    
    console.log('✅ MCP Blocker: SUCCESS');
    testResults.mcpBlockerTest = { success: true };
    testResults.overall.passed++;
    return true;
  } catch (error) {
    console.log('❌ MCP Blocker: FAILED');
    console.log(`   Error: ${error.message}`);
    testResults.mcpBlockerTest = { success: false, error: error.message };
    testResults.overall.failed++;
    return false;
  }
}

/**
 * Test 5: Environment Check
 */
function testEnvironment() {
  console.log('\n🧪 Testing Environment...');
  
  const checks = {
    nodeEnv: process.env.NODE_ENV,
    hasOpenAI: !!process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here',
    hasVapi: !!process.env.VAPI_PRIVATE_KEY,
    hasSupabase: !!process.env.SUPABASE_URL
  };
  
  console.log('📋 Environment Status:');
  console.log(`   Node Environment: ${checks.nodeEnv || 'Not set'}`);
  console.log(`   OpenAI API Key: ${checks.hasOpenAI ? '✅ Configured' : '❌ Missing'}`);
  console.log(`   Vapi API Key: ${checks.hasVapi ? '✅ Configured' : '❌ Missing'}`);
  console.log(`   Supabase URL: ${checks.hasSupabase ? '✅ Configured' : '❌ Missing'}`);
  
  return checks;
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log('🚀 Starting Website Import Test Suite...');
  console.log(`📍 Test URL: ${TEST_CONFIG.testUrl}`);
  
  // Environment check
  const envCheck = testEnvironment();
  
  // Run tests in sequence
  const jinaContent = await testJinaReader();
  const openaiResult = await testOpenAI(jinaContent);
  const apiResult = await testLocalAPI();
  const mcpResult = await testMCPBlocker();
  
  // Print summary
  console.log('\n📊 TEST SUMMARY:');
  console.log(`✅ Passed: ${testResults.overall.passed}`);
  console.log(`❌ Failed: ${testResults.overall.failed}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.overall.passed / (testResults.overall.passed + testResults.overall.failed)) * 100)}%`);
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  
  if (!envCheck.hasOpenAI) {
    console.log('🔑 Add OpenAI API key to .env file');
  }
  
  if (testResults.jinaTest?.success && !testResults.openaiTest?.success) {
    console.log('🤖 Jina works but OpenAI failed - check API key and quota');
  }
  
  if (!testResults.apiTest?.success) {
    console.log('🔧 Local API failed - check server logs and CORS settings');
  }
  
  if (!testResults.mcpBlockerTest?.success) {
    console.log('🚫 MCP blocker failed - check import path and implementation');
  }
  
  return testResults;
}

// Run tests if called directly
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}

// Export for use in other files
if (typeof module !== 'undefined') {
  module.exports = { runAllTests, testResults };
}
