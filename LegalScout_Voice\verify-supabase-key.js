// <PERSON><PERSON><PERSON> to verify that the new Supabase key is working
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_KEY || process.env.REACT_APP_SUPABASE_KEY;

console.log('=== SUPABASE KEY VERIFICATION ===');
console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseKey ? `${supabaseKey.substring(0, 10)}...${supabaseKey.substring(supabaseKey.length - 10)}` : 'missing');

// Create Supabase client
try {
  console.log('Creating Supabase client...');
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  // Test connection
  console.log('Testing connection...');
  supabase
    .from('attorneys')
    .select('*')
    .limit(1)
    .then(({ data, error }) => {
      if (error) {
        console.error('Error querying attorneys table:', error);
        process.exit(1);
      } else {
        console.log('✅ Connection successful!');
        console.log('Data:', data);
        process.exit(0);
      }
    })
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
} catch (error) {
  console.error('Error creating Supabase client:', error);
  process.exit(1);
}
