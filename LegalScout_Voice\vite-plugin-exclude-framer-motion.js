/**
 * Vite plugin to exclude Framer Motion from the build
 * This plugin will replace all imports of Framer Motion with mock implementations
 */

export default function excludeFramerMotion() {
  const mockFramerMotionCode = `
    console.log('[ExcludeFramerMotion] Loading mock Framer Motion implementation');

    // Create empty mock components and functions
    const noop = () => {};
    const Component = (props) => props.children || null;

    // Create a mock context
    const createContext = (defaultValue) => ({
      Provider: Component,
      Consumer: Component,
      displayName: 'MockContext',
      _currentValue: defaultValue,
      _currentValue2: defaultValue,
      _threadCount: 0,
      _defaultValue: defaultValue
    });

    // Create mock contexts
    export const LayoutGroupContext = createContext({});
    export const MotionConfigContext = createContext({
      transformPagePoint: undefined,
      isStatic: false,
      reducedMotion: "never"
    });

    // Make contexts available globally
    if (typeof window !== 'undefined') {
      window.LayoutGroupContext = LayoutGroupContext;
      window.MotionConfigContext = MotionConfigContext;
      window.React = window.React || {};
      window.React.createContext = createContext;
    }

    // Export common components and functions
    export const motion = new Proxy({}, {
      get: (target, prop) => Component
    });

    export const AnimatePresence = Component;
    export const MotionConfig = Component;
    export const LazyMotion = Component;
    export const m = motion;

    // Export common hooks and utilities
    export const useAnimation = () => ({ start: noop, stop: noop });
    export const useMotionValue = () => ({ get: () => 0, set: noop });
    export const useTransform = () => 0;
    export const useSpring = () => ({ get: () => 0, set: noop });
    export const useCycle = () => [0, noop];
    export const useMotionTemplate = () => '';
    export const useInView = () => ({ inView: false, ref: {} });
    export const useScroll = () => ({
      scrollY: { get: () => 0, onChange: noop },
      scrollYProgress: { get: () => 0, onChange: noop }
    });

    // Export common variants
    export const Variants = {};

    // Default export
    export default {
      motion,
      AnimatePresence,
      MotionConfig,
      LazyMotion,
      m,
      useAnimation,
      useMotionValue,
      useTransform,
      useSpring,
      useCycle,
      useMotionTemplate,
      useInView,
      useScroll,
      Variants,
      MotionConfigContext,
      LayoutGroupContext
    };
  `;

  return {
    name: 'exclude-framer-motion',
    resolveId(id) {
      // Check if the import is for framer-motion or any of its submodules
      if (id === 'framer-motion' ||
          id.startsWith('framer-motion/') ||
          id.includes('LayoutGroupContext') ||
          id.includes('MotionConfigContext') ||
          id.includes('framer-motion')) {
        console.log(`[ExcludeFramerMotion] Excluding import: ${id}`);
        // Return a virtual module ID
        return `\0virtual:framer-motion-mock`;
      }
      return null;
    },
    load(id) {
      // If this is our virtual module, return the mock implementation
      if (id === `\0virtual:framer-motion-mock`) {
        console.log('[ExcludeFramerMotion] Providing mock implementation');
        return mockFramerMotionCode;
      }
      return null;
    },
    transform(code, id) {
      // Also transform any code that tries to import from framer-motion
      if (code.includes('framer-motion') ||
          code.includes('LayoutGroupContext') ||
          code.includes('MotionConfigContext')) {
        console.log(`[ExcludeFramerMotion] Transforming code in: ${id}`);
        // Replace dynamic imports
        code = code.replace(
          /import\s*\(\s*['"](.+?framer-motion.+?)['"]\s*\)/g,
          `Promise.resolve({})`
        );
        // Replace static imports
        code = code.replace(
          /import\s+.*?from\s+['"](.+?framer-motion.+?)['"]\s*;?/g,
          '// Removed framer-motion import'
        );
        return code;
      }
      return null;
    }
  };
}
