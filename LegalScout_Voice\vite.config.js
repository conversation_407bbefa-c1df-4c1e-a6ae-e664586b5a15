import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import excludeFramerMotion from './vite-plugin-exclude-framer-motion'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  define: {
    // Define global variables for the build
    'process.env.VITE_DISABLE_FRAMER_MOTION': JSON.stringify(process.env.VITE_DISABLE_FRAMER_MOTION || 'false'),
    'process.env.NODE_ENV': JSON.stringify(mode),
    // Supabase environment variables
    'import.meta.env.VITE_SUPABASE_URL': JSON.stringify(process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co'),
    'import.meta.env.VITE_SUPABASE_KEY': JSON.stringify(process.env.VITE_SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU'),
    'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(process.env.VITE_SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU'),
    // Vapi environment variables
    'import.meta.env.VITE_VAPI_PUBLIC_KEY': JSON.stringify(process.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7'),
    'import.meta.env.VITE_VAPI_SECRET_KEY': JSON.stringify(process.env.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564'),
    // React App environment variables for compatibility
    'import.meta.env.REACT_APP_SUPABASE_URL': JSON.stringify(process.env.REACT_APP_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co'),
    'import.meta.env.REACT_APP_SUPABASE_KEY': JSON.stringify(process.env.REACT_APP_SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU'),
    'import.meta.env.REACT_APP_SUPABASE_ANON_KEY': JSON.stringify(process.env.REACT_APP_SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU')
  },
  plugins: [react(), excludeFramerMotion()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            // Skip Framer Motion completely
            if (id.includes('framer-motion')) {
              return null; // Don't create a chunk for framer-motion
            }
            if (id.includes('react')) return 'vendor-react';
            if (id.includes('three')) return 'vendor-three';
            if (id.includes('react-icons')) return 'vendor-icons';
            return 'vendor'; // all other node_modules
          }
          // Create chunks for page components
          if (id.includes('/pages/')) {
            return 'pages';
          }
        },
        assetFileNames: (assetInfo) => {
          let extType = assetInfo.name.split('.').at(1);
          if (/png|jpe?g|svg|gif|tiff|bmp|ico|webp/i.test(extType)) {
            extType = 'img';
          }
          return `assets/${extType}/[name]-[hash][extname]`;
        }
      }
    },
    chunkSizeWarningLimit: 500,
    target: 'es2020',
    outDir: 'dist',
    assetsDir: 'assets',
    emptyOutDir: true,
    sourcemap: true,
    copyPublicDir: true
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
    exclude: ['framer-motion', 'framer-motion/*', 'three', 'three/*']
  },
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
    alias: {
      // Add explicit alias for problematic Framer Motion modules
      'framer-motion': path.resolve(__dirname, 'src/mocks/framer-motion.js'),
      'framer-motion/dist/es/context/LayoutGroupContext.mjs': path.resolve(__dirname, 'src/mocks/LayoutGroupContext.js'),
      'framer-motion/dist/es/context/MotionConfigContext.mjs': path.resolve(__dirname, 'src/mocks/MotionConfigContext.js')
    }
  },
  publicDir: 'public',
  server: {
    port: 5173,
    strictPort: false,
    host: true,
    hmr: {
      timeout: 5000
    },
    proxy: {
      // Proxy Vapi MCP requests to bypass CORS during development
      '/vapi-mcp/sse': {
        target: 'https://mcp.vapi.ai/sse',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/vapi-mcp\/sse/, ''),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Vapi proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Vapi Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Vapi Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
      // Proxy for the Vapi MCP server
      '/vapi-mcp-server/sse': {
        target: 'https://mcp.vapi.ai',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/vapi-mcp-server\/sse/, '/sse'),
        headers: {
          'Authorization': `Bearer ${process.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7'}`,
        },
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Vapi MCP server proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Vapi MCP server request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Vapi MCP server response:', proxyRes.statusCode, req.url);
          });
        },
      },
      // Proxy for Vapi API requests
      '/api/vapi-proxy': {
        target: 'https://api.vapi.ai',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/api\/vapi-proxy/, ''),
        headers: {
          'Authorization': `Bearer ${process.env.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564'}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Vapi proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Vapi proxy request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Vapi proxy response:', proxyRes.statusCode, req.url);
          });
        },
      },
      // Proxy for Slack webhook to bypass CORS in development
      '/api/bug-report': {
        target: '*******************************************************************************',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/api\/bug-report/, ''),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Slack webhook proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Slack webhook request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Slack webhook response:', proxyRes.statusCode, req.url);
          });
        },
      },
      // Add other proxies if needed
      // Proxy for Supabase API requests
      '/supabase-proxy': {
        target: 'https://utopqxsvudgrtiwenlzl.supabase.co',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/supabase-proxy/, ''),
        headers: {
          'apikey': process.env.VITE_SUPABASE_KEY || '',
        },
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Supabase proxy error', err);
          });
        },
      },
      // Health endpoint for Vapi Web SDK
      '/api/health': {
        target: 'http://localhost:5173',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => '/api/health.json',
        configure: (proxy, _options) => {
          proxy.on('error', (err, req, res) => {
            console.log('Health endpoint error:', err.message);
            // Fallback response for health check
            res.writeHead(200, {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            });
            res.end(JSON.stringify({
              status: 'healthy',
              timestamp: new Date().toISOString(),
              service: 'LegalScout Voice API (Development)',
              version: '1.0.0',
              environment: 'development'
            }));
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Health check request:', req.method, req.url);
          });
        },
      },
      // Proxy API requests to development server
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, req, res) => {
            console.log('API proxy error:', err.message);
            console.log('Make sure dev server is running: npm run dev:api');
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Proxying API request:', req.method, req.url);
          });
        },
      },
      // Proxy for mock services to handle CORS
      '/mock-*': {
        target: 'http://localhost:5173',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // Handle OPTIONS requests for CORS preflight
            if (req.method === 'OPTIONS') {
              res.writeHead(200, {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept',
                'Access-Control-Max-Age': '86400'
              });
              res.end();
              return;
            }
          });
        },
      }
    }
  },
  base: '/'
}))