#!/usr/bin/env python3
"""
Calculate average hours per week from Git commit history
"""

import subprocess
import re
from datetime import datetime, timedelta
from collections import defaultdict

def get_git_commits():
    """Get commit data from git log"""
    try:
        result = subprocess.run([
            'git', 'log', '--pretty=format:%ad', '--date=iso'
        ], capture_output=True, text=True, check=True, encoding='utf-8', errors='ignore')
        return result.stdout.strip().split('\n')
    except Exception as e:
        print(f"Error getting git commits: {e}")
        return []

def parse_commit_times(commit_lines):
    """Parse commit timestamps"""
    commits = []
    for line in commit_lines:
        if not line.strip():
            continue
        try:
            # Parse: "2025-06-07 02:13:19 -0400"
            date_part = line.split(' -')[0]  # Remove timezone
            commit_time = datetime.strptime(date_part, "%Y-%m-%d %H:%M:%S")
            commits.append(commit_time)
        except ValueError:
            continue
    return sorted(commits)

def calculate_daily_hours(commits):
    """Calculate estimated work hours per day"""
    daily_commits = defaultdict(list)
    
    # Group commits by date
    for commit in commits:
        date = commit.date()
        daily_commits[date].append(commit)
    
    daily_hours = {}
    
    for date, day_commits in daily_commits.items():
        if len(day_commits) == 1:
            # Single commit = assume 1 hour of work
            daily_hours[date] = 1.0
        else:
            # Multiple commits = time span between first and last
            first = min(day_commits)
            last = max(day_commits)
            span_hours = (last - first).total_seconds() / 3600
            
            # Minimum 1 hour, maximum 16 hours per day
            daily_hours[date] = max(1.0, min(16.0, span_hours))
    
    return daily_hours

def calculate_weekly_stats(daily_hours):
    """Calculate weekly statistics"""
    if not daily_hours:
        return {}
    
    # Get date range
    dates = list(daily_hours.keys())
    start_date = min(dates)
    end_date = max(dates)
    
    # Calculate total weeks
    total_days = (end_date - start_date).days + 1
    total_weeks = total_days / 7.0
    
    # Calculate totals
    total_hours = sum(daily_hours.values())
    total_days_worked = len(daily_hours)
    
    # Weekly averages
    avg_hours_per_week = total_hours / total_weeks if total_weeks > 0 else 0
    avg_days_per_week = total_days_worked / total_weeks if total_weeks > 0 else 0
    avg_hours_per_day = total_hours / total_days_worked if total_days_worked > 0 else 0
    
    return {
        'start_date': start_date,
        'end_date': end_date,
        'total_weeks': total_weeks,
        'total_hours': total_hours,
        'total_days_worked': total_days_worked,
        'avg_hours_per_week': avg_hours_per_week,
        'avg_days_per_week': avg_days_per_week,
        'avg_hours_per_day': avg_hours_per_day
    }

def main():
    print("Analyzing Git commit history for weekly hours...")
    
    # Get commits
    commit_lines = get_git_commits()
    if not commit_lines:
        print("No commits found")
        return
    
    print(f"Found {len(commit_lines)} commits")
    
    # Parse commit times
    commits = parse_commit_times(commit_lines)
    if not commits:
        print("No valid commit timestamps found")
        return
    
    print(f"Parsed {len(commits)} valid commits")
    
    # Calculate daily hours
    daily_hours = calculate_daily_hours(commits)
    print(f"Work detected on {len(daily_hours)} days")
    
    # Calculate weekly stats
    stats = calculate_weekly_stats(daily_hours)
    
    if not stats:
        print("No statistics available")
        return
    
    # Display results
    print(f"\n{'='*50}")
    print("WEEKLY HOURS ANALYSIS")
    print(f"{'='*50}")
    print(f"Analysis Period: {stats['start_date']} to {stats['end_date']}")
    print(f"Total Weeks: {stats['total_weeks']:.1f}")
    print(f"Days Worked: {stats['total_days_worked']}")
    print(f"Total Hours: {stats['total_hours']:.1f}")
    print(f"\nAVERAGES:")
    print(f"Hours per Week: {stats['avg_hours_per_week']:.1f}")
    print(f"Days per Week: {stats['avg_days_per_week']:.1f}")
    print(f"Hours per Day: {stats['avg_hours_per_day']:.1f}")
    
    # Recent activity (last 4 weeks)
    recent_cutoff = max(daily_hours.keys()) - timedelta(weeks=4)
    recent_hours = {date: hours for date, hours in daily_hours.items() if date >= recent_cutoff}
    
    if recent_hours:
        recent_total = sum(recent_hours.values())
        recent_weeks = 4.0
        recent_avg = recent_total / recent_weeks
        
        print(f"\nRECENT ACTIVITY (Last 4 weeks):")
        print(f"Hours per Week: {recent_avg:.1f}")
        print(f"Days Worked: {len(recent_hours)}")

if __name__ == "__main__":
    main()
