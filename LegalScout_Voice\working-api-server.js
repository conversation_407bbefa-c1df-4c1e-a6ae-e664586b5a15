/**
 * Working API Server for Website Import
 * Bypasses the problematic routing issues
 */

import express from 'express';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = 3001;

// Simple CORS middleware
app.use((req, res, next) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept');
  res.setHeader('Access-Control-Max-Age', '86400');
  
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  next();
});

// Parse JSON bodies
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    environment: {
      hasOpenAI: !!process.env.OPENAI_API_KEY,
      hasVapi: !!process.env.VAPI_PRIVATE_KEY,
      nodeEnv: process.env.NODE_ENV
    }
  });
});

// Website import endpoint
app.post('/api/website-import', async (req, res) => {
  console.log('📥 Website import request:', req.body?.url);
  
  try {
    // Import and call the handler
    const { default: websiteImport } = await import('./api/website-import.js');
    await websiteImport(req, res);
  } catch (error) {
    console.error('❌ Website import error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message,
      debug: {
        stack: error.stack,
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Debug website import endpoint
app.post('/api/debug-website-import', async (req, res) => {
  console.log('🔧 Debug website import request:', req.body?.url);
  
  try {
    // Import and call the handler
    const { default: debugWebsiteImport } = await import('./api/debug-website-import.js');
    await debugWebsiteImport(req, res);
  } catch (error) {
    console.error('❌ Debug website import error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message,
      debug: {
        stack: error.stack,
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Catch all for debugging
app.all('*', (req, res) => {
  console.log(`📍 Unhandled request: ${req.method} ${req.url}`);
  res.status(404).json({ 
    error: 'Endpoint not found',
    method: req.method,
    url: req.url,
    availableEndpoints: [
      'GET /health',
      'POST /api/website-import',
      'POST /api/debug-website-import'
    ]
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('🚨 Server error:', error);
  res.status(500).json({ 
    error: 'Internal server error',
    message: error.message 
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Working API server running on http://localhost:${PORT}`);
  console.log(`📡 Available endpoints:`);
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`   POST http://localhost:${PORT}/api/website-import`);
  console.log(`   POST http://localhost:${PORT}/api/debug-website-import`);
  console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
});
