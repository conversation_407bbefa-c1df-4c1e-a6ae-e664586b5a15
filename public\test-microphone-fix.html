<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Microphone Permission Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🎤 Microphone Permission Fix Test</h1>
    <p>This page tests the fix for the "Cannot read properties of undefined (reading 'enumerateDevices')" error.</p>
    
    <div class="test-container">
        <h2>Browser Capabilities</h2>
        <div id="browser-info"></div>
    </div>

    <div class="test-container">
        <h2>Test Actions</h2>
        <button onclick="testBrowserCapabilities()">🔍 Test Browser Capabilities</button>
        <button onclick="testEnumerateDevices()">📱 Test Enumerate Devices</button>
        <button onclick="testMicrophoneAccess()">🎤 Test Microphone Access</button>
        <button onclick="testOldMethod()">❌ Test Old (Broken) Method</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
    </div>

    <div class="test-container">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('test-results');
        const browserInfoDiv = document.getElementById('browser-info');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        function updateBrowserInfo(info) {
            browserInfoDiv.innerHTML = `
                <div class="test-result info">
                    <strong>Browser Media Capabilities:</strong><br>
                    Navigator available: ${info.hasNavigator ? '✅' : '❌'}<br>
                    MediaDevices available: ${info.hasMediaDevices ? '✅' : '❌'}<br>
                    getUserMedia available: ${info.hasGetUserMedia ? '✅' : '❌'}<br>
                    enumerateDevices available: ${info.hasEnumerateDevices ? '✅' : '❌'}<br>
                    Secure context: ${info.isSecureContext ? '✅' : '❌'}<br>
                    Protocol: ${info.protocol}<br>
                    Fully supported: ${info.isSupported ? '✅' : '❌'}
                    ${info.requiresHTTPS ? '<br><strong>⚠️ HTTPS required for MediaDevices API</strong>' : ''}
                </div>
            `;
        }

        // Media devices utilities (copied from the fix)
        const isMediaDevicesSupported = () => {
            return !!(navigator.mediaDevices && 
                     navigator.mediaDevices.getUserMedia && 
                     navigator.mediaDevices.enumerateDevices);
        };

        const getMediaDevicesErrorMessage = () => {
            if (!navigator.mediaDevices) {
                return 'MediaDevices API not available. Please ensure you\'re using HTTPS and a modern browser.';
            }
            
            if (!navigator.mediaDevices.getUserMedia) {
                return 'getUserMedia not available. Please update your browser or check permissions.';
            }
            
            if (!navigator.mediaDevices.enumerateDevices) {
                return 'enumerateDevices not available. Please update your browser.';
            }
            
            return 'Unknown MediaDevices API issue.';
        };

        const getBrowserMediaInfo = () => {
            const info = {
                hasNavigator: typeof navigator !== 'undefined',
                hasMediaDevices: !!(navigator && navigator.mediaDevices),
                hasGetUserMedia: !!(navigator && navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
                hasEnumerateDevices: !!(navigator && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices),
                isSecureContext: typeof window !== 'undefined' ? window.isSecureContext : false,
                protocol: typeof window !== 'undefined' ? window.location.protocol : 'unknown',
                userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
            };
            
            info.isSupported = info.hasGetUserMedia && info.hasEnumerateDevices;
            info.requiresHTTPS = info.protocol !== 'https:' && info.protocol !== 'file:';
            
            return info;
        };

        const safeEnumerateAudioDevices = async () => {
            if (!isMediaDevicesSupported()) {
                throw new Error(getMediaDevicesErrorMessage());
            }
            
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                return devices.filter(device => device.kind === 'audioinput');
            } catch (error) {
                throw new Error(`Failed to enumerate devices: ${error.message}`);
            }
        };

        // Test functions
        function testBrowserCapabilities() {
            addResult('🔍 Testing browser capabilities...', 'info');
            
            const info = getBrowserMediaInfo();
            updateBrowserInfo(info);
            
            if (info.isSupported) {
                addResult('✅ Browser fully supports MediaDevices API', 'success');
            } else {
                addResult('❌ Browser does not fully support MediaDevices API', 'error');
                if (info.requiresHTTPS) {
                    addResult('⚠️ HTTPS is required for MediaDevices API', 'warning');
                }
            }
        }

        async function testEnumerateDevices() {
            addResult('📱 Testing enumerate devices with safe method...', 'info');
            
            try {
                const audioInputs = await safeEnumerateAudioDevices();
                addResult(`✅ Found ${audioInputs.length} audio input devices`, 'success');
                
                if (audioInputs.length > 0) {
                    const deviceList = audioInputs.map((d, i) => 
                        `${i + 1}. ${d.label || 'Unknown Device'} (${d.deviceId.substring(0, 20)}...)`
                    ).join('<br>');
                    addResult(`Devices:<br>${deviceList}`, 'info');
                }
            } catch (error) {
                addResult(`❌ Enumerate devices failed: ${error.message}`, 'error');
            }
        }

        async function testMicrophoneAccess() {
            addResult('🎤 Testing microphone access with safe method...', 'info');
            
            try {
                if (!isMediaDevicesSupported()) {
                    throw new Error(getMediaDevicesErrorMessage());
                }
                
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                addResult('✅ Microphone access granted successfully', 'success');
                
                // Stop the stream immediately
                stream.getTracks().forEach(track => track.stop());
                addResult('🔇 Microphone stream stopped', 'info');
                
            } catch (error) {
                if (error.name === 'NotAllowedError') {
                    addResult('❌ Microphone access denied by user', 'error');
                } else if (error.name === 'NotFoundError') {
                    addResult('❌ No microphone found', 'error');
                } else {
                    addResult(`❌ Microphone access failed: ${error.message}`, 'error');
                }
            }
        }

        async function testOldMethod() {
            addResult('❌ Testing old (broken) method that causes the error...', 'warning');
            
            try {
                // This is the old method that causes the error
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');
                addResult(`✅ Old method worked: Found ${audioInputs.length} devices`, 'success');
            } catch (error) {
                addResult(`❌ Old method failed (as expected): ${error.message}`, 'error');
                
                if (error.message.includes("Cannot read properties of undefined")) {
                    addResult('🎯 This is the exact error we fixed!', 'warning');
                }
            }
        }

        // Auto-run browser capabilities test on page load
        window.addEventListener('load', () => {
            testBrowserCapabilities();
        });
    </script>
</body>
</html>
